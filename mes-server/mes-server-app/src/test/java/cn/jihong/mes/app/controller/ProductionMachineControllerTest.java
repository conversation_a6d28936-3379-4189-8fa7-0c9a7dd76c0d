package cn.jihong.mes.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListInVO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListOutVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.app.MesBootstrap;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = MesBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
//@ActiveProfiles(value = "dev")
@ActiveProfiles(value = "prod")
public class ProductionMachineControllerTest {

    @Resource
    private IProductionMachineService productionMachineService;
    

    @Test
    public void getProductionMachineList() {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductionMachinePO::getCompanyCode, "SITE-22");
        List<ProductionMachinePO> list = productionMachineService.list(lambdaQueryWrapper);
        List<String> machineNames = Lists.newArrayList();
        list.stream().filter(p -> StringUtil.isNotEmpty(p.getErpMachineName())).forEach(p -> {
            GetProductionMachineListInVO getProductionMachineListInVO = new GetProductionMachineListInVO();
            getProductionMachineListInVO.setMachineName(p.getErpMachineName());
            Pagination<GetProductionMachineListOutVO> productionMachineList = productionMachineService.getProductionMachineList(getProductionMachineListInVO);
            if (productionMachineList.getTotal() <= 0) {
                machineNames.add(p.getErpMachineName());
            }
        });
        System.out.println(machineNames);

    }
}

