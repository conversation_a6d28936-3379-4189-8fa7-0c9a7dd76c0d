package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblusruserbasisPO;
import cn.jihong.mes.api.model.vo.TblusruserbasisVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITblusruserbasisConvertor {

    ITblusruserbasisConvertor INSTANCE = Mappers.getMapper(ITblusruserbasisConvertor.class);

    TblusruserbasisVO POToVO (TblusruserbasisPO iTblusruserbasisPO);
    List<TblusruserbasisVO> POListToVOList (List<TblusruserbasisPO> tblopbasisPOS);
}
