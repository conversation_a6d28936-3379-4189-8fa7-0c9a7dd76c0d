package cn.jihong.mes.app.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.OssUtils;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesVO;
import cn.jihong.mes.api.service.IActualLossStatisticsService;
import cn.jihong.mes.app.property.AliyunOssProperty;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.oa.erp.api.model.dto.ActualLossStatisticsDTO;
import cn.jihong.oa.erp.api.model.vo.ActualLossStatisticsVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ActualLossStatisticsServiceImpl implements IActualLossStatisticsService {
    @DubboReference
    private ISffbTService sffbTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference
    private ISiteViewService siteViewService;
    @Resource
    private AliyunOssProperty aliyunOssProperty;
    @Override
    public List<ActualLossStatisticsMesVO> selectActualLosss(ActualLossStatisticsParamDTO actualLossStatisticsParamDTO) {
        if (actualLossStatisticsParamDTO == null){
            throw new CommonException("请求参数不能为空");
        }
        if (StringUtil.isEmpty(actualLossStatisticsParamDTO.getStartDate())){
            throw new CommonException("生产年月不能为空");
        }else {
            if(!TimeTypeUtil.isLegalDate(actualLossStatisticsParamDTO.getStartDate().length(), actualLossStatisticsParamDTO.getStartDate(), "yyyy-MM")){
                throw new CommonException("生产年月不符合格式：yyyy-MM");
            }
        }


        Long companyId = SecurityUtil.getCompanyId();
        // 当公司是集团的时候，设置为厦门吉宏
        if (companyId == 100L) {
            companyId = 106L;
        }
        // 根据公司id 获得 erp 公司id
        String siteCompanyId = (String)redisTemplate.opsForValue().get(RedisCacheConstant.COMPANY_ID + companyId);
        if (StringUtils.isBlank(siteCompanyId)) {
            siteCompanyId = siteViewService.getByCompanyId(companyId.toString());
            redisTemplate.opsForValue().set(RedisCacheConstant.COMPANY_ID + companyId, siteCompanyId,1, TimeUnit.HOURS);
        }
        log.info("公司信息：" + siteCompanyId);
        ActualLossStatisticsDTO lossStatisticsDTO = new ActualLossStatisticsDTO();
        if (StringUtil.isEmpty(actualLossStatisticsParamDTO.getFactoryCode())){
            lossStatisticsDTO.setFactoryCode(siteCompanyId);
        }else {
            lossStatisticsDTO.setFactoryCode(actualLossStatisticsParamDTO.getFactoryCode());
        }

        Date startDate = DateUtil.beginOfMonth(DateUtil.parse(actualLossStatisticsParamDTO.getStartDate(),"yyyy-MM"));
        Date endDate = DateUtil.offsetMonth(startDate,1);
        lossStatisticsDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setProductCategoryNo(actualLossStatisticsParamDTO.getProductCategoryNo());
        lossStatisticsDTO.setWorkerOrderNo(actualLossStatisticsParamDTO.getWorkerOrderNo());
        lossStatisticsDTO.setProductName(actualLossStatisticsParamDTO.getProductName());
        List<ActualLossStatisticsVO> actualLossStatisticsVOS = sffbTService.selectActualLosss(lossStatisticsDTO);
        if (CollectionUtil.isNotEmpty(actualLossStatisticsVOS)){
            //计算损耗率
            BigDecimal zero = new BigDecimal("0");
            for (ActualLossStatisticsVO actualLossStatisticsVO : actualLossStatisticsVOS) {
                //印刷损耗率
                if (actualLossStatisticsVO.getPrintDefectiveProductNum() != null
                    && actualLossStatisticsVO.getPrintGoodProductNum() != null
                    && actualLossStatisticsVO.getPrintDefectiveProductNum()
                        .add(actualLossStatisticsVO.getPrintGoodProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setPrintLossRate(actualLossStatisticsVO.getPrintDefectiveProductNum()
                        .divide(actualLossStatisticsVO.getPrintDefectiveProductNum()
                            .add(actualLossStatisticsVO.getPrintGoodProductNum()), 4, RoundingMode.HALF_UP)
                        .movePointRight(2) + "%");
                }
                //分条损耗率
                if (actualLossStatisticsVO.getStripingDefectiveProductNum() != null
                        && actualLossStatisticsVO.getStripingGoodProductNum() != null
                        && actualLossStatisticsVO.getStripingDefectiveProductNum()
                        .add(actualLossStatisticsVO.getStripingGoodProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setStripingRate(actualLossStatisticsVO.getStripingDefectiveProductNum()
                            .divide(actualLossStatisticsVO.getStripingDefectiveProductNum()
                                    .add(actualLossStatisticsVO.getStripingGoodProductNum()), 4, RoundingMode.HALF_UP)
                            .movePointRight(2) + "%");
                }
                //平张损耗率
                if (actualLossStatisticsVO.getFlatSheetDefectiveProductNum() != null
                    && actualLossStatisticsVO.getFlatSheetGoodProductNum() != null
                    && actualLossStatisticsVO.getFlatSheetGoodProductNum()
                        .add(actualLossStatisticsVO.getFlatSheetDefectiveProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setFlatSheetLossRate(actualLossStatisticsVO.getFlatSheetDefectiveProductNum()
                        .divide(actualLossStatisticsVO.getFlatSheetGoodProductNum()
                            .add(actualLossStatisticsVO.getFlatSheetDefectiveProductNum()), 4, RoundingMode.HALF_UP)
                        .movePointRight(2) + "%");
                }
                //模切损耗率
                if (actualLossStatisticsVO.getDieCutDefectiveProductNum() != null
                        && actualLossStatisticsVO.getDieCutGoodProductNum() != null
                        && actualLossStatisticsVO.getDieCutDefectiveProductNum()
                        .add(actualLossStatisticsVO.getDieCutGoodProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setDieCutRate(actualLossStatisticsVO.getDieCutDefectiveProductNum()
                            .divide(actualLossStatisticsVO.getDieCutGoodProductNum()
                                    .add(actualLossStatisticsVO.getDieCutDefectiveProductNum()), 4, RoundingMode.HALF_UP)
                            .movePointRight(2) + "%");
                }
                if (actualLossStatisticsVO.getOtherDefectiveProductNum() != null
                    && actualLossStatisticsVO.getOtherGoodProductNum() != null
                    && actualLossStatisticsVO.getOtherGoodProductNum()
                        .add(actualLossStatisticsVO.getOtherDefectiveProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setOtherLossRate(actualLossStatisticsVO.getOtherDefectiveProductNum()
                        .divide(actualLossStatisticsVO.getOtherGoodProductNum()
                            .add(actualLossStatisticsVO.getOtherDefectiveProductNum()), 4, RoundingMode.HALF_UP)
                        .movePointRight(2) + "%");
                }
                if (actualLossStatisticsVO.getDefectiveProductNum() != null
                    && actualLossStatisticsVO.getQualifiedQuantity() != null
                    && actualLossStatisticsVO.getQualifiedQuantity()
                        .add(actualLossStatisticsVO.getDefectiveProductNum()).compareTo(zero) == 1) {
                    actualLossStatisticsVO.setTotalLossRate(actualLossStatisticsVO.getDefectiveProductNum()
                        .divide(actualLossStatisticsVO.getQualifiedQuantity()
                            .add(actualLossStatisticsVO.getDefectiveProductNum()), 4, RoundingMode.HALF_UP)
                        .movePointRight(2) + "%");
                }

            }
            List<ActualLossStatisticsMesVO> actualLossStatisticsMesVOS = BeanUtil.copyToList(actualLossStatisticsVOS,ActualLossStatisticsMesVO.class);
            return actualLossStatisticsMesVOS;
        }
        return null;
    }

    @Override
    public String exportToExcel(ActualLossStatisticsParamDTO actualLossStatisticsParamDTO) {
        List<ActualLossStatisticsMesVO> actualLossStatisticsMesVOS = selectActualLosss(actualLossStatisticsParamDTO);
        String url = getDataToExcel(actualLossStatisticsMesVOS,ActualLossStatisticsMesVO.class,"工单损耗率");
        return url;
    }

    private String getDataToExcel(Collection<?> data,
                                  Class<?> clz, String fileName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        EasyExcel.write(os, clz).sheet("模板").doWrite(() -> {
            // 分页查询数据
            return data;
        });

        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
            String OBJECT_NAME = "temp";
            String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String objectName = OBJECT_NAME + "/" + dateStr + "/" + System.currentTimeMillis()
                    + "/" + fileName + ".xlsx";
            String uploadFile = OssUtils.uploadFile(aliyunOssProperty.getAccessId(), aliyunOssProperty.getAccessKey(),
                    aliyunOssProperty.getHzEndpoint(), aliyunOssProperty.getCommissionBucketName(), objectName,
                    byteArrayInputStream);
            return uploadFile;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 关闭
            try {
                byteArrayInputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            try {
                os.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
}
