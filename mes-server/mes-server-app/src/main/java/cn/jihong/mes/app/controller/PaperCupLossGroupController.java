package cn.jihong.mes.app.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupNotCaseDTO;
import cn.jihong.mes.api.model.vo.PaperActualLossStatisticsMesGroupVO;
import cn.jihong.mes.api.service.IPaperCupLossGroupService;

/**
 * <p>
 * 环保纸杯统计（集团按个）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-1
 */
@RestController
@RequestMapping("/paperCupLossGroup")
@ShenyuSpringMvcClient(path = "/paperCupLossGroup/**")
public class PaperCupLossGroupController {
    @Resource
    private IPaperCupLossGroupService paperCupLossGroupService;

    /**
     * 查询实际损耗率统计
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<PaperActualLossStatisticsMesGroupVO>> getList(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        List<PaperActualLossStatisticsMesGroupVO> actualLossStatisticsMesGroupVOS = paperCupLossGroupService.selectActualLosss(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, actualLossStatisticsMesGroupVOS);
    }

    /**
     * 导出excel
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcel(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出半成品汇总
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcelHalf")
    public StandardResult<String> exportToExcelHalf(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcelHalf(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出明细
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportDetail")
    public StandardResult<String> exportDetail(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcelDetail(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出明细未结案
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportNotCaseDetail")
    public StandardResult<String> exportNotCaseDetail(@RequestBody ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcelNotCaseDetail(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出半成品明细
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportDetailHalf")
    public StandardResult<String> exportDetailHalf(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcelDetailHalf(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出半成品明细未结案
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportNotCaseDetailHalf")
    public StandardResult<String> exportNotCaseDetailHalf(@RequestBody ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupDTO) {
        String url = paperCupLossGroupService.exportToExcelNotCaseDetailHalf(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
