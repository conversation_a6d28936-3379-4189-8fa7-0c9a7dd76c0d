package cn.jihong.mes.app.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.TbleqpequipmentbasisDTO;
import cn.jihong.mes.api.model.po.TbleqpequipmentbasisPO;
import cn.jihong.mes.api.model.vo.TbleqpequipmentbasisVO;
import cn.jihong.mes.api.service.ITbleqpequipmentbasisService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.ITbleqpequipmentbasisConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TbleqpequipmentbasisMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@DubboService
public class TbleqpequipmentbasisServiceImpl extends JiHongServiceImpl<TbleqpequipmentbasisMapper, TbleqpequipmentbasisPO> implements ITbleqpequipmentbasisService {

    
    @Override
    public List<TbleqpequipmentbasisVO> getTbleqpequipmentbasisList(TbleqpequipmentbasisDTO tbleqpequipmentbasisDTO) {
        if (BeanUtil.isEmpty(tbleqpequipmentbasisDTO)) {
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tbleqpequipmentbasisDTO.getFactoryCode()));
        LambdaQueryWrapper<TbleqpequipmentbasisPO> lambdaQueryWrapper = new LambdaQueryWrapper<TbleqpequipmentbasisPO>()
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getEquipmentno()),
                        TbleqpequipmentbasisPO::getEquipmentno, tbleqpequipmentbasisDTO.getEquipmentno())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getEquipmenttype()),
                        TbleqpequipmentbasisPO::getEquipmenttype, tbleqpequipmentbasisDTO.getEquipmenttype())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCapacity()),
                        TbleqpequipmentbasisPO::getCapacity, tbleqpequipmentbasisDTO.getCapacity())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getVendorno()),
                        TbleqpequipmentbasisPO::getVendorno, tbleqpequipmentbasisDTO.getVendorno())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getModelno()),
                        TbleqpequipmentbasisPO::getModelno, tbleqpequipmentbasisDTO.getModelno())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getDescription()),
                        TbleqpequipmentbasisPO::getDescription, tbleqpequipmentbasisDTO.getDescription())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getCreator()),
                        TbleqpequipmentbasisPO::getCreator, tbleqpequipmentbasisDTO.getCreator())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getIssuestate()),
                        TbleqpequipmentbasisPO::getIssuestate, tbleqpequipmentbasisDTO.getIssuestate())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getEngineergroupno()),
                        TbleqpequipmentbasisPO::getEngineergroupno, tbleqpequipmentbasisDTO.getEngineergroupno())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getAssetno()),
                        TbleqpequipmentbasisPO::getAssetno, tbleqpequipmentbasisDTO.getAssetno())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getEquipmentclass()),
                        TbleqpequipmentbasisPO::getEquipmentclass, tbleqpequipmentbasisDTO.getEquipmentclass())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getLoadport()),
                        TbleqpequipmentbasisPO::getLoadport, tbleqpequipmentbasisDTO.getLoadport())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getAutoflag()),
                        TbleqpequipmentbasisPO::getAutoflag, tbleqpequipmentbasisDTO.getAutoflag())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getEacontroller()),
                        TbleqpequipmentbasisPO::getEacontroller, tbleqpequipmentbasisDTO.getEacontroller())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getEqprecipe()),
                        TbleqpequipmentbasisPO::getEqprecipe, tbleqpequipmentbasisDTO.getEqprecipe())
                .eq(StrUtil.isNotBlank(tbleqpequipmentbasisDTO.getQclistno()),
                        TbleqpequipmentbasisPO::getQclistno, tbleqpequipmentbasisDTO.getQclistno())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getMaxTime()),
                        TbleqpequipmentbasisPO::getMaxTime, tbleqpequipmentbasisDTO.getMaxTime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getFixEqpTime()),
                        TbleqpequipmentbasisPO::getFixEqpTime, tbleqpequipmentbasisDTO.getFixEqpTime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getVarEqpTime()),
                        TbleqpequipmentbasisPO::getVarEqpTime, tbleqpequipmentbasisDTO.getVarEqpTime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCountEqpUnitQty()),
                        TbleqpequipmentbasisPO::getCountEqpUnitQty, tbleqpequipmentbasisDTO.getCountEqpUnitQty())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCounter()),
                        TbleqpequipmentbasisPO::getCounter, tbleqpequipmentbasisDTO.getCounter())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getErpno()),
                        TbleqpequipmentbasisPO::getErpno, tbleqpequipmentbasisDTO.getErpno())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getEquipmentName()),
                        TbleqpequipmentbasisPO::getEquipmentName, tbleqpequipmentbasisDTO.getEquipmentName())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getProductioninf()),
                        TbleqpequipmentbasisPO::getProductioninf, tbleqpequipmentbasisDTO.getProductioninf())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getAllowmultiwork()),
                        TbleqpequipmentbasisPO::getAllowmultiwork, tbleqpequipmentbasisDTO.getAllowmultiwork())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getSetupignoremachine()),
                        TbleqpequipmentbasisPO::getSetupignoremachine, tbleqpequipmentbasisDTO.getSetupignoremachine())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getSpcPqc()),
                        TbleqpequipmentbasisPO::getSpcPqc, tbleqpequipmentbasisDTO.getSpcPqc())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getEquipmentcheckup()),
                        TbleqpequipmentbasisPO::getEquipmentcheckup, tbleqpequipmentbasisDTO.getEquipmentcheckup())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getEquipmentcheckuprate()),
                        TbleqpequipmentbasisPO::getEquipmentcheckuprate, tbleqpequipmentbasisDTO.getEquipmentcheckuprate())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getEquipmentcheckuptime()),
                        TbleqpequipmentbasisPO::getEquipmentcheckuptime, tbleqpequipmentbasisDTO.getEquipmentcheckuptime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCounterPre()),
                        TbleqpequipmentbasisPO::getCounterPre, tbleqpequipmentbasisDTO.getCounterPre())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutUserOption()),
                        TbleqpequipmentbasisPO::getOutUserOption, tbleqpequipmentbasisDTO.getOutUserOption())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutLaberTimeOption()),
                        TbleqpequipmentbasisPO::getOutLaberTimeOption, tbleqpequipmentbasisDTO.getOutLaberTimeOption())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutLaberExclusive()),
                        TbleqpequipmentbasisPO::getOutLaberExclusive, tbleqpequipmentbasisDTO.getOutLaberExclusive())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutMachineExclusive()),
                        TbleqpequipmentbasisPO::getOutMachineExclusive, tbleqpequipmentbasisDTO.getOutMachineExclusive())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutQtyDefinition()),
                        TbleqpequipmentbasisPO::getOutQtyDefinition, tbleqpequipmentbasisDTO.getOutQtyDefinition())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutQtyOption()),
                        TbleqpequipmentbasisPO::getOutQtyOption, tbleqpequipmentbasisDTO.getOutQtyOption())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getOutQtyAllowZero()),
                        TbleqpequipmentbasisPO::getOutQtyAllowZero, tbleqpequipmentbasisDTO.getOutQtyAllowZero())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCounterUpdateTime()),
                        TbleqpequipmentbasisPO::getCounterUpdateTime, tbleqpequipmentbasisDTO.getCounterUpdateTime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getCounterEQTime()),
                        TbleqpequipmentbasisPO::getCounterEQTime, tbleqpequipmentbasisDTO.getCounterEQTime())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getSpcPqc2()),
                        TbleqpequipmentbasisPO::getSpcPqc2, tbleqpequipmentbasisDTO.getSpcPqc2())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getRecordTimeOutDate()),
                        TbleqpequipmentbasisPO::getRecordTimeOutDate, tbleqpequipmentbasisDTO.getRecordTimeOutDate())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getStdTimeOut()),
                        TbleqpequipmentbasisPO::getStdTimeOut, tbleqpequipmentbasisDTO.getStdTimeOut())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getStdTimeOutQty()),
                        TbleqpequipmentbasisPO::getStdTimeOutQty, tbleqpequipmentbasisDTO.getStdTimeOutQty())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getLotBinding()),
                        TbleqpequipmentbasisPO::getLotBinding, tbleqpequipmentbasisDTO.getLotBinding())
                .eq(ObjectUtil.isNotEmpty(tbleqpequipmentbasisDTO.getLineInventoryNo()),
                        TbleqpequipmentbasisPO::getLineInventoryNo, tbleqpequipmentbasisDTO.getLineInventoryNo())
                ;
        List<TbleqpequipmentbasisPO> tbleqpequipmentbasisPOS = baseMapper.selectList(lambdaQueryWrapper);
        return ITbleqpequipmentbasisConvertor.INSTANCE.POListToVOList(tbleqpequipmentbasisPOS);
    }
}
