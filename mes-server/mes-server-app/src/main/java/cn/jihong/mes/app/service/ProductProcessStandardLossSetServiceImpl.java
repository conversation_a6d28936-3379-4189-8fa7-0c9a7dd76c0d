package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProductProcessStandardLossSetDTO;
import cn.jihong.mes.api.model.po.ProductProcessStandardLossSetPO;
import cn.jihong.mes.api.model.vo.ProductProcessStandardLossSetVO;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.ProductProcessStandardLossSetMapper;
import cn.jihong.mes.api.service.IProductProcessStandardLossSetService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品工序标准损耗设定表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Service
public class ProductProcessStandardLossSetServiceImpl extends JiHongServiceImpl<ProductProcessStandardLossSetMapper, ProductProcessStandardLossSetPO> implements IProductProcessStandardLossSetService {

    @Override
    public void save(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        if (productProcessStandardLossSetDTO == null){
            return;
        }
        if (StringUtil.isEmpty(productProcessStandardLossSetDTO.getProductCategoryNo())){
            throw new CommonException("产品类别不能为空");
        }
        if (StringUtil.isEmpty(productProcessStandardLossSetDTO.getProductionProcesseNo())){
            throw new CommonException("工序不能为空");
        }
        productProcessStandardLossSetDTO.setCreateTime(new Date());
        productProcessStandardLossSetDTO.setCreateBy(SecurityUtil.getUserId());
//        productProcessStandardLossSetDTO.setCreateBy(19584);
        productProcessStandardLossSetDTO.setIsDeleted(false);
        ProductProcessStandardLossSetPO productProcessStandardLossSetPO = new ProductProcessStandardLossSetPO();
        BeanUtil.copyProperties(productProcessStandardLossSetDTO,productProcessStandardLossSetPO);
        baseMapper.insert(productProcessStandardLossSetPO);
    }

    @Override
    public void edit(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        if (productProcessStandardLossSetDTO == null){
            return;
        }
        if (productProcessStandardLossSetDTO.getId() == null){
            throw new CommonException("id不能为空");
        }
        LambdaUpdateWrapper<ProductProcessStandardLossSetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductProcessStandardLossSetPO::getId,productProcessStandardLossSetDTO.getId());
        updateWrapper.set(ProductProcessStandardLossSetPO::getProductCategoryNo,productProcessStandardLossSetDTO.getProductCategoryNo());
        updateWrapper.set(ProductProcessStandardLossSetPO::getProductCategoryName,productProcessStandardLossSetDTO.getProductCategoryName());
        updateWrapper.set(ProductProcessStandardLossSetPO::getProductionProcesseNo,productProcessStandardLossSetDTO.getProductionProcesseNo());
        updateWrapper.set(ProductProcessStandardLossSetPO::getProductionProcesseName,productProcessStandardLossSetDTO.getProductionProcesseName());
        updateWrapper.set(ProductProcessStandardLossSetPO::getStandardLoss,productProcessStandardLossSetDTO.getStandardLoss());
        updateWrapper.set(ProductProcessStandardLossSetPO::getUpdateBy,SecurityUtil.getUserId());
        updateWrapper.set(ProductProcessStandardLossSetPO::getUpdateTime,new Date());
        update(updateWrapper);
    }

    @Override
    public void delete(String id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        if (StringUtil.isEmpty(id)){
            throw new CommonException("id不能为空");
        }
        LambdaUpdateWrapper<ProductProcessStandardLossSetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductProcessStandardLossSetPO::getId,id);
        updateWrapper.set(ProductProcessStandardLossSetPO::getIsDeleted,true);
        updateWrapper.set(ProductProcessStandardLossSetPO::getUpdateBy,SecurityUtil.getUserId());
        updateWrapper.set(ProductProcessStandardLossSetPO::getUpdateTime,new Date());
        update(updateWrapper);
    }

    @Override
    public Pagination<ProductProcessStandardLossSetVO> getList(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO, PageRequest pageRequest) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductProcessStandardLossSetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductProcessStandardLossSetPO::getProductCategoryNo,productProcessStandardLossSetDTO.getProductCategoryNo());
        queryWrapper.eq(ProductProcessStandardLossSetPO::getProductionProcesseNo,productProcessStandardLossSetDTO.getProductionProcesseNo());
        queryWrapper.orderByDesc(ProductProcessStandardLossSetPO::getUpdateTime,ProductProcessStandardLossSetPO::getCreateTime);
        IPage<ProductProcessStandardLossSetPO> page = baseMapper.selectPage(pageRequest.getPage(),queryWrapper);
        List<ProductProcessStandardLossSetPO> productProcessStandardLossSetPOS = page.getRecords();
        if(CollectionUtil.isEmpty(productProcessStandardLossSetPOS)) {
            return Pagination.newInstance(null, 0, 0);
        }
        List<ProductProcessStandardLossSetVO> collect = BeanUtil.copyToList(productProcessStandardLossSetPOS,ProductProcessStandardLossSetVO.class);
        return Pagination.newInstance(collect, page.getTotal(), page.getPages());
    }
}
