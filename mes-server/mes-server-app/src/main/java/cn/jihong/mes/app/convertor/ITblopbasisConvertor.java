package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblopbasisPO;
import cn.jihong.mes.api.model.vo.TblopbasisVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITblopbasisConvertor {

    ITblopbasisConvertor INSTANCE = Mappers.getMapper(ITblopbasisConvertor.class);

    TblopbasisVO POToVO (TblopbasisPO iTblopbasisPO);
    List<TblopbasisVO> POListToVOList (List<TblopbasisPO> tblopbasisPOS);
}
