package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblCusProcessBarCodeBoxPO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeBoxVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TblCusProcessBarCodeBoxConvertor {

    TblCusProcessBarCodeBoxConvertor INSTANCE = Mappers.getMapper(TblCusProcessBarCodeBoxConvertor.class);

    TblCusProcessBarCodeBoxVO POToVO (TblCusProcessBarCodeBoxPO tblCusProcessBarCodeBoxPO);

    List<TblCusProcessBarCodeBoxVO> POListToVOList (List<TblCusProcessBarCodeBoxPO> tblCusProcessBarCodeBoxPOList);
}
