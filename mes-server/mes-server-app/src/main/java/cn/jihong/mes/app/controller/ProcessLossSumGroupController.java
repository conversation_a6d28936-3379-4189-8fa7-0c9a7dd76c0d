package cn.jihong.mes.app.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProcessLossSumParamGroupDTO;
import cn.jihong.mes.api.model.vo.ProcessLossSumMesGroupVO;
import cn.jihong.mes.api.service.IProcessLossSumGroupService;

/**
 * 集团损耗趋势表
 */
@RestController
@RequestMapping("/processLossSumGroup")
@ShenyuSpringMvcClient(path = "/processLossSumGroup/**")
public class ProcessLossSumGroupController {
    @Resource
    private IProcessLossSumGroupService processLossSumGroupService;

    /**
     * 查询集团损耗趋势
     * @param processLossSumParamGroupDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProcessLossSumMesGroupVO>> getList(@RequestBody ProcessLossSumParamGroupDTO processLossSumParamGroupDTO) {
        List<ProcessLossSumMesGroupVO>  processLossSumMesVOS = processLossSumGroupService.selectProcessLossSum(processLossSumParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, processLossSumMesVOS);
    }

    /**
     * 导出excel
     * @param processLossSumParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ProcessLossSumParamGroupDTO processLossSumParamGroupDTO) {
        String url = processLossSumGroupService.exportToExcel(processLossSumParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
