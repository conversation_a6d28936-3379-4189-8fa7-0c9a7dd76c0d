package cn.jihong.mes.app.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.api.model.dto.ProcessLossFluctuationTrendParamDTO;
import cn.jihong.mes.api.model.vo.ProcessLossFluctuationTrendMesVO;
import cn.jihong.mes.api.service.IProcessLossFluctuationTrendService;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.oa.erp.api.model.dto.ProcessLossDTO;
import cn.jihong.oa.erp.api.model.dto.ProcessLossFluctuationTrendDTO;
import cn.jihong.oa.erp.api.model.vo.ProcessLossFluctuationTrendVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProcessLossFluctuationTrendServiceImpl implements IProcessLossFluctuationTrendService {
    @DubboReference
    private ISffbTService sffbTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference
    private ISiteViewService siteViewService;
    @Override
    public List<ProcessLossFluctuationTrendMesVO> selectFluctuationTrend(ProcessLossFluctuationTrendParamDTO processLossFluctuationTrendParamDTO) {
        if (processLossFluctuationTrendParamDTO == null){
            return null;
        }
        if (StringUtil.isEmpty(processLossFluctuationTrendParamDTO.getStartDate())){
            throw new CommonException("起始年月不能为空");
        }else {
            if (!TimeTypeUtil.isLegalDate(processLossFluctuationTrendParamDTO.getStartDate().length(), processLossFluctuationTrendParamDTO.getStartDate(), "yyyy-MM")){
                throw new CommonException("起始年月不符合格式：yyyy-MM");
            }
        }
        if (StringUtil.isEmpty(processLossFluctuationTrendParamDTO.getEndDate())){
            throw new CommonException("结束年月不能为空");
        }else {
            if (!TimeTypeUtil.isLegalDate(processLossFluctuationTrendParamDTO.getEndDate().length(), processLossFluctuationTrendParamDTO.getEndDate(), "yyyy-MM")){
                throw new CommonException("结束年月不符合格式：yyyy-MM");
            }
        }
        if (StringUtil.isEmpty(processLossFluctuationTrendParamDTO.getProcessNo())){
            throw new CommonException("工序不能为空");
        }
        Long companyId = SecurityUtil.getCompanyId();
        // 当公司是集团的时候，设置为厦门吉宏
        if (companyId == 100L) {
            companyId = 106L;
        }
        // 根据公司id 获得 erp 公司id
        String siteCompanyId = (String)redisTemplate.opsForValue().get(RedisCacheConstant.COMPANY_ID + companyId);
        if (StringUtils.isBlank(siteCompanyId)) {
            siteCompanyId = siteViewService.getByCompanyId(companyId.toString());
            redisTemplate.opsForValue().set(RedisCacheConstant.COMPANY_ID + companyId, siteCompanyId,1, TimeUnit.HOURS);
        }
        log.info("公司信息：" + siteCompanyId);
        Date startDate = DateUtil.beginOfMonth(DateUtil.parse(processLossFluctuationTrendParamDTO.getStartDate(),"yyyy-MM"));
        Date endDate = DateUtil.offsetMonth(DateUtil.beginOfMonth(DateUtil.parse(processLossFluctuationTrendParamDTO.getEndDate(),"yyyy-MM")),1);
        ProcessLossFluctuationTrendDTO processLossFluctuationTrendDTO = new ProcessLossFluctuationTrendDTO();
        ProcessLossDTO processLossDTO = new ProcessLossDTO();
        if (StringUtil.isEmpty(processLossFluctuationTrendParamDTO.getFactoryCode())){
            processLossFluctuationTrendDTO.setFactoryCode(siteCompanyId);
        }else {
            processLossFluctuationTrendDTO.setFactoryCode(processLossFluctuationTrendParamDTO.getFactoryCode());
        }

        processLossFluctuationTrendDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        processLossFluctuationTrendDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        processLossFluctuationTrendDTO.setProductCategoryNo(processLossFluctuationTrendParamDTO.getProductCategoryNo());
        processLossFluctuationTrendDTO.setProcessNo(processLossFluctuationTrendParamDTO.getProcessNo());
        List<ProcessLossFluctuationTrendVO> processLossFluctuationTrendVOS = sffbTService.selectFluctuationTrend(processLossFluctuationTrendDTO);
        if(CollectionUtil.isNotEmpty(processLossFluctuationTrendVOS)){
            List<ProcessLossFluctuationTrendMesVO> processLossFluctuationTrendMesVOS = new ArrayList<>();
            for (ProcessLossFluctuationTrendVO processLossFluctuationTrendVO:processLossFluctuationTrendVOS){
                ProcessLossFluctuationTrendMesVO processLossFluctuationTrendMesVO = new ProcessLossFluctuationTrendMesVO();
                BeanUtil.copyProperties(processLossFluctuationTrendVO,processLossFluctuationTrendMesVO);

                //实际损耗率
                if (processLossFluctuationTrendVO.getDefectiveProductNum()
                        .add(processLossFluctuationTrendVO.getQualifiedQuantity()).compareTo(new BigDecimal("0"))==1){
                    processLossFluctuationTrendMesVO.setActualLossRate(processLossFluctuationTrendVO.getDefectiveProductNum()
                            .divide(processLossFluctuationTrendVO.getDefectiveProductNum()
                                    .add(processLossFluctuationTrendVO.getQualifiedQuantity()), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }
                processLossFluctuationTrendMesVOS.add(processLossFluctuationTrendMesVO);
            }
            return processLossFluctuationTrendMesVOS;
        }
        return null;
    }
}
