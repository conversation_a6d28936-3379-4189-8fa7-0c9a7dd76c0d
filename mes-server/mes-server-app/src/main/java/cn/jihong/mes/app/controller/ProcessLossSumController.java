package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProcessLossParamDTO;
import cn.jihong.mes.api.model.dto.ProcessLossSumParamDTO;
import cn.jihong.mes.api.model.vo.ProcessLossMesVO;
import cn.jihong.mes.api.model.vo.ProcessLossSumMesVO;
import cn.jihong.mes.api.service.IProcessLossSumService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序损耗汇总
 */
@RestController
@RequestMapping("/processLossSum")
@ShenyuSpringMvcClient(path = "/processLossSum/**")
public class ProcessLossSumController {
    @Resource
    private IProcessLossSumService processLossSumService;

    /**
     * 查询工序损耗汇总
     * @param processLossSumParamDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProcessLossSumMesVO>> getList(@RequestBody ProcessLossSumParamDTO processLossSumParamDTO) {
        List<ProcessLossSumMesVO>  processLossSumMesVOS = processLossSumService.selectProcessLossSum(processLossSumParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, processLossSumMesVOS);
    }
}
