package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TbleqpequipmentbasisPO;
import cn.jihong.mes.api.model.po.TbleqpequipmentbasisPO;
import cn.jihong.mes.api.model.vo.TbleqpequipmentbasisVO;
import cn.jihong.mes.api.model.vo.TbleqpequipmentbasisVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITbleqpequipmentbasisConvertor {

    ITbleqpequipmentbasisConvertor INSTANCE = Mappers.getMapper(ITbleqpequipmentbasisConvertor.class);

    TbleqpequipmentbasisVO POToVO (TbleqpequipmentbasisPO iTbleqpequipmentbasisPO);
    List<TbleqpequipmentbasisVO> POListToVOList (List<TbleqpequipmentbasisPO> tbleqpequipmentbasisPOS);
}
