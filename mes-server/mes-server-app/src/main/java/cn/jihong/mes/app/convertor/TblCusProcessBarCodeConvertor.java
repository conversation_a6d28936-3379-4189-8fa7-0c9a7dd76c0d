package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblCusProcessBarCodePO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodePO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeVO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TblCusProcessBarCodeConvertor {

    TblCusProcessBarCodeConvertor INSTANCE = Mappers.getMapper(TblCusProcessBarCodeConvertor.class);

    TblCusProcessBarCodeVO POToVO (TblCusProcessBarCodePO tblCusProcessBarCodePO);
    List<TblCusProcessBarCodeVO> POListToVOList (List<TblCusProcessBarCodePO> tblCusProcessBarCodeBoxPOList);
}
