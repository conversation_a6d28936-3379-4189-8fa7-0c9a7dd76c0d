package cn.jihong.mes.app.service;

import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.OssUtils;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.api.model.dto.DailyProductionPlanAchievementRateDTO;
import cn.jihong.mes.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.api.model.dto.TblwipcontResourceDTO;
import cn.jihong.mes.api.model.vo.DailyProductionPlanAchievementRateVO;
import cn.jihong.mes.api.model.vo.ProductionPlanVO;
import cn.jihong.mes.api.model.vo.TblopbasisVO;
import cn.jihong.mes.api.model.vo.TblwipcontResourceVO;
import cn.jihong.mes.api.service.*;
import cn.jihong.mes.app.property.AliyunOssProperty;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.oa.approve.api.model.dto.FromRequestPageDTO;
import cn.jihong.oa.approve.api.service.IHrmResourceService;
import cn.jihong.oa.erp.api.model.dto.ProcessReportingDTO;
import cn.jihong.oa.erp.api.model.vo.ProcessReportingVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DailyProductionPlanAchievementRateServiceImpl implements IDailyProductionPlanAchievementRateService {
    @Resource
    private ITblwipcontResourceService tblwipcontResourceService;
    @Resource
    private IProductionPlanService productionPlanService;
    @Resource
    private ITblopbasisService tblopbasisService;
    @Resource
    private IProductionMachineService productionMachineService;
    @DubboReference
    private IHrmResourceService hrmResourceService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference
    private ISiteViewService siteViewService;
    @DubboReference
    private ISffbTService sffbTService;
    @Resource
    private AliyunOssProperty aliyunOssProperty;
    @Override
    public List<DailyProductionPlanAchievementRateVO> getList(DailyProductionPlanAchievementRateDTO dailyProductionPlanAchievementRateDTO) {
        if (dailyProductionPlanAchievementRateDTO != null){
            if (!TimeTypeUtil.isLegalDate(dailyProductionPlanAchievementRateDTO.getStartTime().length(),dailyProductionPlanAchievementRateDTO.getStartTime(),"yyyy-MM-dd")){
                throw new CommonException("开始日期格式不符合：yyyy-MM-dd");
            }
            if (!TimeTypeUtil.isLegalDate(dailyProductionPlanAchievementRateDTO.getEndTime().length(),dailyProductionPlanAchievementRateDTO.getEndTime(),"yyyy-MM-dd")){
                throw new CommonException("结束日期格式不符合：yyyy-MM-dd");
            }
            if (cn.jihong.common.util.DateUtil.getDiffDays(DateUtil.parse(dailyProductionPlanAchievementRateDTO.getStartTime(),"yyyy-MM-dd"),DateUtil.parse(dailyProductionPlanAchievementRateDTO.getStartTime(),"yyyy-MM-dd")) > 30){
                throw new CommonException("开始结束日期间隔不能大于30天");
            }
//            TblwipcontResourceDTO tblwipcontResourceDTO = new TblwipcontResourceDTO();
//            tblwipcontResourceDTO.setStartTime(dailyProductionPlanAchievementRateDTO.getStartTime());
//            tblwipcontResourceDTO.setEndTime(dailyProductionPlanAchievementRateDTO.getEndTime());
            //查询报工记录
//            List<TblwipcontResourceVO> tblwipcontResourceVOS = tblwipcontResourceService.getListByEventTime(tblwipcontResourceDTO);
            Long companyId = SecurityUtil.getCompanyId();
            companyId = 125L;
            // 当公司是集团的时候，设置为厦门吉宏
            if (companyId == 100L) {
                companyId = 106L;
            }
            // 根据公司id 获得 erp 公司id
            String siteCompanyId = (String)redisTemplate.opsForValue().get(RedisCacheConstant.COMPANY_ID + companyId);
            if (StringUtils.isBlank(siteCompanyId)) {
                siteCompanyId = siteViewService.getByCompanyId(companyId.toString());
                redisTemplate.opsForValue().set(RedisCacheConstant.COMPANY_ID + companyId, siteCompanyId,1, TimeUnit.HOURS);
            }
            log.info("公司信息：" + siteCompanyId);
            ProcessReportingDTO processReportingDTO = new ProcessReportingDTO();
            processReportingDTO.setStartDate(dailyProductionPlanAchievementRateDTO.getStartTime());
            processReportingDTO.setEndDate(dailyProductionPlanAchievementRateDTO.getEndTime());
            processReportingDTO.setFactoryCode(siteCompanyId);
            List<ProcessReportingVO> processReportingVOS = sffbTService.selectProcessReporting(processReportingDTO);
            //报工记录按生产批次号+工序+设备+报工日期分组
//            Map<String,List<TblwipcontResourceVO>> tblwipcontResourceVOMap = tblwipcontResourceVOS.stream().collect(Collectors.groupingBy(o -> o.getLotno()+o.getOpno()+o.getResitem()+o.getCShiftno(),Collectors.toList()));
            if (CollectionUtil.isEmpty(processReportingVOS)){
                throw new CommonException("该日期范围没有查询不到报工记录");
            }
            List<String> workCodeList = processReportingVOS.stream().map(ProcessReportingVO::getMachineLeaderNo).collect(Collectors.toList());
            List<List<String>> workCodeSubList = ListUtils.partition(workCodeList, 1000);
            Map<String,UserInfo> userInfoMap = new HashMap<>();
            for (List<String> workCodes:workCodeSubList){
                Map<String,UserInfo> userInfoByWorkcodeMap = hrmResourceService.getUserInfoByWorkcode(workCodes);
                if (userInfoByWorkcodeMap != null){
                    userInfoMap.putAll(userInfoByWorkcodeMap);
                }
            }
            Map<String ,List<ProcessReportingVO>> processReportingVOMap = processReportingVOS.stream().collect(Collectors.groupingBy(o -> o.getProductionDate()+o.getWorkerOrderNo()+o.getProductionProcessNo()+o.getProductionMachineNo()+o.getShiftNo(),Collectors.toList()));
            ProductionPlanDTO productionPlanDTO = new ProductionPlanDTO();
            productionPlanDTO.setProductionPlanStartTime(DateUtil.parse(dailyProductionPlanAchievementRateDTO.getStartTime(),"yyyy-MM-dd"));
            productionPlanDTO.setProductionPlanEndTime(DateUtil.parse(dailyProductionPlanAchievementRateDTO.getEndTime(),"yyyy-MM-dd"));
            //查询生产计划记录
            List<ProductionPlanVO> productionPlanVOS = productionPlanService.getListByDate(productionPlanDTO);
            List<DailyProductionPlanAchievementRateVO> dailyProductionPlanAchievementRateVOS = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(productionPlanVOS)){
                for (ProductionPlanVO productionPlanVO:productionPlanVOS){
                    String machineId = productionMachineService.getProductionMachineByName(productionPlanVO.getProductionMachine(),productionPlanVO.getProductionProcess()).getErpMachineId();
                    List<String> machineNoList = Arrays.asList(machineId.split(":"));
                    String machineNo = machineNoList.get(machineNoList.size()-1);
                    DailyProductionPlanAchievementRateVO dailyProductionPlanAchievementRateVO = new DailyProductionPlanAchievementRateVO();
                    dailyProductionPlanAchievementRateVO.setProductionProcess(productionPlanVO.getProductionProcess());
                    dailyProductionPlanAchievementRateVO.setProductionName(productionPlanVO.getProductionName());
                    dailyProductionPlanAchievementRateVO.setProductionPlanDate(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"));
                    dailyProductionPlanAchievementRateVO.setProductionMachine(productionPlanVO.getProductionMachine());
                    dailyProductionPlanAchievementRateVO.setWorkerOrderNo(productionPlanVO.getWorkerOrderNo());
                    TblopbasisVO tblopbasisVO = tblopbasisService.getVOByName(productionPlanVO.getProductionProcess());
                    log.debug("计划key信息："+DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd HH:mm:ss")+productionPlanVO.getWorkerOrderNo()+(tblopbasisVO==null?null:tblopbasisVO.getOpno())+machineNo+(DateUtil.format(productionPlanVO.getProductionPlanEndTime(),"yyyy-MM-dd").equals(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"))?"B001":"W001"));
                    if(processReportingVOMap.containsKey(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd HH:mm:ss")+productionPlanVO.getWorkerOrderNo()+(tblopbasisVO==null?null:tblopbasisVO.getOpno())+machineNo+(DateUtil.format(productionPlanVO.getProductionPlanEndTime(),"yyyy-MM-dd").equals(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"))?"B001":"W001"))){
//                        List<TblwipcontResourceVO> tblwipcontResourceVOList = tblwipcontResourceVOMap.get(productionPlanVO.getWorkerOrderNo()+"-001"+(tblopbasisVO==null?null:tblopbasisVO.getOpno())+machineNo+(DateUtil.format(productionPlanVO.getProductionPlanEndTime(),"yyyy-MM-dd").equals(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"))?"B001":"W001"));
                        List<ProcessReportingVO> processReportingVOList = processReportingVOMap.get(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd HH:mm:ss")+productionPlanVO.getWorkerOrderNo()+(tblopbasisVO==null?null:tblopbasisVO.getOpno())+machineNo+(DateUtil.format(productionPlanVO.getProductionPlanEndTime(),"yyyy-MM-dd").equals(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"))?"B001":"W001"));
//                        Double capacity = 0.0;
//                        Double totalLosss = 0.0;
//                        for (TblwipcontResourceVO tblwipcontResourceVO:tblwipcontResourceVOList){
//                            if (tblwipcontResourceVO.getEventtime().after(productionPlanVO.getProductionPlanStartTime()) && tblwipcontResourceVO.getEventtime().before(productionPlanVO.getProductionPlanEndTime())){
//                                capacity+=tblwipcontResourceVO.getInputqty();
//                                totalLosss+=tblwipcontResourceVO.getResvalue();
//                            }
//                            UserInfo userInfo = hrmResourceService.getUserInfo(tblwipcontResourceVO.getUserno());
//                            dailyProductionPlanAchievementRateVO.setMachineLeader(userInfo==null?null:userInfo.getName());
//                        }

                        Double capacity = 0.0;
                        for (ProcessReportingVO processReportingVO:processReportingVOList){
                            capacity+=processReportingVO.getActualCapacity();
                            dailyProductionPlanAchievementRateVO.setMachineLeader(userInfoMap==null?null:userInfoMap.get(processReportingVO.getMachineLeaderNo()==null?"":processReportingVO.getMachineLeaderNo()).getName());
                        }
                        dailyProductionPlanAchievementRateVO.setActualCapacity(capacity);
                        dailyProductionPlanAchievementRateVO.setTotalLoss(null);
                        dailyProductionPlanAchievementRateVO.setPlannedProductionCapacity(productionPlanVO.getPlannedProductionCapacity());
                        if(DateUtil.format(productionPlanVO.getProductionPlanEndTime(),"yyyy-MM-dd").equals(DateUtil.format(productionPlanVO.getProductionPlanDate(),"yyyy-MM-dd"))){
                            dailyProductionPlanAchievementRateVO.setShiftNo("白班");
                        }else {
                            dailyProductionPlanAchievementRateVO.setShiftNo("晚班");
                        }
                        dailyProductionPlanAchievementRateVO.setPassRate(null);
                        if (dailyProductionPlanAchievementRateVO.getPlannedProductionCapacity() != 0.0){
                            Double achievementRate = dailyProductionPlanAchievementRateVO.getActualCapacity()/dailyProductionPlanAchievementRateVO.getPlannedProductionCapacity()*100;
                            BigDecimal bd = new BigDecimal(achievementRate).setScale(2, RoundingMode.HALF_UP);
                            dailyProductionPlanAchievementRateVO.setAchievementRate(bd.doubleValue()+"%");
                        }else {
                            dailyProductionPlanAchievementRateVO.setAchievementRate("-");
                        }

                        dailyProductionPlanAchievementRateVO.setLossRate(null);
                        dailyProductionPlanAchievementRateVOS.add(dailyProductionPlanAchievementRateVO);
                    }
                }
            }

            return dailyProductionPlanAchievementRateVOS;
        }

        return null;
    }

    @Override
    public String exportToExcel(DailyProductionPlanAchievementRateDTO dailyProductionPlanAchievementRateDTO) {
        List<DailyProductionPlanAchievementRateVO> dailyProductionPlanAchievementRateVOS = getList(dailyProductionPlanAchievementRateDTO);
        String url = getDataToExcel(dailyProductionPlanAchievementRateVOS,DailyProductionPlanAchievementRateVO.class,"生产计划完成度");
        return url;
    }

    private String getDataToExcel(Collection<?> data,
                                  Class<?> clz,String fileName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        EasyExcel.write(os, clz).sheet("模板").doWrite(() -> {
            // 分页查询数据
            return data;
        });

        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
            String OBJECT_NAME = "temp";
            String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String objectName = OBJECT_NAME + "/" + dateStr + "/" + System.currentTimeMillis()
                    + "/" + fileName + ".xlsx";
            String uploadFile = OssUtils.uploadFile(aliyunOssProperty.getAccessId(), aliyunOssProperty.getAccessKey(),
                    aliyunOssProperty.getHzEndpoint(), aliyunOssProperty.getCommissionBucketName(), objectName,
                    byteArrayInputStream);
            return uploadFile;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 关闭
            try {
                byteArrayInputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            try {
                os.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
}
