package cn.jihong.mes.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.vo.*;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 机台信息
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@RestController
@RequestMapping("/productionMachine")
@ShenyuSpringMvcClient(path = "/productionMachine/**")
public class ProductionMachineController {

    @Resource
    private IProductionMachineService productionMachineService;

    /**
     * 查询机台列表
     * @param getProductionMachineListInVO
     * @return
     */
    @PostMapping("/getProductionMachineList")
    public StandardResult<Pagination<GetProductionMachineListOutVO>>
        getProductionMachineList(@RequestBody GetProductionMachineListInVO getProductionMachineListInVO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productionMachineService.getProductionMachineList(getProductionMachineListInVO));
    }

    /**
     * 查询机台明细
     * @param productionMachineInVO
     * @return
     */
    @PostMapping("/getProductionMachine")
    public StandardResult<ProductionMachineOutVO> getProductionMachine(@RequestBody ProductionMachineInVO productionMachineInVO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productionMachineService.getProductionMachine(productionMachineInVO.getErpMachineId()));
    }

    /**
     * 查询机台配置信息
     * 
     * @param productionMachineInVO
     * @return
     */
    @GetMapping("/getProductionMachineConfig/{id}")
    public StandardResult<ProductionMachineInVO.ProductionMachineConfigVO>
        getProductionMachineConfig(@PathVariable Long id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productionMachineService.getProductionMachineConfig(id));
    }

    /**
     * 查询机台物流配置信息
     * 
     * @param productionMachineInVO
     * @return
     */
    @GetMapping("/getProductionMachineLogisticsConfig/{id}")
    public StandardResult<ProductionMachineInVO.ProductionMachineLogisticsConfigVO>
        getProductionMachineLogisticsConfig(@PathVariable Long id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productionMachineService.getProductionMachineLogisticsConfig(id));
    }

    /**
     * 修改机台明细
     * @param productionMachineInVO
     */
    @PostMapping("/editProductionMachine")
    public StandardResult editProductionMachine(@RequestBody ProductionMachineInVO productionMachineInVO){
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        productionMachineService.editProductionMachine(productionMachineInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 修改机台配置信息
     * @param productionMachineInVO
     */
    @PostMapping("/editProductionMachineConfig")
    public StandardResult editProductionMachineConfig(@RequestBody ProductionMachineInVO productionMachineInVO){
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        productionMachineService.editProductionMachineConfig(productionMachineInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 修改机台物流配置信息
     * @param productionMachineInVO
     */
    @PostMapping("/editProductionMachineLogisticsConfig")
    public StandardResult editProductionMachineLogisticsConfig(@RequestBody ProductionMachineInVO productionMachineInVO){
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        productionMachineService.editProductionMachineLogisticsConfig(productionMachineInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 同步机台列表
     * 
     * @return
     */
    @GetMapping("/syncMachine")
    public StandardResult syncMachine() {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        productionMachineService.syncMachine();
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 修改机台稼动配置信息
     * @param inVO
     */
    @PostMapping("/editProductionMachineOperationConfig")
    public StandardResult editProductionMachineOperationConfig(@RequestBody EditProductionMachineOperationConfigInVO inVO){
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        productionMachineService.editProductionMachineOperationConfig(inVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


}

