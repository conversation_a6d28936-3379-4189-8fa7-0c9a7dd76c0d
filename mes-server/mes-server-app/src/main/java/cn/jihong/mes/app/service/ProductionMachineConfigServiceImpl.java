package cn.jihong.mes.app.service;

import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.po.ProductionMachineConfigPO;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineInVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.ProductionMachineConfigMapper;
import cn.jihong.mes.production.api.service.IProductShiftService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.service.IA01Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;

/**
 * 机台配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Slf4j
@DubboService
public class ProductionMachineConfigServiceImpl extends JiHongServiceImpl<ProductionMachineConfigMapper, ProductionMachineConfigPO> implements IProductionMachineConfigService {

    @Resource
    private IProductionMachineService productionMachineService;
    @DubboReference
    private IProductTicketService productTicketService;
    @DubboReference
    private IProductShiftService productShiftService;
    @DubboReference
    private IA01Service ia01Service;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Integer getMachineParts(MachineConfigInVO machineConfigInVO) {
//        if (StringUtils.isBlank(machineConfigInVO.getProcessCode())) {
//            machineConfigInVO.setProcessCode("023"); // 023 -- 抄瓦（股份）
//        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        machineConfigInVO.setCompanyCode(SecurityUtil.getCompanySite());
        ProductionMachineConfigPO productionMachineConfigPO = baseMapper.getMachineParts(machineConfigInVO);
        if (productionMachineConfigPO!= null) {
            return productionMachineConfigPO.getNeedMachineParts();
        }
        // 没有配置，返回0
        return 0;
    }

    @Override
    public Integer getBillingType(MachineConfigInVO machineConfigInVO) {
//        if (StringUtils.isBlank(machineConfigInVO.getProcessCode())) {
//            machineConfigInVO.setProcessCode("023"); // 023 -- 抄瓦（股份）
//        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        machineConfigInVO.setCompanyCode(SecurityUtil.getCompanySite());
        ProductionMachineConfigPO productionMachineConfigPO = baseMapper.getMachineParts(machineConfigInVO);
        if (productionMachineConfigPO!= null) {
            return productionMachineConfigPO.getBillingType();
        }
        // 没有配置，返回 1 一单一结
        return 1;
    }

    @Override
    public void saveMachineConfig(Long id,ProductionMachineInVO.ProductionMachineConfigVO productionMachineConfigVO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        if (productionMachineConfigVO == null) {
            return;
        }
        ProductionMachineConfigPO one = getByMachineId(id);
        if (one != null) {
            one.setNeedMachineParts(productionMachineConfigVO.getNeedMachineParts());
            one.setBillingType(productionMachineConfigVO.getBillingType());
        } else {
            one = new ProductionMachineConfigPO();
            one.setCompanyCode(SecurityUtil.getCompanySite());
            one.setMachineId(String.valueOf(id));
            one.setNeedMachineParts(productionMachineConfigVO.getNeedMachineParts());
            one.setBillingType(productionMachineConfigVO.getBillingType());
            one.setCreateBy(SecurityUtil.getUserId());
            one.setUpdateBy(SecurityUtil.getUserId());
        }
        saveOrUpdate(one);
    }



    @Override
    public ProductionMachineConfigPO getByMachineId(Long id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachineConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachineConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductionMachineConfigPO::getMachineId, id);
        return getOne(lambdaQueryWrapper);
    }

//    @Override
//    public List<WorkshopOutVO> getAllWorkshop() {
//        List<WorkShopEnum> collect = Arrays.stream(WorkShopEnum.values()).collect(Collectors.toList());
//        return collect.stream().map(t->{
//            WorkshopOutVO outVO = new WorkshopOutVO();
//            outVO.setWorkshopCode(t.getCode());
//            outVO.setWorkshopName(t.getName());
//            return outVO;
//        }).collect(Collectors.toList());
//    }
}
