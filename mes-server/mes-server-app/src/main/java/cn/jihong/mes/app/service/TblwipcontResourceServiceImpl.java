package cn.jihong.mes.app.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.TblwipcontResourceDTO;
import cn.jihong.mes.api.model.po.TblwipcontResourcePO;
import cn.jihong.mes.api.model.vo.TblwipcontResourceVO;
import cn.jihong.mes.api.service.ITblwipcontResourceService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.ITblwipcontResourceConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblwipcontResourceMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@DubboService
public class TblwipcontResourceServiceImpl extends JiHongServiceImpl<TblwipcontResourceMapper, TblwipcontResourcePO> implements ITblwipcontResourceService {

    @Override
    public List<TblwipcontResourceVO> getTblwipcontResourceList(TblwipcontResourceDTO tblwipcontResourceDTO) {
        if (BeanUtil.isEmpty(tblwipcontResourceDTO)) {
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tblwipcontResourceDTO.getFactoryCode()));
        LambdaQueryWrapper<TblwipcontResourcePO> lambdaQueryWrapper = new LambdaQueryWrapper<TblwipcontResourcePO>()
                .eq(StrUtil.isNotBlank(tblwipcontResourceDTO.getLotno()),
                        TblwipcontResourcePO::getLotno, tblwipcontResourceDTO.getLotno())
                .eq(StrUtil.isNotBlank(tblwipcontResourceDTO.getMono()),
                        TblwipcontResourcePO::getMono, tblwipcontResourceDTO.getMono())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getBaselotno()),
                        TblwipcontResourcePO::getBaselotno, tblwipcontResourceDTO.getBaselotno())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getOpno()),
                        TblwipcontResourcePO::getOpno, tblwipcontResourceDTO.getOpno())
                .eq(StrUtil.isNotBlank(tblwipcontResourceDTO.getLoggroupserial()),
                        TblwipcontResourcePO::getLoggroupserial, tblwipcontResourceDTO.getLoggroupserial())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getResclass()),
                        TblwipcontResourcePO::getResclass, tblwipcontResourceDTO.getResclass())
                .eq(StrUtil.isNotBlank(tblwipcontResourceDTO.getRestype()),
                        TblwipcontResourcePO::getRestype, tblwipcontResourceDTO.getRestype())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getResitem()),
                        TblwipcontResourcePO::getResitem, tblwipcontResourceDTO.getResitem())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getResvalue()),
                        TblwipcontResourcePO::getResvalue, tblwipcontResourceDTO.getResvalue())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getStdvalue()),
                        TblwipcontResourcePO::getStdvalue, tblwipcontResourceDTO.getStdvalue())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getInputqty()),
                        TblwipcontResourcePO::getInputqty, tblwipcontResourceDTO.getInputqty())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getUserno()),
                        TblwipcontResourcePO::getUserno, tblwipcontResourceDTO.getUserno())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getEventtime()),
                        TblwipcontResourcePO::getEventtime, tblwipcontResourceDTO.getEventtime())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getRwomesno()),
                        TblwipcontResourcePO::getRwomesno, tblwipcontResourceDTO.getRwomesno())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getSubrwomesno()),
                        TblwipcontResourcePO::getSubrwomesno, tblwipcontResourceDTO.getSubrwomesno())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getPriceType()),
                        TblwipcontResourcePO::getPriceType, tblwipcontResourceDTO.getPriceType())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getUnitPrice()),
                        TblwipcontResourcePO::getUnitPrice, tblwipcontResourceDTO.getUnitPrice())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getPriceRate()),
                        TblwipcontResourcePO::getPriceRate, tblwipcontResourceDTO.getPriceRate())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getCPost()),
                        TblwipcontResourcePO::getCPost, tblwipcontResourceDTO.getCPost())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getCType()),
                        TblwipcontResourcePO::getCType, tblwipcontResourceDTO.getCType())
                .eq(ObjectUtil.isNotEmpty(tblwipcontResourceDTO.getCShiftno()),
                        TblwipcontResourcePO::getCShiftno, tblwipcontResourceDTO.getCShiftno())
                ;
        List<TblwipcontResourcePO> tblwipcontResourcePOS = baseMapper.selectList(lambdaQueryWrapper);
        return ITblwipcontResourceConvertor.INSTANCE.POListToVOList(tblwipcontResourcePOS);
    }

    @Override
    public List<TblwipcontResourceVO> getListByEventTime(TblwipcontResourceDTO tblwipcontResourceDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(DataSourceEnum.SITE_20.getCode()));
        LambdaQueryWrapper<TblwipcontResourcePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TblwipcontResourcePO::getEventtime,tblwipcontResourceDTO.getStartTime());
        queryWrapper.le(TblwipcontResourcePO::getEventtime,tblwipcontResourceDTO.getEndTime());
        queryWrapper.ne(TblwipcontResourcePO::getResitem,"EMP");
        queryWrapper.ne(TblwipcontResourcePO::getResitem,"UCB");
        List<TblwipcontResourcePO> tblwipcontResourcePOList = list(queryWrapper);
        return ITblwipcontResourceConvertor.INSTANCE.POListToVOList(tblwipcontResourcePOList);
    }
}
