package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblusrtitlebasisPO;
import cn.jihong.mes.api.model.vo.TblusrtitlebasisVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITblusrtitlebasisConvertor {

    ITblusrtitlebasisConvertor INSTANCE = Mappers.getMapper(ITblusrtitlebasisConvertor.class);

    TblusrtitlebasisVO POToVO (TblusrtitlebasisPO iTblusrtitlebasisPO);
    List<TblusrtitlebasisVO> POListToVOList (List<TblusrtitlebasisPO> tblusrtitlebasisPOS);
}
