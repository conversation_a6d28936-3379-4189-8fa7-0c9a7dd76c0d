package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProcessLossFluctuationTrendParamDTO;
import cn.jihong.mes.api.model.dto.ProcessLossSumParamDTO;
import cn.jihong.mes.api.model.vo.ProcessLossFluctuationTrendMesVO;
import cn.jihong.mes.api.model.vo.ProcessLossSumMesVO;
import cn.jihong.mes.api.service.IProcessLossFluctuationTrendService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序损耗波动趋势
 */
@RestController
@RequestMapping("/processLossFluctuationTrend")
@ShenyuSpringMvcClient(path = "/processLossFluctuationTrend/**")
public class ProcessLossFluctuationTrendController {
    @Resource
    private IProcessLossFluctuationTrendService processLossFluctuationTrendService;

    /**
     * 查询工序损耗波动趋势
     * @param processLossFluctuationTrendParamDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProcessLossFluctuationTrendMesVO>> getList(@RequestBody ProcessLossFluctuationTrendParamDTO processLossFluctuationTrendParamDTO) {
        List<ProcessLossFluctuationTrendMesVO>  processLossFluctuationTrendMesVOS = processLossFluctuationTrendService.selectFluctuationTrend(processLossFluctuationTrendParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, processLossFluctuationTrendMesVOS);
    }
}
