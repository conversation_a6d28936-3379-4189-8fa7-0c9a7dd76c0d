package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblCusProcessBarCodeHistoryPO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodePO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeHistoryVO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TblCusProcessBarCodeHistoryConvertor {

    TblCusProcessBarCodeHistoryConvertor INSTANCE = Mappers.getMapper(TblCusProcessBarCodeHistoryConvertor.class);

    TblCusProcessBarCodeHistoryVO POToVO (TblCusProcessBarCodeHistoryPO tblCusProcessBarCodeHistoryPO);
    List<TblCusProcessBarCodeHistoryVO> POListToVOList (List<TblCusProcessBarCodeHistoryPO> tblCusProcessBarCodeHistoryPOList);
}
