package cn.jihong.mes.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.DailyProductionPlanAchievementRateDTO;
import cn.jihong.mes.api.model.dto.ProductProcessStandardLossSetDTO;
import cn.jihong.mes.api.model.dto.ProductionShiftDetailDTO;
import cn.jihong.mes.api.model.vo.DailyProductionPlanAchievementRateVO;
import cn.jihong.mes.api.model.vo.ProductProcessStandardLossSetVO;
import cn.jihong.mes.api.service.IProductProcessStandardLossSetService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 产品工序标准损耗设定表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@RestController
@RequestMapping("/productProcessStandardLossSet")
@ShenyuSpringMvcClient(path = "/productProcessStandardLossSet/**")
public class ProductProcessStandardLossSetController {
    @Resource
    private IProductProcessStandardLossSetService productProcessStandardLossSetService;

    /**
     * 查询产品工序标准损耗设定
     * @param productProcessStandardLossSetDTO
     * @param pageRequest
     * @return
     */
    @PostMapping("list")
    public StandardResult<Pagination<ProductProcessStandardLossSetVO>> getList(@RequestBody ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO, @RequestBody @Validated PageRequest pageRequest) {
        Pagination<ProductProcessStandardLossSetVO> productProcessStandardLossSetVOPagination = productProcessStandardLossSetService.getList(productProcessStandardLossSetDTO,pageRequest);
        return StandardResult.resultCode(OperateCode.SUCCESS, productProcessStandardLossSetVOPagination);
    }

    /**
     * 新增标准设定
     * @param productProcessStandardLossSetDTO
     * @return
     */
    @PostMapping("/save")
    public StandardResult save(@RequestBody ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO) {
        productProcessStandardLossSetService.save(productProcessStandardLossSetDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 编辑标准设定
     * @param productProcessStandardLossSetDTO
     * @return
     */
    @PostMapping("/edit")
    public StandardResult edit(@RequestBody ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO) {
        productProcessStandardLossSetService.edit(productProcessStandardLossSetDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 删除标准设定
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public StandardResult delete(@PathVariable String id) {
        productProcessStandardLossSetService.delete(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }
}

