package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.MonoDTO;
import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodeHistoryPO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodePO;
import cn.jihong.mes.api.model.vo.*;
import cn.jihong.mes.api.service.ITblCusProcessBarCodeService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.TblCusProcessBarCodeConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblCusProcessBarCodeHistoryMapper;
import cn.jihong.mes.app.mapper.TblCusProcessBarCodeMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@DubboService
public class TblCusProcessBarCodeServiceImpl extends JiHongServiceImpl<TblCusProcessBarCodeMapper, TblCusProcessBarCodePO> implements ITblCusProcessBarCodeService {

    @Resource
    private TblCusProcessBarCodeHistoryMapper tblCusProcessBarCodeHistoryMapper;

    @Override
    public List<TblCusProcessBarCodeVO> getTblCusProcessBarCodeList(TblCusProcessBarCodeDTO tblCusProcessBarCodeDTO) {
        if (BeanUtil.isEmpty(tblCusProcessBarCodeDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tblCusProcessBarCodeDTO.getFactoryCode()));
        LambdaQueryWrapper<TblCusProcessBarCodePO> lambdaQueryWrapper = new LambdaQueryWrapper<TblCusProcessBarCodePO>()
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getProcessBarCode()),
                        TblCusProcessBarCodePO::getProcessBarCode, tblCusProcessBarCodeDTO.getProcessBarCode())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getOpNo()),
                        TblCusProcessBarCodePO::getOpNo, tblCusProcessBarCodeDTO.getOpNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getMono()),
                        TblCusProcessBarCodePO::getMono, tblCusProcessBarCodeDTO.getMono())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getProductName()),
                        TblCusProcessBarCodePO::getProductName, tblCusProcessBarCodeDTO.getProductName())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getUnitNo()),
                        TblCusProcessBarCodePO::getUnitNo, tblCusProcessBarCodeDTO.getUnitNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getBaseLotNo()),
                        TblCusProcessBarCodePO::getBaseLotNo, tblCusProcessBarCodeDTO.getBaseLotNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getNodeId()),
                        TblCusProcessBarCodePO::getNodeId, tblCusProcessBarCodeDTO.getNodeId())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getProductNo()),
                        TblCusProcessBarCodePO::getProductNo, tblCusProcessBarCodeDTO.getProductNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getUserNo()),
                        TblCusProcessBarCodePO::getUserNo, tblCusProcessBarCodeDTO.getUserNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeDTO.getEquipmentNo()),
                        TblCusProcessBarCodePO::getEquipmentNo, tblCusProcessBarCodeDTO.getEquipmentNo());
        List<TblCusProcessBarCodePO> tblCusProcessBarCodePOS = baseMapper.selectList(lambdaQueryWrapper);
        return TblCusProcessBarCodeConvertor.INSTANCE.POListToVOList(tblCusProcessBarCodePOS);
    }

    @Override
    public ProductionProcessesDetailVO getProductionProcessesDetailList(MonoDTO monoDTO) {
        if (BeanUtil.isEmpty(monoDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(monoDTO.getFactoryCode()));
        ProductionProcessesDetailVO productionProcessesDetailVO = new ProductionProcessesDetailVO();

        //工序信息
        List<OpDetailVO> opDetailVOS  = baseMapper.getOpDetail(monoDTO);
        productionProcessesDetailVO.setOpDetailVOS(opDetailVOS);

        //工序栈板信息
        List<ProductionPalletDetailVO> productionPalletDetails = baseMapper.getProductionPalletDetailList(monoDTO);
        productionProcessesDetailVO.setProductionPalletDetailVOs(productionPalletDetails);

        //工序栈板码
        List<TblCusProcessBarCodeHistoryPO> tblCusProcessBarCodeHistoryPOS = productionPalletDetails.stream().map(productionPalletDetailVO -> {
            TblCusProcessBarCodeHistoryPO tblCusProcessBarCodeHistoryPO = new TblCusProcessBarCodeHistoryPO();
            tblCusProcessBarCodeHistoryPO.setOnProcessBarCode(productionPalletDetailVO.getProcessBarCode());
            return tblCusProcessBarCodeHistoryPO;
        }).collect(Collectors.toList());
        //提取工序中使用的原材料详细数据
        List<ProduceProcessesUseMaterialVO> produceProcessesUseMaterialVOS = tblCusProcessBarCodeHistoryMapper.selectProduceProcessesUseMaterialVOs(monoDTO.getMono());
        productionProcessesDetailVO.setProduceProcessesUseMaterialVOS(produceProcessesUseMaterialVOS);

        //提取工序质检信息
        List<ProcessQualityInspectionVO> processQualityInspectionVOS = baseMapper.getProcessQualityInspections(monoDTO.getMono(),monoDTO.getItemNo());
        productionProcessesDetailVO.setProcessQualityInspectionVOS(processQualityInspectionVOS);

        //提取报工信息
        List<ProcessReportingDetailVO> processReportingDetailVOS = baseMapper.getProcessReportingDetails(monoDTO.getMono());
        productionProcessesDetailVO.setProcessReportingDetailVOS(processReportingDetailVOS);

        return productionProcessesDetailVO;
    }
}
