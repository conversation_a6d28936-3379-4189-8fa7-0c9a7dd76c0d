package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.TblCusProcessMaterRecordDTO;
import cn.jihong.mes.api.model.po.TblCusProcessMaterRecordPO;
import cn.jihong.mes.api.model.vo.TblCusProcessMaterRecordVO;
import cn.jihong.mes.api.service.ITblCusProcessMaterRecordService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.TblCusProcessMaterRecordConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblCusProcessMaterRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@DubboService
public class TblCusProcessMaterRecordServiceImpl extends JiHongServiceImpl<TblCusProcessMaterRecordMapper, TblCusProcessMaterRecordPO> implements ITblCusProcessMaterRecordService {

    @Override
    public List<TblCusProcessMaterRecordVO> getTblCusProcessMaterRecordList(TblCusProcessMaterRecordDTO tblCusProcessMaterRecordDTO) {
        if (BeanUtil.isEmpty(tblCusProcessMaterRecordDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tblCusProcessMaterRecordDTO.getFactoryCode()));
        LambdaQueryWrapper<TblCusProcessMaterRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<TblCusProcessMaterRecordPO>()
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getProcessBarCode()),
                        TblCusProcessMaterRecordPO::getProcessBarCode, tblCusProcessMaterRecordDTO.getProcessBarCode())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getBarCode()),
                        TblCusProcessMaterRecordPO::getBarCode, tblCusProcessMaterRecordDTO.getBarCode())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getItemNo()),
                        TblCusProcessMaterRecordPO::getItemNo, tblCusProcessMaterRecordDTO.getItemNo())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getItemName()),
                        TblCusProcessMaterRecordPO::getItemName, tblCusProcessMaterRecordDTO.getItemName())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getUnitNo()),
                        TblCusProcessMaterRecordPO::getUnitNo, tblCusProcessMaterRecordDTO.getUnitNo())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getBaseLotNo()),
                        TblCusProcessMaterRecordPO::getBaseLotNo, tblCusProcessMaterRecordDTO.getBaseLotNo())
                .eq(StrUtil.isNotBlank(tblCusProcessMaterRecordDTO.getUserNo()),
                        TblCusProcessMaterRecordPO::getUserNo, tblCusProcessMaterRecordDTO.getUserNo());
        List<TblCusProcessMaterRecordPO> tblCusProcessMaterRecordPOS = baseMapper.selectList(lambdaQueryWrapper);
        return TblCusProcessMaterRecordConvertor.INSTANCE.POListToVOList(tblCusProcessMaterRecordPOS);
    }
}
