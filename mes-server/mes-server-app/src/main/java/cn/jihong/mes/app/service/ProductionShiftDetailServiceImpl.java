package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProductionShiftDetailDTO;
import cn.jihong.mes.api.model.po.ProductionShiftDetailPO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mes.api.model.vo.TimeVO;
import cn.jihong.mes.api.service.IProductionShiftDetailService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.ProductionShiftDetailMapper;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班次详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@DubboService
public class ProductionShiftDetailServiceImpl extends
    JiHongServiceImpl<ProductionShiftDetailMapper, ProductionShiftDetailPO> implements IProductionShiftDetailService {

    @Override
    public List<ProductionShiftDetailVO> getList(ProductionShiftDetailDTO productionShiftDetailDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionShiftDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(
            productionShiftDetailDTO != null && StringUtil.isNotEmpty(productionShiftDetailDTO.getShiftDetailName()),
            ProductionShiftDetailPO::getShiftDetailName, productionShiftDetailDTO.getShiftDetailName());
        queryWrapper.eq(ProductionShiftDetailPO::getCompanyCode,companyCode);
        List<ProductionShiftDetailPO> productionShiftDetailPOS = list(queryWrapper);
        List<ProductionShiftDetailVO> productionShiftDetailVOS = BeanUtil.copyToList(productionShiftDetailPOS,ProductionShiftDetailVO.class);
        return productionShiftDetailVOS;
    }

    @Override
    public void save(ProductionShiftDetailDTO productionShiftDetailDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        if (StringUtil.isEmpty(productionShiftDetailDTO.getShiftDetailName())){
            throw new CommonException("班次不能为空");
        }
        if (CollectionUtil.isEmpty(productionShiftDetailDTO.getTimeSlot())){
            throw new CommonException("时间不能为空");
        }
        ProductionShiftDetailPO productionShiftDetailPO = new ProductionShiftDetailPO();
        BeanUtil.copyProperties(productionShiftDetailDTO,productionShiftDetailPO);
        productionShiftDetailPO.setCreateTime(new Date());
        productionShiftDetailPO.setIsDeleted(false);
        List<TimeVO> timeSlot = productionShiftDetailDTO.getTimeSlot();
        List<ProductionShiftDetailPO> productionShiftDetailPOList = new ArrayList<>();
        List<String> timeList = new ArrayList<>();
        for (TimeVO timeVO:timeSlot){
            if (!TimeTypeUtil.isLegalDate(timeVO.getStartTime().length(),timeVO.getStartTime(),"HH:mm")){
                throw new CommonException("时间格式不符合：HH:mm");
            }
            if (!TimeTypeUtil.isLegalDate(timeVO.getEndTime().length(),timeVO.getEndTime(),"HH:mm")){
                throw new CommonException("时间格式不符合：HH:mm");
            }
            String timeStr = timeVO.getStartTime()+"-"+timeVO.getEndTime();
            timeList.add(timeStr);
        }
        String companyCode = SecurityUtil.getCompanySite();
        productionShiftDetailPO.setCompanyCode(companyCode);
        productionShiftDetailPO.setTimeSlot(StringUtils.join(timeList.toArray(),","));
        save(productionShiftDetailPO);
    }

    @Override
    public void update(ProductionShiftDetailDTO productionShiftDetailDTO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaUpdateWrapper<ProductionShiftDetailPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductionShiftDetailPO::getId,productionShiftDetailDTO.getId());
        updateWrapper.set(ProductionShiftDetailPO::getShiftDetailName,productionShiftDetailDTO.getShiftDetailName());
        updateWrapper.set(ProductionShiftDetailPO::getSerialNo,productionShiftDetailDTO.getSerialNo());
        List<TimeVO> timeSlot = productionShiftDetailDTO.getTimeSlot();
        List<String> timeList = new ArrayList<>();
        for (TimeVO timeVO:timeSlot){
            String timeStr = timeVO.getStartTime()+"-"+timeVO.getEndTime();
            timeList.add(timeStr);
        }
        updateWrapper.set(ProductionShiftDetailPO::getTimeSlot,StringUtils.join(timeList.toArray(),","));
        updateWrapper.set(ProductionShiftDetailPO::getUpdateTime,new Date());
        update(updateWrapper);
    }

    @Override
    public void delete(String id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaUpdateWrapper<ProductionShiftDetailPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductionShiftDetailPO::getId,id);
        updateWrapper.set(ProductionShiftDetailPO::getIsDeleted,true);
        updateWrapper.set(ProductionShiftDetailPO::getUpdateTime,new Date());
        update(updateWrapper);
    }

    @Override
    public List<ProductionShiftDetailVO> getDetailList(String mainId) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionShiftDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionShiftDetailPO::getMainId,Integer.valueOf(mainId));
        queryWrapper.eq(ProductionShiftDetailPO::getIsDeleted,false);
        queryWrapper.eq(ProductionShiftDetailPO::getCompanyCode,companyCode);
        queryWrapper.orderByAsc(ProductionShiftDetailPO::getSerialNo);
        List<ProductionShiftDetailPO> productionShiftDetailPOS = list(queryWrapper);
        List<ProductionShiftDetailVO> productionShiftDetailVOS = new ArrayList<>();
        for (ProductionShiftDetailPO productionShiftDetailPO:productionShiftDetailPOS){
            List<TimeVO> timeVOS = new ArrayList<>();
            ProductionShiftDetailVO productionShiftDetailVO = new ProductionShiftDetailVO();
            String timeSlot = productionShiftDetailPO.getTimeSlot();
            if(StringUtil.isNotEmpty(timeSlot)){
                List<String> timeSlotList = Arrays.asList(timeSlot.split(","));
                for (String timeStr:timeSlotList){
                    List<String> timeStrList = Arrays.asList(timeStr.split("-"));
                    TimeVO timeVO = new TimeVO();
                    timeVO.setStartTime(timeStrList.get(0));
                    timeVO.setEndTime(timeStrList.get(1));
                    timeVOS.add(timeVO);
                }
            }
            BeanUtil.copyProperties(productionShiftDetailPO,productionShiftDetailVO,"timeSlot");
            productionShiftDetailVO.setTimeSlot(timeVOS);
            productionShiftDetailVOS.add(productionShiftDetailVO);
        }

        return productionShiftDetailVOS;
    }

    @Override
    public List<ProductionShiftDetailPO> getShiftsByCompanyCode(String companyCode) {
        LambdaQueryWrapper<ProductionShiftDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductionShiftDetailPO::getCompanyCode, companyCode);
        return this.list(queryWrapper);
    }

}
