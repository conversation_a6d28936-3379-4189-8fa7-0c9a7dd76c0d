package cn.jihong.mes.app.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupVO;
import cn.jihong.mes.api.service.IActualLossStatisticsGroupService;

/**
 * <p>
 * 实际损耗率统计（集团按个）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-1
 */
@RestController
@RequestMapping("/actualLossStatisticsGroup")
@ShenyuSpringMvcClient(path = "/actualLossStatisticsGroup/**")
public class ActualLossStatisticsGroupController {
    @Resource
    private IActualLossStatisticsGroupService actualLossStatisticsGroupService;

    /**
     * 查询实际损耗率统计
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ActualLossStatisticsMesGroupVO>> getList(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        List<ActualLossStatisticsMesGroupVO> actualLossStatisticsMesGroupVOS = actualLossStatisticsGroupService.selectActualLosss(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, actualLossStatisticsMesGroupVOS);
    }

    /**
     * 导出excel
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = actualLossStatisticsGroupService.exportToExcel(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出明细
     * @param actualLossStatisticsParamGroupDTO
     * @return
     */
    @PostMapping("exportDetail")
    public StandardResult<String> exportDetail(@RequestBody ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        String url = actualLossStatisticsGroupService.exportToExcelDetail(actualLossStatisticsParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
