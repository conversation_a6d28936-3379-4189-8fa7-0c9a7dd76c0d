package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeBoxDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodeBoxPO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeBoxVO;
import cn.jihong.mes.api.service.ITblCusProcessBarCodeBoxService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.TblCusProcessBarCodeBoxConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblCusProcessBarCodeBoxMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@DubboService
public class TblCusProcessBarCodeBoxServiceImpl extends JiHongServiceImpl<TblCusProcessBarCodeBoxMapper, TblCusProcessBarCodeBoxPO> implements ITblCusProcessBarCodeBoxService {

    @Override
    public List<TblCusProcessBarCodeBoxVO> getTblCusProcessBarCodeBoxList(TblCusProcessBarCodeBoxDTO tblCusProcessBarCodeBoxDTO) {
        if (BeanUtil.isEmpty(tblCusProcessBarCodeBoxDTO)) {
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tblCusProcessBarCodeBoxDTO.getFactoryCode()));
        LambdaQueryWrapper<TblCusProcessBarCodeBoxPO> lambdaQueryWrapper = new LambdaQueryWrapper<TblCusProcessBarCodeBoxPO>()
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getProcessBarCode()),
                        TblCusProcessBarCodeBoxPO::getProcessBarCode, tblCusProcessBarCodeBoxDTO.getProcessBarCode())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getBoxId()),
                        TblCusProcessBarCodeBoxPO::getBoxId, tblCusProcessBarCodeBoxDTO.getBoxId())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getMono()),
                        TblCusProcessBarCodeBoxPO::getMono, tblCusProcessBarCodeBoxDTO.getMono())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getItemNo()),
                        TblCusProcessBarCodeBoxPO::getItemNo, tblCusProcessBarCodeBoxDTO.getItemNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getItemName()),
                        TblCusProcessBarCodeBoxPO::getItemName, tblCusProcessBarCodeBoxDTO.getItemName())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getPackUserNo()),
                        TblCusProcessBarCodeBoxPO::getPackUserNo, tblCusProcessBarCodeBoxDTO.getPackUserNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeBoxDTO.getPackUsername()),
                        TblCusProcessBarCodeBoxPO::getPackUsername, tblCusProcessBarCodeBoxDTO.getPackUsername());
        List<TblCusProcessBarCodeBoxPO> tblCusProcessBarCodeBoxPOS = baseMapper.selectList(lambdaQueryWrapper);
        return TblCusProcessBarCodeBoxConvertor.INSTANCE.POListToVOList(tblCusProcessBarCodeBoxPOS);
    }
}
