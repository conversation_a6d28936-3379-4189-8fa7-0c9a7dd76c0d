package cn.jihong.mes.app.controller;

import java.util.List;

import javax.annotation.Resource;

import cn.jihong.mes.api.model.dto.SiteViewDTO;
import cn.jihong.mes.api.model.vo.SiteViewVO;
import cn.jihong.mes.api.service.ISiteViewService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.model.resultcode.OperateCode;

/**
 * <p>
 *  工厂视图
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-8
 */
@RestController
@RequestMapping("/siteView")
@ShenyuSpringMvcClient(path = "/siteView/**")
public class SiteViewController {
    @Autowired
    private ISiteViewService siteViewService;

    /**
     * 查询工厂
     * @param siteViewDTO
     * @return
     */
    @PostMapping("/list")
    public StandardResult<List<SiteViewVO>> getList(@RequestBody SiteViewDTO siteViewDTO) {
        List<SiteViewVO> siteViewVOS = siteViewService.getList(siteViewDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, siteViewVOS);
    }

}

