package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProductionShiftDetailDTO;
import cn.jihong.mes.api.model.dto.ProductionShiftSetDTO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mes.api.service.IProductionShiftSetService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;
//import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  班次设定
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@RestController
@RequestMapping("/productionShiftSet")
@ShenyuSpringMvcClient(path = "/productionShiftSet/**")
public class ProductionShiftSetController {
    @Resource
    private IProductionShiftSetService productionShiftSetService;

    /**
     * 查询班次设定列表
     * @param productionShiftSetDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProductionShiftSetVO>> getList(@RequestBody ProductionShiftSetDTO productionShiftSetDTO) {
        List<ProductionShiftSetVO> productionShiftSetVOS = productionShiftSetService.getList(productionShiftSetDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, productionShiftSetVOS);
    }

    /**
     * 新增班次
     * @param productionShiftSetDTO
     * @return
     */
    @PostMapping("save")
    public StandardResult save(@RequestBody ProductionShiftSetDTO productionShiftSetDTO) {
        productionShiftSetService.save(productionShiftSetDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 编辑班次
     * @param productionShiftSetDTO
     * @return
     */
    @PostMapping("edit")
    public StandardResult edit(@RequestBody ProductionShiftSetDTO productionShiftSetDTO) {
        productionShiftSetService.update(productionShiftSetDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 启用停用班次
     * @param id
     * @return
     */
    @GetMapping("/enable/{id}/{status}")
    public StandardResult enableStatus(@PathVariable("id") String id,@PathVariable("status") boolean status) {
        productionShiftSetService.enableStatus(id,status);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 停用班次
     * @param id
     * @return
     */
    @GetMapping("/disable/{id}")
    public StandardResult disableStatus(@PathVariable String id) {
        productionShiftSetService.disableStatus(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 删除班次
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public StandardResult delete(@PathVariable String id) {
        productionShiftSetService.delete(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


}

