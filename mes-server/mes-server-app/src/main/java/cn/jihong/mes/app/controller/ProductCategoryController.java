package cn.jihong.mes.app.controller;


import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProductCategoryDTO;
import cn.jihong.mes.api.model.dto.ProductionShiftSetDTO;
import cn.jihong.mes.api.model.vo.ProductCategoryVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mes.api.service.IProductCategoryService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 产品类别 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@RestController
@RequestMapping("/productCategory")
@ShenyuSpringMvcClient(path = "/productCategory/**")
public class ProductCategoryController {
    @Resource
    private IProductCategoryService productCategoryService;

    /**
     * 查询产品类别
     * @param productCategoryDTO
     * @param pageRequest
     * @return
     */
    @PostMapping("list")
    public StandardResult<Pagination<ProductCategoryVO>> getList(@RequestBody ProductCategoryDTO productCategoryDTO,@RequestBody @Validated PageRequest pageRequest) {
        Pagination<ProductCategoryVO> pagination = productCategoryService.getList(productCategoryDTO,pageRequest);
        return StandardResult.resultCode(OperateCode.SUCCESS, pagination);
    }
}
