package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblwipcontResourcePO;
import cn.jihong.mes.api.model.vo.TblwipcontResourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITblwipcontResourceConvertor {

    ITblwipcontResourceConvertor INSTANCE = Mappers.getMapper(ITblwipcontResourceConvertor.class);

    TblwipcontResourceVO POToVO (TblwipcontResourcePO iTblwipcontResourcePO);
    List<TblwipcontResourceVO> POListToVOList (List<TblwipcontResourcePO> tblwipcontResourcePOS);
}
