package cn.jihong.mes.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.dto.GroupLossStatisticsDayParamDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupVO;
import cn.jihong.mes.api.model.vo.GroupLossStatisticsDayVO;
import cn.jihong.mes.api.service.IGroupLossStatisticsDayService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 集团损耗统计日报-单位张 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@RestController
@RequestMapping("/groupLossStatisticsDay")
@ShenyuSpringMvcClient(path = "/groupLossStatisticsDay/**")
public class GroupLossStatisticsDayController {
    @Resource
    private IGroupLossStatisticsDayService groupLossStatisticsDayService;
    /**
     * 查询集团损耗统计日报列表
     * @param
     * @return
     */
    @PostMapping("list")
    public StandardResult<Pagination<GroupLossStatisticsDayVO>> getList(@RequestBody GroupLossStatisticsDayParamDTO groupLossStatisticsDayParamDTO) {
        Pagination<GroupLossStatisticsDayVO> groupLossStatisticsDayVOPagination = groupLossStatisticsDayService.getList(groupLossStatisticsDayParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, groupLossStatisticsDayVOPagination);
    }

}

