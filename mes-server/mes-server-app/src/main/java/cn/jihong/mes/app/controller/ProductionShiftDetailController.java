package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProductionShiftDetailDTO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mes.api.service.IProductionShiftDetailService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 班次详情表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@RestController
@RequestMapping("/productionShiftDetail")
@ShenyuSpringMvcClient(path = "/productionShiftDetail/**")
public class ProductionShiftDetailController {
    @Resource
    private IProductionShiftDetailService productionShiftDetailService;


    /**
     * 查询班次详情列表
     * @param productionShiftDetailDTO
     * @return
     */
    @PostMapping("/list")
    public StandardResult<List<ProductionShiftDetailVO>> getList(@RequestBody ProductionShiftDetailDTO productionShiftDetailDTO) {
        List<ProductionShiftDetailVO> productionShiftDetailVOS = productionShiftDetailService.getList(productionShiftDetailDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, productionShiftDetailVOS);
    }

    /**
     * 新增班次详情
     * @param productionShiftDetailDTO
     * @return
     */
    @PostMapping("/save")
    public StandardResult save(@RequestBody ProductionShiftDetailDTO productionShiftDetailDTO) {
        productionShiftDetailService.save(productionShiftDetailDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 编辑班次详情
     * @param productionShiftDetailDTO
     * @return
     */
    @PostMapping("/edit")
    public StandardResult edit(@RequestBody ProductionShiftDetailDTO productionShiftDetailDTO) {
        productionShiftDetailService.update(productionShiftDetailDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 删除班次详情
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public StandardResult delete(@PathVariable String id) {
        productionShiftDetailService.delete(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 查询班次设定详情
     * @param mainId
     * @return
     */
    @GetMapping("/detailList/{mainId}")
    public StandardResult<List<ProductionShiftDetailVO>> getDetailList(@PathVariable String mainId) {
        List<ProductionShiftDetailVO> productionShiftDetailVOS = productionShiftDetailService.getDetailList(mainId);
        return StandardResult.resultCode(OperateCode.SUCCESS, productionShiftDetailVOS);
    }

}

