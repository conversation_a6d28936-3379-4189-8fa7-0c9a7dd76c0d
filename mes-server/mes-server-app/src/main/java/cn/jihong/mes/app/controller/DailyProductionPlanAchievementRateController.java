package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.DailyProductionPlanAchievementRateDTO;
import cn.jihong.mes.api.model.dto.ProductionShiftSetDTO;
import cn.jihong.mes.api.model.vo.DailyProductionPlanAchievementRateVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mes.api.service.IDailyProductionPlanAchievementRateService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 日生产计划产量达成率 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@RestController
@RequestMapping("/achievementRate")
@ShenyuSpringMvcClient(path = "/achievementRate/**")
public class DailyProductionPlanAchievementRateController {
    @Resource
    private IDailyProductionPlanAchievementRateService dailyProductionPlanAchievementRateService;

    /**
     * 查询生产计划达成率
     * @param dailyProductionPlanAchievementRateDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<DailyProductionPlanAchievementRateVO>> getList(@RequestBody DailyProductionPlanAchievementRateDTO dailyProductionPlanAchievementRateDTO) {
        List<DailyProductionPlanAchievementRateVO> dailyProductionPlanAchievementRateVOS = dailyProductionPlanAchievementRateService.getList(dailyProductionPlanAchievementRateDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, dailyProductionPlanAchievementRateVOS);
    }

    /**
     * 导出excel
     * @param dailyProductionPlanAchievementRateDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody DailyProductionPlanAchievementRateDTO dailyProductionPlanAchievementRateDTO) {
        String url = dailyProductionPlanAchievementRateService.exportToExcel(dailyProductionPlanAchievementRateDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
