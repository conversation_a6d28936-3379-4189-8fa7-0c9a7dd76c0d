package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblCusProcessMaterRecordPO;
import cn.jihong.mes.api.model.vo.TblCusProcessMaterRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TblCusProcessMaterRecordConvertor {

    TblCusProcessMaterRecordConvertor INSTANCE = Mappers.getMapper(TblCusProcessMaterRecordConvertor.class);

    TblCusProcessMaterRecordVO POToVO (TblCusProcessMaterRecordPO tblCusProcessMaterRecordPO);
    List<TblCusProcessMaterRecordVO> POListToVOList (List<TblCusProcessMaterRecordPO> tblCusProcessMaterRecordPOS);
}
