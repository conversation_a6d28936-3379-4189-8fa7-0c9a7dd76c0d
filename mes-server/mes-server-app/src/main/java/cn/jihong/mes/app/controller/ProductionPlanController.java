package cn.jihong.mes.app.controller;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.jihong.mes.app.excel.PreCheckProductionPlanExcelListener;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListInVO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListOutVO;
import cn.jihong.mes.api.model.vo.GetProductionPlanListInVO;
import cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.api.service.IProductionPlanService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.excel.ProductionPlanExcelListener;
import cn.jihong.mes.app.excel.XiamenProductionPlanExcelListener;
import cn.jihong.oa.erp.api.model.enums.UnitEnum;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.service.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 生产计划表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Slf4j
@RestController
@RequestMapping("/productionPlan")
@ShenyuSpringMvcClient(path = "/productionPlan/**")
public class ProductionPlanController {
    private String[] siteArr = {"SITE-01","SITE-22","SITE-14","SITE-16","SITE-08","SITE-082","SITE-20"};
    @Resource
    private IProductionPlanService productionPlanService;

    @DubboReference
    private IOocqlTService iOocqlTService;

    @DubboReference
    private ISfcbTService sfcbTService;

    @DubboReference
    private IEcaaucTService ecaaucTService;

    @DubboReference
    private IGzxcTService gzxcTService;

    @DubboReference
    private ISfaaTService sfaaTService;


    /**
     * 获得生产计划列表
     * @return {@link StandardResult}
     */
//    @PostMapping("/getProductionPlanList")
//    public StandardResult<List<GetProductionPlanListOutVO>> getProductionPlanList(@RequestBody @Validated GetProductionPlanListInVO getProductionPlanListInVO) {
//        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
//        List<GetProductionPlanListOutVO> getProductionPlanListOutVOS =
//            productionPlanService.getProductionPlanList(getProductionPlanListInVO);
//        return StandardResult.resultCode(OperateCode.SUCCESS ,getProductionPlanListOutVOS);
//    }

    /**
     * 获得生产计划列表
     *
     * （修改查询方式，基于mybatis分组返回）
     * @return {@link StandardResult}
     */
    @PostMapping("/getProductionPlanList")
    public StandardResult<List<GetProductionPlanListOutVO>> getProductionPlanList(@RequestBody @Validated GetProductionPlanListInVO getProductionPlanListInVO) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        List<GetProductionPlanListOutVO> getProductionPlanListOutVOS =
                productionPlanService.getProductionPlanListNew(getProductionPlanListInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS ,getProductionPlanListOutVOS);
    }

    /**
     * 导出内标签数据文件
     * @param productionPlanDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ProductionPlanDTO productionPlanDTO) {
        String url = productionPlanService.exportToExcel(productionPlanDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
    /**
     * 导入生产计划预检查
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("preCheck")
    @ResponseBody
    public StandardResult preCheck(MultipartFile file) throws IOException {
        String siteNo = SecurityUtil.getCompanySite();
        if (StringUtil.isEmpty(siteNo)){
            throw new CommonException("未获取到据点，请先登陆");
        }
//        List<String> siteList = Arrays.asList(siteArr);
//        if (siteList.contains(siteNo)){
        EasyExcel.read(file.getInputStream(), new PreCheckProductionPlanExcelListener(iOocqlTService,sfcbTService,gzxcTService,sfaaTService)).sheet(0).headRowNumber(0).doRead();
//        }else {
//            EasyExcel.read(file.getInputStream(), new ProductionPlanExcelListener(iOocqlTService,sfcbTService)).sheet(0).doRead();
//        }

        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 导入生产计划
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("upload")
    @ResponseBody
    public StandardResult upload(MultipartFile file) throws IOException {
        String siteNo = SecurityUtil.getCompanySite();
        if (StringUtil.isEmpty(siteNo)){
            throw new CommonException("未获取到据点，请先登陆");
        }
//        List<String> siteList = Arrays.asList(siteArr);
//        if (siteList.contains(siteNo)){
        EasyExcel.read(file.getInputStream(), new XiamenProductionPlanExcelListener(iOocqlTService,sfcbTService,gzxcTService,sfaaTService)).sheet(0).headRowNumber(0).doRead();
//        }else {
//            EasyExcel.read(file.getInputStream(), new ProductionPlanExcelListener(iOocqlTService,sfcbTService)).sheet(0).doRead();
//        }

        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 文件下载（失败了会返回一个有部分数据的Excel）
     * <p>
     * 1. 创建excel对应的实体对象 参照{@link }
     * <p>
     * 2. 设置返回的 参数
     * <p>
     * 3. 直接写，这里注意，finish的时候会自动关闭OutputStream,当然你外面再关闭流问题不大
     */
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            String fileName = URLEncoder.encode("生产计划模板", StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 使用Spring的ResourceUtils或PathUtil获取资源路径
            InputStream templateStream = new ClassPathResource("templatenew.xlsx").getInputStream();

            // 使用try-with-resources确保流正确关闭
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateStream).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
                List<MachineData> datas = new ArrayList<>();
                WriteTable writeTable = EasyExcel.writerTable().build();
                excelWriter.write(datas, writeSheet, writeTable);
                styleSheet(excelWriter, datas);
            }
        } catch (IOException e) {
            // Handle exception properly (e.g., logging and sending an error response)
            log.error("下载模板报错：",e);
            throw new CommonException("模板下载失败，请联系管理员");
        }
    }

    private void styleSheet(ExcelWriter excelWriter, List<MachineData> datas) {
        Sheet sheet = excelWriter.writeContext().writeSheetHolder().getCachedSheet();
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
        CellStyle lightGreenBackgroundStyle = createStyledBackground(workbook, IndexedColors.LIGHT_GREEN.getIndex());

        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        font.setBold(true);

        lightGreenBackgroundStyle.setFont(font);
        for (int i = 0; i < datas.size(); i++) {
            setCellStyleForRange(sheet, 9+i, 0, 9+i, 25, lightGreenBackgroundStyle);
        }
    }

    private static CellStyle createStyledBackground(Workbook workbook, short colorIndex) {
        CellStyle style = workbook.createCellStyle();
        setBorders(style);
        style.setFillForegroundColor(colorIndex);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private static void setBorders(CellStyle style) {
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
    }


    private static void setCellStyleForRange(Sheet sheet, int firstRow, int firstCol, int lastRow, int lastCol, CellStyle style) {
        for (int i = firstRow; i <= lastRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            for (int j = firstCol; j <= lastCol; j++) {
                Cell cell = row.getCell(j);
                if (cell == null) {
                    cell = row.createCell(j);
                }
                cell.setCellStyle(style);
            }
        }
    }

    private  List<MachineData> initData() {
        IProductionMachineService productionMachineService =  SpringUtil.getBean("productionMachineServiceImpl");
        GetProductionMachineListInVO getProductionMachineListInVO = new GetProductionMachineListInVO();
        getProductionMachineListInVO.setPageSize(9999);
        getProductionMachineListInVO.setPageNum(1);
        Pagination<GetProductionMachineListOutVO> getProductionMachineListOutVOS = productionMachineService.getProductionMachineList(getProductionMachineListInVO);
        List<String> processNos = null;
        if (CollectionUtil.isNotEmpty(getProductionMachineListOutVOS.getData())){
            // 使用stream来提取所有processNo：
            processNos = getProductionMachineListOutVOS.getData().stream()
                    .map(GetProductionMachineListOutVO::getProcessNo) // 获取processNo字段值
                    .collect(Collectors.toList()); // 收集为列表
        }
        String companyCode = SecurityUtil.getCompanySite();
        Map<String,String> unitMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(processNos)){
            List<EcaaucTPO> ecaaucTPOS = ecaaucTService.getListBySiteAndProcess(companyCode,processNos);
            for (EcaaucTPO ecaaucTPO:ecaaucTPOS){
                unitMap.put(ecaaucTPO.getEcaauc001(),ecaaucTPO.getEcaauc009());
            }
        }
        List<MachineData> datas = Lists.newArrayList();
        for (GetProductionMachineListOutVO getProductionMachineListOutVO:getProductionMachineListOutVOS.getData()){
            MachineData yourData = new MachineData();
            yourData.setMachine(getProductionMachineListOutVO.getErpEquipmentName());
            yourData.setProcess(getProductionMachineListOutVO.getProcess());
            String unit = unitMap.get(getProductionMachineListOutVO.getProcessNo());
            if (UnitEnum.SHEET.getCode().equals(unit)){
                yourData.setUnit(UnitEnum.SHEET.getName());
            }else if (UnitEnum.PCS.getCode().equals(unit)){
                yourData.setUnit(UnitEnum.PCS.getName());
            }else if (UnitEnum.RICE.getCode().equals(unit)){
                yourData.setUnit(UnitEnum.RICE.getName());
            }else if (UnitEnum.SLICED_RICE.getCode().equals(unit)){
                yourData.setUnit(UnitEnum.SLICED_RICE.getName());
            }else {
                yourData.setUnit(UnitEnum.PCS.getName());
            }

            datas.add(yourData);
        }

        return datas;
    }

    @Data
    public static class MachineData {

        /**
         * 工序
         */
        private String process;
        /**
         * 机台
         */
        private String machine;

        private String bak1;

        private String bak2;

        private String bak3;

        private String bak4;

        private String bak5;

        private String bak6;

        private String bak7;

        private String bak8;

        private String bak9;

        private String bak10;

        private String bak11;

        private String bak12;

        private String bak13;

        private String bak14;

        /**
         * 单位
         */
        private String unit;
    }



}

