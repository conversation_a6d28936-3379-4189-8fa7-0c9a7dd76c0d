package cn.jihong.mes.app.convertor;

import cn.jihong.mes.api.model.po.TblusrshiftbasisPO;
import cn.jihong.mes.api.model.vo.TblusrshiftbasisVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ITblusrshiftbasisConvertor {

    ITblusrshiftbasisConvertor INSTANCE = Mappers.getMapper(ITblusrshiftbasisConvertor.class);

    TblusrshiftbasisVO POToVO (TblusrshiftbasisPO iTblusrshiftbasisPO);
    List<TblusrshiftbasisVO> POListToVOList (List<TblusrshiftbasisPO> tblusrshiftbasisPOS);
}
