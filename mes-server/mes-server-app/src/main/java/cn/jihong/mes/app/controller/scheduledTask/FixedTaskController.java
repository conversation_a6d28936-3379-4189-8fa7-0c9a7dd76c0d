package cn.jihong.mes.app.controller.scheduledTask;

import cn.jihong.common.model.StandardResult;
import cn.jihong.mes.app.service.scheduledTask.FixedTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/fixedTasks")
public class FixedTaskController {

    private final FixedTaskService taskService;

    @Autowired
    public FixedTaskController(FixedTaskService taskService) {
        this.taskService = taskService;
    }

    // 修改任务的 Cron 表达式
    @PostMapping("/updateCron")
    public StandardResult updateCron(@RequestParam String taskId, @RequestParam String cronExpression) {
        taskService.updateCronExpression(taskId, cronExpression);
        return StandardResult.ok("任务 " + taskId + " 的 Cron 表达式已更新为: " + cronExpression);
    }

    // 查询所有任务状态（包括 Cron 表达式）
    @GetMapping("/status")
    public StandardResult<Map<String, String>> getTaskStatus() {
        return StandardResult.ok(taskService.getTaskStatus());
    }
}
