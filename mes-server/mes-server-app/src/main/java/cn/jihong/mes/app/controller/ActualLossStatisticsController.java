package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsMesDTO;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamDTO;
import cn.jihong.mes.api.model.dto.DailyProductionPlanAchievementRateDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesVO;
import cn.jihong.mes.api.model.vo.DailyProductionPlanAchievementRateVO;
import cn.jihong.mes.api.service.IActualLossStatisticsService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 实际损耗率统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-1
 */
@RestController
@RequestMapping("/actualLossStatistics")
@ShenyuSpringMvcClient(path = "/actualLossStatistics/**")
public class ActualLossStatisticsController {
    @Resource
    private IActualLossStatisticsService actualLossStatisticsService;

    /**
     * 查询实际损耗率统计
     * @param actualLossStatisticsParamDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ActualLossStatisticsMesVO>> getList(@RequestBody ActualLossStatisticsParamDTO actualLossStatisticsParamDTO) {
        List<ActualLossStatisticsMesVO> actualLossStatisticsMesVOS = actualLossStatisticsService.selectActualLosss(actualLossStatisticsParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, actualLossStatisticsMesVOS);
    }

    /**
     * 导出excel
     * @param actualLossStatisticsParamDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ActualLossStatisticsParamDTO actualLossStatisticsParamDTO) {
        String url = actualLossStatisticsService.exportToExcel(actualLossStatisticsParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
