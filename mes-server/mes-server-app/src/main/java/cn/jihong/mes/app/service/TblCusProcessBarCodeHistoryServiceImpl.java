package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.mes.api.model.dto.ProcessBarCodeDTO;
import cn.jihong.mes.api.model.dto.ProcessTeamMembersDTO;
import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeHistoryDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodeHistoryPO;
import cn.jihong.mes.api.model.po.TblCusProcessMaterRecordPO;
import cn.jihong.mes.api.model.vo.*;
import cn.jihong.mes.api.service.ITblCusProcessBarCodeHistoryService;
import cn.jihong.mes.api.service.ITblCusProcessMaterRecordService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.convertor.TblCusProcessBarCodeHistoryConvertor;
import cn.jihong.mes.app.convertor.TblCusProcessMaterRecordConvertor;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblCusProcessBarCodeHistoryMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@DubboService
public class TblCusProcessBarCodeHistoryServiceImpl extends JiHongServiceImpl<TblCusProcessBarCodeHistoryMapper, TblCusProcessBarCodeHistoryPO> implements ITblCusProcessBarCodeHistoryService {

    @Resource
    private ITblCusProcessMaterRecordService iTblCusProcessMaterRecordService;

    @Override
    public List<TblCusProcessBarCodeHistoryVO> getTblCusProcessBarCodeHistoryList(TblCusProcessBarCodeHistoryDTO tblCusProcessBarCodeHistoryDTO) {
        if (BeanUtil.isEmpty(tblCusProcessBarCodeHistoryDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(tblCusProcessBarCodeHistoryDTO.getFactoryCode()));
        LambdaQueryWrapper<TblCusProcessBarCodeHistoryPO> lambdaQueryWrapper = new LambdaQueryWrapper<TblCusProcessBarCodeHistoryPO>()
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getUpProcessBarCode()),
                        TblCusProcessBarCodeHistoryPO::getUpProcessBarCode, tblCusProcessBarCodeHistoryDTO.getUpProcessBarCode())
                .in(CollUtil.isNotEmpty(tblCusProcessBarCodeHistoryDTO.getOnProcessBarCode()),
                        TblCusProcessBarCodeHistoryPO::getOnProcessBarCode, tblCusProcessBarCodeHistoryDTO.getOnProcessBarCode())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getMono()),
                        TblCusProcessBarCodeHistoryPO::getMono, tblCusProcessBarCodeHistoryDTO.getMono())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getProductName()),
                        TblCusProcessBarCodeHistoryPO::getProductName, tblCusProcessBarCodeHistoryDTO.getProductName())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getUnitNo()),
                        TblCusProcessBarCodeHistoryPO::getUnitNo, tblCusProcessBarCodeHistoryDTO.getUnitNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getBaseLotNo()),
                        TblCusProcessBarCodeHistoryPO::getBaseLotNo, tblCusProcessBarCodeHistoryDTO.getBaseLotNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getProductNo()),
                        TblCusProcessBarCodeHistoryPO::getProductNo, tblCusProcessBarCodeHistoryDTO.getProductNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getUserNo()),
                        TblCusProcessBarCodeHistoryPO::getUserNo, tblCusProcessBarCodeHistoryDTO.getUserNo())
                .eq(StrUtil.isNotBlank(tblCusProcessBarCodeHistoryDTO.getEquipmentNo()),
                        TblCusProcessBarCodeHistoryPO::getEquipmentNo, tblCusProcessBarCodeHistoryDTO.getEquipmentNo());
        List<TblCusProcessBarCodeHistoryPO> tblCusProcessBarCodeHistoryPOS = baseMapper.selectList(lambdaQueryWrapper);
        return TblCusProcessBarCodeHistoryConvertor.INSTANCE.POListToVOList(tblCusProcessBarCodeHistoryPOS);
    }



    @Override
    public List<ProcessTeamMembersVO> getProcessTeamMembers(ProcessTeamMembersDTO processTeamMembersDTO) {
        if (BeanUtil.isEmpty(processTeamMembersDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(processTeamMembersDTO.getFactoryCode()));
        return baseMapper.selectProcessTeamMembers(processTeamMembersDTO);
    }

    @Override
    public List<ProcessDetailVO> getProcessDetail(ProcessTeamMembersDTO processTeamMembersDTO) {
        if (BeanUtil.isEmpty(processTeamMembersDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(processTeamMembersDTO.getFactoryCode()));
        return baseMapper.selectProcessDetail(processTeamMembersDTO);
    }

    @Override
    public List<TblCusProcessBarCodeHistoryVO> recursionTblCusProcessBarCodeHistoryListByChildOnProcessBarCode(ProcessBarCodeDTO processBarCodeDTO) {
        List<TblCusProcessBarCodeHistoryPO> tblCusProcessBarCodeHistoryPOS = baseMapper.recursionTblCusProcessBarCodeHistoryListByChildOnProcessBarCode(processBarCodeDTO.getOnProcessBarCode());
        return TblCusProcessBarCodeHistoryConvertor.INSTANCE.POListToVOList(tblCusProcessBarCodeHistoryPOS);
    }

    /**
     * 获取各栈板加工信息数据
     * @param processBarCodeDTO
     * @return
     */
    @Override
    public List<ProductionProcessesVO> getProductionProcessesVOs(ProcessBarCodeDTO processBarCodeDTO) {
        //切换数据源
        if (BeanUtil.isEmpty(processBarCodeDTO)){
            return null;
        }
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(processBarCodeDTO.getFactoryCode()));

        //获取所有栈板信息
        List<TblCusProcessBarCodeHistoryPO> oldTblCusProcessBarCodeHistoryPOS = baseMapper.recursionTblCusProcessBarCodeHistoryListByChildOnProcessBarCode(processBarCodeDTO.getOnProcessBarCode());
        if (CollUtil.isEmpty(oldTblCusProcessBarCodeHistoryPOS)){
            return null;
        }
        Map<String, List<TblCusProcessBarCodeHistoryPO>> oldTblCusProcessBarCodeHistoryPOSMap = oldTblCusProcessBarCodeHistoryPOS.stream().collect(Collectors.groupingBy(TblCusProcessBarCodeHistoryPO::getOnProcessBarCode));
        //根据栈板码封装栈板信息
        List<TblCusProcessBarCodeHistoryPO> newTblCusProcessBarCodeHistoryPOS = new ArrayList<>();
        for (Map.Entry<String, List<TblCusProcessBarCodeHistoryPO>> stringListEntry : oldTblCusProcessBarCodeHistoryPOSMap.entrySet()) {
            newTblCusProcessBarCodeHistoryPOS.add(CollUtil.getFirst(stringListEntry.getValue()));
        }

        //提取栈板详细填充数据
        List<ProductionMachineVO> productionMachineVOS = baseMapper.selectProductionMachineVOs(newTblCusProcessBarCodeHistoryPOS);
        Map<String, List<ProductionMachineVO>> productionMachineVOSMap = productionMachineVOS.stream().collect(Collectors.groupingBy(ProductionMachineVO::getOnProcessBarCode));

        //提取栈板人员信息
        List<ProcessTeamMembersVO> processTeamMembersVOS = baseMapper.selectProcessTeamMembersVOs(newTblCusProcessBarCodeHistoryPOS);
        Map<String, List<ProcessTeamMembersVO>> processTeamMembersVOSMap = processTeamMembersVOS.stream().collect(Collectors.groupingBy(ProcessTeamMembersVO::getOnProcessBarCode));

        //提取工序使用原材料数据
        LambdaQueryWrapper<TblCusProcessMaterRecordPO> tblCusProcessMaterRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tblCusProcessMaterRecordPOLambdaQueryWrapper.in(TblCusProcessMaterRecordPO::getProcessBarCode,newTblCusProcessBarCodeHistoryPOS.stream().map(TblCusProcessBarCodeHistoryPO::getOnProcessBarCode).collect(Collectors.toList()));
        List<TblCusProcessMaterRecordPO> tblCusProcessMaterRecordPOS = iTblCusProcessMaterRecordService.list(tblCusProcessMaterRecordPOLambdaQueryWrapper);
        List<TblCusProcessMaterRecordVO> tblCusProcessMaterRecordVOS = TblCusProcessMaterRecordConvertor.INSTANCE.POListToVOList(tblCusProcessMaterRecordPOS);
        Map<String, List<TblCusProcessMaterRecordVO>> tblCusProcessMaterRecordVOsmap = tblCusProcessMaterRecordVOS.stream().collect(Collectors.groupingBy(TblCusProcessMaterRecordVO::getProcessBarCode));


        //封装工序数据返回
        List<ProductionProcessesVO> productionProcessesVOS = new ArrayList<>();
        for (TblCusProcessBarCodeHistoryPO newTblCusProcessBarCodeHistoryPO : newTblCusProcessBarCodeHistoryPOS) {
            String onProcessBarCode = newTblCusProcessBarCodeHistoryPO.getOnProcessBarCode();
            //封装工序基础信息
            ProductionProcessesVO productionProcessesVO = new ProductionProcessesVO();
            productionProcessesVO.setDeviceNo(newTblCusProcessBarCodeHistoryPO.getEquipmentNo());
            productionProcessesVO.setDeviceName(newTblCusProcessBarCodeHistoryPO.getEquipmentName());
            productionProcessesVO.setMono(newTblCusProcessBarCodeHistoryPO.getMono());
            productionProcessesVO.setProcessingDate(newTblCusProcessBarCodeHistoryPO.getCreateDate());

            //封装栈板详细填充数据
            List<ProductionMachineVO> productionMachineVOList = productionMachineVOSMap.get(onProcessBarCode);
            if (CollUtil.isNotEmpty(productionMachineVOList)){
                ProductionMachineVO productionMachineVO = CollUtil.getFirst(productionMachineVOList);
                productionProcessesVO.setProcessNo(productionMachineVO.getProcessNo());
                productionProcessesVO.setProcessName(productionMachineVO.getProcessNo());
                productionProcessesVO.setRwoMesNos(StrUtil.join(",",productionMachineVOList.stream().map(ProductionMachineVO::getRwoMesNo).collect(Collectors.toList())));
                productionProcessesVO.setSumInputQty(productionMachineVOList.stream().map(ProductionMachineVO::getInputQty).reduce(BigDecimal.ZERO,BigDecimal::add));
                productionProcessesVO.setSumErrorQty(productionMachineVOList.stream().filter(s-> ObjectUtil.isNotEmpty(s.getErrorQty())).map(ProductionMachineVO::getErrorQty).reduce(BigDecimal.ZERO,BigDecimal::add));
            }

            //封装栈板人员信息
            List<ProcessTeamMembersVO> processTeamMembersVOList = processTeamMembersVOSMap.get(onProcessBarCode);
            if (CollUtil.isNotEmpty(processTeamMembersVOList)){
                productionProcessesVO.setProcessTeamMembersVOS(processTeamMembersVOList);
            }

            //封装工序使用原材料数据
            List<TblCusProcessMaterRecordVO> tblCusProcessMaterRecordVOList = tblCusProcessMaterRecordVOsmap.get(onProcessBarCode);
            if (CollUtil.isNotEmpty(tblCusProcessMaterRecordVOList)){
                productionProcessesVO.setItemNos(tblCusProcessMaterRecordVOList.stream().map(TblCusProcessMaterRecordVO::getItemNo).collect(Collectors.toList()));
                productionProcessesVO.setLotNos(tblCusProcessMaterRecordVOList.stream().map(TblCusProcessMaterRecordVO::getItemLotNo).collect(Collectors.toList()));
            }
            productionProcessesVOS.add(productionProcessesVO);
        }
        return productionProcessesVOS.stream().
                        sorted(Comparator.comparing(ProductionProcessesVO::getProcessingDate)).
                        collect(Collectors.toList());
    }
}
