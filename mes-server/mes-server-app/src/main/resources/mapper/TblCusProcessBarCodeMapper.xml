<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.app.mapper.TblCusProcessBarCodeMapper">

    <select id="getProductionPalletDetailList"
            resultType="cn.jihong.mes.api.model.vo.ProductionPalletDetailVO">
        SELECT
            tpc.PROCESSBARCODE as processBarCode,
            tpc.PRODUCTNO as productNo,
            tpc.PRODUCTNAME as productName,
            tpc.QTY as qty,
            tpc.UNIT_NO as unitNo,
            tpc.MONO as mono,
            tpc.BASELOTNO as baseLotNo,
            tpc.EQUIPMENTNO as equipmentNo,
            tpc.EQUIPMENTNAME as equipmentName,
            tpc.NODEID as nodeId,
            tpc.OPNO as opNo,
            tps.OPNAME as opName,
            tpc.CREATEDATETIME as productDate,
            tpc.USERNO as userNo,
            tpc.USERNAME as username,
            trr.TITLENO as titleNo,
            trb.TITLENAME as titleName
        FROM
            TBLCUS_Processbarcode tpc
                LEFT JOIN TBLOPBASIS tps on tpc.OPNO = tps.OPNO
                LEFT JOIN TBLUSRUSERBASIS trr on tpc.USERNO = trr.USERNO
                LEFT JOIN TBLUSRTITLEBASIS trb on trr.TITLENO = trb.TITLENO
        WHERE
                tpc.PRODUCTNO = #{monoDTO.itemNo}
            AND tpc.MONO = #{monoDTO.mono}
    </select>
    <select id="getProcessQualityInspections"
            resultType="cn.jihong.mes.api.model.vo.ProcessQualityInspectionVO">
        SELECT
            A.QCFORMNO AS qcFromNo, -- 检验单号
            D.MONO AS mono, -- 工单号
            A.LotNo as baseLotNo, -- 生产工单号
            A.OPNo as opNo, -- 工序编号
            E.OPNAME as opName, -- 工序名称
            (CASE
                 WHEN A.QCRESULT = 'Y' THEN
                     N'Y:合格'
                 WHEN A.QCRESULT = 'N' THEN
                     N'N:不合格'
                END) as qcResult, -- 检验结果
            (CASE
                 WHEN A.QCItemResult = 'Y' THEN
                     N'Y:合格'
                 WHEN A.QCItemResult = 'N' THEN
                     N'N:不合格' ELSE N'未判定'
                END)as qcItemResult, -- 检验详情结果
            A.CheckQty AS checkQty, -- 检验数量
            A.DefectQty AS defectQty, -- 不良数量
            A.CheckTime AS checkTime, -- 检验时间
            A.UserNo AS checkUserNo,-- 检验人员编号
            C.USERNAME AS checkUserName,-- 检验人员
            (CASE
                 WHEN A.QCType = '2' THEN
                     N'2:首检'
                 WHEN A.QCType = '3' THEN
                     N'3:巡检'
                 WHEN A.QCType = '4' THEN
                     N'4:末检'
                 WHEN A.QCType = '5' THEN
                     N'5:自检'
                 WHEN A.QCType = '6' THEN
                     N'6:复检'
                 WHEN A.QCType = '7' THEN
                     N'7:重新自检'
                END) as  checkQcType, -- 检验类别
            A.AreaNo  as checkArea -- 检验区域
        FROM
            tblWIPFirstCheck A
                LEFT JOIN TBLWIPLOTBASIS D ON A.LOTNO = D.BASELOTNO
                LEFT JOIN TBLOPBASIS E ON A.OPNo = E.OPNo
                LEFT JOIN TBLUSRUSERBASIS C ON C.USERNO = A.UserNo
        WHERE
            D.MONO = #{mono}
          AND D.PRODUCTNO = #{itemNo}
    </select>
    <select id="getProcessReportingDetails" resultType="cn.jihong.mes.api.model.vo.ProcessReportingDetailVO">
        SELECT
            tblre.RWOMESNO AS rwoMesNo,-- 报工单号
            tblre.MONO AS mono, -- 工单号
            tblre.LOTNO AS baseLotNo,-- 生产单号
            tblre.OPNO AS opNo,-- 工序编号
            tba.OPNAME AS opName,-- 工序名称
            tblre.EVENTTIME AS rwoMesDate, -- 报工时间
            tblre.inputQty AS inputQty, -- 总报工产量
            SUM(tbls.ERRORQTY) AS errorQTY --当前报工单报废数量
        FROM
            (SELECT
                 tblre.RWOMESNO,
                 tblre.MONO,
                 tblre.LOTNO,
                 tblre.OPNO,
                 tblre.EVENTTIME,
                 tblre.LOGGROUPSERIAL,
                 sum(tblre.INPUTQTY) AS inputQty
             FROM
                 TBLWIPCONT_RESOURCE AS tblre
             WHERE
                 tblre.MONO = #{mono}
             GROUP BY
                 tblre.RWOMESNO,
                 tblre.MONO,
                 tblre.LOTNO,
                 tblre.OPNO,
                 tblre.EVENTTIME,
                 tblre.LOGGROUPSERIAL) tblre
                LEFT JOIN TBLINVWIP_SCRAPLOG tbls ON tbls.LOTNO = tblre.LOTNO
                AND tblre.LOGGROUPSERIAL = tbls.LOGGROUPSERIAL AND tbls.OPNO = tblre.OPNO
                AND tbls.TBLWIPCONTERROR_EVENTTIME = tblre.EVENTTIME
                LEFT JOIN TBLOPBASIS tba on tblre.OPNO = tba.OPNO
        GROUP BY
            tblre.RWOMESNO,
            tblre.MONO,
            tblre.LOTNO,
            tblre.OPNO,
            tblre.EVENTTIME,
            tblre.LOGGROUPSERIAL,
            tblre.inputQty,
            tba.OPNAME
    </select>
    <select id="getOpDetail" resultType="cn.jihong.mes.api.model.vo.OpDetailVO">
        SELECT
            tpc.OPNO as opNo,
            tps.OPNAME as opName
        FROM
            TBLCUS_Processbarcode tpc
                LEFT JOIN TBLOPBASIS tps on tpc.OPNO = tps.OPNO
        WHERE
            tpc.PRODUCTNO = #{monoDTO.itemNo}
          AND tpc.MONO = #{monoDTO.mono}
        GROUP BY tpc.OPNO,tps.OPNAME
    </select>
</mapper>
