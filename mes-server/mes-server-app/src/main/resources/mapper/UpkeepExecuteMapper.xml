<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.app.mapper.UpkeepExecuteMapper">

    <select id="getUndistributedList" resultType="cn.jihong.mes.api.model.po.UpkeepExecutePO">
        SELECT * FROM upkeep_execute WHERE distribute = 0 and production_plan_start_time >= CURDATE() - INTERVAL 1 WEEK;
    </select>


    <select id="getOneByNameAndType" resultType="cn.jihong.mes.api.model.dto.UpkeepExecuteDTO">
        SELECT * FROM upkeep_execute WHERE production_machine = #{machineName} and upkeep_type = #{upkeepType} production_plan_start_time >= CURDATE() - INTERVAL 3 DAY;
    </select>

</mapper>
