<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.app.mapper.ProductionMachineConfigMapper">

    <select id="getMachineParts" resultType="cn.jihong.mes.api.model.po.ProductionMachineConfigPO">
        select pmc.* from
            production_machine pm
        inner join production_machine_config pmc on pm.id = pmc.machine_id
        where pm.company_code = #{machineConfigInVO.companyCode}
          and pm.erp_machine_name = #{machineConfigInVO.machineName}
          and pmc.deleted = 0
        limit 1
    </select>

</mapper>
