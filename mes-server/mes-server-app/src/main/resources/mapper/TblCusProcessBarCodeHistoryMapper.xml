<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.app.mapper.TblCusProcessBarCodeHistoryMapper">

    <select id="selectProcessTeamMembers" resultType="cn.jihong.mes.api.model.vo.ProcessTeamMembersVO">
        SELECT
            tblur.USERNO AS userNo,
            tblur.USERNAME AS userName,
            tblur.TITLENO AS titleNo,
            tblul.TITLENAME AS titleName
        FROM
            TBLUSRUSERBASIS AS tblur
                LEFT JOIN TBLUSRTITLEBASIS AS tblul ON tblul.TITLENO = tblur.TITLENO
        WHERE
                tblur.USERNO IN (
                SELECT
                    tblre.USERNO AS userNo
                FROM
                    TBLWIPCONT_RESOURCE AS tblre
                        LEFT JOIN TBLCUS_Processbarcode AS tblp ON tblp.OPNO = tblre.OPNO
                        LEFT JOIN TBLCUS_Processbarcodehistory AS tblph ON tblph.MONO = tblre.MONO
                        AND tblph.EQUIPMENTNO = tblre.RESITEM
                        AND tblph.ONPROCESSBARCODE = tblp.PROCESSBARCODE
                WHERE
                    tblph.ONPROCESSBARCODE = #{processTeamMembersDTO.onProcessBarCode}
                AND (tblre.EVENTTIME between #{processTeamMembersDTO.startDate} and #{processTeamMembersDTO.endDate})
            )
    </select>

    <select id="selectProcessDetail" resultType="cn.jihong.mes.api.model.vo.ProcessDetailVO">
        SELECT
            tblph.ONPROCESSBARCODE as onProcessBarCode,
            tblph.UPPROCESSBARCODE as upProcessBarCode,
            tblpb.OPNO as processNo,
            tblbs.OPNAME as processName,
            tblph.EQUIPMENTNO as deviceNo,
            tblph.EQUIPMENTNAME AS deviceName,
            tblph.MONO as mono,
            tblph.CREATEDATE as processingDate
        FROM TBLCUS_Processbarcodehistory as tblph
                 left join TBLCUS_Processbarcode as tblpb on tblph.ONPROCESSBARCODE = tblpb.PROCESSBARCODE
                 left join TBLOPBASIS as tblbs on tblbs.OPNO = tblpb.OPNO
        WHERE
            tblph.ONPROCESSBARCODE = #{processTeamMembersDTO.onProcessBarCode}
    </select>
    <select id="recursionTblCusProcessBarCodeHistoryListByChildOnProcessBarCode"
            resultType="cn.jihong.mes.api.model.po.TblCusProcessBarCodeHistoryPO">
        WITH child AS
                 (SELECT * FROM TBLCUS_Processbarcodehistory WHERE ONPROCESSBARCODE = #{onProcessBarCode}
                  UNION ALL
                  SELECT parent.* FROM TBLCUS_Processbarcodehistory parent
                      INNER JOIN child ON parent.ONPROCESSBARCODE = child.UPPROCESSBARCODE
                                              AND child.UPPROCESSBARCODE != '' AND child.UPPROCESSBARCODE IS NOT NULL)
        SELECT * FROM child ORDER BY CREATEDATE ASC
    </select>
    <select id="selectProductionMachineVOs" resultType="cn.jihong.mes.api.model.vo.ProductionMachineVO">
        SELECT
            tblph.ONPROCESSBARCODE as onProcessBarCode, --本级栈板码
            tblre.RWOMESNO as rwoMesNo, --报工单号
            tblre.INPUTQTY as inputQty, 	--产量
            tblp.OPNO as processNo, --工序编号
            tblbs.OPNAME as processName, --工序名称
            SUM(tbls.ERRORQTY) as errorQty --报废数量
        FROM
            TBLWIPCONT_RESOURCE AS tblre
                LEFT JOIN TBLCUS_Processbarcode AS tblp ON tblp.OPNO = tblre.OPNO
                LEFT JOIN TBLCUS_Processbarcodehistory AS tblph ON tblph.MONO = tblre.MONO  AND tblph .BASELOTNO = tblre.LOTNO
                AND tblph.EQUIPMENTNO = tblre.RESITEM
                AND tblph.ONPROCESSBARCODE = tblp.PROCESSBARCODE
                LEFT JOIN TBLOPBASIS as tblbs on tblbs.OPNO = tblre.OPNO
                LEFT JOIN TBLINVWIP_SCRAPLOG AS tbls ON tbls.LOTNO = tblre.LOTNO AND tbls.OPNO = tblre.OPNO AND tbls.TBLWIPCONTERROR_EVENTTIME = tblre.EVENTTIME
                LEFT JOIN TBLWIPSCRAPLOG as tblsl ON tbls.TBLWIPCONTERROR_EVENTTIME = tblsl.CREATEDATE and tblsl.OPERATIONTYPE IN (3,4)
        WHERE
        <foreach collection="tblCusProcessBarCodeHistoryPOS" item="tblCusProcessBarCodeHistoryPO"
                 index="index" open="(" close=")" separator="OR">
            tblph.ONPROCESSBARCODE = #{tblCusProcessBarCodeHistoryPO.onProcessBarCode}
            AND (tblre.EVENTTIME BETWEEN (DATEADD(ms,0,DATEADD(dd,DATEDIFF(dd,0,#{tblCusProcessBarCodeHistoryPO.createDate}),0))) AND (DATEADD(ms,-2,DATEADD(dd,DATEDIFF(dd,-1,#{tblCusProcessBarCodeHistoryPO.createDate}),0))))
        </foreach>
        GROUP BY tblph.ONPROCESSBARCODE,tblre.RWOMESNO,tblre.INPUTQTY,tblp.OPNO,tblbs.OPNAME
    </select>
    <select id="selectProcessTeamMembersVOs" resultType="cn.jihong.mes.api.model.vo.ProcessTeamMembersVO">
        SELECT
            tblur.USERNO AS userNo,  	--工人编号
            tblur.USERNAME AS userName, --工人名称
            tblur.TITLENO AS titleNo,		--工人职位编号
            tblul.TITLENAME AS titleName,	--工人职位名称
            tblph.ONPROCESSBARCODE as onProcessBarCode --本级栈板码
        FROM
            TBLUSRUSERBASIS AS tblur
                LEFT JOIN TBLUSRTITLEBASIS AS tblul ON tblul.TITLENO = tblur.TITLENO
                LEFT JOIN TBLWIPCONT_RESOURCE AS tblre ON tblur.USERNO = tblre.USERNO
                LEFT JOIN TBLCUS_Processbarcode AS tblp ON tblp.OPNO = tblre.OPNO
                LEFT JOIN TBLCUS_Processbarcodehistory AS tblph ON tblph.MONO = tblre.MONO  AND tblph .BASELOTNO = tblre.LOTNO
                AND tblph.EQUIPMENTNO = tblre.RESITEM
                AND tblph.ONPROCESSBARCODE = tblp.PROCESSBARCODE
        WHERE
        <foreach collection="tblCusProcessBarCodeHistoryPOS" item="tblCusProcessBarCodeHistoryPO"
                 index="index" open="(" close=")" separator="OR">
            tblph.ONPROCESSBARCODE = #{tblCusProcessBarCodeHistoryPO.onProcessBarCode}
            AND (tblre.EVENTTIME BETWEEN (DATEADD(MINUTE,-3,#{tblCusProcessBarCodeHistoryPO.createDate})) AND (DATEADD(MINUTE,3,#{tblCusProcessBarCodeHistoryPO.createDate})))
        </foreach>
        GROUP BY tblph.ONPROCESSBARCODE,tblur.USERNO,tblur.USERNAME,tblur.TITLENO,tblul.TITLENAME
    </select>
    <select id="selectProduceProcessesUseMaterialVOs"
            resultType="cn.jihong.mes.api.model.vo.ProduceProcessesUseMaterialVO">
        SELECT
            a.PROCESSBARCODE AS onProcessBarCode,  --栈板条码
            a.BARCODE AS barCode, --标签条码
            a.ITEM_NO AS itemNo, --物料编号
            a.ITEM_NAME AS itemName,--物料名称
            a.ITEM_LOT_NO AS lotNo,--物料批次号
            a.USE_QTY AS useQty,--物料用量
            a.CREATEDATE AS useDate,--使用时间
            a.USERNO AS userNo,--操作人编号
            a.USERNAME AS userName,--操作人名称
            a.BASELOTNO AS produceLotNo,--生产批次
            b.EQUIPMENTNO AS deviceNo,--设备编号
            b.EquipmentName AS deviceName, --设备名称
            b.OPNO AS opNo --工序编号
        FROM
            TBLCUS_processmaterrecord a
                INNER JOIN TBLCUS_Processbarcode b ON a.PROCESSBARCODE= b.PROCESSBARCODE
        WHERE
            b.MONO = #{mono}
    </select>
</mapper>
