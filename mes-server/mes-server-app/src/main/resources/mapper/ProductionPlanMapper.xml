<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.app.mapper.ProductionPlanMapper">
    <resultMap id="GetProductionPlanListOutVOResultMap" type="cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO">
        <id property="companyCode" column="company_code"/>
        <collection property="companyProductionPlans" ofType="cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO$CompanyProductionPlan">
            <result property="machine" column="production_machine"/>
            <result property="productionProcess" column="production_process"/>

            <collection property="machineProductionPlans" ofType="cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO$CompanyProductionPlan$MachineProductionPlan">
                <result property="product" column="production_name"/>
                <result property="workerOrderNo" column="worker_order_no"/>
                <result property="deptNo" column="dept_no"/>
                <result property="deptName" column="dept_name"/>
                <result property="unit" column="unit"/>
                <result property="standardProductionCapacity" column="standard_production_capacity"/>
                <result property="productionChangeHours" column="production_change_hours"/>

                <collection property="productProductionPlans" ofType="cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO$CompanyProductionPlan$MachineProductionPlan$ProductProductionPlan">
                    <id property="id" column="id"/>
                    <result property="productionPlanDate" column="production_plan_date"/>
                    <result property="weekDay" column="week_day"/>
                    <result property="plannedProductionCapacity" column="planned_production_capacity" />
                </collection>
            </collection>
        </collection>
    </resultMap>
    <select id="getListByDate" resultType="cn.jihong.mes.api.model.po.ProductionPlanPO">
        SELECT
        a.company_id AS companyId,
        a.company_code AS companyCode,
        a.production_machine AS productionMachine,
        a.production_name AS productionName,
        a.production_plan_date AS productionPlanDate,
        a.production_process AS productionProcess,
        a.standard_production_capacity AS standardProductionCapacity,
        a.unit AS unit,
        a.worker_order_no AS workerOrderNo,
        a.serial_no AS serialNo,
        min(a.production_plan_start_time) AS productionPlanStartTime,
        MAX(a.production_plan_end_time) AS productionPlanEndTime,
        SUM( a.planned_production_capacity ) AS plannedProductionCapacity
        FROM
        production_plan a
        WHERE
        a.is_deleted = 0
        AND a.production_plan_date <![CDATA[ >= ]]> #{startTime}
        AND a.production_plan_date <![CDATA[ <= ]]> #{endTime}
        AND a.company_code = #{companyCode}
        GROUP BY
        a.company_id,
        a.company_code,
        a.production_machine,
        a.production_name,
        a.production_plan_date,
        a.production_process,
        a.standard_production_capacity,
        a.unit,
        a.worker_order_no,
        a.serial_no
    </select>


    <select id="getListByNameAndDateAndSerialNo" resultType="cn.jihong.mes.api.model.po.ProductionPlanPO">
        SELECT *
        FROM production_plan
        WHERE is_deleted = 0
          AND production_plan_date = #{productionPlanDate}
          AND serial_no = #{serialNo}
          AND production_machine = #{machineName}
          AND plan_type = 2
          AND company_code = #{companyCode}
    </select>

    <select id="getProductionPlanList" resultMap="GetProductionPlanListOutVOResultMap">
        SELECT
        company_code,
        dept_no,
        dept_name,
        production_machine,
        production_process,
        production_name,
        worker_order_no,
        unit,
        standard_production_capacity,
        production_change_hours,
        production_plan_date,
        week_day,
        MIN(id) id,
        GROUP_CONCAT( ROUND(planned_production_capacity) order by serial_no SEPARATOR ',' ) AS planned_production_capacity
        FROM
        production_plan pp
        WHERE
        pp.is_deleted = 0
        AND pp.production_plan_date <![CDATA[ >= ]]> #{startTime}
        AND pp.production_plan_date <![CDATA[  < ]]> #{endTime}
        <if test="machine != '' and machine != null">
            AND pp.production_machine LIKE CONCAT('%',#{machine},'%')
        </if>
        <if test="process != '' and process != null">
            AND pp.production_process LIKE CONCAT('%',#{process},'%')
        </if>
        <if test="deptName != '' and deptName != null">
            AND pp.dept_name LIKE CONCAT('%',#{deptName},'%')
        </if>

        AND pp.company_code = #{companyCode}
        GROUP BY
        company_code,
        dept_no,
        dept_name,
        production_machine,
        production_process,
        production_name,
        worker_order_no,
        unit,
        standard_production_capacity,
        production_change_hours,
        production_plan_date,
        week_day
    </select>
</mapper>
