<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--日志路径 -->
    <property name="LOG_PATH" value="/data/logs/mes-server"/>
    <property name="LOG_FILE" value="mes-server"/>
    <!-- 格式化-->
    <springProperty scope="context" name="serviceName" source="spring.application.name" />
    <!-- 定义从 SLS 中读取配置 -->
    <springProperty scope="context" name="slsEndpoint" source="sls.logback.endpoint"/>
    <springProperty scope="context" name="slsAccessKeyId" source="sls.logback.accessKeyId"/>
    <springProperty scope="context" name="slsAccessKeySecret" source="sls.logback.accessKeySecret"/>
    <springProperty scope="context" name="slsProject" source="sls.logback.project"/>
    <springProperty scope="context" name="slsLogStore" source="sls.logback.logStore"/>

    <property name="CONSOLE_LOG_PATTERN"
              value="|serviceName=${serviceName}|%level|%d{yyyy-MM-dd HH:mm:ss.SSS}|%thread|%X{tid}|traceId=%X{TraceId}|%msg|%X{callChain}%n"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <!-- 全部日志的配置-->
    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}.log</file>
        <append>true</append>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天生成日志文件 -->
            <fileNamePattern>
                ${LOG_PATH}/archived/${LOG_FILE}.%d{dd-MM-yyyy}.log
            </fileNamePattern>
            <!--保留天数-->
            <maxHistory>30</maxHistory>
            <!--单个文件的大小-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <!-- error日志的配置-->
    <appender name="fileError" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${LOG_PATH}/${LOG_FILE}.error.log</file>
        <append>true</append>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH}/archived/${LOG_FILE}.%d{dd-MM-yyyy}.error.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>



    <appender name="SLS" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>

        <endpoint>${slsEndpoint}</endpoint>
        <accessKeyId>${slsAccessKeyId}</accessKeyId>
        <accessKeySecret>${slsAccessKeySecret}</accessKeySecret>
        <project>${slsProject}</project>
        <logStore>${slsLogStore}</logStore>

        <totalSizeInBytes>104857600</totalSizeInBytes>
        <maxBlockMs>0</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>10</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>

        <timeFormat>yyyy-MM-dd'T'HH:mmZ</timeFormat>
        <timeZone>UTC</timeZone>
    </appender>


    <root level="info">
        <!--文件输出-->
        <appender-ref ref="fileAppender"/>
        <appender-ref ref="fileError"/>
        <!--制台输出-->
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="SLS"/>
    </root>

</configuration>
