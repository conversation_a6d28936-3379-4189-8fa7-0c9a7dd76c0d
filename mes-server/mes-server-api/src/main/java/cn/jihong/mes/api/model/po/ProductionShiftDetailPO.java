package cn.jihong.mes.api.model.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 班次详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Getter
@Setter
@TableName("production_shift_detail")
public class ProductionShiftDetailPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MAIN_ID = "main_id";
    public static final String SHIFT_DETAIL_NAME = "shift_detail_name";
    public static final String TIME_SLOT = "time_slot";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String IS_DELETED = "is_deleted";
    public static final String SERIAL_NO = "serial_no";



    /**
     * 主键id
     */
    @TableId(value = ID,type = IdType.AUTO)
    private Integer id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 主表id
     */
    @TableField(MAIN_ID)
    private Integer mainId;

    /**
     * 班次详情名称
     */
    @TableField(SHIFT_DETAIL_NAME)
    private String shiftDetailName;


    /**
     * 时间段
     */
    @TableField(TIME_SLOT)
    private String timeSlot;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(IS_DELETED)
    private Boolean isDeleted;

    /**
     * 顺序号
     */
    @TableField(SERIAL_NO)
    private Integer serialNo;

}
