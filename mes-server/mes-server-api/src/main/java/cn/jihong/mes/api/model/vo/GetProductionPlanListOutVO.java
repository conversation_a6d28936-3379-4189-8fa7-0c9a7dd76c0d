package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 得到生产计划列表
 *
 * <AUTHOR>
 * @date 2023/07/10
 */
@Data
public class GetProductionPlanListOutVO implements Serializable {

    /**
     * 公司id
     */
//    private Integer companyId;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 公司生产计划
     */
    private List<CompanyProductionPlan> companyProductionPlans;

    /**
     * 公司生产计划
     *
     * <AUTHOR>
     * @date 2023/07/10
     */
    @Data
    public static class CompanyProductionPlan {

        /**
         * 机台
         */
        private String machine;

        /**
         * 工序
         */
        private String productionProcess;

        /**
         * 机台生产计划
         */
        private List<MachineProductionPlan> machineProductionPlans;

        /**
         * 机台生产计划
         *
         * <AUTHOR>
         * @date 2023/07/10
         */
        @Data
        public static class MachineProductionPlan {

            /**
             * 产品
             */
            private String product;

            /**
             * 生产部门编号
             */
            private String deptNo;

            /**
             * 生产部门名称
             */
            private String deptName;

            /**
             * 工单号
             */
            private String workerOrderNo;

            /**
             * 单位
             */
            private String unit;

            /**
             * 标准产能/H
             */
            private Double standardProductionCapacity;

            /**
             * 换产时长
             */
            private Double productionChangeHours;

            /**
             * 产品生产计划
             */
            private List<ProductProductionPlan> productProductionPlans;

            /**
             * 产品生产计划
             *
             * <AUTHOR>
             * @date 2023/07/10
             */
            @Data
            public static class ProductProductionPlan {
                private Long id;
                
                /**
                 * 生产计划日期
                 */
                private String productionPlanDate;

                /**
                 * 星期几
                 */
                private String weekDay;

                /**
                 * 计划产量
                 */
                private String plannedProductionCapacity;
            }

        }


    }


}
