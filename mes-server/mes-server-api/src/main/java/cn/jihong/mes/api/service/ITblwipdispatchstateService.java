package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.TblwipdispatchstateDTO;
import cn.jihong.mes.api.model.po.TblwipdispatchstatePO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface ITblwipdispatchstateService extends IJiHongService<TblwipdispatchstatePO> {
    void saveBatch(List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList);
    List<TblwipdispatchstateDTO> getListByLotNos(List<String> lotNos);
    void updateBatch(List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList);
}
