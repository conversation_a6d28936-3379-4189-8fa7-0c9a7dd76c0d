package cn.jihong.mes.api.service;


import cn.jihong.mes.api.model.dto.TblopbasisDTO;
import cn.jihong.mes.api.model.po.TblopbasisPO;
import cn.jihong.mes.api.model.vo.TblopbasisVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface ITblopbasisService extends IJiHongService<TblopbasisPO> {

    List<TblopbasisVO> getTblopbasisList(TblopbasisDTO tblopbasisDTO);

    TblopbasisVO getVOByName(String name);
}
