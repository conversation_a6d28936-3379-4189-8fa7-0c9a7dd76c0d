package cn.jihong.mes.api.model.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *  集团损耗统计日报-
 */
public class GroupLossStatisticsDayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;


    /**
     * 报表日期
     */
    private Date reportDate;


    /**
     * 公司编号
     */
    private String companyCode;


    /**
     * 公司名称
     */
    private String companyName;


    /**
     * 产品编号
     */
    private String productNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 工单号
     */
    private String workOrderNo;


    /**
     * 分类
     */
    private String category;


    /**
     * 良品转入数量
     */
    private Integer goodProductQuantity;


    /**
     * 首工序报工数量
     */
    private Integer initReportingNum;


    /**
     * 首工序不良数量
     */
    private Integer initDefectiveProductNum;


    /**
     * 已入库合格数量
     */
    private Integer qualifiedQuantity;


    /**
     * 退料数量
     */
    private Integer returnQuantity;


    /**
     * 虚拟报工数量
     */
    private Integer virtualReporteQuantity;


    /**
     * 客户下线数量
     */
    private Integer customerOfflineQuantity;


    /**
     * 实际损耗量
     */
    private Double defectiveProductNum;


    /**
     * 实际损耗率
     */
    private Double actualLossRate;


    /**
     * 标准损耗率
     */
    private Double standardLossRate;


    /**
     * 额定损耗率
     */
    private Double ratedLossRate;


    /**
     * 理论良品数量
     */
    private Integer workOrderQuantity;


    /**
     * 欠数数量
     */
    private Integer underCountQuantity;


    /**
     * 额定损耗量
     */
    private Integer ratedLossQuantity;


    /**
     * 首工序额定损耗量
     */
    private Integer initRatedLossQuantity;


    /**
     * 标准损耗量
     */
    private Integer standardLossQuantity;


    /**
     * 首工序标准损耗量
     */
    private Integer initStandardLossQuantity;


    /**
     * 客户编号
     */
    private String customerNo;


    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 工序编号
     */
    private String processNo;


    /**
     * 工序名称
     */
    private String processName;


    /**
     * 生产损耗类型
     */
    private String lossType;


    /**
     * 上一道工序
     */
    private String lastProcess;


    /**
     * 下一道工序
     */
    private String nextProcess;


    /**
     * 拼数
     */
    private Integer piece;


    /**
     * 理论投料量
     */
    private Integer billingQuantity;


    /**
     * 首工序理论产出
     */
    private Integer initTheoryOutPut;


    /**
     * 开单损耗量
     */
    private Integer billingLossQuantity;


    /**
     * 首工序开单损耗量
     */
    private Integer initBillingLossQuantity;


    /**
     * 开单损耗率
     */
    private Double billingLossRate;

}
