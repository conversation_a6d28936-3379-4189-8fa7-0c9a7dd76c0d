package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class ProductionShiftSetVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 班次名称
     */
    private String productionShiftName;


    /**
     * 生效日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;


    /**
     * 失效日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;


    /**
     * 启动状态：1-启用，0-停用
     */
    private Boolean status;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    private Boolean isDeleted;

}
