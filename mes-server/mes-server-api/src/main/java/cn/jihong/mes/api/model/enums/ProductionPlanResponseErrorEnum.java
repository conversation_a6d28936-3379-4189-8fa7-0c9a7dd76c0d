package cn.jihong.mes.api.model.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public enum ProductionPlanResponseErrorEnum {

    NOT_EXIST_ERP_WORKORDERNO("1001","存在ERP没有的工单"),
    ;

    private String code;

    private String name;

    ProductionPlanResponseErrorEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductionPlanResponseErrorEnum getDataSourceEnum(String code){
        if (StringUtils.isBlank(code)){
            throw new RuntimeException("请输入code");
        }
        for (ProductionPlanResponseErrorEnum value : ProductionPlanResponseErrorEnum.values()) {
            if (StringUtils.equals(value.getCode(),code)){
                return value;
            }
        }
        throw new RuntimeException("输入的code不存在");
    }
}
