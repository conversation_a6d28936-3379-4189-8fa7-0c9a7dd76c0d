package cn.jihong.mes.api.service;


import cn.jihong.mes.api.model.dto.TblwipcontResourceDTO;
import cn.jihong.mes.api.model.po.TblwipcontResourcePO;
import cn.jihong.mes.api.model.vo.TblwipcontResourceVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface ITblwipcontResourceService extends IJiHongService<TblwipcontResourcePO> {

    List<TblwipcontResourceVO> getTblwipcontResourceList(TblwipcontResourceDTO tblwipcontResourceDTO);

    List<TblwipcontResourceVO> getListByEventTime(TblwipcontResourceDTO tblwipcontResourceDTO);
}
