package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
public class TbleqpequipmentbasisVO implements Serializable {


    private String equipmentno;

    private String equipmenttype;

    private Double capacity;

    private String vendorno;

    private String modelno;

    private String description;

    private String creator;

    private Date createdate;

    private Double issuestate;

    private String engineergroupno;

    private String assetno;

    private String equipmentclass;

    private Double loadport;

    private Double autoflag;

    private String eacontroller;

    private Double eqprecipe;

    private String qclistno;

    private Double maxTime;

    private Double fixEqpTime;

    private Double varEqpTime;

    private Double countEqpUnitQty;

    private Double counter;

    private String erpno;

    private String equipmentName;

    private Double productioninf;

    private Double allowmultiwork;

    private Double setupignoremachine;

    private Double spcPqc;

    private String equipmentcheckup;

    private String equipmentcheckuprate;

    private Date equipmentcheckuptime;

    private Double counterPre;

    private Double outUserOption;

    private Double outLaberTimeOption;

    private Double outLaberExclusive;

    private Double outMachineExclusive;

    private Double outQtyDefinition;

    private Double outQtyOption;

    private Double outQtyAllowZero;

    private Date counterUpdateTime;

    private Date counterEQTime;

    private String spcPqc2;

    private Date recordTimeOutDate;

    private Double stdTimeOut;

    private Double stdTimeOutQty;

    private Double lotBinding;

    private String lineInventoryNo;

}
