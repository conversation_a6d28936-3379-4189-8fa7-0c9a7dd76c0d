package cn.jihong.mes.api.model.vo;


import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class PaperActualLossStatisticsMesGroupHalfDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 公司编号
     */
    @ExcelProperty("公司编号")
    private String factoryNo;

    /**
     * 公司名称
     */
    @ExcelProperty("公司名称")
    private String factoryName;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 产品编号
     */
    @ExcelProperty("产品编号")
    private String productNo;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 半成品产品编号
     */
    @ExcelProperty("半成品产品编号")
    private String halfProductNo;

    /**
     * 半成品产品名称
     */
    @ExcelProperty("半成品产品名称")
    private String halfProductName;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String workOrderNO;

    /**
     * 半成品工单号
     */
    @ExcelProperty("半成品工单号")
    private String halfWorkOrderNO;

    /**
     * 分类
     */
    @ExcelProperty("分类")
    private String category;

    /**
     * 拼数
     */
    @ExcelProperty("拼数")
    private Integer piece;

    /**
     * 首工序报工数量
     */
    @ExcelProperty("首工序报工数量")
    private BigDecimal initReportingNum;

    /**
     * 首工序不良数量
     */
    @ExcelProperty("首工序不良数量")
    private BigDecimal initDefectiveProductNum;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("已入库合格数量")
    private BigDecimal qualifiedQuantity;

    /**
     * 不良品总数
     */
    @ExcelProperty("实际损耗量")
    private BigDecimal defectiveProductNum;

    /**
     * 实际损耗率
     */
    @ExcelProperty("实际损耗率")
    private BigDecimal actualLossRate;

    /**
     * 额定损耗率
     */
    @ExcelProperty("额定损耗率")
    private BigDecimal ratedLossRate;

    /**
     * 标准损耗率
     */
    @ExcelProperty("标准损耗率")
    private BigDecimal standardLossRate;

    /**
     *工单数量
     */
    @ExcelProperty("理论良品数量")
    private BigDecimal workOrderQuantity;

    /**
     * 欠数数量
     */
    @ExcelProperty("欠数数量")
    private BigDecimal underCountQuantity;

    /**
     * 客户下线数量
     */
    @ExcelProperty("客户下线数量")
    private BigDecimal customerOfflineQuantity;

    @ExcelProperty("退料数量")
    private BigDecimal returnQuantity;

    @ExcelProperty("虚拟报工数量")
    private BigDecimal virtualReporteQuantity;

    /**
     * 额定损耗量
     */
    @ExcelProperty("额定损耗量")
    private BigDecimal ratedLossQuantity;

    /**
     * 标准损耗量
     */
    @ExcelProperty("标准损耗量")
    private BigDecimal standardLossQuantity;

    /**
     * 客户编号
     */
    @ExcelProperty("客户编号")
    private String customerNo;

    /**
     * 工序编号
     */
    @ExcelProperty("半成品首工序编号")
    private String processNo;

    /**
     * 工序名称
     */
    @ExcelProperty("半成品首工序名称")
    private String processName;

    /**
     * 生产损耗类型
     */
    @ExcelProperty("生产损耗类型")
    private String lossType;

//    /**
//     * 无半成品工单首工序编号
//     */
//    @ExcelProperty("无半成品工单首工序编号")
//    private String firstProcess;

//    /**
//     * 无半成品工单首工序名称
//     */
//    @ExcelProperty("无半成品工单首工序名称")
//    private String firstProcessName;


    /**
     * 上一道工序
     */
    @ExcelProperty("上一道工序")
    private String lastProcess;

    /**
     * 下一道工序
     */
    @ExcelProperty("下一道工序")
    private String nextProcess;

    /**
     * 首工序投料数量
     */
    @ExcelProperty("首工序投料数量")
    private BigDecimal inputQuantity;

    /**
     * 开单数量
     */
    @ExcelProperty("理论投料量")
    private BigDecimal billingQuantity;

    /**
     * 开单损耗量
     */
    @ExcelProperty("开单损耗量")
    private BigDecimal billingLossQuantity;

//    /**
//     * 首工序开单损耗量
//     */
//    @ExcelIgnore
//    private BigDecimal initBillingLossQuantity;

    /**
     * 首工序额定损耗量
     */
    @ExcelIgnore
    private BigDecimal initRatedLossQuantity;

    /**
     * 首工序标准损耗量
     */
    @ExcelIgnore
    private BigDecimal initStandardLossQuantity;


//    /**
//     * 研发退料数量
//     */
//    @ExcelProperty("研发退料数量")
//    private BigDecimal developmentReturns;

//    /**
//     * 超领数量
//     */
//    @ExcelProperty("超领数量")
//    private BigDecimal overUseQuantity;

//    /**
//     * 实际损耗量(末工序报工)
//     */
//    @ExcelProperty("实际损耗量(末工序报工)")
//    private BigDecimal endDefectiveProductNum;

//    /**
//     * 末道工序报工数量
//     */
//    @ExcelProperty("末道工序报工数量")
//    private BigDecimal endReportingNum;

//    /**
//     * 首工序理论产出
//     */
//    @ExcelProperty("首工序理论产出")
//    private BigDecimal initTheoryOutPut;

//    /**
//     * 开单损耗率
//     */
//    @ExcelProperty("开单损耗率")
//    private BigDecimal billingLossRate;

//    /**
//     * 实际损耗率(末工序报工)%
//     */
//    @ExcelProperty("实际损耗率(末工序报工)%")
//    private BigDecimal endActualLossRate;

    /**
     * 产出比
     */
    @ExcelIgnore
    private BigDecimal outputRatio;

}
