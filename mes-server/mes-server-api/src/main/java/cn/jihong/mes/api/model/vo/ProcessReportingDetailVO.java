package cn.jihong.mes.api.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工序报工详细信息
 */
@Data
public class ProcessReportingDetailVO implements Serializable {

    /**
     * 报工单号
     */
    @ExcelProperty("报工单号")
    private String rwoMesNo;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String mono;

    /**
     * 生产单号
     */
    @ExcelProperty("生产单号")
    private String baseLotNo;

    /**
     * 工序编号
     */
    @ExcelProperty("工序编号")
    private String opNo;

    /**
     * 工序名称
     */
    @ExcelProperty("工序名称")
    private String opName;

    /**
     * 报工时间
     */
    @ExcelProperty("报工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date rwoMesDate;

    /**
     * 总报工产量
     */
    @ExcelProperty("总报工产量")
    private BigDecimal inputQty;

    /**
     * 当前报工单报废数量
     */
    @ExcelProperty("当前报工单报废数量")
    private BigDecimal errorQTY;
}
