package cn.jihong.mes.api.model.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Getter
@Setter
@TableName("production_shift_set")
public class ProductionShiftSetPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PRODUCTION_SHIFT_NAME = "production_shift_name";
    public static final String EFFECTIVE_DATE = "effective_date";
    public static final String EXPIRE_DATE = "expire_date";
    public static final String STATUS = "status";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String IS_DELETED = "is_deleted";



    /**
     * 主键
     */
    @TableId(value = ID,type = IdType.AUTO)
    private Integer id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 班次名称
     */
    @TableField(PRODUCTION_SHIFT_NAME)
    private String productionShiftName;


    /**
     * 生效日期
     */
    @TableField(EFFECTIVE_DATE)
    private Date effectiveDate;


    /**
     * 失效日期
     */
    @TableField(EXPIRE_DATE)
    private Date expireDate;


    /**
     * 启动状态：1-启用，0-停用
     */
    @TableField(STATUS)
    private Boolean status;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(IS_DELETED)
    private Boolean isDeleted;


}
