package cn.jihong.mes.api.model.enums;

import org.apache.commons.lang3.StringUtils;

import cn.jihong.common.enums.Dict;


/**
 * 机台状态  0:成立1:使用中 2:暂停使用 3:待报废4:已报废5:已售出
 *
 */
public enum MachineStateEnum implements Dict {
	SET_UP("成立", "0"),
	IN_USE("使用中", "1"),
	STOP_USING("暂停使用", "2"),
	TO_BEEN_SCRAPPED("待报废", "3"),
	HAVE_BEEN_SCRAPPED("已报废", "4"),
	HAVE_BEEN_SOLD("已售出", "5"),
	;

	/** 名称  */
	private String name;

	/** 代码  */
	private String code;

	MachineStateEnum(String name, String code) {
		this.name = name;
		this.code = code;
	}

	@Override
	public String getName() {
		return name;
	}


	@Override
	public String getCode() {
		return code;
	}


	public static String getNameByCode (String code) {
		if (StringUtils.isBlank(code)) {
			return null;
		}
		for (MachineStateEnum machineStateEnum : values()) {
			if(machineStateEnum.getCode().equals(code)) {
				return machineStateEnum.getName();
			}
		}
		return null;
	}

	public static String getNameByCode (Integer code) {
		if (code == null) {
			return null;
		}
		String codeStr = code.toString();
		for (MachineStateEnum machineStateEnum : values()) {
			if(machineStateEnum.getCode().equals(codeStr)) {
				return machineStateEnum.getName();
			}
		}
		return null;
	}
}
