package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@TableName("TBLWIPDISPATCHSTATE")
public class TblwipdispatchstatePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String EQUIPMENTNO = "EQUIPMENTNO";
    public static final String LOTNO = "LOTNO";
    public static final String OPNO = "OPNO";
    public static final String QTY = "QTY";
    public static final String WORKDATE = "WORKDATE";
    public static final String SEQ = "SEQ";
    public static final String REVISER = "REVISER";
    public static final String REVISEDATE = "REVISEDATE";
    public static final String DISPENDTIME = "DispEndTime";
    public static final String QCLOTFLAG = "QcLotFlag";
    public static final String DISPQCLOT = "DispQCLot";
    public static final String DISPSTARTTIME = "DispStartTime";
    public static final String DISPAREANO = "DispAreaNo";
    public static final String SHIFTNO = "ShiftNO";
    public static final String STDDISPSTARTTIME = "StdDispStartTime";
    public static final String STDDISPENDTIME = "StdDispEndTime";
    public static final String COMBINEDTAG = "CombinedTag";
    public static final String CHILDPROCESSNO = "ChildProcessNo";
    public static final String CHILDPROCESSVERSION = "ChildProcessVersion";


    @TableField(EQUIPMENTNO)
    private String equipmentno;

    @TableField(LOTNO)
    private String lotno;

    @TableField(OPNO)
    private String opno;

    @TableField(QTY)
    private Double qty;

    @TableField(WORKDATE)
    private Date workdate;

    @TableField(SEQ)
    private Integer seq;

    @TableField(REVISER)
    private String reviser;

    @TableField(REVISEDATE)
    private Date revisedate;

    @TableField(DISPENDTIME)
    private Date dispEndTime;

    @TableField(QCLOTFLAG)
    private Integer qcLotFlag;

    @TableField(DISPQCLOT)
    private Integer dispQCLot;

    @TableField(DISPSTARTTIME)
    private Date dispStartTime;

    @TableField(DISPAREANO)
    private String dispAreaNo;

    @TableField(SHIFTNO)
    private String shiftNO;

    @TableField(STDDISPSTARTTIME)
    private Date stdDispStartTime;

    @TableField(STDDISPENDTIME)
    private Date stdDispEndTime;

    @TableField(COMBINEDTAG)
    private String combinedTag;

    @TableField(CHILDPROCESSNO)
    private String childProcessNo;

    @TableField(CHILDPROCESSVERSION)
    private String childProcessVersion;

}
