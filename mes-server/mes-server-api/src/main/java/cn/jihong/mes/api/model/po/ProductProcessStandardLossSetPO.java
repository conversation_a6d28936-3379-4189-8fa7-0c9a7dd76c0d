package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 产品工序标准损耗设定表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Getter
@Setter
@TableName("product_process_standard_loss_set")
public class ProductProcessStandardLossSetPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_CATEGORY_NO = "product_category_no";
    public static final String PRODUCT_CATEGORY_NAME = "product_category_name";
    public static final String PRODUCTION_PROCESSE_NO = "production_processe_no";
    public static final String PRODUCTION_PROCESSE_NAME = "production_processe_name";
    public static final String STANDARD_LOSS = "standard_loss";
    public static final String IS_DELETED = "is_deleted";
    public static final String CREATE_BY = "create_by";
    public static final String UPDATE_BY = "update_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";



    /**
     * 主键id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 产品类型编号
     */
    @TableField(PRODUCT_CATEGORY_NO)
    private String productCategoryNo;


    /**
     * 产品类型名称
     */
    @TableField(PRODUCT_CATEGORY_NAME)
    private String productCategoryName;


    /**
     * 产品工序编号
     */
    @TableField(PRODUCTION_PROCESSE_NO)
    private String productionProcesseNo;


    /**
     * 产品工序名称
     */
    @TableField(PRODUCTION_PROCESSE_NAME)
    private String productionProcesseName;


    /**
     * 标准损耗(单位：%)
     */
    @TableField(STANDARD_LOSS)
    private BigDecimal standardLoss;


    /**
     * 是否删除1-是，0-否
     */
    @TableField(IS_DELETED)
    private Boolean isDeleted;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
