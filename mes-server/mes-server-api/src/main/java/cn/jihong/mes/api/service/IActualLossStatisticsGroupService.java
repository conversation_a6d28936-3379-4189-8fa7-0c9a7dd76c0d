package cn.jihong.mes.api.service;

import java.util.List;

import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupDetailVO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupVO;

public interface IActualLossStatisticsGroupService {
    List<ActualLossStatisticsMesGroupVO> selectActualLosss(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    List<ActualLossStatisticsMesGroupDetailVO> selectActualLosssDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcel(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
}
