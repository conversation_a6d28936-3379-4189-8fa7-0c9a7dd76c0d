package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 机台配置表
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Getter
@Setter
@TableName("production_machine_config")
public class ProductionMachineConfigPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_ID = "machine_id";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String NEED_MACHINE_PARTS = "need_machine_parts";
    public static final String BILLING_TYPE = "billing_type";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 机台id
     */
    @TableField(MACHINE_ID)
    private String machineId;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;


    /**
     * 是否需要机位： 0 不需要  1 需要
     */
    @TableField(NEED_MACHINE_PARTS)
    private Integer needMachineParts;


    /**
     * 结算类型：1 一单一结  2  一日一结
     */
    @TableField(BILLING_TYPE)
    private Integer billingType;


}
