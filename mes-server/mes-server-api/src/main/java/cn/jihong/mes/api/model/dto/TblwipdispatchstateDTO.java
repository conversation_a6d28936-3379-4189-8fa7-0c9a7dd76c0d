package cn.jihong.mes.api.model.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
public class TblwipdispatchstateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String equipmentno;

    private String lotno;

    private String opno;

    private Double qty;

    private Date workdate;

    private Integer seq;

    private String reviser;

    private Date revisedate;

    private Date dispEndTime;

    private Integer qcLotFlag;

    private Integer dispQCLot;

    private Date dispStartTime;

    private String dispAreaNo;

    private String shiftNO;

    private Date stdDispStartTime;

    private Date stdDispEndTime;

    private String combinedTag;

    private String childProcessNo;

    private String childProcessVersion;

}
