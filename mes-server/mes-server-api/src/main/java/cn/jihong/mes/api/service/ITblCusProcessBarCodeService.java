package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.MonoDTO;
import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodePO;
import cn.jihong.mes.api.model.vo.ProductionProcessesDetailVO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITblCusProcessBarCodeService extends IJiHongService<TblCusProcessBarCodePO> {

    List<TblCusProcessBarCodeVO> getTblCusProcessBarCodeList(TblCusProcessBarCodeDTO tblCusProcessBarCodeDTO);

    ProductionProcessesDetailVO getProductionProcessesDetailList(MonoDTO monoDTO);
}
