package cn.jihong.mes.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
public class TblwipcontResourceDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String lotno;

    private String mono;

    private String baselotno;

    private String opno;

    private String loggroupserial;

    private Double resclass;

    private String restype;

    private String resitem;

    private Double resvalue;

    private Double stdvalue;

    private Double inputqty;

    private String userno;

    private Date eventtime;

    private String rwomesno;

    private String subrwomesno;

    private Double priceType;

    private Double unitPrice;

    private Double priceRate;

    private String cPost;

    private String cType;

    private String cShiftno;

    private String startTime;

    private String endTime;

}
