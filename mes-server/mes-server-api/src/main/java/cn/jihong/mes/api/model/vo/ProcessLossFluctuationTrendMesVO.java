package cn.jihong.mes.api.model.vo;


import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
public class ProcessLossFluctuationTrendMesVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 完工日期
     */
    private String finishDate;

    /**
     * 实际损耗率
     */
    private BigDecimal actualLossRate;


    /**
     * 不良品总数
     */
    private BigDecimal defectiveProductNum;

    /**
     * 已入库合格数量
     */
    private BigDecimal qualifiedQuantity;


}
