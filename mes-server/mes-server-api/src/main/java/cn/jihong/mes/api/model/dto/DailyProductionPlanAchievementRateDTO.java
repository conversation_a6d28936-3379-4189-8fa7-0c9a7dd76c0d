package cn.jihong.mes.api.model.dto;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 日生产计划达成率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class DailyProductionPlanAchievementRateDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 日期
     */
    private String productionPlanDate;


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


    /**
     * 产品名称
     */
    private String productionName;


    /**
     * 工序
     */
    private String productionProcess;

    /**
     * 生产机台编号
     */
    private String productionMachineNo;

    /**
     * 生产机台
     */
    private String productionMachine;

    /**
     * 工单号
     */
    private String workerOrderNo;

    /**
     * 单位
     */
    private String unit;

    /**
     * 标准计划产量
     */
    private Double plannedProductionCapacity;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 实际产量
     */
    private Double actualCapacity;

    /**
     * 产量达成率
     */
    private String achievementRate;

    /**
     * 当班总损耗
     */
    private Double totalLoss;

    /**
     * 当班合格率
     */
    private String passRate;

    /**
     * 工艺标准损耗
     */
    private Double processStandardLoss;

    /**
     * 损耗率
     */
    private String lossRate;

    /**
     * 总停机时长
     */
    private Double totalDownTimeDuration;

    /**
     * 计划外停机时长
     */
    private Double unplannedDowntimeDuration;

    /**
     * 备注
     */
    private String remark;

    /**
     * 不良品原因分析
     */
    private String rejectCausesAnalysis;


}
