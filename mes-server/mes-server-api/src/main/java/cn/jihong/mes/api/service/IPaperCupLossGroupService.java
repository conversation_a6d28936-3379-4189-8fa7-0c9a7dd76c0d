package cn.jihong.mes.api.service;

import java.util.List;

import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupNotCaseDTO;
import cn.jihong.mes.api.model.vo.PaperActualLossStatisticsMesGroupDetailVO;
import cn.jihong.mes.api.model.vo.PaperActualLossStatisticsMesGroupHalfDetailVO;
import cn.jihong.mes.api.model.vo.PaperActualLossStatisticsMesGroupVO;

public interface IPaperCupLossGroupService {
    List<PaperActualLossStatisticsMesGroupVO> selectActualLosss(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    List<PaperActualLossStatisticsMesGroupVO> selectActualHalfLosss(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    List<PaperActualLossStatisticsMesGroupDetailVO> selectActualLosssDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    List<PaperActualLossStatisticsMesGroupDetailVO> selectActualLosssNotCaseDetail(ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupNotCaseDTO);
    List<PaperActualLossStatisticsMesGroupHalfDetailVO> selectActualHalfLosssDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    List<PaperActualLossStatisticsMesGroupHalfDetailVO> selectActualHalfLosssNotCaseDetail(ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupDTO);
    String exportToExcel(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelHalf(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelNotCaseDetail(ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelDetailHalf(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO);
    String exportToExcelNotCaseDetailHalf(ActualLossStatisticsParamGroupNotCaseDTO actualLossStatisticsParamGroupDTO);
}
