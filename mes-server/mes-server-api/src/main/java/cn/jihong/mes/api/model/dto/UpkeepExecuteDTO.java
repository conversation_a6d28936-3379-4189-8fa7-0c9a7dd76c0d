package cn.jihong.mes.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备保养执行
 * <AUTHOR>
 * @date 2023/9/1 15:01
 */
@Data
public class UpkeepExecuteDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;


    /**
     * 工序
     */
    private String productionProcess;



    /**
     * 生产机台
     */
    private String productionMachine;

    /**
     * 生产计划开始时间
     */
    private Date productionPlanStartTime;

    /**
     *  保养用时
     */
    private Double upkeepTime;

    /**
     *  保养类型
     */
    private String upkeepType;


    /**
     * 创建来源 0：系统创建 1：人工创建
     */
    private Integer createSource;

}
