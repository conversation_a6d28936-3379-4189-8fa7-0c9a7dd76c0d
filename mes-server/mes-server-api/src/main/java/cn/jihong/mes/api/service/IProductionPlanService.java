package cn.jihong.mes.api.service;

import java.util.Date;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import cn.jihong.mes.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.api.model.vo.GetProductionPlanListInVO;
import cn.jihong.mes.api.model.vo.GetProductionPlanListOutVO;
import cn.jihong.mes.api.model.vo.ProductionPlanVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 生产计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface IProductionPlanService extends IJiHongService<ProductionPlanPO> {

//    List<GetProductionPlanListOutVO> getProductionPlanList(GetProductionPlanListInVO getProductionPlanListInVO);
    List<GetProductionPlanListOutVO> getProductionPlanListNew(GetProductionPlanListInVO getProductionPlanListInVO);
    void importExcel(MultipartFile file);
    List<ProductionPlanVO> getListByDate(ProductionPlanDTO productionPlanDTO);

    List<ProductionPlanVO> getListByProductionPlanDate(ProductionPlanDTO productionPlanDTO);

    String exportToExcel(ProductionPlanDTO productionPlanDTO);


    /**
     * 根据机台名称、生产日期、班次获取列表
     * @param machineName
     * @param productionPlanDate
     * @param serialNo
     * @return: java.util.List<cn.jihong.mes.api.model.po.ProductionPlanPO>
     * <AUTHOR>
     * @date: 2023/9/23 13:34
     */
    List<ProductionPlanPO> getListByNameAndDateAndSerialNo(String machineName,String productionPlanDate,Integer serialNo);

    /**
     * 根据工单和机台名称查询
     * @param machineName
     * @param ticketNo
     * @return
     */
    List<ProductionPlanPO> getListByNameAndTicketNo(String machineName,String ticketNo);

    List<ProductionPlanPO> getListByMachineTicket(String machineName, Date productionPlanDate, Integer serialNo,
        String ticketNo);

    ProductionPlanPO getOneByName(String machineName);

    /**
     * 批量保存
     * @param productionPlanPOS
     */
    void addBatch(List<ProductionPlanPO> productionPlanPOS);

    /**
     * 批量保存(厦门工厂)
     * @param productionPlanPOS
     */
    void addBatchXM(List<ProductionPlanPO> productionPlanPOS);

    List<ProductionPlanPO> getListByIds(List<Long> idList);
}
