package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.ProductionShiftSetDTO;
import cn.jihong.mes.api.model.po.ProductionShiftSetPO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface IProductionShiftSetService extends IJiHongService<ProductionShiftSetPO> {
    List<ProductionShiftSetVO> getList(ProductionShiftSetDTO productionShiftSetDTO);
    void save(ProductionShiftSetDTO productionShiftSetDTO);
    void update(ProductionShiftSetDTO productionShiftSetDTO);
    void delete(String id);
    void enableStatus(String id,boolean status);
    void disableStatus(String id);
    ProductionShiftSetVO getOneByDate(String currentDate);
}
