package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工序信息
 */
@Data
public class ProductionProcessesVO implements Serializable {

    /**
     * 工序编号 TBLOPBASIS->OPNO
     */
    private String processNo;

    /**
     * 工序名称 TBLOPBASIS->OPNAME
     */
    private String processName;

    /**
     * 设备编号 Processbarcodehistory->EQUIPMENTNO
     */
    private String deviceNo;

    /**
     * 设备名称 Processbarcodehistory->EQUIPMENTNAME
     */
    private String deviceName;

    /**
     * 工单号 Processbarcodehistory->MONO
     */
    private String mono;

    /**
     * 加工时间
     */
    private Date processingDate;

    /**
     * 报工单号
     */
    private String rwoMesNos;

    /**
     * 报废数量
     */
    private BigDecimal sumErrorQty;

    /**
     * 产量
     */
    private BigDecimal sumInputQty;

    /**
     * 班组信息
     */
    private List<ProcessTeamMembersVO> processTeamMembersVOS;

    /**
     * 原材料编号
     */
    private List<String> itemNos;

    /**
     * 原材料批号
     */
    private List<String> lotNos;

    /**
     * 工序使用原材料详细信息
     */
    private List<ProduceProcessesUseMaterialVO> produceProcessesUseMaterialVOS;
}
