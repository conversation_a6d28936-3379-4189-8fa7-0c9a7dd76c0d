package cn.jihong.mes.api.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 栈板信息
 */
@Data
public class ProductionPalletDetailVO implements Serializable {

    /**
     * 栈板码
     */
    @ExcelProperty("栈板码")
    private String processBarCode;

    /**
     * 产品编号
     */
    @ExcelProperty("产品编号")
    private String productNo;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 生产数量
     */
    @ExcelProperty("生产数量")
    private BigDecimal qty;

    /**
     * 生产单位
     */
    @ExcelProperty("生产单位")
    private String unitNo;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String mono;

    /**
     * 生产单号
     */
    @ExcelProperty("生产单号")
    private String baseLotNo;

    /**
     * 机器编号
     */
    @ExcelProperty("机器编号")
    private String equipmentNo;

    /**
     * 机器名称
     */
    @ExcelProperty("机器名称")
    private String equipmentName;

    /**
     * 节点ID
     */
    @ExcelProperty("节点ID")
    private String nodeId;

    /**
     * 工序编号
     */
    @ExcelProperty("工序编号")
    private String opNo;

    /**
     * 工序名称
     */
    @ExcelProperty("工序名称")
    private String opName;

    /**
     * 加工时间
     */
    @ExcelProperty("加工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productDate;

    /**
     * 操作人编号
     */
    @ExcelProperty("操作人编号")
    private String userNo;

    /**
     * 操作人名称
     */
    @ExcelProperty("操作人名称")
    private String username;

    /**
     * 操作人职位编号
     */
    @ExcelProperty("操作人职位编号")
    private String titleNo;

    /**
     * 操作人职位名称
     */
    @ExcelProperty("操作人职位名称")
    private String titleName;
}
