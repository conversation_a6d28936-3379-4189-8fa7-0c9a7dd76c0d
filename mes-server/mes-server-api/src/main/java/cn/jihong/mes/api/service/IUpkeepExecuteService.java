package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.UpkeepExecuteDTO;
import cn.jihong.mes.api.model.po.UpkeepExecutePO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/** 设备保养执行
 * <AUTHOR>
 * @date 2023/9/1 15:00
 */
public interface IUpkeepExecuteService extends IJiHongService<UpkeepExecutePO> {


    /***
     * @description: 获取七天内还未推送的保养计划
     * @param
     * @return: java.util.List<cn.jihong.mes.api.model.po.UpkeepExecutePO>
     * <AUTHOR>
     * @date: 2023/9/21 18:19
     */
    List<UpkeepExecutePO> getUndistributedList();

    /***
     * @description: 根据 设备名称和保养类型 获取保养计划
     * @param machineName
     * @param upkeepType
     * @return: cn.jihong.mes.api.model.po.UpkeepExecutePO
     * <AUTHOR>
     * @date: 2023/9/21 18:19
     */
    UpkeepExecuteDTO getOneByNameAndType(String machineName, String upkeepType);
}
