package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.po.ProductionMachineConfigPO;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineInVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * 机台配置表 服务类
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface IProductionMachineConfigService extends IJiHongService<ProductionMachineConfigPO> {

    Integer getMachineParts(MachineConfigInVO machineConfigInVO);

    Integer getBillingType(MachineConfigInVO machineConfigInVO);

    void saveMachineConfig(Long id, ProductionMachineInVO.ProductionMachineConfigVO productionMachineConfigVO);


    ProductionMachineConfigPO getByMachineId(Long id);

//    List<WorkshopOutVO> getAllWorkshop();
}
