package cn.jihong.mes.api.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 质检信息
 */
@Data
public class ProcessQualityInspectionVO implements Serializable {

    /**
     * 检验单号
     */
    @ExcelProperty("检验单号")
    private String qcFromNo;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String mono;

    /**
     * 生产工单号
     */
    @ExcelProperty("生产工单号")
    private String baseLotNo;

    /**
     * 工序编号
     */
    @ExcelProperty("工序编号")
    private String opNo;

    /**
     * 工序名称
     */
    @ExcelProperty("工序名称")
    private String opName;

    /**
     * 检验结果
     */
    @ExcelProperty("检验结果")
    private String qcResult;

    /**
     * 检验详情结果
     */
    @ExcelProperty("检验详情结果")
    private String qcItemResult;

    /**
     * 检验数量
     */
    @ExcelProperty("检验数量")
    private BigDecimal checkQty;

    /**
     * 不良数量
     */
    @ExcelProperty("不良数量")
    private BigDecimal defectQty;

    /**
     * 检验时间
     */
    @ExcelProperty("检验时间")
    private String checkTime;

    /**
     * 检验人员编号
     */
    @ExcelProperty("检验人员编号")
    private String checkUserNo;

    /**
     * 检验人员名称
     */
    @ExcelProperty("检验人员名称")
    private String checkUserName;

    /**
     * 检验类别
     */
    @ExcelProperty("检验类别")
    private String checkQcType;

    /**
     * 检验区域
     */
    @ExcelProperty("检验区域")
    private String checkArea;




}
