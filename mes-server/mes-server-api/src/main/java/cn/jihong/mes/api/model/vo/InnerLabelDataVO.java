package cn.jihong.mes.api.model.vo;

import java.io.Serializable;

import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 内标签数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Data
public class InnerLabelDataVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productionName;

    /**
     * 产品编码
     */
    @ExcelProperty("产品编码")
    private String productionNo;

    /**
     * 内标签模板文件地址
     */
    @ExcelProperty("内标签模板文件地址")
    private String templateUrl;

    /**
     * 包装数量
     */
    @ExcelProperty("包装数量")
    private Integer packageNum;

    /**
     * 材质
     */
    @ExcelProperty("材质")
    private String materialQuality;

    /**
     * 重量范围
     */
    @ExcelProperty("重量")
    private String weightRange;

    /**
     * 工程单号
     */
    @ExcelProperty("工程单号")
    private String workerOrderNo;

    /**
     * 生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("生产日期")
    private String productionPlanDate;

    /**
     * 生产班次
     */
    @ExcelProperty("生产班次")
    private String serialNo;

    /**
     * 机长编码
     */
    @ExcelProperty("机长编码")
    private String machineLeaderNo;

    /**
     * 有效期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("有效期")
    private String expirationDate;

    /**
     * 打印数量
     */
    @ExcelProperty("打印数量")
    private Integer printNum;

    /**
     * 使用条件
     */
    @ExcelProperty("使用条件")
    private String useCondition;

    /**
     * 批次识别码
     */
    @ExcelProperty("批次识别码")
    private String batchCode;

}
