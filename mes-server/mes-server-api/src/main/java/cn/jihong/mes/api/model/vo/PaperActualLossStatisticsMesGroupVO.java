package cn.jihong.mes.api.model.vo;


import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class PaperActualLossStatisticsMesGroupVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分类
     */
    @ExcelProperty("分类")
    private String category;

    /**
     * 工厂名称
     */
    @ExcelProperty("公司")
    private String factoryName;

    /**
     * 标准损耗率
     */
    @ExcelProperty("标准损耗率(%)")
    private BigDecimal standardLossRate;

    /**
     * 额定损耗率
     */
    @ExcelProperty("额定损耗率(%)")
    private BigDecimal ratedLossRate;

    /**
     * 开单损耗率
     */
    @ExcelProperty("开单损耗率(%)")
    private BigDecimal billingLossRate;

    /**
     * 实际损耗率
     */
    @ExcelProperty("实际损耗率(%)")
    private BigDecimal actualLossRate;

    /**
     * 实际损耗率(末工序报工)%
     */
    @ExcelProperty("实际损耗率(末工序报工)%")
    private BigDecimal endActualLossRate;

    /**
     * 产出比
     */
    @ExcelProperty("投入产出比(%)")
    private BigDecimal outputRatio;

    /**
     * 首工序报工数量
     */
    @ExcelProperty("首工序报工数量")
    private BigDecimal initReportingNum;

    /**
     * 首工序不良数量
     */
    @ExcelProperty("首工序不良数量")
    private BigDecimal initDefectiveProductNum;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("实际成品量")
    private BigDecimal qualifiedQuantity;

    /**
     * 末道工序报工数量
     */
    @ExcelProperty("末道工序报工数量")
    private BigDecimal endReportingNum;

    /**
     * 不良品总数
     */
    @ExcelProperty("实际损耗量")
    private BigDecimal defectiveProductNum;

    /**
     * 虚拟报工数量
     */
    @ExcelProperty("虚拟报工数量")
    private BigDecimal virtualReporteQuantity;

    /**
     * 研发退料
     */
    @ExcelProperty("退料数量")
    private BigDecimal developmentReturns;

    /**
     * 客户下线数量
     */
    @ExcelProperty("客户下线数量")
    private BigDecimal customerOfflineQuantity;

    /**
     * 开单数量
     */
    @ExcelIgnore
    private BigDecimal billingQuantity;

    /**
     * 投料数量
     */
    @ExcelProperty("实际投料量")
    private BigDecimal inputQuantity;



    /**
     * 实际损耗量(末工序报工)
     */
    @ExcelProperty("实际损耗量(末工序报工)")
    private BigDecimal endDefectiveProductNum;

    /**
     * 开单损耗量
     */
    @ExcelProperty("开单损耗量")
    private BigDecimal billingLossQuantity;

    /**
     * 额定损耗量
     */
    @ExcelProperty("额定损耗量")
    private BigDecimal ratedLossQuantity;

    /**
     * 标准损耗量
     */
    @ExcelProperty("标准损耗量")
    private BigDecimal standardLossQuantity;

    /**
     * 首工序开单损耗量
     */
    @ExcelProperty("首工序开单损耗量")
    private BigDecimal initBillingLossQuantity;

    /**
     * 首工序额定损耗量
     */
    @ExcelProperty("首工序额定损耗量")
    private BigDecimal initRatedLossQuantity;

    /**
     * 首工序标准损耗量
     */
    @ExcelProperty("首工序标准损耗量")
    private BigDecimal initStandardLossQuantity;

    /**
     *工单数量
     */
    @ExcelProperty("理论良品数量")
    private BigDecimal workOrderQuantity;

}
