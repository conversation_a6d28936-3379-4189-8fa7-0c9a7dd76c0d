package cn.jihong.mes.api.model.vo;


import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
public class ProcessLossSumMesVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工序编号
     */
    private String processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 完工日期
     */
    private String finishDate;

    /**
     * 实际损耗率
     */
    private BigDecimal actualLossRate;

    /**
     * 标准损耗率
     */
    private String standardLossRate;

    /**
     * 额定损耗率
     */
    private String ratedLossRateRate;

    /**
     * 开单损耗率
     *
     */
    private BigDecimal billingLossRate;

    /**
     * 实际产出比
     */
    private BigDecimal actualOutputRatio;

    /**
     * 不良品总数
     */
    private BigDecimal defectiveProductNum;

    /**
     * 已入库合格数量
     */
    private BigDecimal qualifiedQuantity;

    /**
     * 投料数量（PCS）
     */
    private BigDecimal inputQuantity;

}
