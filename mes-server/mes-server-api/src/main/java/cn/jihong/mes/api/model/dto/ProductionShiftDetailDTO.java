package cn.jihong.mes.api.model.dto;

import cn.jihong.mes.api.model.vo.TimeVO;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 班次详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class ProductionShiftDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer mainId;

    /**
     * 班次详情名称
     */
    private String shiftDetailName;


    /**
     * 时间段
     */
    private List<TimeVO> timeSlot;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 顺序号
     */
    private Integer serialNo;

}
