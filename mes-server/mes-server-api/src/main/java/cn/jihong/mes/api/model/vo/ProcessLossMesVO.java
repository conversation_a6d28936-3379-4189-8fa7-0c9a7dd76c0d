package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessLossMesVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工序编号
     */
    private String processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工单号
     */
    private String workerOrderNo;

    /**
     * 产品类别编号
     */
    private String productCategoryNo;

    /**
     * 产品类别名称
     */
    private String productCategoryName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 完工日期
     */
    private String finishDate;

    /**
     * 工序损耗率
     */
    private BigDecimal processLossRate;

    /**
     * 工单损耗率
     */
    private BigDecimal workOrderLossRate;

    /**
     * 标准损耗率
     */
    private String standardLossRate;

    /**
     * 额定损耗率
     */
    private String ratedLossRateRate;

    /**
     * 开单损耗率
     *
     */
    private BigDecimal billingLossRate;

}
