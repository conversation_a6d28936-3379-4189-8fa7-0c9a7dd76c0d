package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Getter
@Setter
@TableName("TBLWIPCONT_RESOURCE")
public class TblwipcontResourcePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String LOTNO = "LOTNO";
    public static final String MONO = "MONO";
    public static final String BASELOTNO = "BASELOTNO";
    public static final String OPNO = "OPNO";
    public static final String LOGGROUPSERIAL = "LOGGROUPSERIAL";
    public static final String RESCLASS = "RESCLASS";
    public static final String RESTYPE = "RESTYPE";
    public static final String RESITEM = "RESITEM";
    public static final String RESVALUE = "RESVALUE";
    public static final String STDVALUE = "STDVALUE";
    public static final String INPUTQTY = "INPUTQTY";
    public static final String USERNO = "USERNO";
    public static final String EVENTTIME = "EVENTTIME";
    public static final String RWOMESNO = "RWOMESNO";
    public static final String SUBRWOMESNO = "SUBRWOMESNO";
    public static final String PRICETYPE = "PriceType";
    public static final String UNITPRICE = "UnitPrice";
    public static final String PRICERATE = "PriceRate";
    public static final String C_POST = "C_POST";
    public static final String C_TYPE = "C_TYPE";
    public static final String C_SHIFTNO = "C_SHIFTNO";


    @TableField(LOTNO)
    private String lotno;

    @TableField(MONO)
    private String mono;

    @TableField(BASELOTNO)
    private String baselotno;

    @TableField(OPNO)
    private String opno;

    @TableId(LOGGROUPSERIAL)
    private String loggroupserial;

    @TableField(RESCLASS)
    private Double resclass;

    @TableField(RESTYPE)
    private String restype;

    @TableField(RESITEM)
    private String resitem;

    @TableField(RESVALUE)
    private Double resvalue;

    @TableField(STDVALUE)
    private Double stdvalue;

    @TableField(INPUTQTY)
    private Double inputqty;

    @TableField(USERNO)
    private String userno;

    @TableField(EVENTTIME)
    private Date eventtime;

    @TableField(RWOMESNO)
    private String rwomesno;

    @TableField(SUBRWOMESNO)
    private String subrwomesno;

    @TableField(PRICETYPE)
    private Double priceType;

    @TableField(UNITPRICE)
    private Double unitPrice;

    @TableField(PRICERATE)
    private Double priceRate;

    @TableField(C_POST)
    private String cPost;

    @TableField(C_TYPE)
    private String cType;

    @TableField(C_SHIFTNO)
    private String cShiftno;

}
