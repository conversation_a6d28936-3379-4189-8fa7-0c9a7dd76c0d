package cn.jihong.mes.api.model.vo;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import cn.jihong.common.constant.DateFormatConstant;
import lombok.Data;

/**
 * 得到生产计划列表
 *
 * <AUTHOR>
 * @date 2023/07/10
 */
@Data
public class GetProductionPlanListInVO implements Serializable {

    /**
     * 工序
     */
    private String process;
    /**
     * 机台
     */
    private String machine;
    /**
     * 开始日期
     */
    @NotBlank(message = "开始日期不能为空")
    @Pattern(regexp = DateFormatConstant.DATE_REX, message = "开始日期格式必须为" + DateFormatConstant.DATE)
    private String startDate;
    /**
     * 结束日期
     */
    @NotBlank(message = "结束日期不能为空")
    @Pattern(regexp = DateFormatConstant.DATE_REX, message = "结束日期格式必须为" + DateFormatConstant.DATE)
    private String endDate;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 生产部门
     */
    private String deptName;

}
