package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Getter
@Setter
@TableName("TBLEQPEQUIPMENTBASIS")
public class TbleqpequipmentbasisPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String EQUIPMENTNO = "EQUIPMENTNO";
    public static final String EQUIPMENTTYPE = "EQUIPMENTTYPE";
    public static final String CAPACITY = "CAPACITY";
    public static final String VENDORNO = "VENDORNO";
    public static final String MODELNO = "MODELNO";
    public static final String DESCRIPTION = "DESCRIPTION";
    public static final String CREATOR = "CREATOR";
    public static final String CREATEDATE = "CREATEDATE";
    public static final String ISSUESTATE = "ISSUESTATE";
    public static final String ENGINEERGROUPNO = "ENGINEERGROUPNO";
    public static final String ASSETNO = "ASSETNO";
    public static final String EQUIPMENTCLASS = "EQUIPMENTCLASS";
    public static final String LOADPORT = "LOADPORT";
    public static final String AUTOFLAG = "AUTOFLAG";
    public static final String EACONTROLLER = "EACONTROLLER";
    public static final String EQPRECIPE = "EQPRECIPE";
    public static final String QCLISTNO = "QCLISTNO";
    public static final String MAXTIME = "MaxTime";
    public static final String FIXEQPTIME = "FixEqpTime";
    public static final String VAREQPTIME = "VarEqpTime";
    public static final String COUNTEQPUNITQTY = "CountEqpUnitQty";
    public static final String COUNTER = "COUNTER";
    public static final String ERPNO = "ERPNO";
    public static final String EQUIPMENTNAME = "EquipmentName";
    public static final String PRODUCTIONINF = "PRODUCTIONINF";
    public static final String ALLOWMULTIWORK = "ALLOWMULTIWORK";
    public static final String SETUPIGNOREMACHINE = "SETUPIGNOREMACHINE";
    public static final String SPC_PQC = "SPC_PQC";
    public static final String EQUIPMENTCHECKUP = "EQUIPMENTCHECKUP";
    public static final String EQUIPMENTCHECKUPRATE = "EQUIPMENTCHECKUPRATE";
    public static final String EQUIPMENTCHECKUPTIME = "EQUIPMENTCHECKUPTIME";
    public static final String COUNTER_PRE = "Counter_Pre";
    public static final String OUTUSEROPTION = "OutUserOption";
    public static final String OUTLABERTIMEOPTION = "OutLaberTimeOption";
    public static final String OUTLABEREXCLUSIVE = "OutLaberExclusive";
    public static final String OUTMACHINEEXCLUSIVE = "OutMachineExclusive";
    public static final String OUTQTYDEFINITION = "OutQtyDefinition";
    public static final String OUTQTYOPTION = "OutQtyOption";
    public static final String OUTQTYALLOWZERO = "OutQtyAllowZero";
    public static final String COUNTERUPDATETIME = "CounterUpdateTime";
    public static final String COUNTEREQTIME = "CounterEQTime";
    public static final String SPC_PQC2 = "SPC_PQC2";
    public static final String RECORDTIMEOUTDATE = "RecordTimeOutDate";
    public static final String STDTIMEOUT = "StdTimeOut";
    public static final String STDTIMEOUTQTY = "StdTimeOutQty";
    public static final String LOTBINDING = "LotBinding";
    public static final String LINEINVENTORYNO = "LineInventoryNo";


    @TableId(EQUIPMENTNO)
    private String equipmentno;

    @TableField(EQUIPMENTTYPE)
    private String equipmenttype;

    @TableField(CAPACITY)
    private Double capacity;

    @TableField(VENDORNO)
    private String vendorno;

    @TableField(MODELNO)
    private String modelno;

    @TableField(DESCRIPTION)
    private String description;

    @TableField(CREATOR)
    private String creator;

    @TableField(CREATEDATE)
    private Date createdate;

    @TableField(ISSUESTATE)
    private Double issuestate;

    @TableField(ENGINEERGROUPNO)
    private String engineergroupno;

    @TableField(ASSETNO)
    private String assetno;

    @TableField(EQUIPMENTCLASS)
    private String equipmentclass;

    @TableField(LOADPORT)
    private Double loadport;

    @TableField(AUTOFLAG)
    private Double autoflag;

    @TableField(EACONTROLLER)
    private String eacontroller;

    @TableField(EQPRECIPE)
    private Double eqprecipe;

    @TableField(QCLISTNO)
    private String qclistno;

    @TableField(MAXTIME)
    private Double maxTime;

    @TableField(FIXEQPTIME)
    private Double fixEqpTime;

    @TableField(VAREQPTIME)
    private Double varEqpTime;

    @TableField(COUNTEQPUNITQTY)
    private Double countEqpUnitQty;

    @TableField(COUNTER)
    private Double counter;

    @TableField(ERPNO)
    private String erpno;

    @TableField(EQUIPMENTNAME)
    private String equipmentName;

    @TableField(PRODUCTIONINF)
    private Double productioninf;

    @TableField(ALLOWMULTIWORK)
    private Double allowmultiwork;

    @TableField(SETUPIGNOREMACHINE)
    private Double setupignoremachine;

    @TableField(SPC_PQC)
    private Double spcPqc;

    @TableField(EQUIPMENTCHECKUP)
    private String equipmentcheckup;

    @TableField(EQUIPMENTCHECKUPRATE)
    private String equipmentcheckuprate;

    @TableField(EQUIPMENTCHECKUPTIME)
    private Date equipmentcheckuptime;

    @TableField(COUNTER_PRE)
    private Double counterPre;

    @TableField(OUTUSEROPTION)
    private Double outUserOption;

    @TableField(OUTLABERTIMEOPTION)
    private Double outLaberTimeOption;

    @TableField(OUTLABEREXCLUSIVE)
    private Double outLaberExclusive;

    @TableField(OUTMACHINEEXCLUSIVE)
    private Double outMachineExclusive;

    @TableField(OUTQTYDEFINITION)
    private Double outQtyDefinition;

    @TableField(OUTQTYOPTION)
    private Double outQtyOption;

    @TableField(OUTQTYALLOWZERO)
    private Double outQtyAllowZero;

    @TableField(COUNTERUPDATETIME)
    private Date counterUpdateTime;

    @TableField(COUNTEREQTIME)
    private Date counterEQTime;

    @TableField(SPC_PQC2)
    private String spcPqc2;

    @TableField(RECORDTIMEOUTDATE)
    private Date recordTimeOutDate;

    @TableField(STDTIMEOUT)
    private Double stdTimeOut;

    @TableField(STDTIMEOUTQTY)
    private Double stdTimeOutQty;

    @TableField(LOTBINDING)
    private Double lotBinding;

    @TableField(LINEINVENTORYNO)
    private String lineInventoryNo;

}
