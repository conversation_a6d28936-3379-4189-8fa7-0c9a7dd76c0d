package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("TBLCUS_Processbarcode")
public class TblCusProcessBarCodePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String PROCESSBARCODE = "PROCESSBARCODE";
    public static final String PRODUCTNO = "PRODUCTNO";
    public static final String STATE = "STATE";
    public static final String PRODUCTNAME = "PRODUCTNAME";
    public static final String QTY = "QTY";
    public static final String UNIT_NO = "UNIT_NO";
    public static final String MONO = "MONO";
    public static final String BASELOTNO = "BASELOTNO";
    public static final String EQUIPMENTNO = "EQUIPMENTNO";
    public static final String EQUIPMENTNAME = "EQUIPMENTNAME";
    public static final String NODEID = "NODEID";
    public static final String OPNO = "OPNO";
    public static final String CREATEDATETIME = "CREATEDATETIME";
    public static final String USERNO = "USERNO";
    public static final String USERNAME = "USERNAME";
    public static final String CREATEUSERID = "CREATEUSERID";
    public static final String PRODUCTTYPE = "PRODUCTTYPE";


    @TableId(PROCESSBARCODE)
    private String processBarCode;

    @TableField(PRODUCTNO)
    private String productNo;

    @TableField(STATE)
    private Double state;

    @TableField(PRODUCTNAME)
    private String productName;

    @TableField(QTY)
    private Double qty;

    @TableField(UNIT_NO)
    private String unitNo;

    @TableField(MONO)
    private String mono;

    @TableField(BASELOTNO)
    private String baseLotNo;

    @TableField(EQUIPMENTNO)
    private String equipmentNo;

    @TableField(EQUIPMENTNAME)
    private String equipmentName;

    @TableField(NODEID)
    private String nodeId;

    @TableField(OPNO)
    private String opNo;

    @TableField(CREATEDATETIME)
    private Date createDateTime;

    @TableField(USERNO)
    private String userNo;

    @TableField(USERNAME)
    private String username;

    @TableField(CREATEUSERID)
    private String createUserId;

    @TableField(PRODUCTTYPE)
    private String productType;

}
