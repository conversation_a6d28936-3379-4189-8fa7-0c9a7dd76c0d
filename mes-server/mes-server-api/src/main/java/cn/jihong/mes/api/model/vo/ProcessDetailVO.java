package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProcessDetailVO implements Serializable {

    private String upProcessBarCode;

    private String onProcessBarCode;

    /**
     * 工序编号 TBLOPBASIS->OPNO
     */
    private String processNo;

    /**
     * 工序名称 TBLOPBASIS->OPNAME
     */
    private String processName;

    /**
     * 设备编号 Processbarcodehistory->EQUIPMENTNO
     */
    private String deviceNo;

    /**
     * 设备名称 Processbarcodehistory->EQUIPMENTNAME
     */
    private String deviceName;

    /**
     * 工单号 Processbarcodehistory->MONO
     */
    private String mono;

    /**
     * 加工时间
     */
    private Date processingDate;






}
