package cn.jihong.mes.api.service;

import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.vo.ProductionMachineInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 机台物流相关属性 服务类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface IProductionMachineLogisticsConfigService extends IJiHongService<ProductionMachineLogisticsConfigPO> {


    /**
     * 获取设备配置信息
     * @param machineName
     * @param processCode
     * @return
     */
    ProductionMachineLogisticsConfigPO getMachineConfig(String machineName, String processCode);

    ProductionMachineLogisticsConfigPO getMachineConfig(String machineName);


    ProductionMachineLogisticsConfigPO getByMachineId(Long id);

    List<ProductionMachineLogisticsConfigPO> getByMachineIds(List<Long> machineIds);

    ProductionMachineOutVO getByExternalMachineId(String externalMachineId,String workshopCode);

    UserInfo getByUserInfoExternalMachineId(String externalMachineId, String workshopCode);

    void saveMachineLogisticsConfig(Long id, ProductionMachineInVO.ProductionMachineLogisticsConfigVO productionMachineLogisticsConfigVO);


}
