package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
public class TblopbasisVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String opno;

    private String opname;

    private String optype;

    private Double opclass;

    private String opshortname;

    private Double oporder;

    private String description;

    private String creator;

    private Date createdate;

    private Double issuestate;

    private String rulexmlstring;

    private String psno;

    private Double printoutonruncard;

    private Double stdunitruntime;

    private Double countopunitqty;

    private Double stdqueuetime;

    private Double partialmode;

    private Double materialOption;

    private Double multioperatormode;

    private String erpno;

    private Double osoption;

    private String osno;

    private Double needfirstcheckok;

    private Double spcPqc;

    private Double autoci;

    private String defaulteqpno;

    private Double outputrate;

    private Double qcControl;

    private String ischeckstartingchecklist;

    private String spcPqc2;

    private String qcControl2;

    private String qCCheckRate;

    private String pQCCheckRate;

    private Double needPQCheckOK;

    private Double plugInUnit;

    private Double plugIn;

    private Double checkoutstdqty;

    private Double autosplitlot;

    private Double autoDispCallMtl;

    private Double needSelfCheckOK;

    private Double selfCheckRate;

    private Double needEndCheckOK;

    private Double endCheckRate;

    private String cUnit;

    private String cProportion;

    private Date sysdate;

}
