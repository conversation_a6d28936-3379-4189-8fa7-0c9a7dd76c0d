package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@TableName("TBLCUS_Processbarcodehistory")
public class TblCusProcessBarCodeHistoryPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String UPPROCESSBARCODE = "UPPROCESSBARCODE";
    public static final String ONPROCESSBARCODE = "ONPROCESSBARCODE";
    public static final String PRODUCTNO = "PRODUCTNO";
    public static final String PRODUCTNAME = "PRODUCTNAME";
    public static final String PRODUCTTYPE = "PRODUCTTYPE";
    public static final String QTY = "QTY";
    public static final String MONO = "MONO";
    public static final String BASELOTNO = "BASELOTNO";
    public static final String UNIT_NO = "UNIT_NO";
    public static final String EQUIPMENTNO = "EQUIPMENTNO";
    public static final String EQUIPMENTNAME = "EQUIPMENTNAME";
    public static final String CREATEDATE = "CREATEDATE";
    public static final String USERNO = "USERNO";
    public static final String USERNAME = "USERNAME";


    @TableField(UPPROCESSBARCODE)
    private String upProcessBarCode;

    @TableField(ONPROCESSBARCODE)
    private String onProcessBarCode;

    @TableField(PRODUCTNO)
    private String productNo;

    @TableField(PRODUCTNAME)
    private String productName;

    @TableField(PRODUCTTYPE)
    private String productType;

    @TableField(QTY)
    private Double qty;

    @TableField(MONO)
    private String mono;

    @TableId(BASELOTNO)
    private String baseLotNo;

    @TableField(UNIT_NO)
    private String unitNo;

    @TableField(EQUIPMENTNO)
    private String equipmentNo;

    @TableField(EQUIPMENTNAME)
    private String equipmentName;

    @TableField(CREATEDATE)
    private Date createDate;

    @TableField(USERNO)
    private String userNo;

    @TableField(USERNAME)
    private String userName;

}
