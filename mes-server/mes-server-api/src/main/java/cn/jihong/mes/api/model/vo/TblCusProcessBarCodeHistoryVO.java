package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
public class TblCusProcessBarCodeHistoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String upProcessBarCode;

    private String onProcessBarCode;

    private String productNo;

    private String productName;

    private String productType;

    private Double qty;

    private String mono;

    private String baseLotNo;

    private String unitNo;

    private String equipmentNo;

    private String equipmentName;

    private Date createDate;

    private String userNo;

    private String userName;

}
