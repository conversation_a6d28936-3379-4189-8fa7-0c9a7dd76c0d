package cn.jihong.mes.api.model.vo;


import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class ProcessLossSumMesGroupVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 完成日期
     */
    @ExcelProperty("完成日期")
    private String finishDate;

    /**
     * 标准损耗率
     */
    @ExcelProperty("标准损耗率")
    private BigDecimal standardLossRate;

    /**
     * 额定损耗率
     */
    @ExcelProperty("额定损耗率")
    private BigDecimal ratedLossRate;

    /**
     * 开单损耗率
     */
    @ExcelProperty("开单损耗率")
    private BigDecimal billingLossRate;

    /**
     * 实际损耗率
     */
    @ExcelProperty("实际损耗率")
    private BigDecimal actualLossRate;

    /**
     * 产出比
     */
    @ExcelProperty("产出比")
    private BigDecimal outputRatio;

    /**
     *工单数量
     */
    @ExcelIgnore
    private BigDecimal workOrderQuantity;

    /**
     * 投料数量
     */
    @ExcelProperty("实际投料量")
    private BigDecimal inputQuantity;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("实际成品量")
    private BigDecimal qualifiedQuantity;

    /**
     * 不良品总数
     */
    @ExcelProperty("实际损耗量")
    private BigDecimal defectiveProductNum;

    /**
     * 开单损耗量
     */
    @ExcelProperty("开单损耗量")
    private BigDecimal billingLossQuantity;

    /**
     * 额定损耗量
     */
    @ExcelProperty("额定损耗量")
    private BigDecimal ratedLossQuantity;

    /**
     * 标准损耗量
     */
    @ExcelProperty("标准损耗量")
    private BigDecimal standardLossQuantity;


}
