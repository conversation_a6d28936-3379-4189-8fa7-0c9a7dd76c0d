package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("TBLCUS_processmaterrecord")
public class TblCusProcessMaterRecordPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String PROCESSBARCODE = "PROCESSBARCODE";
    public static final String BARCODE = "BARCODE";
    public static final String ITEM_NO = "ITEM_NO";
    public static final String ITEM_NAME = "ITEM_NAME";
    public static final String ITEM_BARCODE_TYPE = "ITEM_BARCODE_TYPE";
    public static final String IS_DEDUCT = "IS_DEDUCT";
    public static final String ITEM_LOT_NO = "ITEM_LOT_NO";
    public static final String USE_QTY = "USE_QTY";
    public static final String UNIT_NO = "UNIT_NO";
    public static final String CREATEDATE = "CREATEDATE";
    public static final String USERNO = "USERNO";
    public static final String USERNAME = "USERNAME";
    public static final String BASELOTNO = "BASELOTNO";


    @TableId(PROCESSBARCODE)
    private String processBarCode;

    @TableField(BARCODE)
    private String barCode;

    @TableField(ITEM_NO)
    private String itemNo;

    @TableField(ITEM_NAME)
    private String itemName;

    @TableField(ITEM_BARCODE_TYPE)
    private String itemBarcodeType;

    @TableField(IS_DEDUCT)
    private String isDeduct;

    @TableField(ITEM_LOT_NO)
    private String itemLotNo;

    @TableField(USE_QTY)
    private Double useQty;

    @TableField(UNIT_NO)
    private String unitNo;

    @TableField(CREATEDATE)
    private Date createDate;

    @TableField(USERNO)
    private String userNo;

    @TableField(USERNAME)
    private String userName;

    @TableField(BASELOTNO)
    private String baseLotNo;

}
