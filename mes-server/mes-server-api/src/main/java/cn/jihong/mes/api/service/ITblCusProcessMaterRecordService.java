package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.TblCusProcessMaterRecordDTO;
import cn.jihong.mes.api.model.po.TblCusProcessMaterRecordPO;
import cn.jihong.mes.api.model.vo.TblCusProcessMaterRecordVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITblCusProcessMaterRecordService extends IJiHongService<TblCusProcessMaterRecordPO> {

    List<TblCusProcessMaterRecordVO> getTblCusProcessMaterRecordList(TblCusProcessMaterRecordDTO tblCusProcessMaterRecordDTO);

}
