package cn.jihong.mes.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
public class TblusruserbasisDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<String> userno;

    private String username;

    private Double userlevel;

    private String password;

    private String creator;

    private Date createdate;

    private String description;

    private Double issuestate;

    private Double usertype;

    private String departmentno;

    private String customerno;

    private String emailaddress;

    private Date changedate;

    private String shiftno;

    private String mobileno;

    private Double resetpassword;

    private String eRPNo;

    private Date resignationdate;

    private String titleno;

    private Date comeday;

    private String lineId;

    private String weChatId;

    private String watchId;

    private String iCCard;

}
