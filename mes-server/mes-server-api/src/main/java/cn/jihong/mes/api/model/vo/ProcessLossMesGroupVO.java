package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class ProcessLossMesGroupVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司编号
     */
    @ExcelProperty("据点")
    private String factoryCode;

    /**
     * 公司名称
     */
    @ExcelProperty("公司名称")
    private String factoryName;
    /**
     * 工序编号
     */
    @ExcelIgnore
    private String processNo;

    /**
     * 工序名称
     */
    @ExcelProperty("工序")
    private String processName;


//    /**
//     * 工序类型
//     */
//    @ExcelProperty("工序类型")
//    private String processType;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;

    /**
     * 标准损耗率
     */
    @ExcelProperty("标准损耗率")
    private BigDecimal standardLossRate;

    /**
     * 额定损耗率
     */
    @ExcelProperty("额定损耗率")
    private BigDecimal ratedLossRate;

//    /**
//     * 开单损耗率
//     */
//    @ExcelProperty("开单损耗率")
//    private BigDecimal billingLossRate;

    /**
     * 实际损耗率
     */
    @ExcelProperty("实际损耗率")
    private BigDecimal actualLossRate;

    /**
     * 产出比
     */
    @ExcelProperty("产出比")
    private BigDecimal outputRatio;

    /**
     *工单数量
     */
    @ExcelIgnore
    private BigDecimal workOrderQuantity;

    /**
     * 投料数量
     */
    @ExcelProperty("实际投料量")
    private BigDecimal inputQuantity;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("实际成品量")
    private BigDecimal qualifiedQuantity;

    /**
     * 不良品总数
     */
    @ExcelProperty("实际损耗量")
    private BigDecimal defectiveProductNum;

//    /**
//     * 开单损耗量
//     */
//    @ExcelProperty("开单损耗量")
//    private BigDecimal billingLossQuantity;

    /**
     * 额定损耗量
     */
    @ExcelProperty("额定损耗量")
    private BigDecimal ratedLossQuantity;

    /**
     * 标准损耗量
     */
    @ExcelProperty("标准损耗量")
    private BigDecimal standardLossQuantity;

    /**
     * 其它工序报本工序不良数量
     */
    @ExcelProperty("其它工序报本工序不良数量")
    private BigDecimal otherDefectiveProductNum;

    /**
     * 本工序报其它工序不良数量
     */
    @ExcelProperty("本工序报其它工序不良数量")
    private BigDecimal currentDefectiveNum;



}
