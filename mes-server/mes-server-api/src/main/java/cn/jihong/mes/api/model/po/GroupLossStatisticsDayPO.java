package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 集团损耗统计日报-单位张
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Getter
@Setter
@TableName("group_loss_statistics_day")
public class GroupLossStatisticsDayPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String REPORT_DATE = "report_date";
    public static final String COMPANY_CODE = "company_code";
    public static final String COMPANY_NAME = "company_name";
    public static final String PRODUCT_NO = "product_no";
    public static final String PRODUCT_NAME = "product_name";
    public static final String WORK_ORDER_NO = "work_order_no";
    public static final String CATEGORY = "category";
    public static final String GOOD_PRODUCT_QUANTITY = "good_product_quantity";
    public static final String INIT_REPORTING_NUM = "init_reporting_num";
    public static final String INIT_DEFECTIVE_PRODUCT_NUM = "init_defective_product_num";
    public static final String QUALIFIED_QUANTITY = "qualified_quantity";
    public static final String RETURN_QUANTITY = "return_quantity";
    public static final String VIRTUAL_REPORTE_QUANTITY = "virtual_reporte_quantity";
    public static final String CUSTOMER_OFFLINE_QUANTITY = "customer_offline_quantity";
    public static final String DEFECTIVE_PRODUCT_NUM = "defective_product_num";
    public static final String ACTUAL_LOSS_RATE = "actual_loss_rate";
    public static final String STANDARD_LOSS_RATE = "standard_loss_rate";
    public static final String RATED_LOSS_RATE = "rated_loss_rate";
    public static final String WORK_ORDER_QUANTITY = "work_order_quantity";
    public static final String UNDER_COUNT_QUANTITY = "under_count_quantity";
    public static final String RATED_LOSS_QUANTITY = "rated_loss_quantity";
    public static final String INIT_RATED_LOSS_QUANTITY = "init_rated_loss_quantity";
    public static final String STANDARD_LOSS_QUANTITY = "standard_loss_quantity";
    public static final String INIT_STANDARD_LOSS_QUANTITY = "init_standard_loss_quantity";
    public static final String CUSTOMER_NO = "customer_no";
    public static final String CUSTOMER_NAME = "customer_name";
    public static final String PROCESS_NO = "process_no";
    public static final String PROCESS_NAME = "process_name";
    public static final String LOSS_TYPE = "loss_type";
    public static final String LAST_PROCESS = "last_process";
    public static final String NEXT_PROCESS = "next_process";
    public static final String PIECE = "piece";
    public static final String BILLING_QUANTITY = "billing_quantity";
    public static final String INIT_THEORY_OUT_PUT = "init_theory_out_put";
    public static final String BILLING_LOSS_QUANTITY = "billing_loss_quantity";
    public static final String INIT_BILLING_LOSS_QUANTITY = "init_billing_loss_quantity";
    public static final String BILLING_LOSS_RATE = "billing_loss_rate";



    /**
     * 主键
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 报表日期
     */
    @TableField(REPORT_DATE)
    private Date reportDate;


    /**
     * 公司编号
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 公司名称
     */
    @TableField(COMPANY_NAME)
    private String companyName;


    /**
     * 产品编号
     */
    @TableField(PRODUCT_NO)
    private String productNo;


    /**
     * 产品名称
     */
    @TableField(PRODUCT_NAME)
    private String productName;


    /**
     * 工单号
     */
    @TableField(WORK_ORDER_NO)
    private String workOrderNo;


    /**
     * 分类
     */
    @TableField(CATEGORY)
    private String category;


    /**
     * 良品转入数量
     */
    @TableField(GOOD_PRODUCT_QUANTITY)
    private Integer goodProductQuantity;


    /**
     * 首工序报工数量
     */
    @TableField(INIT_REPORTING_NUM)
    private Integer initReportingNum;


    /**
     * 首工序不良数量
     */
    @TableField(INIT_DEFECTIVE_PRODUCT_NUM)
    private Integer initDefectiveProductNum;


    /**
     * 已入库合格数量
     */
    @TableField(QUALIFIED_QUANTITY)
    private Integer qualifiedQuantity;


    /**
     * 退料数量
     */
    @TableField(RETURN_QUANTITY)
    private Integer returnQuantity;


    /**
     * 虚拟报工数量
     */
    @TableField(VIRTUAL_REPORTE_QUANTITY)
    private Integer virtualReporteQuantity;


    /**
     * 客户下线数量
     */
    @TableField(CUSTOMER_OFFLINE_QUANTITY)
    private Integer customerOfflineQuantity;


    /**
     * 实际损耗量
     */
    @TableField(DEFECTIVE_PRODUCT_NUM)
    private Double defectiveProductNum;


    /**
     * 实际损耗率
     */
    @TableField(ACTUAL_LOSS_RATE)
    private Double actualLossRate;


    /**
     * 标准损耗率
     */
    @TableField(STANDARD_LOSS_RATE)
    private Double standardLossRate;


    /**
     * 额定损耗率
     */
    @TableField(RATED_LOSS_RATE)
    private Double ratedLossRate;


    /**
     * 理论良品数量
     */
    @TableField(WORK_ORDER_QUANTITY)
    private Integer workOrderQuantity;


    /**
     * 欠数数量
     */
    @TableField(UNDER_COUNT_QUANTITY)
    private Integer underCountQuantity;


    /**
     * 额定损耗量
     */
    @TableField(RATED_LOSS_QUANTITY)
    private Integer ratedLossQuantity;


    /**
     * 首工序额定损耗量
     */
    @TableField(INIT_RATED_LOSS_QUANTITY)
    private Integer initRatedLossQuantity;


    /**
     * 标准损耗量
     */
    @TableField(STANDARD_LOSS_QUANTITY)
    private Integer standardLossQuantity;


    /**
     * 首工序标准损耗量
     */
    @TableField(INIT_STANDARD_LOSS_QUANTITY)
    private Integer initStandardLossQuantity;


    /**
     * 客户编号
     */
    @TableField(CUSTOMER_NO)
    private String customerNo;


    /**
     * 客户名称
     */
    @TableField(CUSTOMER_NAME)
    private String customerName;


    /**
     * 工序编号
     */
    @TableField(PROCESS_NO)
    private String processNo;


    /**
     * 工序名称
     */
    @TableField(PROCESS_NAME)
    private String processName;


    /**
     * 生产损耗类型
     */
    @TableField(LOSS_TYPE)
    private String lossType;


    /**
     * 上一道工序
     */
    @TableField(LAST_PROCESS)
    private String lastProcess;


    /**
     * 下一道工序
     */
    @TableField(NEXT_PROCESS)
    private String nextProcess;


    /**
     * 拼数
     */
    @TableField(PIECE)
    private Integer piece;


    /**
     * 理论投料量
     */
    @TableField(BILLING_QUANTITY)
    private Integer billingQuantity;


    /**
     * 首工序理论产出
     */
    @TableField(INIT_THEORY_OUT_PUT)
    private Integer initTheoryOutPut;


    /**
     * 开单损耗量
     */
    @TableField(BILLING_LOSS_QUANTITY)
    private Integer billingLossQuantity;


    /**
     * 首工序开单损耗量
     */
    @TableField(INIT_BILLING_LOSS_QUANTITY)
    private Integer initBillingLossQuantity;


    /**
     * 开单损耗率
     */
    @TableField(BILLING_LOSS_RATE)
    private Double billingLossRate;

}
