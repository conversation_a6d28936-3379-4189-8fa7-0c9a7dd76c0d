package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 生产计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class ProductionPlanVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 生产计划日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanDate;

    /**
     * 生产计划开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private Date productionPlanStartTime;

    /**
     * 生产计划结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private Date productionPlanEndTime;


    /**
     * 产品名称
     */
    private String productionName;


    /**
     * 工序
     */
    private String productionProcess;


    /**
     * 生产机台
     */
    private String productionMachine;


    /**
     * 工单号
     */
    private String workerOrderNo;


    /**
     * 单位
     */
    private String unit;


    /**
     * 标准产能/H
     */
    private Double standardProductionCapacity;


    /**
     * 换产时长
     */
    private Double productionChangeHours;


    /**
     * 计划产量
     */
    private Double plannedProductionCapacity;


    /**
     * 星期几
     */
    private String weekDay;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 班次
     */
    private Integer serialNo;

    /**
     * 工厂代码
     */
    private String companyCode;
}
