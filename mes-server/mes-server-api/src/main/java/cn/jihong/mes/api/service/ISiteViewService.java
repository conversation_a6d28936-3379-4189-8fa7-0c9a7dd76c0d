package cn.jihong.mes.api.service;


import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.mes.api.model.dto.SiteViewDTO;
import cn.jihong.mes.api.model.vo.SiteViewVO;
import cn.jihong.common.model.Pagination;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-08
 */
public interface ISiteViewService {

    /**
     * 查询工厂列表
     * @param siteViewDTO
     *
     * @return
     */
    List<SiteViewVO> getList(SiteViewDTO siteViewDTO);




}
