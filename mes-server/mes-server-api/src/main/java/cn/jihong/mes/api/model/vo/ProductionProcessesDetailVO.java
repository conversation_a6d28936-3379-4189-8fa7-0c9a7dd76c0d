package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工序详情
 */
@Data
public class ProductionProcessesDetailVO implements Serializable {

    /**
     * 工序信息
     */
    private List<OpDetailVO> opDetailVOS;

    /**
     * 栈板详情
     */
    private List<ProductionPalletDetailVO> productionPalletDetailVOs;

    /**
     * 工序使用物料信息
     */
    private List<ProduceProcessesUseMaterialVO> produceProcessesUseMaterialVOS;

    /**
     * 工序质检信息
     */
    private List<ProcessQualityInspectionVO> processQualityInspectionVOS;

    /**
     * 工序报工信息
     */
    private List<ProcessReportingDetailVO> processReportingDetailVOS;
}
