package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 生产工序使用物料
 */
@Data
public class ProduceProcessesUseMaterialVO implements Serializable {

    /**
     * 栈板条码
     */
    private String onProcessBarCode;
    /**
     * 物料标签条码
     */
    private String barCode;
    /**
     * 物料编号
     */
    private String itemNo;
    /**
     * 物料名称
     */
    private String itemName;
    /**
     * 物料批次号
     */
    private String lotNo;
    /**
     * 物料用量
     */
    private String useQty;
    /**
     * 使用时间
     */
    private String useDate;
    /**
     * 操作人编号
     */
    private String userNo;
    /**
     * 操作人名称
     */
    private String userName;
    /**
     * 生产批次
     */
    private String produceLotNo;
    /**
     * 设备编号
     */
    private String deviceNo;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 工序编号
     */
    private String opNo;

}
