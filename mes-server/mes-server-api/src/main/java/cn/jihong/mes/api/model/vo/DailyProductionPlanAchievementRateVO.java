package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <p>
 * 日生产计划达成率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class DailyProductionPlanAchievementRateVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 日期
     */
    @ExcelProperty("日期")
    private String productionPlanDate;

    /**
     * 工序
     */
    @ExcelProperty("工序")
    private String productionProcess;

    /**
     * 生产机台编号
     */
    @ExcelIgnore
    private String productionMachineNo;

    /**
     * 生产机台
     */
    @ExcelProperty("机器名称")
    private String productionMachine;

    /**
     * 效率
     */
    @ExcelProperty("效率")
    private String efficiency;

    /**
     * 班次
     */
    @ExcelProperty("班次")
    private String shiftNo;

    /**
     * 机长
     */
    @ExcelProperty("机长")
    private String machineLeader;

    /**
     * 工单号
     */
    @ExcelProperty("工程单号")
    private String workerOrderNo;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productionName;


    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;

    /**
     * 标准计划产量
     */
    @ExcelProperty("标准计划产量")
    private Double plannedProductionCapacity;

    /**
     * 公司id
     */
    @ExcelIgnore
    private Integer companyId;

    /**
     * 实际产量
     */
    @ExcelProperty("实际产量")
    private Double actualCapacity;

    /**
     * 成品实际产量
     */
    @ExcelProperty("成品实际产量")
    private Double finishProductActualCapacity;

    /**
     * 产量达成率
     */
    @ExcelProperty("产量达成率")
    private String achievementRate;

    /**
     * 当班总损耗
     */
    @ExcelProperty("当班总损耗")
    private Double totalLoss;

    /**
     * 当班合格率
     */
    @ExcelProperty("当班合格率")
    private String passRate;

    /**
     * 工艺标准损耗
     */
    @ExcelProperty("工艺标准损耗")
    private Double processStandardLoss;

    /**
     * 损耗率
     */
    @ExcelProperty("损耗率")
    private String lossRate;

    /**
     * 总停机时长
     */
    @ExcelProperty("总停机时长")
    private Double totalDownTimeDuration;

    /**
     * 计划外停机时长
     */
    @ExcelProperty("计划外停机时长")
    private Double unplannedDowntimeDuration;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 不良品原因分析
     */
    @ExcelProperty("不良品原因分析")
    private String rejectCausesAnalysis;



}
