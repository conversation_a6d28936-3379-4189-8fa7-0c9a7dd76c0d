package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("TBLCUS_PROCESSBARCODEBOX")
public class TblCusProcessBarCodeBoxPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String PROCESSBARCODE = "PROCESSBARCODE";
    public static final String BOXID = "BOXID";
    public static final String QTY = "QTY";
    public static final String MONO = "MONO";
    public static final String LOTNO = "LOTNO";
    public static final String ITEM_NO = "ITEM_NO";
    public static final String ITEM_NAME = "ITEM_NAME";
    public static final String ITEM_BARCODE_TYPE = "ITEM_BARCODE_TYPE";
    public static final String PACKUSERNO = "PACKUSERNO";
    public static final String PACKUSERNAME = "PACKUSERNAME";
    public static final String PACKTIME = "PACKTIME";


    @TableId(PROCESSBARCODE)
    private String processBarCode;

    @TableField(BOXID)
    private String boxId;

    @TableField(QTY)
    private Double qty;

    @TableField(MONO)
    private String mono;

    @TableField(LOTNO)
    private String lotNo;

    @TableField(ITEM_NO)
    private String itemNo;

    @TableField(ITEM_NAME)
    private String itemName;

    @TableField(ITEM_BARCODE_TYPE)
    private String itemBarcodeType;

    @TableField(PACKUSERNO)
    private String packUserNo;

    @TableField(PACKUSERNAME)
    private String packUsername;

    @TableField(PACKTIME)
    private Date packTime;

}
