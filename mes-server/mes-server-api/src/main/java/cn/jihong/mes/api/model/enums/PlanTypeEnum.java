package cn.jihong.mes.api.model.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public enum PlanTypeEnum {

    CHANGE_PRODUCTION("1","换产"),
    ON_PRODUCE("2","生产"),
    STATEMENT_ACCOUNT("3","结单"),
    REDO("4","重工"),
    ;

    private String code;

    private String name;

    PlanTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PlanTypeEnum getDataSourceEnum(String code){
        if (StringUtils.isBlank(code)){
            throw new RuntimeException("请输入code");
        }
        for (PlanTypeEnum value : PlanTypeEnum.values()) {
            if (StringUtils.equals(value.getCode(),code)){
                return value;
            }
        }
        throw new RuntimeException("输入的code不存在");
    }
}
