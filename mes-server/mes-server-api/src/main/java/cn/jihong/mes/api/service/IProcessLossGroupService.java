package cn.jihong.mes.api.service;

import java.util.List;

import cn.jihong.mes.api.model.dto.ProcessLossParamGroupDTO;
import cn.jihong.mes.api.model.vo.ProcessLossMesGroupDetailVO;
import cn.jihong.mes.api.model.vo.ProcessLossMesGroupVO;

public interface IProcessLossGroupService {
    List<ProcessLossMesGroupVO> selectProcessLoss(ProcessLossParamGroupDTO processLossParamGroupDTO);
    List<ProcessLossMesGroupDetailVO> selectProcessLossDetail(ProcessLossParamGroupDTO processLossParamGroupDTO);
    String exportToExcel(ProcessLossParamGroupDTO processLossParamGroupDTO);
    String exportToExcelDetail(ProcessLossParamGroupDTO processLossParamGroupDTO);
}
