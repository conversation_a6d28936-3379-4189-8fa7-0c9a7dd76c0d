package cn.jihong.mes.api.service;


import cn.jihong.common.model.Pagination;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.model.vo.*;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 机台表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface IProductionMachineService extends IJiHongService<ProductionMachinePO> {

    /**
     * 查询机台明细
     * @param erpMachineId
     * @return
     */
    ProductionMachineOutVO getProductionMachine(String erpMachineId);

    /**
     * 查询机台明细
     * @param machineName
     * @return
     */
    ProductionMachineOutVO getProductionMachineByName(String machineName,String process);

    /**
     * 查询机台明细
     * @param machineName
     * @return
     */
    List<ProductionMachineOutVO> getProductionMachineByErpName(String machineName);

    /**
     *  查询机台明细
     * @param machineName
     * @return: java.util.List<cn.jihong.mes.api.model.vo.ProductionMachineOutVO>
     * <AUTHOR>
     * @date: 2025-03-27 21:21
     */
    List<ProductionMachineOutVO> getProductionMachineByErpMachineName(String machineName);

    List<ProductionMachineOutVO> getProductionMachineByErpMachineName(String machineName, String companyCode);

    /**
     *  查询机台明细
     * @param processCode
     * @return: java.util.List<cn.jihong.mes.api.model.vo.ProductionMachineOutVO>
     * <AUTHOR>
     * @date: 2025-03-27 21:21
     */
    List<ProductionMachineOutVO> getProductionMachineByProcessCode(String processCode);

    /**
     * 查询机台明细
     * @param erpMachineName
     * @return
     */
    ProductionMachineOutVO getProductionMachineByErpName(String erpMachineName,String process);

    /**
     * 查询机台明细
     * @param erpMachineName
     * @return
     */
    ProductionMachineOutVO getByErpNameProcessCode(String erpMachineName,String processCode);



    /**
     * 修改机台明细
     * @param productionMachineInVO
     */
    void editProductionMachine(ProductionMachineInVO productionMachineInVO);

    /**
     * 查询机台列表
     * @param getProductionMachineListInVO
     * @return
     */
    Pagination<GetProductionMachineListOutVO> getProductionMachineList(GetProductionMachineListInVO getProductionMachineListInVO);

    IPage<ProductionMachinePO> getMachineListByName(IPage page, String machineName);

    IPage<ProductionMachinePO> getMachineListByName(IPage page, String machineName,List<String> ignoreMachineName);

    void syncMachine();

    ProductionMachinePO getMachineById(Long id);

    void editProductionMachineLogisticsConfig(ProductionMachineInVO productionMachineInVO);

    void editProductionMachineConfig(ProductionMachineInVO productionMachineInVO);

    void editProductionMachineOperationConfig(EditProductionMachineOperationConfigInVO inVO);

    ProductionMachineInVO.ProductionMachineConfigVO getProductionMachineConfig(Long id);

    ProductionMachineInVO.ProductionMachineLogisticsConfigVO getProductionMachineLogisticsConfig(Long id);

    List<ProductionMachinePO> getByMachineName(String erpMachineName);

    List<ProductionMachinePO> getListByMachineName(String erpMachineName);

    List<String> getAllMachine();
}
