package cn.jihong.mes.production.api.service.report;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/3/8 11:23
 */
public interface IProjectDailyReportServer {

    /**
     * 获取工程单列表
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetProjectDailyReportsOutVO>
     * <AUTHOR>
     * @date: 2024/3/8 11:30
     */
    Pagination<GetProjectDailyReportsOutVO> getProjectDailyReports(GetProjectDailyReportsInVO inVO);


    /**
     * 工程单日报表-日内
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetPageByDayOutVO>
     * <AUTHOR>
     * @date: 2024/4/23 10:22
     */
    Pagination<GetPageByDayOutVO> getPageByDay(GetPageByDayInVO inVO);

    /**
     * 工程单日报表-日内详情
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetPageByDayDetailOutVO>
     * <AUTHOR>
     * @date: 2024/4/23 10:22
     */
    Pagination<GetPageByDayDetailOutVO> getPageByDayDetail(GetPageByDayDetailInVO inVO);

    /**
     * 工程单日报表-截止
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetPageByEndOutVO>
     * <AUTHOR>
     * @date: 2024/4/19 17:33
     */
    Pagination<GetPageByEndOutVO> getPageByEnd(GetPageByEndInVO inVO);


    /**
     * 工程单日报表-截止详情
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetPageByEndDetailOutVO>
     * <AUTHOR>
     * @date: 2024/4/23 10:22
     */
    Pagination<GetPageByEndDetailOutVO> getPageByEndDetail(GetPageByEndDetailInVO inVO);

}
