package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 入库表操作记录
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
public class ProductStoreRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 入库id
     */
    private Long productStoreId;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 操作类型 10 申请 20 拉货 30 入库
     */
    private String operation;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 单号
     */
    private String docNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑人
     */
    private Long updateBy;

    /**
     * 编辑时间
     */
    private Date updateTime;

}
