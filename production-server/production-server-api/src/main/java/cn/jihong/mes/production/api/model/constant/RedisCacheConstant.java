package cn.jihong.mes.production.api.model.constant;

/**
 * redis缓存域常量类
 *
 * <AUTHOR>
 * Created on 2019/9/24
 */
public class RedisCacheConstant {


    public static final String PREFIX = "production:";

    /**
     * 出站信息
     */
    public static final String OUTBOUND = PREFIX + "temporaryStorageSaveOutboundAndDefective:";

    /**
     * 入库申请信息
     */
    public static final String STORAGE_APPLICATION = PREFIX + "temporaryStoragePushWmsAndErp:";

    /**
     * 入库锁
     */
    public static final String STORAGE  = PREFIX + "storage:lock:";

    /**
     * 报工锁
     */
    public static final String REPORT  = PREFIX + "report:lock:";

    /**
     * 首检锁
     */
    public static final String FIRST_CHECK  = PREFIX + "firstCheck:lock:";

    /**
     * 更新物料锁
     */
    public static final String UPDATE_MATERIAL  = PREFIX + "updateMaterial:lock:";

    /**
     * 不良品申请信息
     */
    public static final String DEFECTIVE = PREFIX + "defective:";

    /**
     * 开始机台任务锁
     */
    public static final String SAVE_MACHINE_TASK  = PREFIX + "saveMachineTask:";

    /**
     * 稼动上报
     */
    public static final String OPERATION_REPORT  = PREFIX + "operationReport:";

    /**
     * 下机锁
     */
    public static final String FINISH_MACHINE_TASK  = PREFIX + "finishMachineTask:";


    /**
     * 人员选择机组历史信息
     */
    public static final String MACHINE_GROUP_OPERATION_HISTORY  = PREFIX + "machineGroupOperationHistory:";

    /**
     * 箱码申请
     */
    public static final String BOX_APPLY  = PREFIX + "boxApply:";

    /**
     * 箱码申请更新
     */
    public static final String BOX_APPLY_UPDATE  = PREFIX + "boxApplyUpdate:";

    /**
     * 箱码打印
     */
    public static final String BOX_PRINT  = PREFIX + "boxPrint:";

    /**
     * 条码拆分
     */
    public static final String SPLIT_BARCODE  = PREFIX + "SplitBarcode:";

    /**
     * 入库信息
     */
    public static final String STORE_REQUEST = PREFIX + "storeRequest:";
    /**
     * 拉货
     */
    public static final String STORE_PULL = PREFIX + "storePull:";
    /**
     * 入库
     */
    public static final String STORE_WAREHOUS = PREFIX + "storeWarehous:";

    /**
     * 默认库位
     */
    public static final String DEFAULT_STORAGE = PREFIX + "userId:defaultStorage:";

    /**
     * 默认仓库
     */
    public static final String DEFAULT_WAREHOUS = PREFIX + "userId:defaultWarehous:";

    /**
     * 倒扣料
     */
    public static final String POUR_USE_MATERIA = PREFIX + "pourUseMateria:";

    /**
     * 物料分摊日结
     */
    public static final String MATERIAL_DAILY = PREFIX + "MaterialDaily:";

    /**
     * 校验箱码
     */
    public static final String VERIFY_BOX_CODE = PREFIX + "VerifyBoxCode:";

    /**
     * 出站锁
     */
    public static final String save_outbound  = PREFIX + "saveOutbound:";


    /**
     * 报不良锁
     */
    public static final String save_defective  = PREFIX + "saveDefective:";

}
