package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchPushWmsAndErpInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 批量栈板
     */
    @NotEmpty(message = "批量栈板不能为空")
    @Size(min = 1, message = "批量栈板不能为空")
    private List<PushWmsAndErpInVO> pushWmsAndErpInVOs;


}
