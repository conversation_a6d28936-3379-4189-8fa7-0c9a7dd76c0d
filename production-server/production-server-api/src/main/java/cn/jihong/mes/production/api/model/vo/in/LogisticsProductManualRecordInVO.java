package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流产品手动出站记录入参
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
public class LogisticsProductManualRecordInVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    private String planTicketNo;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序code
     */
    private String processCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板短码
     */
    private String palletShortCode;

    /**
     * 物料类别
     */
    private String materialCategory;

    /**
     * 栈板一拖的数量
     */
    private Integer palletCount;

    /**
     * 堆长度
     */
    private BigDecimal stackLength;

    /**
     * 堆宽度
     */
    private BigDecimal stackWidth;

    /**
     * 托盘长
     */
    private BigDecimal palletLength;

    /**
     * 托盘宽
     */
    private BigDecimal palletWidth;

    /**
     * 托盘列
     */
    private BigDecimal palletColumns;

    /**
     * 客户简称
     */
    private String customerShortName;

    /**
     * 产品简称
     */
    private String productShortName;

    /**
     * 计划产量
     */
    private Integer planCount;
} 