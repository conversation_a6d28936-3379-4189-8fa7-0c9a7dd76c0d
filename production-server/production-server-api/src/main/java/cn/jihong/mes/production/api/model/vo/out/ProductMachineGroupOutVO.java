package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductMachineGroupOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 机组名称
     */
    private String machineGroupName;

    /**
     * 条数
     */
    private Integer count;

    private List<ProductMachineGroupRelationship> productMachineGroupRelationships;

    @Data
    public static class ProductMachineGroupRelationship implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * erp机台名称
         */
        private String machineName;

    }

}
