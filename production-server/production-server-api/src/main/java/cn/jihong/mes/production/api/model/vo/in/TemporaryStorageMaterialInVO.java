package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TemporaryStorageMaterialInVO implements Serializable {


    private static final long serialVersionUID = 819241487986955081L;
    /**
     * id
     */
    private Long id;

    /**
     * 生产工单id
     */
    private Long productTicketId;


    /**
     * 物料消耗数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal consumptionQuantity = BigDecimal.ZERO;

    /**
     * 剩余数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal remainingQuantity = BigDecimal.ZERO;

    /**
     * 剩余原因
     */
    private String remainingReason;

}
