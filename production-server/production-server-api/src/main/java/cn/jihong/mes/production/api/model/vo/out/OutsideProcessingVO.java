package cn.jihong.mes.production.api.model.vo.out;

import cn.jihong.common.model.dto.BaseWorkflowFormDataDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 外加工
 */
@Data
public class OutsideProcessingVO extends BaseWorkflowFormDataDTO {

    private Integer id;

    /**
     *
     */
    private Long requestId;
    /**
     *申请日期
     */
    private String sqrq;
    /**
     *单号
     */
    private String dh;
    /**
     *申请人
     */
    private Long sqr;
    /**
     *所属公司
     */
    private Integer ssgs;
    /**
     *所属部门
     */
    private Integer ssbm;
    /**
     *员工编号
     */
    private String ygbh;
    /**
     *岗位
     */
    private Integer gw;
    /**
     *
     */
    private String bz;
    /**
     *
     */
    private String fjsc;
    /**
     *相关流程
     */
    private String xglc;
    /**
     *相关文档
     */
    private String xgwd;
    /**
     *
     */
    private Integer manager;
    /**
     *
     */
    private Integer flag;
    /**
     *
     */
    private Integer cjr;
    /**
     *创建人编号
     */
    private String creatorID;
    /**
     *	产品名称
     */
    private String productName;
    /**
     *工作单号
     */
    private String workOrderNumber;
    /**
     *加工工艺
     */
    private String processingTechnology;
    /**
     *外发数量
     */
    private Double externalQuantity;
    /**
     *期望完成时间
     */
    private String expectsCompletionTime;
    /**
     *加工要求及品管确认
     */
    private String processingRequirements;
    /**
     *是否督办
     */
    private Integer supervise;
    /**
     *督办人
     */
    private String supervisor;
    /**
     *归属部门
     */
    private Integer belongingDepartment;
    /**
     *归属公司
     */
    private String belongingCompany;
    /**
     *廊坊一期
     */
    private String lfyq;
    /**
     *廊坊二期
     */
    private String lfeq;
    /**
     *厦门包装工业
     */
    private String xmbzgy;
    /**
     *股份
     */
    private String jtbm;
    /**
     *	加工供应商
     */
    private String processingSupplier;
    /**
     *总加工费
     */
    private BigDecimal totalProcessingFee;
    /**
     *	加工单价
     */
    private BigDecimal processingUnitPrice;
    /**
     *工单号
     */
    private String workOrderId;
    /**
     *工序名称
     */
    private String processName;
    /**
     *供应商id
     */
    private String supplier;
    /**
     *供应商名称
     */
    private String supplierName;
    /**
     *ERP据点
     */
    private String ERPSite;
    /**
     *产品规格
     */
    private String productSpecification;
    /**
     *呼市老厂
     */
    private String hsold;
    /**
     *呼市新厂
     */
    private String hsnew;
    /**
     *工序名称1
     */
    private String processName1;
    /**
     *供应商类型
     */
    private Integer supplierType;

    private List<OutsideProcessingDetailVO> outsideProcessingDetailDTO;

}
