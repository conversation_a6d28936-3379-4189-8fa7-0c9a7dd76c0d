package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class QueryPalletInfoInVO extends PageRequest implements java.io.Serializable {
    private final static long serialVersionUID = 1L;

    /**
     * 入库单号
     */
    private String storeNo;
    /**
     * 栈板码
     */
    private String palletCode;
    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 入库类型 1 按箱入库 2 按托入库
     */
    private Integer storeType;

    /**
     * 入库申请单号
     */
    private String storeApplyNo;

    /**
     * 拉货确认单号
     */
    private String storePulledNo;

    /**
     * 入库状态 10 已申请 20 已拉货 30 已入库
     */
    private String storeStatus;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    private String companyCode;

    /**
     * 箱码
     */
    private String boxCode;

}
