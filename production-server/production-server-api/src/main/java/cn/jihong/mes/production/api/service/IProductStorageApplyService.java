package cn.jihong.mes.production.api.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.po.ProductStorageApplyPO;
import cn.jihong.mes.production.api.model.vo.in.GetStorageApplyInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * 入库申请表 服务类
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
public interface IProductStorageApplyService extends IJiHongService<ProductStorageApplyPO> {

    Page<GetStorageApplyInfoOutVO> getStorageApplyInfo(GetStorageApplyInfoInVO getStorageApplyInfoInVO);

    String getPlanTicketNoByPalletCode(String palletCode);
}
