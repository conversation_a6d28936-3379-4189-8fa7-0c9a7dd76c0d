package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/20 14:22
 */
@Data
public class GetSettlementEndProductionCollectDetailOutVO implements Serializable {
    private static final long serialVersionUID = -8529356624813025634L;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 栈板编码
     */
    private String palletCode;

    /**
     * 出站日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date outboundTime;

    /**
     * 出站班次
     */
    private Integer outboundShift;

    /**
     * 出站数量
     */
    private BigDecimal productQuantity;

    /**
     * 用料日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date loadingTime;

    /**
     * 用料班次
     */
    private Integer palletShift;

    /**
     * 用料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 出站id
     */
    private Long outboundId;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 生产工单关联的 用料生产工单id
     */
    private Long requisitionTicketId;



}
