package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ApplyProductInnerLabelInVO implements java.io.Serializable {

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;


    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String customerName;


    /**
     * 生产工程单号
     */
    @NotBlank(message = "生产工程单号不能为空")
    private String planTicketNo;


    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;


    /**
     * 料号
     */
    @NotBlank(message = "产品编号不能为空")
    private String materialCode;


    /**
     * 箱码总数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量不能小于1")
    private Long barcodeTotal;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "生产日期不能为空")
    private Date productionDate;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "保质期不能为空")
    private Date expirationDate;

    /**
     * 模板ID
     */
    @NotBlank(message = "模板ID不能为空")
    private String templateId;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;


    /**
     * 作废状态 0 作废   1 未作废
     */
    private Integer disableStatus;



}
