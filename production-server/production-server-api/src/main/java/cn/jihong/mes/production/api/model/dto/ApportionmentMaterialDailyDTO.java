package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ApportionmentMaterialDailyDTO implements java.io.Serializable {

    /**
     * 机位
     */
    private String parts;
    /**
     * 部位
     */
    private String place;
    /**
     * 物料code
     */
    private String materialCode;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 工序编号
     */
    private String process;
    private String processCode;
    /**
     * 标准消耗
     */
    private BigDecimal standardConsumption;
    /**
     * 总产量
     */
    private BigDecimal allYield;
    /**
     * 标准消耗*总产量
     */
    private BigDecimal standardConsumptionYield;
    /**
     * 消耗占比
     */
    private BigDecimal consumptionPercentage;

    /**
     * 实际消耗
     */
    private BigDecimal realConsumption;

    /**
     * 任务剩余需要扣除的数量
     */
    private BigDecimal remainingConsumption = BigDecimal.ZERO;

}
