package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机台工单关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-05
 */
@Getter
@Setter
@TableName("product_machine_ticket_relation")
public class ProductMachineTicketRelationPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    public static final String LAST_TICKET_ID = "last_ticket_id";
    public static final String CURRENT_TICKET_ID = "current_ticket_id";
    public static final String STATUS = "status";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 上一个生产工单
     */
    @TableField(LAST_TICKET_ID)
    private Long lastTicketId;


    /**
     * 当前生产工单id
     */
    @TableField(CURRENT_TICKET_ID)
    private Long currentTicketId;


    /**
     * 状态 0 已结单  1 未结单
     */
    @TableField(STATUS)
    private Integer status;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
