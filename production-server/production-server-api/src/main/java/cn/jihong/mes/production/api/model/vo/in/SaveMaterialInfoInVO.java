package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保存物料信息
 * 
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class SaveMaterialInfoInVO extends ProductTicketVO implements Serializable {

    /**
     * 机台止码
     */
    private Integer machineStopNo;

    /**
     * 上料信息
     */
    @NotNull(message = "上料信息不能为空")
    @Valid
    public MaterialInfo upMaterialInfo;


    @Data
    public static class MaterialInfo {

        /**
         * id
         */
        private Long id;

        /**
         * 物料编号
         */
        @NotBlank(message = "物料编号不能为空")
        private String materialBarcodeNo;

        /**
         * 物料code
         */
        @NotBlank(message = "物料code不能为空")
        private String materialCode;

        /**
         * 物料名称
         */
        @NotBlank(message = "物料名称不能为空")
        private String materialName;

        /**
         * 工序
         */
        private String process;

        /**
         * 物料类别
         */
        private String materialType;

        /**
         * 物料部件（用途）
         */
//        @NotNull(message = "物料部件（用途）不能为空")
        private String materialPlace;

        /**
         * 物料部件名称（用途名称）
         */
//        @NotNull(message = "物料部件名称（用途名称）不能为空")
        private String materialPlaceName;

        /**
         * 物料单位
         */
        private String materialUnit;

        /**
         * 物料入库时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date materialWarehousingTime;


        /**
         * 上料数量
         */
        private BigDecimal loadingQuantity;

        /**
         * 物料消耗数量
         */
        private BigDecimal consumptionQuantity;

        /**
         * 剩余数量
         */
        private BigDecimal remainingQuantity;

        /**
         * 剩余原因
         */
        private String remainingReason;

        /**
         * 状态
         */
        private Integer status;

        /**
         * 采购批次
         */
        private String purchaseBatch;

        private String parts;

        private String partsName;


    }

}
