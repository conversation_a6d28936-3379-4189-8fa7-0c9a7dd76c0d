package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/15 11:08
 */
@Data
public class GetProductTicketInfoListOutVO implements Serializable {
    private static final long serialVersionUID = -4898676850922529342L;

    /**
     * 机台名称带编号
     */
    private String erpMachineName;

    /**
     * 工程单号
     */
    private String workerOrderNo;

    /**
     * 计划生产日期
     */
    private Date productionPlanDate;

    /**
     * 产品名称
     */
    private String productionName;

    /**
     * 班次
     */
    private Integer serialNo;

    /**
     * 计划开始时间
     */
    private Date productionPlanStartTime;

    /**
     * 计划结束时间
     */
    private Date productionPlanEndTime;

    /**
     * 计划产量
     */
    private Double plannedProductionCapacity;

    /**
     * 工序代码
     */
    private String productionProcessCode;

    /**
     * 工序类型
     */
    private String productionProcessType;

    /**
     * 工序名称
     */
    private String productionProcess;

}
