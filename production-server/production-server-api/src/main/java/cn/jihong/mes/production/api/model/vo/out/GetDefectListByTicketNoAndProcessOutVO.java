package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/22 14:33
 */
@Data
public class GetDefectListByTicketNoAndProcessOutVO implements Serializable {
    private static final long serialVersionUID = -7531669558892109921L;

    /**
     * 工序名称
     */
    private String process;


    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

}
