package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 一日一结机台物料消耗记录信息
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Getter
@Setter
@TableName("product_machine_material_record")
public class ProductMachineMaterialRecordPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PRODUCE_DATE = "produce_date";
    public static final String SHIFT = "shift";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";
    public static final String MATERIAL_BARCODE_NO = "material_barcode_no";
    public static final String MATERIAL_CODE = "material_code";
    public static final String MATERIAL_NAME = "material_name";
    public static final String MATERIAL_UNIT = "material_unit";
    public static final String CONSUMPTION_QUANTITY = "consumption_quantity";
    public static final String REMAINING_QUANTITY = "remaining_quantity";
    public static final String MATERIAL_TYPE = "material_type";
    public static final String MATERIAL_PLACE = "material_place";
    public static final String MATERIAL_PLACE_NAME = "material_place_name";
    public static final String PURCHASE_BATCH = "purchase_batch";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String PROCESS = "process";
    public static final String PROCESS_CODE = "process_code";
    public static final String MATERIAL_ID = "material_id";
    public static final String MATERIAL_OPERATION_ID = "material_operation_id";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String USE_TYPE = "use_type";
    public static final String PARTS = "parts";
    public static final String PARTS_NAME = "parts_name";
    public static final String MATERIAL_SEQ = "material_seq";

    public static final String WAREHOUSE_NO = "warehouse_no";
    public static final String WAREHOUSE_NAME = "warehouse_name";
    public static final String STORAGE_NO = "storage_no";
    public static final String STORAGE_NAME = "storage_name";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 生产日期
     */
    @TableField(PRODUCE_DATE)
    private Date produceDate;


    /**
     * 班次
     */
    @TableField(SHIFT)
    private Integer shift;


    /**
     * 生产工程单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;


    /**
     * 物料编号
     */
    @TableField(MATERIAL_BARCODE_NO)
    private String materialBarcodeNo;


    /**
     * 物料code
     */
    @TableField(MATERIAL_CODE)
    private String materialCode;


    /**
     * 物料名称
     */
    @TableField(MATERIAL_NAME)
    private String materialName;


    /**
     * 物料单位
     */
    @TableField(MATERIAL_UNIT)
    private String materialUnit;


    /**
     * 物料消耗数量
     */
    @TableField(CONSUMPTION_QUANTITY)
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    @TableField(REMAINING_QUANTITY)
    private BigDecimal remainingQuantity;


    /**
     * 物料类别
     */
    @TableField(MATERIAL_TYPE)
    private String materialType;


    /**
     * 物料部件(用途)
     */
    @TableField(MATERIAL_PLACE)
    private String materialPlace;


    /**
     * 物料部件名称（用途名称）
     */
    @TableField(MATERIAL_PLACE_NAME)
    private String materialPlaceName;


    /**
     * 采购批次
     */
    @TableField(PURCHASE_BATCH)
    private String purchaseBatch;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;


    /**
     * 工序名称
     */
    @TableField(PROCESS)
    private String process;


    /**
     * 工序code
     */
    @TableField(PROCESS_CODE)
    private String processCode;


    /**
     * 物料id
     */
    @TableField(MATERIAL_ID)
    private Long materialId;


    /**
     * 物料操作id
     */
    @TableField(MATERIAL_OPERATION_ID)
    private Long materialOperationId;

    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;


    /**
     * 物料使用类型：  10 正扣料  20 分摊  30 倒扣料
     */
    @TableField(USE_TYPE)
    private Integer useType;


    @TableField(PARTS)
    private String parts;

    @TableField(PARTS_NAME)
    private String partsName;

    /**
     * 物料序列
     */
    @TableField(MATERIAL_SEQ)
    private String materialSeq;

    /**
     * 仓库编号
     */
    @TableField(WAREHOUSE_NO)
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @TableField(WAREHOUSE_NAME)
    private String warehouseName;

    /**
     * 储位编号
     */
    @TableField(STORAGE_NO)
    private String storageNo;

    /**
     * 储位名称
     */
    @TableField(STORAGE_NAME)
    private String storageName;

}
