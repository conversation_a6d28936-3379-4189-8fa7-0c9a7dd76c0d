package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DailySettlementMaterialInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;


    private List<DailySettlementMaterial> dailySettlementMaterials;


    @Valid
    @Data
    public static class DailySettlementMaterial implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotNull(message = "物料ID不能为空")
        private Long id;

        /**
         * 剩余数量
         */
        @NotNull(message = "剩余数量不能为空")
        private BigDecimal remainingQuantity;

    }


}
