package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/19 17:31
 */
@Data
public class GetPageByEndInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = -2510345761369274075L;

    /**
     * 工程单状态 0:所有  1：进行中
     */
    @NotBlank(message = "工程单状态不能为空")
    private String workOrderStatus;

    /**
     * 工厂代码
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 截止日期
     */
    private String endDate;

}
