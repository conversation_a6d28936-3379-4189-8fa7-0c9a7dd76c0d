package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-18 15:13
 */
@Data
public class PostOrderInVO implements Serializable {

    /**
     * 工单号
     */
    @NotBlank(message = "工单号不能为空")
    private String planTicketNo;

    /**
     * 工艺编码
     */
    @NotBlank(message = "工艺编码不能为空")
    private String processCode;

    /**
     * 排程单号
     */
    @NotBlank(message = "排程单号不能为空")
    private String schno;

}
