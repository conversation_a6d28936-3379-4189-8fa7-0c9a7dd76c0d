package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum MaterialEnum {


    IN_LIBRARY(1, "在库"),
    IN_PRODUCTION(2, "在产"),
    TEMPORARY_STORAGE(3, "暂存"),
    PREPARE(4, "备料"),
    ;

    private Integer code;

    private String name;

    MaterialEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MaterialEnum getMaterialEnum(Integer code){
        for (MaterialEnum value : MaterialEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }
}
