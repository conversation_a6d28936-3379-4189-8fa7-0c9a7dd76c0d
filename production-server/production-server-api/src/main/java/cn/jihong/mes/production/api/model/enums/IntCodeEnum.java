package cn.jihong.mes.production.api.model.enums;


public interface IntCodeEnum {

    default Integer getIntCode() {
        // 如果枚举类实现了 getCode() 方法，则将 String 类型的 code 转换为 Integer
        if (this instanceof Enum<?>) {
            Enum<?> enumValue = (Enum<?>) this;
            Object codeValue = getCode(enumValue);
            if (codeValue instanceof String) {
                return Integer.valueOf((String) codeValue);
            }
            if (codeValue instanceof Integer) {
                return (Integer) codeValue;
            }
        }
        throw new IllegalStateException("Cannot get integer code for enum value: " + this);
    }

    // 获取枚举值的 code
    <T extends Enum<?>> Object getCode(T enumValue);

}
