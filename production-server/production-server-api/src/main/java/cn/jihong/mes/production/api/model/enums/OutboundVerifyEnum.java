package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum OutboundVerifyEnum {

    BASE_PAPER(0, "原纸"),
    ;

    private Integer code;

    private String name;

    OutboundVerifyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OutboundVerifyEnum getMaterialEnum(Integer code) {
        for (OutboundVerifyEnum value : OutboundVerifyEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的物料");
    }

}
