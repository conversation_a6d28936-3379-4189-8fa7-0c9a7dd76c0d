package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class ProductMachineGroupInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 机组名称
     */
    @NotBlank(message = "机组名称不能为空")
    private String machineGroupName;

    private List<ProductMachineGroupInVO.ProductMachineGroupRelationship> productMachineGroupRelationships;


    @Data
    public static class ProductMachineGroupRelationship implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * erp机台名称
         */
        private String machineName;

    }

}