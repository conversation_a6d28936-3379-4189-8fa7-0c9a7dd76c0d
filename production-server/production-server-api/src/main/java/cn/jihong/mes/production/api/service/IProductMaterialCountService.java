package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.ProductMaterialCountDTO;
import cn.jihong.mes.production.api.model.po.ProductMaterialCountPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 机台上料和上栈板的数量 服务类
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
public interface IProductMaterialCountService extends IJiHongService<ProductMaterialCountPO> {

    /**
     * 根据processtype查询机台的可上栈板数量
     * @param processtype
     * @param palletSource
     * @return
     */
    ProductMaterialCountDTO getPalletCountCount(String processtype, String palletSource);

    /**
     * 根据processtype查询机台的可上料数量
     * @param processtype
     * @param materialType
     * @return
     */
    ProductMaterialCountDTO getMaterialCountCount(String processtype, String materialType,String materialPlace);

    List<ProductMaterialCountDTO> GetList();

    void doAdd(ProductMaterialCountDTO ProductMaterialCountDTO);

    void doUpdate(ProductMaterialCountDTO ProductMaterialCountDTO);

    void doDelete(Integer id);
}
