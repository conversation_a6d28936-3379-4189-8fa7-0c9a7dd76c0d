package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-22 16:46
 */
@Data
public class DeviceOEEReportShowOutVO implements Serializable {


    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 每天的比率
     */
    private List<DayRate> dayRateList;

    @Data
     public static class DayRate implements Serializable {

        /**
         * 生产日期
         */
        private String produceDate;


        /**
         * 时间开动率
         */
        private BigDecimal timeActuationRate;

        /**
         * 性能开动率
         */
        private BigDecimal performanceRate;


        /**
         * 良品率
         */
        private BigDecimal goodProductRate;

        /**
         * OEE
         */
        private BigDecimal oeeRate;

    }








}
