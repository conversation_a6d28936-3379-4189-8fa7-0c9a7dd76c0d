package cn.jihong.mes.production.api.service;


import cn.jihong.mes.production.api.model.dto.ProductMachineTicketRelationDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineTicketRelationPO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 机台工单关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-05
 */
public interface IProductMachineTicketRelationService extends IJiHongService<ProductMachineTicketRelationPO> {

    void createRelation(ProductMachineTicketRelationDTO productMachineTicketRelationDTO);

    void stopRelation(ProductMachineTicketRelationDTO productMachineTicketRelationDTO);

    ProductMachineTicketRelationPO getByProductTicketId(Long productTicketId);

    ProductMachineTicketRelationPO getByMachineName(String machineName);
}
