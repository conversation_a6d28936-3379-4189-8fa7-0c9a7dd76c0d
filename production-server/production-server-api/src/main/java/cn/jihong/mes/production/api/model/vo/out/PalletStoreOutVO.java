package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 栈板入库信息
 */
@Data
public class PalletStoreOutVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 箱码
     */
    private String caseCode;

    /**
     * 已生产
     */
    private BigDecimal producedQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 入库类型 1 按箱入库 2 按托入库
     */
    private Integer storeType;

    /**
     * 入库申请单号
     */
    private String storeApplyNo;

    /**
     * 入库单号
     */
    private String storeNo;

    /**
     * 入库状态 10 已申请 20 已拉货 30 已入库
     */
    private String storeStatus;

    /**
     * 储位
     */
    private String storage;

    /**
     * 规格
     */
    private String standard;

    /**
     * 批次号
     */
    private String lotNo;

    /**
     * 箱数
     */
    private Integer boxCount;

}
