package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ChangeMaterialInVO extends ProductTicketVO implements Serializable {

    /**
     * 机台止码
     */
    private Integer machineStopNo;

    /**
     * 上料信息
     */
    public SaveMaterialInfoInVO.MaterialInfo upMaterialInfo;

    /**
     * 当前用料
     */
    public MaterialInfo currentMaterialInfo;

    @Data
    public static class MaterialInfo {

        /**
         * id
         */
        private Long id;

        /**
         * 物料消耗数量
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private BigDecimal consumptionQuantity = BigDecimal.ZERO;

        /**
         * 剩余数量
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private BigDecimal remainingQuantity = BigDecimal.ZERO;

        /**
         * 剩余原因
         */
        private String remainingReason;

    }

}
