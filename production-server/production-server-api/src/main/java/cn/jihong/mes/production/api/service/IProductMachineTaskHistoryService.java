package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskHistoryPO;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetChangeVersionHistoryPageOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetChangeVersionInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskHistoryPageOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 生产机台任务操作历史 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
public interface IProductMachineTaskHistoryService extends IJiHongService<ProductMachineTaskHistoryPO> {

    /**
     * 获取请求OA超产单相关参数
     * @param inVO
     * @return: cn.jihong.workflow.common.model.request.SubmitWorkflowRequest
     * <AUTHOR>
     * @date: 2024-09-12 17:18
     */
    StandardResult sendOverProductionFlow(SendOverProductionFlowInVO inVO);

    Boolean saveMachineTaskHistory(ProductMachineTaskPO productMachineTaskPO, BigDecimal reportedQuantity,String reason,Long productOutboundId);

    Pagination<GetProductMachineTaskHistoryPageOutVO> getProductMachineTaskHistoryPage(GetProductMachineTaskHistoryPageInVO inVO);

    Boolean updateMachineTaskHistory(UpdateMachineTaskHistoryInVO inVO);

    List<ProductMachineTaskHistoryPO> getHistoryListByProductMachineTaskId(Long productMachineTaskId,Integer type);


    List<GetChangeVersionInfoOutVO> getChangeVersionInfo(GetChangeVersionInfoInVO inVO);

    Pagination<GetChangeVersionHistoryPageOutVO> getChangeVersionHistoryPage(GetChangeVersionHistoryPageInVO inVO);

    Boolean updateChangeVersion(UpdateChangeVersionInVO inVO);

    List<ProductMachineTaskHistoryPO> getListByProductTicketIds(Integer type,List<Long> productTicketIdList);

    List<ProductMachineTaskHistoryPO> getSubmitApprovalList(Long productTicketId,Integer workflowRequestStatus);

    ProductMachineTaskHistoryPO getSingleByWorkRequestId(Long workRequestId);

    ProductMachineTaskHistoryPO updateByOutboundId(UpdateOutboundInVO updateOutboundInVO);

    List<ProductMachineTaskHistoryPO> getListByProductMachineTaskId(Long productMachineTaskId);

}
