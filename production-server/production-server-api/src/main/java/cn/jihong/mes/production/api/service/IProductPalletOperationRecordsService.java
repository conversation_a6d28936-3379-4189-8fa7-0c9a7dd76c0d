package cn.jihong.mes.production.api.service;


import cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductPalletOperationRecordsPO;
import cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 栈板操作记录信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface IProductPalletOperationRecordsService extends IJiHongService<ProductPalletOperationRecordsPO> {

    /**
     * 保存栈板操作记录
     * @param productPalletOperationRecordsDTO
     */
    void saveOrUpdatePalletOperationRecords(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO);

    /**
     * 通用操作
     * @param productPalletOperationRecordsDTO
     * @param code
     */
    void work(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO,String code);

    /**
     * 报工
     *
     * @param productPalletOperationRecordsDTO
     */
    void signingUpWork(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO);

    /**
     * 出站
     *
     * @param productPalletOperationRecordsDTO
     */
    void outBound(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO);

    /**
     * 下栈板
     *
     * @param productPalletOperationRecordsDTO
     */
    void downPallet(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) ;

    /**
     * 上栈板
     *
     * @param productPalletOperationRecordsDTO
     */
    void upPallet(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO);

    Page<LastPalletRecordsOutVO> getLastPalletRecords(IPage page, Long productTicketId);

    Page<LastPalletRecordsOutVO> getLastPalletRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    List<ProductPalletOperationRecordsDTO> getByProductTicketIds(List<Long> productTicketIds, List<Integer> operationTypes);

    ProductPalletOperationRecordsPO getOutBoundByPalletId(Long palletId);

    Page<LastPalletRecordsOutVO> getDownPalletRecords(IPage page, Long productTicketId);
}
