package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductInfoPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.ProductInfoPageInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketShiftInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 生产工程单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
public interface IProductInfoService extends IJiHongService<ProductInfoPO> {

    /**
     * 创建/更新生产工程单信息
     */
    ProductInfoPO createOrUpdateProductInfo(Long productTicketId, String firstCheckResult);

    Pagination<ProductTicketShiftOutVO> getProductInfoList(ProductTicketShiftInVO productTicketShiftInVO);

    /**
     * 获得生产工单
     */
    Pagination<ProductionTicketInfoOutVO> getProductTicket(ProductInfoPageInVO productInfoPageInVO);

    /**
     * 获得上料记录
     */
    Pagination<MaterialRecordsOutVO> getMaterialRecords(ProductInfoPageInVO productInfoPageInVO);

    /**
     * 获得出站记录
     */
    Pagination<OutboundInfoOutVO> getOutboundRecords(ProductInfoPageInVO productInfoPageInVO);

    /**
     * 获得不良品记录
     */
    Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsRecords(ProductInfoPageInVO productInfoPageInVO);

    /**
     * 获得上栈板记录
     */
    Pagination<LastPalletRecordsOutVO> getLastPalletRecords(ProductInfoPageInVO productInfoPageInVO);

    ProductTicketShiftOutVO getProductInfo(Long id);

    /**
     * 查询班组信息
     * @param productTicketPO
     * @return
     */
    ProductInfoPO getProductInfo(ProductTicketPO productTicketPO);

    Boolean verifyFirstCheckCompleted(Long productTicketId);

    /**
     * 将之前的首件结果置空
     * @param id
     */
    void updateFirstCheckResult(Long id);
}
