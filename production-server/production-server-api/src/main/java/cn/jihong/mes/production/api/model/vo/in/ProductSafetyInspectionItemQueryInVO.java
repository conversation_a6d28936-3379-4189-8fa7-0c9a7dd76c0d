package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 安全点检项配置 查询入参
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductSafetyInspectionItemQueryInVO extends PageRequest {

    /**
     * 点检项编码
     */
    private String inspectionCode;

    /**
     * 点检项名称
     */
    private String inspectionName;

    /**
     * 状态(0-停用，1-启用)
     */
    private Integer status;
} 