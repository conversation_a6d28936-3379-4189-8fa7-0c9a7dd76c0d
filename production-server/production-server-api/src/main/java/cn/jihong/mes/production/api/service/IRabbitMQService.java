package cn.jihong.mes.production.api.service;


import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import org.apache.commons.lang3.StringUtils;

public interface IRabbitMQService {

    String getRabbitMQType();

    void sendMessage(MessageStructDTO msg);

    default void sendMessage(MessageStructDTO msg,String ttlTimes) {
        if (StringUtils.isBlank(ttlTimes)) {
            sendMessage(msg);
        }
    }

}
