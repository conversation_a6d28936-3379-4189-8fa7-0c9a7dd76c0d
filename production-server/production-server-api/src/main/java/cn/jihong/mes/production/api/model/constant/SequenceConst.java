package cn.jihong.mes.production.api.model.constant;

public class SequenceConst {

    // 生产订单序列
    public static final String ORDER_SEQUENCE_PREFIX = "#MES#";
    public static final String ORDER_KEY = "barcode:sequence";
    public static final String LOCK_KEY = "barcode:lock";

    // 入库序列
    public static final String STORE_SEQUENCE_PREFIX = "RKSQ"; // 入库申请
    public static final String STORE_ORDER_KEY = "rksq:sequence";
    public static final String STORE_LOCK_KEY = "rksq:lock";

    // 拉货序列
    public static final String PULLED_SEQUENCE_PREFIX = "LHQR"; // 拉货确认
    public static final String PULLED_ORDER_KEY = "lhqr:sequence";
    public static final String PULLED_LOCK_KEY = "lhqr:lock";


    // 创建栈板ID
    public static final String CREATE_PALLET_SEQUENCE_PREFIX = "PALLTET"; // 创建栈板
    public static final String CREATE_PALLET_ORDER_KEY = "pallet:sequence";
    public static final String CREATE_PALLET_LOCK_KEY = "pallet:lock";

    // 创建短码
    public static final String CREATE_SHORT_PALLET_SEQUENCE_PREFIX = "PALLTET"; // 创建栈板短码
    public static final String CREATE_SHORT_PALLET_ORDER_KEY = "pallet:short:sequence";
    public static final String CREATE_SHORT_PALLET_LOCK_KEY = "pallet:short:lock";


}
