package cn.jihong.mes.production.api.model.vo.out.logistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-14 11:06
 */
@Data
public class GetLatestTileWireDatOutVO implements Serializable {

    /**
     * 订单编号，标识当前订单
     */
    private String dingDanHaoMa;

    /**
     * 班别，记录生产班次
     */
    private String banBie;

    /**
     * 不良，记录当前订单的不良品数量
     */
    private Integer benDanBuLiangZhangShu;


    /**
     * 采集时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acquisitionTime;

}
