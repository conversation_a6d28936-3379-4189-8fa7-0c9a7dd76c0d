package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefectiveReasonsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO;
import cn.jihong.mes.production.api.model.vo.out.SaveDefectiveProductsInfoOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 不良品记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductDefectiveProductsService extends IJiHongService<ProductDefectiveProductsPO> {

    /**
     * 保存不良品信息
     */
    SaveDefectiveProductsInfoOutVO saveDefectiveProductsInfo(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO);
    /**
     * 保存不良品信息   --- 不包含seata全局事务
     */
    SaveDefectiveProductsInfoOutVO saveDefectiveProductsInfoNoSeata(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO);


    Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsRecords(ProductTicketPageInVO productTicketPageInVO);

    /**
     * 查询不良原因
     */
    List<GetDefectiveReasonsOutVO> getEcffucTList(Long productTicketId);

    /**
     * 获得不良品记录
     */
    Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    /**
     * 查询工序信息列表
     * @param productTicketId
     * @return
     */
    Object getProcessList(Long productTicketId);

    /**
     * 获取不良品汇总
     * @param inVO
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO>
     * <AUTHOR>
     * @date: 2023/11/17 10:50
     */
    List<GetListByDefectiveSourceNameOutVO> getListByDefectiveSourceName(GetListByDefectiveSourceNameInVO inVO);

    /**
     * 质检回调更新不良品信息
     */
    void qualityCheckUpdatesDefective(List<QualityCheckUpdatesDefectiveInVO> qualityCheckUpdatesDefectiveInVOs);

    List<ProductDefectiveProductsPO> getListByProductTicketId(Long productTicketId);

    void saveDefectiveProductsInfoByPC(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO);

    void updateDefectiveProductsInfo(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO);

    List<GetDefectiveReasonsOutVO> getDefectiveReasons(GetDefectiveReasonsInVO getDefectiveReasonsInVO);

    List<EnumDTO> getLossType(@Valid GetDefectiveReasonsInVO getDefectiveReasonsInVO);

    List<String> getDefectiveType(@Valid GetDefectiveReasonsInVO getDefectiveReasonsInVO);
}
