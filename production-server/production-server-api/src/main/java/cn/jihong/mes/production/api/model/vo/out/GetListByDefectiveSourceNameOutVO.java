package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/16 17:51
 */
@Data
public class GetListByDefectiveSourceNameOutVO implements Serializable {
    private static final long serialVersionUID = 8560798403524126616L;


    /**
     * 工序名称
     */
    private String defectiveProductsSourceName;

    /**
     * 实际生产数量
     */
    private BigDecimal realProduct;


    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

    /**
     * 损耗率(%)
     */
    private BigDecimal attritionRate;

}
