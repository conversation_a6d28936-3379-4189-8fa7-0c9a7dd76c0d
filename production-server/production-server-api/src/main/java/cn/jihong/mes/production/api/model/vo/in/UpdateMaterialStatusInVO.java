package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdateMaterialStatusInVO implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;


    /**
     * 操作  10 上料  20 下料
     */
    @NotNull(message = "操作不能为空")
    private String operation;

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

}
