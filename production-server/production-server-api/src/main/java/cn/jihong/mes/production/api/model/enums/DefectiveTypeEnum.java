package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum DefectiveTypeEnum implements IntCodeEnum{

    // 0  白片  1 彩片
    BLACK_MATERIAL("0", "白片"),
    COLOR_MATERIAL("1", "彩片"),
    ;

    private String code;

    private String name;

    DefectiveTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DefectiveTypeEnum getDefectiveTypeEnum(String code) {
        for (DefectiveTypeEnum value : DefectiveTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((DefectiveTypeEnum) enumValue).getCode();
    }
}
