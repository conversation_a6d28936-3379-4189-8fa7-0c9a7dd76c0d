package cn.jihong.mes.production.api.model.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class ProductMachineTicketDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 工程单号
     */
    private String planTicketNo;


    /**
     * 绑定状态：0-解除绑定，1-已绑定
     */
    private String status;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 修改人
     */
    private Long updateBy;


    /**
     * 修改时间
     */
    private Date updateTime;


    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
}
