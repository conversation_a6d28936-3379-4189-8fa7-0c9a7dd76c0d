package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 物流产品手动出站记录分组输出
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
public class LogisticsProductManualRecordGroupOutVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    private String planTicketNo;
    
    /**
     * 物料类别
     */
    private String materialCategory;
    
    /**
     * 物料类别名称
     */
    private String materialCategoryName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 记录列表
     */
    private List<LogisticsProductManualRecordOutVO> records;
} 