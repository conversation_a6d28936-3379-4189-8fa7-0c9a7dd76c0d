package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:09
 */
@Data
public class GetSettlementWorkReportDetailOutVO implements Serializable {
    private static final long serialVersionUID = -2574156938631633473L;



    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productDate;

    /**
     * 实际生产数量
     */
    private BigDecimal realProduct;


    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

    /**
     * 损耗率(%)
     */
    private BigDecimal attritionRate;

}
