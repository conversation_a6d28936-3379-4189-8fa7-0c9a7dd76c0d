package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/17 17:49
 */
@Data
public class GetSettlementMaterialDetailOutVO implements Serializable {
    private static final long serialVersionUID = 5036645183154707540L;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 使用部位
     */
    private String materialPlaceName;


    /**
     * 用料数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 计划用量
     */
    private BigDecimal loadingQuantity;

    /**
     * 计划产量
     */
    private BigDecimal plannedProduct;


    /**
     * 实际产量
     */
    private BigDecimal realProduct;

    /**
     * 损耗率（%）
     */
    private BigDecimal attritionRate;


}
