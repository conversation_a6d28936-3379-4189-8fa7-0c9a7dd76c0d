package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料操作记录信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("product_material_operation_records")
public class ProductMaterialOperationRecordsPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String PRODUCT_MATERIAL_ID = "product_material_id";
    public static final String OPERATION_TYPE = "operation_type";
    public static final String TICKET_TYPE = "ticket_type";
    public static final String TICKET_NUMBER = "ticket_number";
    public static final String OPERATOR = "operator";
    public static final String ORIGINAL_QUANTITY = "original_quantity";
    public static final String CONSUMPTION_QUANTITY = "consumption_quantity";
    public static final String REMAINING_QUANTITY = "remaining_quantity";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String MATERIAL_BARCODE_NO = "material_barcode_no";
    public static final String MACHINE_STOP_NO = "machine_stop_no";
    public static final String REMAINING_REASON = "remaining_reason";
    public static final String REQUEST_ID = "request_id";

    public static final String TRANSFER_NO = "transfer_no";
    public static final String DEDUCTION_NO = "deduction_no";

    public static final String WORKFLOW_REQUEST_ID = "workflow_request_id";
    public static final String WORKFLOW_REQUEST_STATUS = "workflow_request_status";

    public static final String WAREHOUSE_NO = "warehouse_no";
    public static final String WAREHOUSE_NAME = "warehouse_name";
    public static final String STORAGE_NO = "storage_no";
    public static final String STORAGE_NAME = "storage_name";



    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 操作类型
     */
    @TableField(OPERATION_TYPE)
    private String operationType;

    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;

    /**
     * 物料表id
     */
    @TableField(PRODUCT_MATERIAL_ID)
    private Long productMaterialId;


    /**
     * 工单类型
     */
    @TableField(TICKET_TYPE)
    private String ticketType;


    /**
     * 工单编号
     */
    @TableField(TICKET_NUMBER)
    private String ticketNumber;


    /**
     * 操作人员
     */
    @TableField(OPERATOR)
    private Long operator;


    /**
     * 原数量
     */
    @TableField(ORIGINAL_QUANTITY)
    private BigDecimal originalQuantity;


    /**
     * 消耗数量
     */
    @TableField(CONSUMPTION_QUANTITY)
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    @TableField(REMAINING_QUANTITY)
    private BigDecimal remainingQuantity;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 物料编号
     */
    @TableField(MATERIAL_BARCODE_NO)
    private String materialBarcodeNo;

    /**
     * 设备止码
     */
    @TableField(MACHINE_STOP_NO)
    private Integer machineStopNo;

    /**
     * 剩余原因
     */
    @TableField(REMAINING_REASON)
    private String remainingReason;

    /**
     * 请求id，用于记录请求到外部服务的id
     */
    @TableField(REQUEST_ID)
    private String requestId;


    /**
     * 调拨单号
     */
    @TableField(TRANSFER_NO)
    private String transferNo;

    /**
     * 扣料单号
     */
    @TableField(DEDUCTION_NO)
    private String deductionNo;


    /**
     * OA审批流id
     */
    @TableField(WORKFLOW_REQUEST_ID)
    private Long workflowRequestId;

    /**
     * OA审批流状态
     */
    @TableField(WORKFLOW_REQUEST_STATUS)
    private Integer workflowRequestStatus;

    /**
     * 仓位编号
     */
    @TableField(WAREHOUSE_NO)
    private String warehouseNo;

    /**
     * 仓位库名称
     */
    @TableField(WAREHOUSE_NAME)
    private String warehouseName;

    /**
     * 储位编号
     */
    @TableField(STORAGE_NO)
    private String storageNo;

    /**
     * 储位名称
     */
    @TableField(STORAGE_NAME)
    private String storageName;

}
