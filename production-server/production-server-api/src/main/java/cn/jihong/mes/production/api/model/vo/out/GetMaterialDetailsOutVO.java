package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetMaterialDetailsOutVO implements Serializable {

    /**
     * 工序
     */
    private String process;
    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 物料列表
     */
    private List<MaterialInfo> materialInfos;

    @Data
    public static class MaterialInfo implements Serializable {

        /**
         * 工程单号
         */
        private String planTicketNo;

        /**
         * 物料code
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 物料单位
         */
        private String materialUnit;

        /**
         * 物料数量
         */
        private String materialCount;

        /**
         * 批次
         */
        private String purchaseBatch;

    }


}
