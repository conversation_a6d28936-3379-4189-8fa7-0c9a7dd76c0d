package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-05-15 16:38
 */
@Data
public class VerifyInboundRequestOutVO implements Serializable {


    private static final long serialVersionUID = -2291683325200544867L;
    /**
     * 已生产数量
     */
    private BigDecimal producedQuantity;


    /**
     * 可入库数量
     */
    private BigDecimal canStorageQuantity;

    /**
     * 已入库数量
     */
    private BigDecimal totalStorageQuantity;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;


    /**
     * true可报工,false无法报工
     */
    private Boolean result;

}
