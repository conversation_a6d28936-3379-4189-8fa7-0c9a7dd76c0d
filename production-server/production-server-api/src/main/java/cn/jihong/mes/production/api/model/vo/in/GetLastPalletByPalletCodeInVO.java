package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetLastPalletByPalletCodeInVO implements Serializable {

    /**
     * 工单id
     */
    @NotNull(message = "工单id不能为空")
    private Long productTicketId;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板短码
     */
    private String palletShortCode;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

}
