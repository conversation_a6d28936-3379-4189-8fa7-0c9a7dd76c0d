package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetOneDayTaskSummaryOutVO implements java.io.Serializable {

    private Long taskId;

    private String planTicketNo;
    /**
     * 产品名称
     */
    private String productName;
    private String machineName;
    private String produceDate;
    private String shift;
    private String processCode;
    private BigDecimal defectiveProduct = BigDecimal.ZERO;
    /**
     * 未确认不良品数量
     */
    private BigDecimal unconfirDefectiveQuantity = BigDecimal.ZERO;
    private BigDecimal reportedQuantity = BigDecimal.ZERO;

    /**
     * 良品+不良品的占比
     */
    private BigDecimal  productRatio = BigDecimal.ZERO;

}
