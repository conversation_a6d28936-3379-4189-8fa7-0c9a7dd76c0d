package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO;
import cn.jihong.mes.production.api.model.po.ProductBarcodeSplitBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.PrintBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductBarcodeSplitBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveWmsBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.SpitWmsBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.PrintBarcodeOutVO;
import cn.jihong.mes.production.api.model.vo.out.SpitWmsBarcodeOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 条码拆分 服务类
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
public interface IProductBarcodeSplitBarcodeService extends IJiHongService<ProductBarcodeSplitBarcodePO> {

    Pagination<ProductBarcodeSplitBarcodeDTO> queryWmsBarcode(ProductBarcodeSplitBarcodeInVO productBarcodeSplitBarcodeInVO);

    SpitWmsBarcodeOutVO splitWmsBarcode(SpitWmsBarcodeInVO spitWmsBarcodeInVO);

    String saveWmsBarcode(String redisLockKey,SaveWmsBarcodeInVO saveWmsBarcodeInVO);

    Pagination<ProductBarcodeSplitBarcodeDTO> getPageList(ProductBarcodeSplitBarcodeInVO productBarcodeSplitBarcodeInVO);

    List<PrintBarcodeOutVO> printBarcode(PrintBarcodeInVO printBarcodeInVO);
}
