package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetLastTaskInfoInVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;


    /**
     * 班次  1 白班  2 夜班
     */
    @NotNull(message = "班次不能为空")
    private Integer shift;


}
