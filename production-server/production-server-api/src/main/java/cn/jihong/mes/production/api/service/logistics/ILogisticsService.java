package cn.jihong.mes.production.api.service.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.logistics.api.model.request.GetFOrderToErpRequest;
import cn.jihong.logistics.api.model.response.*;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.production.api.model.po.LogisticsProductConfigPO;
import cn.jihong.mes.production.api.model.po.LogisticsProductManualRecordPO;
import cn.jihong.mes.production.api.model.vo.in.GetErpOrderIdListInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.*;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetDeviceOrderCarrayPauseStatusOutVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.ItemQueryByConditionOutVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-24 15:10
 */
public interface ILogisticsService {


    /**
     * 生成 ERPOrderId
     * @param planTickNo 工程单号
     * @param processCode 工序编码
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2025-02-24 15:33
     */
    public String buildErpOrderId(String planTickNo,String processCode);


    /**
     * 网带设备详情
     * @param
     * @return: java.util.List<cn.jihong.logistics.api.model.response.MeshDeviceResponse>
     * <AUTHOR>
     * @date: 2025-02-26 14:13
     */
    public List<MeshDeviceResponse> meshDeviceDetails();


    /**
     * 同步生产计划
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-02-26 14:16
     */
    public Boolean syncProductionPlanning(String workshopCode,SyncProductionPlanningInVO inVO);



        /**
         * 叫料
         * @param productTicketId
         * @return: java.lang.Boolean
         * <AUTHOR>
         * @date: 2025-02-24 16:06
         */
    public String callForMaterial(Long productTicketId,String erpOrderId);

    /**
     * 创建订单的流水号
     * @param planTickNo
     * @param processCode
     * @return
     */
    String generatePalletId(String planTickNo, String processCode);




    /**
     * 可退料网带列表
     * @param inVO
     * @return: ApiMeshDeviceScadaltemResponse
     * <AUTHOR>
     * @date: 2025-02-26 14:25
     */
    public List<ApiMeshDeviceScadaltemResponse> returnOfMaterialMeshList(ReturnOfMaterialMeshListInVO inVO);


    /**
     * 手动退料
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-02-26 14:37
     */
    public Boolean returnOfMaterial(ReturnOfMaterialInVO inVO);


        /**
         * 通过设备 ID 暂停供料计划
         * @param inVO
         * @return: R
         * <AUTHOR>
         * @date: 2025-02-21 17:28
         */
    public Boolean pauseCallMaterial(PauseCallMaterialInVO inVO);


    /** 
     * 根据目标网带ID 获取供料计划列表
     * @param inVO	 
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-03-13 14:58
     */
    public List<ApiOrderCarryInstructionResponse> getInstructionByTargetDeviceId(GetInstructionByTargetDeviceIdInVO inVO);

    /**
     * 通过设备 ID 取消供料计划
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-03-12 17:10
     */
    public Boolean cancelOrderCarry(CancelOrderCarryInVO inVO);


    /** 
     * 通过设备 ID 启动叫料
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-02-25 15:37
     */
    public Boolean startCallMaterial(StartCallMaterialInVO inVO);


    /** 
     * 通过设备 ID 获取供料计划执行状态  0：开启 1:暂停 2：无供料计划
     * @param inVO
     * @return: java.lang.Integer 
     * <AUTHOR>
     * @date: 2025-03-10 15:56
     */
    public List<GetDeviceOrderCarrayPauseStatusOutVO> getDeviceOrderCarrayPauseStatus(GetDeviceOrderCarrayPauseStatusInVO inVO);

    /**
     * 在制品条件查询
     * @param inVO
     * @return: cn.jihong.logistics.api.model.response.ApiPalletResponse
     * <AUTHOR>
     * @date: 2025-02-25 15:21
     */
    public Pagination<ItemQueryByConditionOutVO> itemQueryByCondition(ItemQueryByConditionInVO inVO);

    /** 
     * 获取京山瓦线数据
     * @param 	 
     * @return: cn.jihong.logistics.api.model.response.GetBoardInfoJstResponse
     * <AUTHOR>
     * @date: 2025-03-04 18:23
     */
    GetBoardInfoJstResponse getBoardInfoJst();

    /**
     * 待产订单查询
     * @param
     * @return: java.util.List<cn.jihong.logistics.api.model.response.GetOrdersDataResponse>
     * <AUTHOR>
     * @date: 2025-02-28 14:48
     */
    public List<GetOrdersDataResponse> getOrdersData();

    /**
     * 实时信息查询
     * @param
     * @return: GetRealtimeDataResponse
     * <AUTHOR>
     * @date: 2025-02-28 14:45
     */
    public List<GetRealtimeDataResponse> getRealtimeData();

    /**
     * 获取堆码机纸板信息(列表)"
     * @param
     * @return: java.util.List<cn.jihong.logistics.api.model.response.GetStackeredResponse>
     * <AUTHOR>
     * @date: 2025-02-28 14:48
     */
    public List<GetStackeredResponse> getStackered();

    /**
     * 获取堆码机纸板信息(详情)
     * @param searchId
     * @return: cn.jihong.logistics.api.model.response.GetSearchStackeredResponse
     * <AUTHOR>
     * @date: 2025-02-28 14:49
     */
    public GetSearchStackeredResponse getSearchStackered(String searchId);


    /** 
     * 瓦线DCS订单API
     * @param productionPlanPOS
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.logistics.api.model.response.PostOrderResponse> 
     * <AUTHOR>
     * @date: 2025-03-14 17:58
     */
    List<PostOrderResponse> postOrder(List<ProductionPlanPO> productionPlanPOS);

    /** 
     * 瓦线DCS完工API
     * @param request
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.logistics.api.model.response.GetFOrderToErpResponse>>
     * <AUTHOR>
     * @date: 2025-03-14 17:59
     */
    List<GetFOrderToErpResponse> getFOrderToErp(GetFOrderToErpRequest request);

    /** 
     * 
     * @param inVO	 
     * @return: java.util.List<java.lang.String>
     * <AUTHOR>
     * @date: 2025-03-11 17:32
     */
    List<String> getErpOrderIdList(GetErpOrderIdListInVO inVO);


    /**
     * 获取当前工序 上一个工序出站的erpOrderId列表
     * @param planTicketNo
     * @param processCode
     * @return: java.util.List<java.lang.String>
     * <AUTHOR>
     * @date: 2025-03-14 10:26
     */
    List<String> getPrevioutErpOrderIdList(String planTicketNo, String processCode);

//    /**
//     * 创建栈板信息
//     * @param planTickNo
//     * @param processCode
//     * @param machineName
//     * @param palletQuquantity
//     * @return
//     */
//    StandardResult internetTape(String planTickNo,String processCode,String machineName,
//                                        BigDecimal palletQuquantity,String palletId,BigDecimal plannedProduct);


    StandardResult internetTape(LogisticsProductManualRecordPO logisticsProductManualRecordPO);

    /**
     * 上栈板信息
     * @param logisticsUpPalletInVO
     */
    void savePalletInfo(LogisticsUpPalletInVO logisticsUpPalletInVO);

    /**
     * 出站信息
     * @param logisticsOutPalletInVO
     */
    void outPalletInfo(LogisticsOutPalletInVO logisticsOutPalletInVO);

    /**
     * 启动托盘自动创建
     * @return: Boolean
     */
    StandardResult startPalletAutoCreator(String machineName,String processCode);


    /**
     * 暂停托盘生成器
     * @return: Boolean
     */
    StandardResult pausePalletAutoCreator(String machineName,String processCode);


    /**
     * 修改托盘生成器
     * @return: PalletAutoCreator
     */
    StandardResult updatePalletAutoCreator(LogisticsProductConfigPO logisticsProductConfigPO,BigDecimal plannedProduct);

}
