package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 栈板操作记录信息表
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Data
public class ProductPalletOperationRecordsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 工单类型
     */
    private String ticketType;

    /**
     * 工单编号
     */
    private String ticketNumber;

    /**
     * 操作人员
     */
    private Long operator;

    /**
     * 原数量
     */
    private BigDecimal originalQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 栈板表id
     */
    private Long productPalletId;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 请求id，用于记录请求到外部服务的id
     */
    private String requestId;

    private BigDecimal loadingQuantity;
    private String palletSource;
    private String unit;

}
