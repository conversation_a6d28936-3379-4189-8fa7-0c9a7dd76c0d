package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 员工录用
 */
@Data
public class OutsideProcessingDetailVO implements Serializable {

    private Integer id;

    private Integer mainid;

    /**
     *工单号
     */
    private String workOrderNumber;

    /**
     *产品编号
     */
    private String productNumber;

    /**
     *产品名称
     */
    private String productName;

    /**
     *产品规格
     */
    private String productSpecification;

    /**
     *工序编号
     */
    private String processNumber;

    /**
     *工序名称
     */
    private String processName;

    /**
     *备注
     */
    private String bz;
    /**
     *加工单价（含税）
     */
    private BigDecimal processingUnitPrice;

    /**
     *	外发数量
     */
    private Integer outboundQuantity;

    /**
     *加工总价
     */
    private BigDecimal processingTotalPrice;

    /**
     *供应商名称
     */
    private String supplierName;

    /**
     *单位
     */
    private String unit;


}
