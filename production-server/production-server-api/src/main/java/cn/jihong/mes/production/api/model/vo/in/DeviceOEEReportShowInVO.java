package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-03-22 16:39
 */
@Data
public class DeviceOEEReportShowInVO implements Serializable {

    /**
     * 年月 (2025-03)
     */
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date yearMonth;

    /** 
     * 工序名称
     */
    private String processName;

    /** 
     * 机台名称
     */
    private String machineName;

}
