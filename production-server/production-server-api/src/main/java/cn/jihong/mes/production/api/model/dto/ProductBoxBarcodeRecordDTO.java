package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 条码操作记录
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class ProductBoxBarcodeRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 箱码号段id
     */
    private Long productBarcodeId;

    /**
     * 箱码号段明细id
     */
    private Long productBarcodeDetailId;

    /**
     * 操作类型 10 打印 20 补打
     */
    private String operationType;

    /**
     * 箱码开始号
     */
    private String barcodeNoStart;

    /**
     * 箱码截止号
     */
    private String barcodeNoEnd;

    /**
     * 箱码总数量
     */
    private Long barcodeTotal;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑人
     */
    private Long updateBy;

    /**
     * 编辑时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer deleted;

}
