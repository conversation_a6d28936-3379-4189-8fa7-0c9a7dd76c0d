package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetValuationTypeInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String process;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

}
