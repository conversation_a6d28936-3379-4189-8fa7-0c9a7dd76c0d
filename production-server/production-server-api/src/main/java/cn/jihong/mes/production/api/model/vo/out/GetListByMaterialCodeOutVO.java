package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/8 16:50
 */
@Data
public class GetListByMaterialCodeOutVO implements Serializable {

    /**
     * id
     */
    private Long id;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 生产工单id
     */
    private Long productTicketId;


    /**
     * 物料编号
     */
    private String materialBarcodeNo;


    /**
     * 物料code
     */
    private String materialCode;


    /**
     * 物料名称
     */
    private String materialName;


    /**
     * 物料单位
     */
    private String materialUnit;


    /**
     * 物料入库时间
     */
    private Date materialWarehousingTime;


    /**
     * 上料时间
     */
    private Date loadingTime;

    /**
     * 下料时间
     */
    private Date downTime;


    /**
     * 领料数量
     */
    private BigDecimal loadingQuantity;


    /**
     * 用料数量
     */
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;


    /**
     * 剩余原因
     */
    private String remainingReason;


    /**
     * 状态
     */
    private Integer status;

}
