package cn.jihong.mes.production.api.model.vo.in.logistics;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-07 11:46
 */
@Data
public class ItemQueryByConditionInVO extends PageRequest implements Serializable {

    /**
     * 网带id
     */
    private String meshId;

    /**
     * 开始时间（必填）
     */
    private String startAt;

    /**
     * 结束时间（必填）
     */
    private String endAt;

    /**
     * 车间编码
     */
    @NotBlank(message = "车间编码不能为空")
    private String workshopCode;


}
