package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductConfigPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 生产配置表 服务类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProductConfigService extends IJiHongService<ProductConfigPO> {

    ProductConfigPO getProductConfig(String businessType);

//    /**
//     * 获得结算方式
//     * @return
//     */
//    Integer getBillingType();


    String getMinimumVersion();


    List<ProductConfigPO> getAllMinimumVersion();

}
