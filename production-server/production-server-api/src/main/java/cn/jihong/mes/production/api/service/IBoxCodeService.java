package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.HandleBoxCodeDTO;
import cn.jihong.mes.production.api.model.enums.BarcodeOperateTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeDetailInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyCaseCodeInVO;
import cn.jihong.oa.erp.api.model.dto.SaveLsafTDTO;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;

import java.util.Date;
import java.util.List;

public interface IBoxCodeService {

    /**
     * 封装箱码处理逻辑
     * @param handleBoxCodeDTO
     * @param sfac006
     */
    List<BarcodeDetailDTO.BoxInfo> handleBoxCode(HandleBoxCodeDTO handleBoxCodeDTO, String sfac006);


    /**
     * 验证箱码是否存在
     * @return
     */
    void verifyCaseCodeByErp(String caseCode,String palletCode);

    /**
     * 校验单个箱码
     * @param verifyCaseCodeInVO
     */
    void verifyBoxCode(VerifyCaseCodeInVO verifyCaseCodeInVO);

    void verifyBoxCodeAndTicketNo(VerifyCaseCodeInVO verifyCaseCodeInVO, List<String> barcodeList);

    /**
     * 发送消息
     */
    void sendMessage(Integer code, String direct, String caseCode);



    /**
     * 设置LsafT信息
     * @param barcodeDetailDTO
     * @param boxInfo
     * @param curentDate
     * @param sfac006
     * @param sfaa013
     * @param sfac001
     * @return
     */
    SaveLsafTDTO setLsafTInfo(BarcodeDetailDTO barcodeDetailDTO, BarcodeDetailDTO.BoxInfo boxInfo, Date curentDate, String sfac006, String sfaa013, String sfac001);

    String getCompanyCode();

    /**
     * 激活箱码
     * @param boxCode
     */
    String activeBoxCode(String boxCode);

    Long applyBoxBarcodeDetail(AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO);

    List<String> getBarcodeList(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO, BarcodeOperateTypeEnum print);

    List<BarcodeDetailDTO.BoxInfo> oldBarcodeNO(HandleBoxCodeDTO handleBoxCodeDTO, String sfac006);
}
