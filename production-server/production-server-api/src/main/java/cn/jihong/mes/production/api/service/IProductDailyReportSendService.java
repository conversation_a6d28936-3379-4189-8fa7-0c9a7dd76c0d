package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductDailyReportSendPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;


/**
 * 生产日报发送 服务类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface IProductDailyReportSendService extends IJiHongService<ProductDailyReportSendPO> {

    List<ProductDailyReportSendPO> getList(List<Long> groupIds, String produceDate, Integer shit);

}
