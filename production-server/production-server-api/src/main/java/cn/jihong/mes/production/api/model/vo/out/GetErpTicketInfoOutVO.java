package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GetErpTicketInfoOutVO  implements Serializable {
    private static final long serialVersionUID = -7531669558892109921L;
    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 箱码规格
     */
    private BigDecimal boxSpece;

    /**
     * 客户集团信息
     */
    private String customerNo;
    private String customerName;

    /**
     * 外部产品编号
     */
    private String skuCode;

    /**
     * 外部产品名称
     */
    private String skuName;

    /**
     * 产品模板
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 过期月数
     */
    private Integer overdueMonth;

    /**
     * 过期天数
     */
    private Integer overdueDay;

}
