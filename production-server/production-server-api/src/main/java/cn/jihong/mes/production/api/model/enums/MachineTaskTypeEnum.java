package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum MachineTaskTypeEnum {


    IN_PRODUCTION(4, 0,"生产",false),


    PRODUCTION(1, MachineTaskTypeEnum.IN_PRODUCTION.getCode(),"生产中",true),

    MACHINE_SETTING(2, MachineTaskTypeEnum.IN_PRODUCTION.getCode(),"调机中",true),


    NO_PRODUCTION(-1, 0,"停机",false),


    SHORT_STANDBY(-11,MachineTaskTypeEnum.NO_PRODUCTION.getCode(),"短期待机",false),

    MEETING(3, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"会议中",true),

    TRANSFER_OF_ORDER(-1101, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"转单/换单中",true),

    NEW_PROOF(-1102, MachineTaskTypeEnum.IN_PRODUCTION.getCode(),"新品打样/测试中",true),

    EQUIPMENT_MAINTENANCE(-1103, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"设备保养中",true),

    TAKE_STOCK(-1104, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"盘点中",true),

    CLEANING_EQUIPMENT(-1105, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"清洁设备中",true),

    PLANNED_STOP(-1106, MachineTaskTypeEnum.SHORT_STANDBY.getCode(),"计划停机中",true),


    LONG_STANDBY(-12,MachineTaskTypeEnum.NO_PRODUCTION.getCode(),"长期待机",false),


    STAND_DOWN(-1201, MachineTaskTypeEnum.LONG_STANDBY.getCode(),"停机待料中",true),

    POOR_INCOMING_MATERIAL(-1202, MachineTaskTypeEnum.LONG_STANDBY.getCode(),"来料不良中",true),

    EQUIPMENT_FAULT(-1203, MachineTaskTypeEnum.LONG_STANDBY.getCode(),"设备故障中",true),

    PERSONNEL_LEAVE(-1204, MachineTaskTypeEnum.LONG_STANDBY.getCode(),"人员请假/离职中",true);




    private Integer code;

    private Integer parentCode;

    private String name;

    /**
     * 是否在报工界面显示
     */
    private Boolean show;

    MachineTaskTypeEnum(Integer code,Integer parentCode, String name,Boolean show) {
        this.code = code;
        this.parentCode = parentCode;
        this.name = name;
        this.show = show;
    }

    public static MachineTaskTypeEnum getMachineTaskTypeEnum(Integer code){
        for (MachineTaskTypeEnum value : MachineTaskTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("获取机台任务类型异常");
    }

    public static List<MachineTaskTypeEnum> getShowMachineTaskTypeEnumList(){
        return Arrays.stream(MachineTaskTypeEnum.values()).filter(MachineTaskTypeEnum::getShow).collect(Collectors.toList());
    }

}
