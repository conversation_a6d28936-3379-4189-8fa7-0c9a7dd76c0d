package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/22 14:36
 */
@Data
public class GetDefectListByTicketNoAndProcessInVO implements Serializable {
    private static final long serialVersionUID = -3396314633960479309L;

    /**
     * 生产工程单号
     */
    @NotBlank(message = "生产工程单号不能为空")
    private String productTicketNo;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String defectiveProductsSourceName;
}
