package cn.jihong.mes.production.api.model.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public enum StorageApplyTypeEnum implements IntCodeEnum{

    NOT_SUBMITTED("1", "未提交"),
    SUBMITTED("2", "已提交"),
    WITHDRAWN("3", "被退回"),
    WAREHOUSING("4", "已入库"),
    ;

    private String code;

    private String name;

    StorageApplyTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StorageApplyTypeEnum getUpdateMaterialTypeEnum(String code) {
        for (StorageApplyTypeEnum value : StorageApplyTypeEnum.values()) {
            if (StringUtils.equals(value.getCode(), code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }


    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((StorageApplyTypeEnum) enumValue).getCode();
    }

}
