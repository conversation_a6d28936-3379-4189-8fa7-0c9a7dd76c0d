package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GetInboundQuantityOutVO implements Serializable {

    /**
     * 工单号
     */
    private String planTicketNo;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;

    /**
     * 已入库数量
     */
    private BigDecimal hadStorageQuantity;

    /**
     * 可入库数量
     */
    private BigDecimal canStorageQuantity;

    /**
     * 报废数量
     */
    private BigDecimal scrapQuantity;


}
