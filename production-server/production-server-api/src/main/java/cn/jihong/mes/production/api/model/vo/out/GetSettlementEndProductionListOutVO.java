package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/8 15:36
 */
@Data
public class GetSettlementEndProductionListOutVO implements Serializable {

    private static final long serialVersionUID = -3395103558739023018L;
    /**
     * id
     */
    private Long id;

    /**
     * 生产工程单号
     */
    private String productTicketNo;

    /**
     * 工序名称
     */
    private String processName;


    /**
     * 产品状态：0：半成品 1：成品
     */
    private Integer productionStatus;


    /**
     * 生产数量
     */
    private BigDecimal productQuantity;


    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 下道工序生产数量
     */
    private BigDecimal nextProductQuantity;


    /**
     * 下道工序报废数量
     */
    private BigDecimal nextScrapQuantity;

    /**
     * 数据差异度（%）
     */
    private BigDecimal dataVarianceDegree;


    /**
     * 产出率(%)
     */
    private BigDecimal outputRate;

    /**
     * 工序顺序
     */
    private Integer processSeq;
}
