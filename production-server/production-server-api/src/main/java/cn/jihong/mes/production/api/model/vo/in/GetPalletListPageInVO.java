package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetPalletListPageInVO extends PageRequest implements Serializable {

    /**
     * 公司code
     */
    private String companyCode;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 栈板来源
     */
    private String palletSource;

    /**
     * 来源班次
     */
    private String shiftSource;

    /**
     * 栈板状态  1 在库  2 在产
     */
    private String status;

    /**
     * 核销状态
     */
    private String writeOffStatus;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

}
