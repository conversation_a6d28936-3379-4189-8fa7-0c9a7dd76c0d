package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum OutboundTypeEnum {

    // 出站类型  1 手动  2 自动
    MANUAL("1", "手动"),
    AUTO("2", "自动"),
    SUBMIT("3", "上网带"),
    ;

    private String code;

    private String name;



    OutboundTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OutboundTypeEnum getUnitEnum(String code){
        for (OutboundTypeEnum value : OutboundTypeEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        throw new RuntimeException("不存在的类型");
    }
}
