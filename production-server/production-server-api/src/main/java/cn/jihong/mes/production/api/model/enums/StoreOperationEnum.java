package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum StoreOperationEnum implements IntCodeEnum{

    REQUEST("10", "申请"),
    PULL("20", "拉货"),
    WAREHOUS("30", "入库"),

    DELETE("99", "删除"),
    ;

    private String code;

    private String name;

    StoreOperationEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StoreOperationEnum getStoreOperationEnum(String code) {
        for (StoreOperationEnum value : StoreOperationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((StoreOperationEnum) enumValue).getCode();
    }
}
