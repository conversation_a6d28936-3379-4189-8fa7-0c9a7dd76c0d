package cn.jihong.mes.production.api.model.enums;


import lombok.Getter;

/**
 * 接口类型 枚举
 */
@Getter
public enum InterfaceBusinessEnum {
    GOOD_REPORT(1, "良品报工"),
    DEFECTION_REPORT(2, "不良品报工"),
    UPDATE_MATERIAL(3, "更新物料"),
    INBOUND_REQUESTS(4, "入库申请"),
    ;

    private Integer code;

    private String name;

    InterfaceBusinessEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static InterfaceBusinessEnum getFirstCheckStatusEnum(Integer code) {
        for (InterfaceBusinessEnum value : InterfaceBusinessEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的接口类型");
    }

}
