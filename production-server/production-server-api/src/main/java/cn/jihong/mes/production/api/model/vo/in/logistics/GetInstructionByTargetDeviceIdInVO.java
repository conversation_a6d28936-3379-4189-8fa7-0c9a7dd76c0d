package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-13 14:58
 */
@Data
public class GetInstructionByTargetDeviceIdInVO implements Serializable {

    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long productTicketId;


    /**
     * 目标网带id
     */
    private String targetMeshId;
}
