package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 创建工单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
public class CreateProductTicketInVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String process;

    /**
     * 工序类型
     */
    @NotBlank(message = "工序类型不能为空")
    private String processType;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;


    /**
     * 班次  1 白班  2 夜班
     */
    @NotNull(message = "班次不能为空")
    private Integer shift;

    /**
     * 生产计划工单单号
     */
    @NotBlank(message = "生产计划工单单号不能为空")
    private String planTicketNo;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date planStartDate;


    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date planEndDate;

    /**
     * 计划产量
     */
    private BigDecimal plannedProduct;



    /**
     * 备注
     */
    private String remark;

    /**
     * 工单类型
     */
    private Integer ticketType;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计件类型
     */
    private String pieceType = "ALL";

    /**
     * 部门编号
     */
    private String deptNo;

    /**
     * 部门名称
     */
    private String deptName;


    @NotNull(message = "班组人员不能为空")
    @Size(min = 1, message = "班组人员不能为空")
    @Valid
    private List<TeamUser> teamUsers;

    /**
     * 机位列表
     */
    private List<Parts> partList;

    @Data
    public static class TeamUser {

        /**
         * 用户id
         */
        @NotBlank(message = "用户id不能为空")
        private String userId;

        /**
         * 角色id
         */
        @NotBlank(message = "岗位code不能为空")
        private String roleCode;

        /**
         * 角色名称
         */
        @NotBlank(message = "岗位名称不能为空")
        private String roleName;
    }


    /**
     * 机位信息
     */
    @Data
    public static class Parts {

        /**
         * 机位
         */
        private String parts;
        private String partsName;

        /**
         * 部位（用途）
         */
        private String place;
        private String placeName;

    }

    /**
     * 生产批次
     */
    private String productionBatch;

    /**
     * 报工班次
     */
    private Integer reportShift;


}
