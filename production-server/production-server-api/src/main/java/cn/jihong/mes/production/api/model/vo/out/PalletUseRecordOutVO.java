package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PalletUseRecordOutVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 操作类型
     */
    private String operationType;
    private String operationTypeName;

    /**
     * 工单类型
     */
    private String ticketType;
    private String ticketTypeName;

    /**
     * 工单号
     */
    private String ticketRequestId;

    /**
     * 操作人
     */
    private Long createBy;
    private String createrName;

    /**
     * 原数量
     */
    private BigDecimal originalQuantity;
    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;
    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 出站表id
     */
    private Long outboundId;


}
