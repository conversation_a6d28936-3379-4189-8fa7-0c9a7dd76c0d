package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-05 15:13
 */
@Data
public class GetOeeBaseDataOutVO implements Serializable {

    private String companyCode;

    private String machineName;

    private String process;

    private Date produceDate;

    private BigDecimal standardProductionCapacity;

    private String planTicketNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private BigDecimal realProduct;

    private BigDecimal defectiveProduct;

    private BigDecimal reportedQuantity;

    private BigDecimal durationMinutes;

}
