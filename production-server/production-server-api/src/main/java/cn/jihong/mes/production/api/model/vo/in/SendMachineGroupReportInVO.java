package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.mes.production.api.model.vo.out.GetMachineGroupReportOutVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class SendMachineGroupReportInVO  implements Serializable {

    @NotNull(message = "日报数据不能为空")
    List<GetMachineGroupReportOutVO> machineGroupReport;

    @NotNull(message = "微信群组不能为空")
    List<Integer> weChatTeamCodes;

}
