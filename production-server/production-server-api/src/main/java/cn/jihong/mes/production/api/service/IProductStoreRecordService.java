package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.ProductStoreRecordDTO;
import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.po.ProductStoreRecordPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 入库表操作记录 服务类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface IProductStoreRecordService extends IJiHongService<ProductStoreRecordPO> {

    void createRecord(ProductStoreRecordDTO productStoreRecordDTO);

    void createRecords(List<ProductStoreRecordDTO> productStoreRecordDTOs);

    void deleteApplyNo(List<ProductStorePO> productStorePOS);
}
