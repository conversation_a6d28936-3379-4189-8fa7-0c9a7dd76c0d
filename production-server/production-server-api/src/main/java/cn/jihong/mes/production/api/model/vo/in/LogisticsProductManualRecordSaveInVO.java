package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流产品手动出站记录保存入参
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
public class LogisticsProductManualRecordSaveInVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 网待编号
     */
    private String meshCode;


    /**
     * 工单号
     */
    private String planTicketNo;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序code
     */
    private String processCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板短码
     */
    private String palletShortCode;

    /**
     * 物料类别
     */
    private String materialCategory;
    private String materialCategoryName;

    /**
     * 栈板一拖的数量
     */
    private Integer palletCount;

    /**
     * 堆长度
     */
    private Integer stackLength;

    /**
     * 堆宽度
     */
    private Integer stackWidth;

    /**
     * 托盘长
     */
    private Integer palletLength;

    /**
     * 托盘宽
     */
    private Integer palletWidth;

    /**
     * 托盘列
     */
    private Integer palletColumns;

    /**
     * 客户简称
     */
    private String customerShortName;

    /**
     * 产品简称
     */
    private String productShortName;


    /**
     * 状态：1 出站  2 已上网带
     */
    private Integer status;

    /**
     * 状态：1 出站  2 已上网带
     */
    private String result;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态描述
     */
    private String statusDesc;


    private Integer planCount;

} 