package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetMaterialDailySettlementInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 工序
     */
//    private String processCode;

    private Integer isFinish;


    private Integer isApportionment;


    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

}
