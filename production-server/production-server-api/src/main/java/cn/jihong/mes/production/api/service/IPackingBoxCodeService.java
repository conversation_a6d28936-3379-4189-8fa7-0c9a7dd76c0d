package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.PackingBoxCodePO;
import cn.jihong.mes.production.api.model.vo.in.packing.*;
import cn.jihong.mes.production.api.model.vo.out.packing.*;

import java.util.List;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * 包装线箱码信息服务接口
 * 定义自动包装生产线相关业务接口
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
public interface IPackingBoxCodeService extends IJiHongService<PackingBoxCodePO> {

    /**
     * 箱码校验
     * 校验箱码有效性并返回箱码所带数据
     * 对应表: packing_box_code
     *
     * @param vo 箱码校验输入参数
     * @return 箱码校验结果
     */
    BoxCodeValidationOutVO validateBoxCode(BoxCodeValidationInVO vo);

    /**
     * 检测数据上传
     * 工站检测结果上报，将检验结果绑定到外箱ID
     * 对应表: packing_box_code_check
     *
     * @param vo 检测数据上传输入参数
     * @return 上传结果响应
     */
    DetectionDataUploadOutVO uploadDetectionData(DetectionDataUploadInVO vo);

    /**
     * 组托结果处理
     * 获取箱托绑定数据，生成出站数据，自动提交入库申请
     * 对应表: bar_code_tree
     *
     * @param vo 组托结果输入参数
     * @return 组托结果响应
     */
    PalletizeResultOutVO palletizeResult(PalletizeResultInVO vo);

    /**
     * 获取工站状态
     * 查询当前工站的运行状态和统计信息
     *
     * @param vo 工站状态查询输入参数
     * @return 工站状态信息
     */
    StationStatusOutVO getStationStatus(StationStatusInVO vo);

    /**
     * 根据箱码查询详细信息
     * 用于追溯和问题排查
     *
     * @param boxCode 箱码
     * @return 箱码详细信息
     */
    BoxCodeInfoOutVO getBoxCodeInfo(String boxCode);

    /**
     * 查询箱码检测历史
     * 获取指定箱码在各工站的检测记录
     *
     * @param vo 查询条件
     * @return 检测历史记录
     */
    BoxCodeCheckHistoryOutVO getBoxCodeCheckHistory(BoxCodeCheckHistoryInVO vo);

    // ==================== 内标签绑定相关方法 ====================

    /**
     * 箱码内标签绑定
     * 将内标签码与箱码建立绑定关系，支持三级层次：栈板码 → 箱码 → 内标签码
     * 基于BarCodeTree表实现，同时从ERP接口获取并更新产品信息
     *
     * @param vo 绑定输入参数
     * @return 绑定结果
     */
    InnerLabelBindingOutVO bindInnerLabels(InnerLabelBindingInVO vo);

    /**
     * 查询箱码绑定的内标签
     * 获取指定箱码下的所有内标签绑定信息
     *
     * @param boxCode 箱码
     * @return 内标签列表
     */
    List<InnerLabelQueryOutVO> getInnerLabels(String boxCode);

    /**
     * 解绑内标签
     * 解除内标签与箱码的绑定关系
     *
     * @param vo 解绑输入参数
     * @return 操作结果
     */
    Boolean unbindInnerLabel(InnerLabelUnbindInVO vo);
}