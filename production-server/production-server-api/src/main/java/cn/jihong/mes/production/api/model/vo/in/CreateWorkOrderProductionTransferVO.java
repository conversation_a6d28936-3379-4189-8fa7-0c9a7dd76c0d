package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 转产的时候创建工单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Data
public class CreateWorkOrderProductionTransferVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String process;

    /**
     * 工序
     */
    @NotBlank(message = "工序类型不能为空")
    private String processType;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;


    /**
     * 班次  1 白班  2 夜班
     */
    @NotNull(message = "班次不能为空")
    private Integer shift;

    /**
     * 生产计划工单单号
     */
    @NotBlank(message = "生产计划工单单号不能为空")
    private String planTicketNo;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date planStartDate;


    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date planEndDate;

    /**
     * 计划产量
     */
    private BigDecimal plannedProduct;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工单类型
     */
    private Integer ticketType;


}
