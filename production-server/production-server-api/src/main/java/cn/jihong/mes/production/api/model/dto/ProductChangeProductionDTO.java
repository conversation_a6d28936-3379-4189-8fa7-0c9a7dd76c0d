package cn.jihong.mes.production.api.model.dto;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;

@Data
public class ProductChangeProductionDTO implements Serializable {

    /**
     * 公司code
     */
    private String companyCode;
    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;
    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

}
