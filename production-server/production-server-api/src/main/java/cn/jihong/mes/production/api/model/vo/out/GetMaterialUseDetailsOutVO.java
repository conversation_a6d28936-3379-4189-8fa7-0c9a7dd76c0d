package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetMaterialUseDetailsOutVO implements Serializable {

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;


    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 原数量
     */
    private BigDecimal originalQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 调拨单号
     */
    private String transferNo;

    /**
     * 扣料单号
     */
    private String deductionNo;

}
