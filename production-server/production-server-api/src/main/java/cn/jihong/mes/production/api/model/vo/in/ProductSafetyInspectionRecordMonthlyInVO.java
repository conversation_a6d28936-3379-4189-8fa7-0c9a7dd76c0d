package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 按月查询安全点检记录的输入参数
 */
@Data
public class ProductSafetyInspectionRecordMonthlyInVO implements Serializable {

    /**
     * 年月 (2025-03)
     */
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date yearMonth;

    /**
     * 机台名称
     */
    private String machineName;
} 