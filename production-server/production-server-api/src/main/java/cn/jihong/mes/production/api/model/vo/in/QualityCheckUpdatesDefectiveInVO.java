package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QualityCheckUpdatesDefectiveInVO implements Serializable {

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

    /**
     * 不良品id
     */
//    private Long palletId;
    private Long productDefectiveProductsId;

    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 质检结果
     */
    private Integer checkResut;

}
