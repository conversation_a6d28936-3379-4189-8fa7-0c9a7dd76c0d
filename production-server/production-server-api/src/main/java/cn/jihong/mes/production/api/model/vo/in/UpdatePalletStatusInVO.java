package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdatePalletStatusInVO implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;


    /**
     * 操作  30 下栈板 40 上栈板
     */
    @NotNull(message = "操作不能为空")
    private String operation;

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

}
