package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Getter
@Setter
@TableName("workflow_request_attachment_file")
public class WorkflowRequestAttachmentFilePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String REQUEST_ID = "request_id";
    public static final String WORKFLOW_ID = "workflow_id";
    public static final String WORKFLOW_TYPE = "workflow_type";
    public static final String FILE_ID = "file_id";
    public static final String ATTACHMENT_SOURCE_TYPE = "attachment_source_type";
    public static final String DELETED = "deleted";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";



    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 请求ID
     */
    @TableField(REQUEST_ID)
    private Long requestId;


    /**
     * 工作流ID
     */
    @TableField(WORKFLOW_ID)
    private Long workflowId;


    /**
     * 工作流类型
     */
    @TableField(WORKFLOW_TYPE)
    private Integer workflowType;


    /**
     * 文件ID
     */
    @TableField(FILE_ID)
    private Long fileId;

    /**
     * 附件来源类型
     */
    @TableField(ATTACHMENT_SOURCE_TYPE)
    private Integer attachmentSourceType;


    /**
     * 逻辑删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
