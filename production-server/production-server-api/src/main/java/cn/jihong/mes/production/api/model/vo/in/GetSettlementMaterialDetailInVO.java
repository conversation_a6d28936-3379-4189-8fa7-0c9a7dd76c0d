package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/14 17:46
 */
@Data
public class GetSettlementMaterialDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1757089481609300109L;


    /**
     * 生产工程单号
     */
    @NotBlank(message = "生产工程单号不能为空")
    private String productTicketNo;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String materialBarcodeNo;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String process;
}
