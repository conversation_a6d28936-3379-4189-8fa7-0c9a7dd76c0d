package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/8 15:39
 */
@Data
public class GetSettlementMaterialListOutVO implements Serializable {

    private static final long serialVersionUID = 4242146454558891818L;
    /**
     * 工序名称
     */
    private String processName;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;


    /**
     * 物料code
     */
    private String materialCode;


    /**
     * 物料名称
     */
    private String materialName;


    /**
     * 物料单位
     */
    private String materialUnit;


    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 采购批次
     */
    private Integer purchaseBatch;

    /**
     * 领料数量
     */
    private BigDecimal pickingQuantity;


    /**
     * 用料数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 退料数量
     */
    private BigDecimal returnQuantity;

    /**
     * 损耗率（%）
     */
    private BigDecimal attritionRate;
}
