package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-15 15:06
 */
@Data
public class VerifyMaterialOverClaimRequestInVO implements Serializable {

    /**
     * 栈板id
     */
    @NotNull(message = "栈板id不能为空")
    private Long id;

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

    /**
     * 物料消耗数量
     */
    @NotNull(message = "物料消耗数量不能为空")
    private BigDecimal consumptionQuantity;


}
