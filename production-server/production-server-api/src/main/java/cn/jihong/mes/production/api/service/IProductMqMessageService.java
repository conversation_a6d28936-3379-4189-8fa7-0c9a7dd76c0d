package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.model.po.ProductMqMessagePO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
public interface IProductMqMessageService extends IJiHongService<ProductMqMessagePO> {

    void saveMqMessage(MessageStructDTO messageStructDTO, String message);

}
