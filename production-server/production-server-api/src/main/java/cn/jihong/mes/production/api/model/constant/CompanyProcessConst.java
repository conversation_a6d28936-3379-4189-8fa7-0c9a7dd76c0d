package cn.jihong.mes.production.api.model.constant;


import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class CompanyProcessConst implements Serializable {
    private static final long serialVersionUID = 1L;

    // 成型工序 + 冲切工序
    public static final List<String> chengxingList = new ArrayList<>(Arrays.asList(new String[]{
            "1080","1110","1308","040_1","1235",
            "1220",  "1221", "1230", "1231"
    }));
    // 模切冲切切纸工序
    public static final List<String> moqieList_anhui = new ArrayList<>(Arrays.asList(new String[]{
            "1260",
            "1293",
            "1297",
            "1299",
            "1305",
            "1221",
            "1060",
            "1090",
            "1160",
            "1210",
            "1220",
            "1230",
            "1250",
            "1231",
            "1270",
            "1261"
    }));

    // 模切冲切切纸工序
    public static final List<String> moqieList_langfang = new ArrayList<>(Arrays.asList(new String[]{
            "031",
            "033",
            "032",
            "1060",
            "1090",
            "1160",
            "1210",
            "1220",
            "1230",
            "1270",
            "1280"
    }));

}
