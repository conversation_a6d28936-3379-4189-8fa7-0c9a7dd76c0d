package cn.jihong.mes.production.api.model.vo.out.packing;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 内标签查询输出结果
 * 基于BarCodeTreePO查询箱码下的内标签信息
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class InnerLabelQueryOutVO implements Serializable {

    /**
     * 内标签码
     */
    private String innerLabelCode;

    /**
     * 箱码
     */
    private String boxCode;

    /**
     * 栈板码（如果有）
     */
    private String palletCode;

    /**
     * 树节点ID
     */
    private Long treeId;

    /**
     * 绑定时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindingTime;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 工厂代码
     */
    private String companyCode;
}