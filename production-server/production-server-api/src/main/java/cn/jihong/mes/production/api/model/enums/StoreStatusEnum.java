package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

/**
 * 物料入库状态
 */
@Getter
public enum StoreStatusEnum implements IntCodeEnum{

    REQUESTED("10", "已申请"),
    PULLED("20", "已拉货"),
    WAREHOUSED("30", "已入库"),
    CONFIRMED("40", "已确认"),
    ;

    private String code;

    private String name;

    StoreStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StoreStatusEnum getStoreStatusEnum(String code) {
        for (StoreStatusEnum value : StoreStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((StoreStatusEnum) enumValue).getCode();
    }
}
