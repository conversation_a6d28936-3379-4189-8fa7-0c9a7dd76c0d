package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

/**
 * 
 * 绑定码和产品信息
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class BandingProductBoxBarcodeInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 料号
     */
    private String materialCode;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 箱数量
     */
    private BigDecimal boxNum;

    /**
     * 批次识别码
     */
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;


    /**
     * 班次 1 2
     */
    private String shift;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    private String expirationDate;


    /**
     * 绑定id
     */
    private List<Long> ids;


}
