package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO;
import cn.jihong.mes.production.api.model.vo.in.GetProductSettlementInfoListInVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <p>
 * 生产工程单结算信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface IProductSettlementInfoService extends IJiHongService<ProductSettlementInfoPO> {


    /**
     * 获取单条工程结算
     * @param productTickNo
     * @return: cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO
     * <AUTHOR>
     * @date: 2023/11/14 11:02
     */
    ProductSettlementInfoPO getOneByProductTickNo(String productTickNo);

    /**
     * 工程结算 汇总更新
     * @param productTicketId
     * @return: Void
     * <AUTHOR>
     * @date: 2023/11/14 11:02
     */
    void collect(Long productTicketId);

    /**
     * 获取工程结算列表
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO>
     * <AUTHOR>
     * @date: 2023/11/14 17:23
     */
    Pagination<ProductSettlementInfoPO> getProductSettlementInfoList(GetProductSettlementInfoListInVO inVO);


    /**
     * 获取工程结算详情
     * @param id
     * @return: cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO
     * <AUTHOR>
     * @date: 2023/11/15 15:19
     */
    ProductSettlementInfoPO getSingleById(Long id);


    /**
     * 工程结算 结单
     * @param id
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2023/11/15 10:52
     */
    Boolean finish(Long id);
}
