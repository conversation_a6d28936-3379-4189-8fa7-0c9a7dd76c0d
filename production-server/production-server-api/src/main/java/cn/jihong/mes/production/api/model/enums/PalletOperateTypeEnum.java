package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum PalletOperateTypeEnum {

    SIGNING_UP_WORK("10", "报工"),
    OUT_BOUND("20", "出站"),
    DOWN_PALLET("30", "下栈板"),
    UP_PALLET("40", "上栈板"),
    DEFECTIVE_PRODUCTS("50", "报不良"),
    QUALITY_CHECK_UPDATES("60", "质检更新"),
    TEMPORARY_STORAGE("70", "暂存"),
    PREPARE("80", "备料"),
    ;

    private String code;

    private String name;

    PalletOperateTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PalletOperateTypeEnum getPalletOperateTypeEnum(String code) {
        for (PalletOperateTypeEnum value : PalletOperateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的操作类型");
    }
    
}
