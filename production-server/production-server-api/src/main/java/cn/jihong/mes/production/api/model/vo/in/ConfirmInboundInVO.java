package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ConfirmInboundInVO implements java.io.Serializable {
    private final static long serialVersionUID = 1L;

    /**
     * 储位
     */
    private String storage;
    private String storageName;

    /**
     * 仓库
     */
    @NotBlank(message = "仓位不能为空")
    private String warehouse;
    private String warehouseName;

    /**
     * 入库申请id
     */
    private List<Long> ids;

    /**
     * 入库申请编号
     */
    private List<String> storeApplyNos;

}
