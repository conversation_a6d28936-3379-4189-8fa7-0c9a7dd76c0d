package cn.jihong.mes.production.api.model.vo.out;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class QueryPalletInfoOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 入库单号
     */
    private String storeNo;
    /**
     * 栈板码
     */
    private String palletCode;
    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 入库类型 1 按箱入库 2 按托入库
     */
    private Integer storeType;

    /**
     * 入库申请单号
     */
    private String storeApplyNo;

    /**
     * 拉货确认单号
     */
    private String storePulledNo;

    /**
     * 入库状态 10 已申请 20 已拉货 30 已入库
     */
    private String storeStatus;

    /**
     * 已生产
     */
    private BigDecimal producedQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 拉货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pullTime;

    /**
     * 拉货人名称
     */
    private String pullUserName;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inboundTime;

    /**
     * 入库人名称
     */
    private String inboundUserName;


    /**
     * 储位
     */
    private String storage;
    private String storageName;

    private String lotNo;

    /**
     * OA审批流id
     */
    private Long workflowRequestId;

    /**
     * OA审批流状态
     */
    private Integer workflowRequestStatus;

}
