package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.ProductMachineTicketDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineTicketPO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 生产机台工程单绑定表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface IProductMachineTicketService extends IJiHongService<ProductMachineTicketPO> {


    /**
     * 根据机台名称查询工程单号
     * @param machineName
     * @return
     */
    ProductMachineTicketPO getByMachineName(String machineName);

    /**
     * 根据机台名称更新工程单号
     * @param machineName
     * @param planTicketNo
     * @return
     */
    void updateByMachineName(String machineName, String planTicketNo);

    void save(ProductMachineTicketDTO productMachineTicketDTO);
}
