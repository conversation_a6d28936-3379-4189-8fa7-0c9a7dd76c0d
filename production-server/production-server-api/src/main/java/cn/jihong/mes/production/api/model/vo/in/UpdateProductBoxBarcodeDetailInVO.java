package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UpdateProductBoxBarcodeDetailInVO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;


    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 料号
     */
    private String productNo;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;


    /**
     * 箱数量
     */
    private Integer boxSpece;


    /**
     * 批次识别码
     */
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;


    /**
     * 班次 1 2
     */
    private String shift;


}
