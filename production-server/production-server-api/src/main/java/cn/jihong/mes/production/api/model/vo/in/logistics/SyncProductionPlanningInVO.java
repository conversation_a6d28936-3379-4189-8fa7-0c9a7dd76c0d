package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26 14:16
 */
@Data
public class SyncProductionPlanningInVO implements Serializable {

    /**
     * ErpId
     */
    private String ErpId;

    /**
     * ERP 设备名称
     */
    private String ErpEquId;

    /**
     * 设备部件 - 同一个设备有好几个供料点时使用，默认为 0
     */
    private int EquPart;

    /**
     * Erp 订单号
     */
    private String ErpOrderId;

    /**
     * 网带编号
     */
    private int MeshId;

    /**
     * 本次生产数量
     */
    private int ProduceCount;

    /**
     * 生产工序代码
     */
    private String ProcedureCode;

    /**
     * 生产工序名称
     */
    private String ProcedureName;

    /**
     * 客户简称
     */
    private String CustNameShort;

    /**
     * 产品简称
     */
    private String ProdDescShort;

    /**
     * 备注
     */
    private String Remark;

    /**
     * 板长
     */
    private int SheetLength;

    /**
     * 板宽
     */
    private int SheetWidth;

    /**
     * 状态
     */
    private int Status;

    /**
     * 生产序号
     */
    private int SequenceNo;

    /**
     * 创建者
     */
    private String CreateBy;

}
