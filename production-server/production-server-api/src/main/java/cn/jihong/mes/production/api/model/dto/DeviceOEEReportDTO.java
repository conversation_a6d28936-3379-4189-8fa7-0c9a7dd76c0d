package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DeviceOEEReportDTO implements Serializable {


    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 生产日期
     */
    private String produceDate;


    /**
     * 时间开动率
     */
    private BigDecimal timeActuationRate;

    /**
     * 性能开动率
     */
    private BigDecimal performanceRate;


    /**
     * 良品率
     */
    private BigDecimal goodProductRate;

    /**
     * OEE
     */
    private BigDecimal oeeRate;

    /**
     * 总报工数
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 运行时长（H）
     */
    private BigDecimal totalDurationHours;

    /**
     * 标准产能
     */
    private BigDecimal standardProductionCapacity;
    
}
