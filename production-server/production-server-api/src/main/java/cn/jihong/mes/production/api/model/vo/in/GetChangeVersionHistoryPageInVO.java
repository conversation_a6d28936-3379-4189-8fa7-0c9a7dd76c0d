package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/9 16:45
 */
@Data
public class GetChangeVersionHistoryPageInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 7219115406998503080L;

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;


}
