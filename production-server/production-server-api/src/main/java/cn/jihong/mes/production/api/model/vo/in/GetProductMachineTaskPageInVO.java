package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/10 10:03
 */
@Data
public class GetProductMachineTaskPageInVO extends PageRequest implements Serializable{


    private static final long serialVersionUID = 1406798003713321576L;

    /**
     * 生产工单id
     */
    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

}
