package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全点检项配置表
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Getter
@Setter
@TableName("product_safety_inspection_item")
public class ProductSafetyInspectionItemPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String INSPECTION_CODE = "inspection_code";
    public static final String INSPECTION_NAME = "inspection_name";
    public static final String DESCRIPTION = "description";
    public static final String INSPECTION_STANDARD = "inspection_standard";
    public static final String SORT_ORDER = "sort_order";
    public static final String STATUS = "status";
    public static final String VERSION = "version";
    public static final String REMARK = "remark";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";

    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 据点
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 点检项编码
     */
    @TableField(INSPECTION_CODE)
    private String inspectionCode;

    /**
     * 点检项名称
     */
    @TableField(INSPECTION_NAME)
    private String inspectionName;

    /**
     * 点检描述
     */
    @TableField(DESCRIPTION)
    private String description;

    /**
     * 点检标准
     */
    @TableField(INSPECTION_STANDARD)
    private String inspectionStandard;

    /**
     * 排序
     */
    @TableField(SORT_ORDER)
    private Integer sortOrder;

    /**
     * 状态(0-停用，1-启用)
     */
    @TableField(STATUS)
    private Integer status;

    /**
     * 版本号
     */
    @TableField(VERSION)
    private String version;

    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;

    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;
} 