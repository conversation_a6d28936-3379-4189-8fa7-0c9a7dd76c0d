package cn.jihong.mes.production.api.model.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public enum UpdateMaterialTypeEnum implements IntCodeEnum{

    ADD("1", "正"),
    SUBTRACT("-1", "负"),
    ;

    private String code;

    private String name;

    UpdateMaterialTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UpdateMaterialTypeEnum getUpdateMaterialTypeEnum(String code) {
        for (UpdateMaterialTypeEnum value : UpdateMaterialTypeEnum.values()) {
            if (StringUtils.equals(value.getCode(), code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }


    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((UpdateMaterialTypeEnum) enumValue).getCode();
    }

}
