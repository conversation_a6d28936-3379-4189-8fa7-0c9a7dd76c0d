package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-05-15 16:38
 */
@Data
public class VerifyMaterialOverClaimRequestOutVO implements Serializable {


    private static final long serialVersionUID = -2291683325200544867L;

    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;


    /**
     * 可领用数量
     */
    private BigDecimal canUseQuantity;

    /**
     * 应发数量
     */
    private BigDecimal shouldUseQuantity;


    /**
     * true可下料,false无法下料
     */
    private Boolean result;

}
