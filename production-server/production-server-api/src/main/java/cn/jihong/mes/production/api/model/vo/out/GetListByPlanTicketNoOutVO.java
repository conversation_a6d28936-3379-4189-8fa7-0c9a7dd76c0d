package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/14 10:34
 */
@Data
public class GetListByPlanTicketNoOutVO implements Serializable {


    /**
     * 生产工单id
     */
    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 工序
     */
    private String processType;


    /**
     * 工序名称
     */
    private String process;


    /**
     * 生产工程单号
     */
    private String productTicketNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 实际开始时间
     */
    private Date startDate;


    /**
     * 实际结束时间
     */
    private Date endDate;


    /**
     * 计划开始时间
     */
    private Date planStartDate;


    /**
     * 计划结束时间
     */
    private Date planEndDate;


    /**
     * 计划产量
     */
    private Double plannedProduct;


    /**
     * 实际产量
     */
    private Double realProduct;
}
