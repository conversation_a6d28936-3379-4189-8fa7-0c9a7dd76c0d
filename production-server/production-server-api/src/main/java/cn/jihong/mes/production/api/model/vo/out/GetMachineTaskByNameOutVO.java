package cn.jihong.mes.production.api.model.vo.out;

import cn.jihong.mes.production.api.model.po.EquipmentDowntimePO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:42
 */
@Data
public class GetMachineTaskByNameOutVO implements Serializable {
    private static final long serialVersionUID = -1573757836464321392L;

    /**
     * 主键id
     */
    private Long id;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 机台任务类型: 1-生产中 2-调机中 3-会议中
     */
    private Integer type;

    /**
     * 机台任务类型名称: 1-生产中 2-调机中 3-会议中
     */
    private String typeName;


    /**
     * 机台任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;


    /**
     * 机台任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 是否结束
     */
    private String finished;


    /**
     * 报工生产工单id
     */
    private Long reportedProductId;


    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity;


    /**
     * 换版类型
     */
    private String changeVersionType;

    /**
     * 换版新旧版
     */
    private String changeVersionNewOld;

    /**
     * 换版数量
     */
    private BigDecimal changeVersionQuantity;

    /**
     * 备注
     */
    private String remark;


     private Long equipmentDowntimeId;

    /**
     * 设备停机
     */
    private EquipmentDowntimeOutVO equipmentDowntimeOutVO;


    /**
     * 稼动类型 1:人工上报 2：设备上报
     */
    private Integer operationType;

}
