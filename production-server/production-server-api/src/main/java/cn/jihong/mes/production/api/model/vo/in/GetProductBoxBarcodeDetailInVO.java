package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class GetProductBoxBarcodeDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工厂代码
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 客户编号
     */
    private String customerNo;


    /**
     * 码段标识
     */
    private String barcodeName;


    /**
     * 工程单号
     */
    private String planTicketNo;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 班次 1 2
     */
    private String shift;

    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;

}
