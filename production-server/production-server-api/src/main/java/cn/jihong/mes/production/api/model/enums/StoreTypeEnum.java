package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum StoreTypeEnum implements IntCodeEnum{

    BY_BOX("1", "按箱入库"),
    BY_PALLET("2", "按托入库"),
    ;

    private String code;

    private String name;

    StoreTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StoreTypeEnum getStoreTypeEnum(String code) {
        for (StoreTypeEnum value : StoreTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((StoreTypeEnum) enumValue).getCode();
    }
}
