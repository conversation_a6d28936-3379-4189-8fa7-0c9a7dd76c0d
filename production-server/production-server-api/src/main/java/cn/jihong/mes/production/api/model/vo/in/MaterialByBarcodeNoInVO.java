package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class MaterialByBarcodeNoInVO extends ProductTicketVO implements Serializable {

    /**
     * 物料编号
     */
    @NotBlank(message = "物料编号不能为空")
    private String barcodeNo;


    public MaterialByBarcodeNoInVO(String barcodeNo) {
        this.barcodeNo = barcodeNo;
    }

    public MaterialByBarcodeNoInVO() {
    }
}
