package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class AddProductBoxBarcodeInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;


    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 箱码开始号
     */
    @NotBlank(message = "箱码开始号不能为空")
    private String barcodeNoStart;


    /**
     * 箱码截止号
     */
    @NotBlank(message = "箱码截止号不能为空")
    private String barcodeNoEnd;


}
