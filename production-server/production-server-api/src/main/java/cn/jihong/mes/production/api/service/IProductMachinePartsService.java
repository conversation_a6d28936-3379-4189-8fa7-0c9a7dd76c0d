package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductMachinePartsPO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.UpdateProductMachinePartsInVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 任务机位信息 服务类
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface IProductMachinePartsService extends IJiHongService<ProductMachinePartsPO> {

    Pagination<ProductMachinePartsPO> getProductMachineParts(ProductTicketPageInVO productTicketPageInVO);

    List<ProductMachinePartsPO> getProductMachineParts(Long productTicketId);

    Long updateProductMachineParts(UpdateProductMachinePartsInVO invo);

    Object addProductMachineParts(ProductMachinePartsPO productMachinePartsPO);

    Object deleteById(Long id);
}
