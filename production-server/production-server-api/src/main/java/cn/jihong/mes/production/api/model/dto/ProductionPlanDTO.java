package cn.jihong.mes.production.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 生产计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class ProductionPlanDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 生产计划日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanDate;

    /**
     * 生产计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionPlanStartTime;

    /**
     * 生产计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionPlanEndTime;

    /**
     * 产品名称
     */
    private String productionName;

    /**
     * 工序
     */
    private String productionProcess;
    private String productionProcessCode;

    /**
     * 工序类型
     */
    private String productionProcessType;

    private String productionMachineNo;

    /**
     * 生产机台
     */
    private String productionMachine;

    /**
     * 工单号
     */
    private String workerOrderNo;

    /**
     * 单位
     */
    private String unit;

    /**
     * 标准产能/H
     */
    private BigDecimal standardProductionCapacity;

    /**
     * 换产时长
     */
    private BigDecimal productionChangeHours;

    /**
     * 计划产量
     */
    private BigDecimal plannedProductionCapacity;

    /**
     * 计划产量文本显示
     */
    private String plannedProductionCapacityText;

    /**
     * 星期几
     */
    private String weekDay;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑人
     */
    private Long updateBy;

    /**
     * 编辑时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 班次顺序
     */
    private Integer serialNo;

    /**
     * 部门编号
     */
    private String deptNo;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 生产批次
     */
    private String productionBatch;

    /**
     * 备注
     */
    private String remark;

}
