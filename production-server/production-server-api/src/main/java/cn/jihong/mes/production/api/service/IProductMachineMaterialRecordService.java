package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.ProductMachineMaterialRecordDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 一日一结机台物料消耗记录信息 服务类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProductMachineMaterialRecordService extends IJiHongService<ProductMachineMaterialRecordPO> {

    void save(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);

    /**
     * 获取一台机台的消耗量
     * @param productMachineMaterialRecordDTO
     * @return
     */
    List<ProductMachineMaterialRecordPO> getConsumptionQuantity(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);

    /**
     * 获取一台机台的消耗量 需要分摊的物料
     * @param productMachineMaterialRecordDTO
     * @return
     */
    List<ProductMachineMaterialRecordPO> getConsumptionQuantityNeedDistribution(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);


    List<ProductMachineMaterialRecordPO> getUndercutByProductTicketId(Long productTicketId);

    List<ProductMachineMaterialRecordPO> getConsumptionQuantityApp(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);
}
