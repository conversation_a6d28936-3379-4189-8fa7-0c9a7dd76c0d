package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GetMachineGroupReportInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组ID
     */
    @NotNull(message = "组ID不能为空")
    private List<Long> groupIds;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次  1 白班  2 夜班
     */
    @NotNull(message = "班次不能为空")
    private Integer shift;

    /**
     * 物料使用类型：  10 正扣料  20 分摊  30 倒扣料
     */
    private Integer useMaterialType;


}
