package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class VerifyCaseCodeInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 箱码
     */
    @NotBlank(message = "箱码不能为空")
    private String boxCode;

    /**
     * 生产工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;


}
