package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OutboundInfoOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 箱码
     */
    private String caseCode;

    /**
     * 已生产
     */
    private BigDecimal producedQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    /**
     * 出站时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outboundTime;




    private Long createBy;

    private String createrName;

    /**
     * 班组人员id
     */
    private String teamUsers;
    private String teamUsersName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次  1 白班  2 夜班
     */
    private Integer shift;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 工序名称
     */
    private Long outboundId;

    /**
     * 栈板短码
     */
    private String palletShortCode;

}
