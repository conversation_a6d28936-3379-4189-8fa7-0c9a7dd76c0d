package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

/**
 * 安全点检项配置 保存入参
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
public class ProductSafetyInspectionItemSaveInVO {

    /**
     * ID(修改时传入)
     */
    private Long id;

    /**
     * 点检项编码
     */
    private String inspectionCode;

    /**
     * 点检项名称
     */
    private String inspectionName;

    /**
     * 点检描述
     */
    private String inspectionDesc;

    /**
     * 点检标准
     */
    private String inspectionStandard;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态(0-停用，1-启用)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
} 