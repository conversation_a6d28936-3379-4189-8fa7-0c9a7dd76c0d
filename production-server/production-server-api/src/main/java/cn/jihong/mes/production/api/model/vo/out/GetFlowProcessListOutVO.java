package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-04-18 15:36
 */
@Data
public class GetFlowProcessListOutVO implements Serializable {


    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;


    /**
     * 当前工序名称
     */
    private BigDecimal totalGoodProductQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 排序
     */
    private int seq;

    /**
     * 最后报工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
}
