package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum ShiftNoEnum {

    DAY_SHIFT(1, "B001"),
    GRAVEYARD_SHIFT(2, "W001")
    ;

    private Integer code;

    private String name;

    ShiftNoEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ShiftNoEnum getShiftNoEnum(Integer code) {
        for (ShiftNoEnum value : ShiftNoEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的班次");
    }

}
