package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetOldBoxCodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.math.BigInteger;
import java.util.List;

/**
 * 箱码号段明细 服务类
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface IProductBoxBarcodeDetailService extends IJiHongService<ProductBoxBarcodeDetailPO> {

    Long applyBoxBarcodeDetail(String lockKey, AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO);

    Long update(String lockKey, UpdateProductBoxBarcodeDetailInVO updateProductBoxBarcodeDetailInVO);

    void disableBoxBarcode(Integer id);

    List<String> printBoxBarcode(String key, Integer id);

    List<String> printPMJBoxBarcode(String key, Integer id);

    List<String> rePrintPMJBoxBarcode(String key, Integer id);

    Pagination<GetProductBoxBarcodeDetailOutVO> getList(GetProductBoxBarcodeDetailInVO getProductBoxBarcodeDetailInVO);

    GetProductBoxBarcodeDetailOutVO getBoxBarcodeDetail(Integer id);

    List<String> rePrintBoxBarcode(String lock_key, Integer id);

    List<String> getLotNosByBarcodes(List<String> barcodes);

    List<ProductBoxBarcodeDetailPO> getByBarcodes(List<String> barcodes);

    ProductBoxBarcodeDetailPO getByBarcode(String barcode);

    List<ProductBoxBarcodeDetailDetailDTO> getDetail(Long id);

    void verify(String planTicketNo, String customerNo);

    String getNextBarcode(String barcodeNoStart, BigInteger currentHex, BigInteger bigInteger);

    Long applyBoxBarcodeDetailNoInfo(String lock_key, ApplyBoxBarcodeDetailNoInfoInVO applyBoxBarcodeDetailNoInfoInVO);

    GetOldBoxCodeDetailOutVO getOldBoxCodeDetail(GetOldBoxCodeDetailInVO getOldBoxCodeDetailInVO);

    String copyOldBoxCodeToNewBoxCode(CopyOldBoxCodeToNewBoxCodeInVO getOldBoxCodeDetailInVO);

    String printPMJBoxBarcode(String lock_key, PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO);

    String firstPrintPMJBoxBarcode(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO);

    String rePrintPMJBoxBarcode(String lock_key, PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO);

    Object getClintList();

    void printOpen(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO);

    void printClose(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO);
}
