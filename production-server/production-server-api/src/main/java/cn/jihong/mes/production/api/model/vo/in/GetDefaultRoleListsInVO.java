package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class GetDefaultRoleListsInVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long productTicketId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private List<Long> userIds;


}
