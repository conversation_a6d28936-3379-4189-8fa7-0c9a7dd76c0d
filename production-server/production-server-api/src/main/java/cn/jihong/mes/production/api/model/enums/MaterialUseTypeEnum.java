package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

/**
 * 物料使用类型枚举
 */
@Getter
public enum MaterialUseTypeEnum implements IntCodeEnum{

    POSITIVE_MATERIAL("10", "正扣料"),
    APPORTIONMENT_MATERIAL("20", "分摊"),
    UNDERCUT_MATERIAL("30", "倒扣料"),
    ;

    private String code;

    private String name;

    MaterialUseTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MaterialUseTypeEnum getMaterialUseTypeEnum(String code) {
        for (MaterialUseTypeEnum value : MaterialUseTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((MaterialUseTypeEnum) enumValue).getCode();
    }
}
