package cn.jihong.mes.production.api.model.vo.in;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:27
 */
@Data
public class UpdateEquipmentOperationReportInVO implements Serializable {

    private static final long serialVersionUID = -27039779959847876L;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 机台任务类型: 停机：-1 , 生产：1
     */
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    /**
     * 设备停机ID
     */
    private Long equipmentDowntimeId;

    /**
     * 设备停机编码
     */
    private String equipmentDowntimeCode;

    /**
     * 损失类型
     */
    private String lossType;

    /**
     * 机台任务id
     */
    private Long productMachineTaskId;


    /**
     * 是否结束当前任务 0：否 1：是
     */
    private String finished;

}
