package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/21 13:43
 */
@Data
public class TemporaryStoragePalletInVO implements Serializable {
    private static final long serialVersionUID = 1910013125444712436L;

    /**
     * id
     */
    private Long id;

    /**
     * 生产工单id
     */
//    @NotNull(message = "生产工单id不能为空")
    private Long productTicketId;

    /**
     * 机台止码
     */
    private Integer machineStopNo;

    /**
     * 物料消耗数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal consumptionQuantity = BigDecimal.ZERO;

    /**
     * 剩余数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal remainingQuantity = BigDecimal.ZERO;

    /**
     * 剩余原因
     */
    private String remainingReason;

}
