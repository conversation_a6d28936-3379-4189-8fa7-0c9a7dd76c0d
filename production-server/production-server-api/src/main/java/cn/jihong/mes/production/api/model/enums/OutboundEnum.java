package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum OutboundEnum {

    NOT_OUTBOUND(0, "未出站"),
    OUTBOUND(1, "已出站"),
    ;

    private Integer code;

    private String name;

    OutboundEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OutboundEnum getMaterialEnum(Integer code) {
        for (OutboundEnum value : OutboundEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }
}
