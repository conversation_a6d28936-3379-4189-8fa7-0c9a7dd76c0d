package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保存上料信息
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class SaveLastPalletInfoInVO extends ProductTicketVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 机台止码
     */
    private Integer machineStopNo;

    /**
     * 栈板来源
     */
    private String palletSource;


    /**
     * 栈板码
     */
    @NotBlank(message = "栈板码不能为空")
    private String palletCode;


    /**
     * 生产工程单号
     */
    private String productionOrder;


    /**
     * 生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionTime;


    /**
     * 领料数量
     */
    private BigDecimal loadingQuantity = BigDecimal.ZERO;


    /**
     * 单位
     */
    private String unit;


    /**
     * 物料消耗
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal consumptionQuantity = BigDecimal.ZERO;


    /**
     * 剩余数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal remainingQuantity = BigDecimal.ZERO;


    /**
     * 剩余原因
     */
    private String remainingReason;



}
