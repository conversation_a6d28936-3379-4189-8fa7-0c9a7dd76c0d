package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/23 14:06
 */
@Data
public class GetProductPlanListVO implements Serializable {

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 计划类型：1-换产，2-生产，3-结单，4-重工
     */
//    private String planType;

    /**
     * 生产日期
     */
    @NotNull(message = "生产日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanDate;

    /**
     * 班次 1:白班 2:晚班
     */
//    @NotNull(message = "班次不能为空")
    private Integer serialNo;

    /**
     * 工单号
     */
    private String workerOrderNo;
}
