package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UpdateMaterialDTO {

    /**
     * 调用erp的接口id，一般也是一个唯一值
     */
    private String nextIdStr;
    /**
     * 正负 扣料
     */
    private Integer flag;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 传给erp的幂等key
     */
    private Long key;
    /**
     * 物料操作记录id
     */
    private Long materialOperationRecordsId;

    /**
     * 结算类型
     */
    private Integer billingType;


    /**
     * 消耗数量
     */
    private BigDecimal qty;

}
