package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MaterialTypeoutVO implements Serializable {

    /**
     * 材料类别
     */
    private String materialType;

    /**
     * 材料部件信息列表
     */
    private List<MaterialPlace> materialPlaces;

    @Data
    public static class MaterialPlace {
        /**
         * 材料部件
         */
        private String place;

        /**
         * 材料部件名称
         */
        private String placeName;
    }



}
