package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductSafetyInspectionRecordPO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordMonthlyInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordMonthlyOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.Date;
import java.util.List;

/**
 * 安全点检记录 服务接口
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface IProductSafetyInspectionRecordService extends IJiHongService<ProductSafetyInspectionRecordPO> {

    /**
     * 分页查询
     *
     * @param inVO 查询参数
     * @return 分页结果
     */
    Pagination<ProductSafetyInspectionRecordOutVO> page(ProductSafetyInspectionRecordQueryInVO inVO);

    /**
     * 获取详情
     *
     * @param id ID
     * @return 详情
     */
    ProductSafetyInspectionRecordOutVO getDetail(Long id);

    /**
     * 保存
     *
     * @param inVO 保存参数
     * @return 是否成功
     */
    Boolean save(ProductSafetyInspectionRecordSaveInVO inVO);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    Boolean delete(Long id);

    /**
     * 根据机台名称、生产日期、班次获取记录
     *
     * @param machineName  机台名称
     * @param produceDate  生产日期
     * @param shift       班次
     * @return 记录
     */
    ProductSafetyInspectionRecordOutVO getByMachineDateShift(String machineName, Date produceDate, Integer shift);

    /**
     * 根据生产日期获取记录列表
     *
     * @param produceDate 生产日期
     * @return 记录列表
     */
    List<ProductSafetyInspectionRecordOutVO> getListByDate(Date produceDate);

    /**
     * 按月查询安全点检记录
     * 
     * @param inVO 查询参数
     * @return 点检记录列表
     */
    List<ProductSafetyInspectionRecordMonthlyOutVO> getMonthlyRecords(ProductSafetyInspectionRecordMonthlyInVO inVO);

    /**
     * 判断当天指定班次是否可以提交点检记录
     * @return true-可以提交，false-不可提交
     */
    Boolean canSubmitTodayShift(ProductTicketVO productTicketVO);

    /**
     * 获取当前时间对应的生产日期和班次
     * 
     * @return 当前班次信息
     */
    ProductSafetyInspectionRecordOutVO getCurrentShiftInfo();
} 