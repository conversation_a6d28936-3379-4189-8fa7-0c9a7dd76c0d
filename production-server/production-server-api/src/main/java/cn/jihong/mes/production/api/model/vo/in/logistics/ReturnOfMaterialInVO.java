package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26 14:35
 */
@Data
public class ReturnOfMaterialInVO implements Serializable {

    /**
     * 任务id
     */
    private Long productTicketId;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 栈板id
     */
    @NotBlank(message = "栈板id不能为空")
    private String palletId;

    /**
     * 托盘的列
     */
    private int palletColumn;

    /**
     * 托盘的宽
     */
    private int palletWidth;

    /**
     * 托盘的长
     */
    private int palletLength;

    /**
     * 纸堆数量
     */
    private int paperStackCount;

    /**
     * 退到最后的目的地网带 ID
     */
    private int targetMeshId;

    /**
     * 退回的原始地址
     */
    private int sourceMeshId;

    /**
     * 执行操作的轨道号--可以不填写，默认设置为 0
     */
    private int trackIndex;

    /**
     * 退回的托盘状态  0 良 品 ， 1 废 品 ，2 待整理， 3 不正常
     */
    @NotNull(message = "退料状态不能为空")
    private int statusTag;

    /**
     * 退回的人员或者设备信息-IDS 用于标识
     */
    private String createdBy;
}
