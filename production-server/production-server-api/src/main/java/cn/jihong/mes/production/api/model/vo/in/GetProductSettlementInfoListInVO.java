package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/14 15:49
 */
@Data
public class GetProductSettlementInfoListInVO extends PageRequest implements Serializable {

    private static final long serialVersionUID = -8511268970781956880L;


    /**
     * 生产工程单号
     */
    private String productTicketNo;

    /**
     * 结单： 0：待结  1：已结
     */
    private Integer finish;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 工厂代码
     */
    private String companyCode;
}
