package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/26 11:33
 */
@Data
public class UnfinishedOrderListInVO implements Serializable {
    private static final long serialVersionUID = -3378165256639362697L;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;
}
