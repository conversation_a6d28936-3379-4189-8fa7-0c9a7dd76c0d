package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DownMaterialInVO implements Serializable {


    /**
     * id
     */
    private Long id;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 机台止码
     */
    private Integer machineStopNo;

    /**
     * 物料消耗数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal consumptionQuantity = BigDecimal.ZERO;

    /**
     * 剩余数量
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private BigDecimal remainingQuantity = BigDecimal.ZERO;

    /**
     * 剩余原因
     */
    private String remainingReason;

    /**
     * 超领原因
     */
    private String reason;

}
