package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class LastPalletRecordsOutVO implements Serializable {


    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板来源
     */
    private String palletSource;


    /**
     * 生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionTime;

    /**
     * 上料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loadingTime;

    /**
     * 领料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    private Long createBy;

    private String createrName;

    /**
     * 班组人员id
     */
    private String teamUsers;
    private String teamUsersName;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次  1 白班  2 夜班
     */
    private Integer shift;


    private String id;
    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 工单类型
     */
    private String ticketType;
    /**
     * 工单编号
     */
    private String ticketNumber;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 原数量
     */
    private BigDecimal originalQuantity;
    /**
     * 生产工单id
     */
    private String productTicketId;
    /**
     * 栈板表id
     */
    private String productPalletId;
    /**
     * 公司code
     */
    private String companyCode;
    /**
     * 工序类型
     */
    private String processType;
    /**
     * 工序名称
     */
    private String process;
    /**
     * 生产工程单号
     */
    private String planTicketNo;
    /**
     * 工单号
     */
    private String ticketRequestId;
    /**
     * 领用工单id
     */
    private String requisitionTicketId;
    /**
     * 生产工程单
     */
    private String productionOrder;
    /**
     * 领用工程单
     */
    private String requisitionOrder;

    /**
     * 栈板状态  1 在库  2 在产 3 暂存 4 备料
     */
    private Integer status;
    private String statusName;


}
