package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:01
 */
@Data
public class CommonReplenishDTO implements Serializable {
    private static final long serialVersionUID = -1786820054123429788L;

    private BigDecimal totalReportedPcsQuantity;

    private BigDecimal totalDefectiveProductsPcsQuantity;

    private BigDecimal totalPlannedPcsQuantity;

    private BigDecimal billingAttritionRate;

    private BigDecimal billingPcsQuantity;



}
