package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum UnitEnum {


    SHEET("Y", "张","z"),
    PCS("N", "个","pcs"),
    RICE("M", "米","m"),
    SLICED_RICE("A", "分条米","a"),
    PCS_1("PCS", "个","pcs"),
    KG("KG", "千克","kg"),
    TAO("TAO", "TAO","tao"),

    ;

    private String code;

    private String name;
    /**
     * 标准单位
     */
    private String standardCode;

    UnitEnum(String code, String name,String standardCode) {
        this.code = code;
        this.name = name;
        this.standardCode = standardCode;
    }

    public static UnitEnum getUnitEnum(String code){
        for (UnitEnum value : UnitEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        throw new RuntimeException("不存在的单位");
    }
}
