package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.vo.in.ExportBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeDetailDetailInVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 箱码明细 服务类
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public interface IProductBoxBarcodeDetailDetailService extends IJiHongService<ProductBoxBarcodeDetailDetailPO> {

    List<ProductBoxBarcodeDetailDetailPO> getByBarcodeNos(List<String> barcodeNos);

    void saveProductBoxBarcodeDetailDetailPOs(List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOs);

    void saveProductBoxBarcodeDetailDetailPO(ProductBoxBarcodeDetailDetailDTO productBoxBarcodeDetailDetailDTO);

    void activa(List<String> barcodeNos);

    void activa(String barcodeNo);

    void updateSkuCode(String barcodeNo);

    void destroyBox(String barcodeNo);

    void activeMessage(MessageStructDTO messageStructDTO);

    List<ProductBoxBarcodeDetailDetailPO> getByDetailId(Long id);

    Pagination<ProductBoxBarcodeDetailDetailDTO> getList(GetProductBoxBarcodeDetailDetailInVO getProductBoxBarcodeDetailDetailInVO);

    ProductBoxBarcodeDetailDetailPO getByBarcodeNo(String barcode);

    String exportBoxBarcode(ExportBoxBarcodeInVO exportBoxBarcodeInVO);
}
