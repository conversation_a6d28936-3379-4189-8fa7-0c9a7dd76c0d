package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.BasePaperDTO;
import cn.jihong.mes.production.api.model.enums.OutboundVerifyEnum;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public interface IProductVerifyService {

    /**
     * 类型
     */
    OutboundVerifyEnum getOutboundVerify();

    /**
     * 全局校验
     * @param planTicketNo
     * @param machineName
     * @param unit
     * @return
     */
    boolean verifyOutBount(String planTicketNo, String machineName, String unit, String processCode,
        BigDecimal producedQuantity);

    boolean verifyPallet(String planTicketNo, String machineName, String unit, String processCode,
        BigDecimal producedQuantity);

    /**
     * 获得工艺损耗率
     * @param planTicketNo
     * @return
     */
    BigDecimal getProcessAttritionRate(String planTicketNo,String process);

    /**
     * 获得开单标准消耗值
     * @param planTicketNo
     * @return
     */
    Map<String,BigDecimal> getStandardConsumption(String planTicketNo);

    /**
     * 获得重量、幅宽、克重
     * @param planTicketNo
     * @return
     */
    HashMap<String, BasePaperDTO> getBasePaperInfo(String planTicketNo, String machineName);

    /**
     * kg到pcs单位转换
     * @param planTicketNo
     * @param weight
     * @return
     */
    BigDecimal kgToPcs(String planTicketNo,BigDecimal weight,String itemCode,String process);

    /**
     * pcs到kg单位转换
     * @param planTicketNo
     * @param pcs
     * @return
     */
    BigDecimal pcsToKg(String planTicketNo, BigDecimal pcs,String process);

    /**
     * Kg到m单位转换
     * @param planTicketNo
     * @param machineName
     * @return
     */
    BigDecimal kgToM(BigDecimal loadingQuantity,String planTicketNo,String machineName,String materialCode,String process);

    /**
     * m到Kg单位转换
     * @param planTicketNo
     * @return
     */
    BigDecimal mToKg(String planTicketNo,String machineName,String materialCode, BigDecimal loadingQuantity,String process);

    /**
     * m到pcs单位转换
     * @param planTicketNo
     * @return
     */
    BigDecimal mToPcs( String planTicketNo,String itemCode,String machineName,String materialCode, BigDecimal loadingQuantity,String processCode);

    /**
     * pcs到m单位转换
     * @param planTicketNo
     * @param pcs
     * @return
     */
    BigDecimal pcsToM(BigDecimal loadingQuantity,String planTicketNo, String machineName,BigDecimal pcs,String materialCode,String processCode);

    /**
     * 米转张
     * @param planTicketNo
     * @return
     */
    BigDecimal mToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialCode,String processCode);


    BigDecimal pcsToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialCode,String processCode);


    BigDecimal kgToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialType,String processCode);

    BigDecimal pcsToM(String planTicketNo, String machineName, BigDecimal pcs, String materialCode,String processCode);

    BigDecimal yToPcs(BigDecimal loadingQuantity, String planTicketNo,String processCode);


}
