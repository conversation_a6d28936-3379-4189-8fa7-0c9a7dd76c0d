package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.mes.production.api.model.dto.ProductSettingDelayedActiveDTO;
import cn.jihong.mes.production.api.model.po.ProductSettingDelayedActivePO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 工单配置箱码延迟激活 服务类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface IProductSettingDelayedActiveService extends IJiHongService<ProductSettingDelayedActivePO> {

    Pagination<ProductSettingDelayedActiveDTO> getPage(PageRequest pageRequest);

    Long add(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO);

    Long update(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO);

    List<ProductSettingDelayedActiveDTO> getList(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO);

}
