package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetProductBoxBarcodeDetailDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 箱码号
     */
    private String barcodeNo;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 料号
     */
    private String materialCode;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 批次号
     */
    private String lotNo;

    /**
     * 班次 01 02
     */
    private String shift;

    /**
     * 打印状态 0 未打印 1 已打印
     */
    private Integer printStatus;

    /**
     * 激活状态 0 未激活 1 已激活  2 已销毁
     */
    private Integer activa;

}
