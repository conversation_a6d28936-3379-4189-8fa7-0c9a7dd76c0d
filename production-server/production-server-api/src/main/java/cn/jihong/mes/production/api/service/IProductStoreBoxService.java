package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductStoreBoxPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 入库箱码信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface IProductStoreBoxService extends IJiHongService<ProductStoreBoxPO> {

    List<ProductStoreBoxPO> getByStoreId(Long storeId);

    List<ProductStoreBoxPO> getByBoxCode(String boxCode);

    List<ProductStoreBoxPO> getByBoxCodes(List<String> boxCodes);

    void verifyBoxCode(List<String> boxCodes);

    void verifyBoxCodeFromWms(List<String> boxCodes);
}
