package cn.jihong.mes.production.api.service;


import cn.jihong.common.model.StandardResult;
import cn.jihong.mes.production.api.model.dto.DownMaterialFlowDTO;
import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductMaterialOperationRecordsPO;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.vo.in.GetSubmitWorkflowRequestInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyMaterialOverClaimRequestInVO;
import cn.jihong.mes.production.api.model.vo.out.MaterialRecordsOutVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyMaterialOverClaimRequestOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OaMaterialOverClaimDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 物料操作记录信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface IProductMaterialOperationRecordsService extends IJiHongService<ProductMaterialOperationRecordsPO> {


    void work(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO,String code);
    /**
     * 上料
     *
     * @param productMaterialOperationRecordsDTO
     */
    void upMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO);

    /**
     * 下料
     *
     * @param productMaterialOperationRecordsDTO
     */
    void downMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO);

    /**
     * 换料
     *
     * @param productMaterialOperationRecordsDTO
     */
    void changeMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO);

    /**
     * 保存物料管理信息
     * @param productMaterialOperationRecordsDTO
     */
    ProductMaterialOperationRecordsPO saveOrUpdateMaterialUse(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO);

    Page<MaterialRecordsOutVO> getMaterialRecords(ProductTicketPageInVO productTicketPageInVO);

    Page<MaterialRecordsOutVO> getMaterialRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    /**
     * 获得上料信息
     * @param productTicketIds
     * @return
     */
    List<ProductMaterialOperationRecordsDTO> getByProductTicketIds(List<Long> productTicketIds,List<Integer> operationType);

    List<ProductMaterialOperationRecordsPO> getByMateriaId(Long materiaId);

    Page<MaterialRecordsOutVO> getMaterialDownRecords(ProductTicketPageInVO productTicketPageInVO);

    /**
     * 获取操作记录根据OA请求id
     */
    ProductMaterialOperationRecordsPO getRecordByWorkflowRequestId(Long workflowRequestId);


    VerifyMaterialOverClaimRequestOutVO verifyMaterialOverClaimRequest(VerifyMaterialOverClaimRequestInVO inVO);

    /**
     * 获取请求OA超领单相关参数
     * @param productMaterialOperationRecordId
     * @return: cn.jihong.workflow.common.model.request.SubmitWorkflowRequest
     * <AUTHOR>
     * @date: 2024-09-12 17:18
     */
    StandardResult sendSubmitWorkflowRequest(Long productMaterialOperationRecordId,String reason);

    /**
     * 超领单最后批准
     */
    Boolean  finalApprove(DownMaterialFlowDTO inVO);

    Long getLatestRecordId(Long id);
}
