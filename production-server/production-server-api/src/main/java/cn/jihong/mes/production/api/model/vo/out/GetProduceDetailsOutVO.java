package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetProduceDetailsOutVO implements Serializable {

    /**
     * 工序
     */
    private String process;
    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 生产列表
     */
    private List<ProduceInfo> produceLists;

    @Data
    public static class ProduceInfo implements Serializable {

        /**
         * 工程单号
         */
        private String planTicketNo;

        /**
         * 生产机台
         */
        private String machineName;

        /**
         * 生产日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date produceDate;

        /**
         * 班次
         */
        private Integer shift;

        /**
         * 班组人员id
         */
        private String teamUsers;

        /**
         * 班组人员名称
         */
        private String teamUsersName;

        /**
         * 生产数量
         */
        private BigDecimal productionNum;

        /**
         * 不良品数量
         */
        private BigDecimal defectiveProduct;

        /**
         * 首检
         */
        private String firstCheckResult;

    }

}
