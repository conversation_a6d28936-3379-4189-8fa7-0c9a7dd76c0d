package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class GetProductBoxBarcodeDetailOutVO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 班次 1 2
     */
    private String shift;


    /**
     * 码段标识
     */
    private String barcodeName;

    /**
     * 箱码开始号
     */
    private String barcodeNoStart;


    /**
     * 箱码截止号
     */
    private String barcodeNoEnd;


    /**
     * 箱码总数量
     */
    private Integer barcodeTotal;


    /**
     * 箱码剩余数量
     */
    private Integer barcodeRemaining;

    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;

    /**
     * 作废状态 0 作废  1 未作废
     */
    private Integer disableStatus;

    /**
     * 箱码数量
     */
    private Integer boxNum;

    /**
     * 物料编码
     */
    private String materialCode;

    /*
     * 批次识别号
     */
    private String batchCode;

    /**
     * 机台
     */
    private String machine;

    /**
     * 过期日期
     */
    private String expirationDate;

    /**
     * 喷码状态 0 未打印  1 已打印
     */
    private Integer spurtStatus;

}
