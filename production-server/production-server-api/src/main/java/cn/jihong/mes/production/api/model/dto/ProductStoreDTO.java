package cn.jihong.mes.production.api.model.dto;

import cn.jihong.mes.production.api.model.vo.in.InboundRequestByPalletInVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入库表
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
public class ProductStoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 箱码
     */
    private String caseCode;

    /**
     * 已生产
     */
    private BigDecimal producedQuantity;

    /**
     * 箱规
     */
    private BigDecimal boxSpace;

    /**
     * 单位
     */
    private String unit;

    /**
     * 入库申请单号
     */
    private String storeApplyNo;

    /**
     * 入库单号
     */
    private String storeNo;

    /**
     * 入库状态 10 已申请 20 已拉货 30 已入库
     */
    private String storeStatus;

    /**
     * 入库类型  1 按箱入库  2 按托入库
     */
    private Integer storeType;

    /**
     * 栈板信息
     */
    private List<InboundRequestByPalletInVO.PalletCodeInfo> palletCodeInfos;

    /**
     * 是否是成品
     */
    private Integer isFinalProduct = 1;

    /**
     * 超产原因
     */
    private String reason;



    @Data
    public static class PalletCodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 栈板数量
         */
        @NotNull(message = "栈板数量不能为空")
        private BigDecimal palletCodeQuantity;

        /**
         * 栈板码
         */
        private String palletCode;

    }

}
