package cn.jihong.mes.production.api.model.enums;

import cn.jihong.common.util.AssertUtil;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum ReportShiftNoEnum {

    DAY_SHIFT(1, "B001","白班",0),
    GRAVEYARD_SHIFT(2, "W001","晚班",0),
    A_DAY_SHIFT(3, "A-B001","A组-白班",1),
    A_GRAVEYARD_SHIFT(4, "A-W001","A组-晚班",2),
    B_DAY_SHIFT(5, "B-B001","B组-白班",1),
    B_GRAVEYARD_SHIFT(6, "B-W001","B组-晚班",2)
    ;

    private Integer code;

    private String name;

    private String description;

    private Integer parentCode;


    ReportShiftNoEnum(Integer code, String name,String description,Integer parentCode) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.parentCode = parentCode;
    }

    public static ReportShiftNoEnum getReportShiftNoEnum(Integer code) {
        for (ReportShiftNoEnum value : ReportShiftNoEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的报工班次");
    }

    public static List<ReportShiftNoEnum> getReportShiftListEnumByParentCode(Integer parentCode) {
        List<ReportShiftNoEnum> list = new ArrayList<>();
        for (ReportShiftNoEnum value : ReportShiftNoEnum.values()) {
            if (value.getParentCode().equals(parentCode)) {
                list.add(value);
            }
        }
        AssertUtil.isNotEmpty(list,"不存在的报工班次");
        return list;
    }

}
