package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/30 10:05
 */
@Data
public class UpdateMachineTaskHistoryInVO implements Serializable {
    private static final long serialVersionUID = 6667126554681072243L;

    /**
     * 报工操作记录id
     */
    @NotNull(message = "报工操作记录id不能为空")
    private Long machineTaskHistoryId;

    /**
     * 报工数量
     */
    @NotNull(message = "报工数量不能为空")
    private BigDecimal reportedQuantity;
}
