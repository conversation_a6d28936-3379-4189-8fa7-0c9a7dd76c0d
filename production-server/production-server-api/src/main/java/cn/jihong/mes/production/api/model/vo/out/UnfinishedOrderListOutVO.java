package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/26 11:37
 */
@Data
public class UnfinishedOrderListOutVO implements Serializable {
    private static final long serialVersionUID = 4517653694149338489L;


    /**
     * id
     */
    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 工序名称
     */
    private String process;

    /**
     * 工序
     */
    private String processType;


    /**
     * 工序code
     */
    private String processCode;


    /**
     * 设备止码
     */
    private Integer machineStopNo;


    /**
     * 班次
     */
    private Integer shift;


    /**
     * 生产计划工单单号
     */
    private String planTicketNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 开始时间
     */
    private Date startDate;


    /**
     * 结束时间
     */
    private Date endDate;


    /**
     * 计划开始时间
     */
    private Date planStartDate;


    /**
     * 计划结束时间
     */
    private Date planEndDate;


    /**
     * 计划产量
     */
    private BigDecimal plannedProduct;


    /**
     * 工单中心工单号
     */
    private String ticketRequestId;


    /**
     * 业务编号
     */
    private String businessNo;


    /**
     * 处理人
     */
    private Long processorId;


    /**
     * 处理状态
     */
    private Integer status;



    /**
     * 备注
     */
    private String remark;


    /**
     * 已生产
     */
    private BigDecimal realProduct;


    /**
     * 班组人员id
     */
    private String teamUsers;

    /**
     * 工单类型  5  生产工单  10 转产工单  11  结单工单
     */
    private Integer ticketType;

    /**
     * 不良品数量
     */
    private BigDecimal defectiveProduct;

    /**
     * 重工数量
     */
    private BigDecimal heavyProduct;

    /**
     * 是否报工  0 否 1 是
     */
    private Integer isSignUp;

}
