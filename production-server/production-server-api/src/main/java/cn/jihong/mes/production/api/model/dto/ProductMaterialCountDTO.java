package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 机台上料和上栈板的数量
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
public class ProductMaterialCountDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序
     */
    private String process;

    /**
     * 物料类型
     */
    private String materialCode;

    /**
     * 栈板数量
     */
    private Integer palletCount;

    /**
     * 物料数量
     */
    private Integer materialCount;

    /**
     * 备注
     */
    private String remark;



}
