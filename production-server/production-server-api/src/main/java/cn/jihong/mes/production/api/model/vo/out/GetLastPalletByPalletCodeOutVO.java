package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetLastPalletByPalletCodeOutVO implements Serializable {



    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板来源
     */
    private String palletSource;


    /**
     * 生产工程单号
     */
    private String productionOrder;


    /**
     * 生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionTime;

    /**
     * 上料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loadingTime;

    /**
     * 领料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 单位名称
     */
    private String unitName;


    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 是否存在旧的相同工序的栈板正在使用  0 否  1  是
     */
    private Integer hasOldPallet;

    /**
     * 上一个栈板的信息
     */
    private LastPalletOutVO lastPalletOutVO;

    @Data
    public static class LastPalletOutVO{

        /**
         * 消耗数量
         */
        private BigDecimal consumptionQuantity;

        /**
         * 剩余数量
         */
        private BigDecimal remainingQuantity;

    }

    /**
     * 栈板短码状态  0 未查到  1  已查到
     */
    private Integer palletShortCodeStatus;

    /**
     * 栈板短码信息
     */
    private String palletShortCodeMessage;

    /**
     * 生产工程单号
     */
    private String planTicketNo;


}
