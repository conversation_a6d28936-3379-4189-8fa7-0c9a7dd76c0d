package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UpdateOutboundInVO implements Serializable {

    /**
     * 出站记录表id
     */
    @NotNull(message = "记录表id不能为空")
    private Long outboundId;

    /**
     * 出站数量
     */
    @NotNull(message = "出站数量不能为空")
    private BigDecimal producedQuantity;

}
