package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ChangeBoxInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 旧箱码
     */
    @NotBlank(message = "旧箱码不能为空")
    private String oldBoxCode;

    /**
     * 新箱码
     */
    @NotBlank(message = "新箱码不能为空")
    private String newBoxCode;



}
