package cn.jihong.mes.production.api.service;


import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsCallbackPO;
import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 * 不良品回调表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface IProductDefectiveProductsCallbackService extends IJiHongService<ProductDefectiveProductsCallbackPO> {

    List<ProductDefectiveProductsCallbackPO> getListByProductTicketId(Long productTicketId);

    void updateDefectiveProductsCallback(ProductDefectiveProductsPO productDefectiveProductsPO);
}
