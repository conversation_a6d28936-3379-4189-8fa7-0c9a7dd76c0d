package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.mes.production.api.model.vo.out.GetOldBoxCodeDetailOutVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class CopyOldBoxCodeToNewBoxCodeInVO implements Serializable {

    @NotNull(message = "旧箱码信息不能为空")
    private GetOldBoxCodeDetailOutVO oldBoxCodeDetailOutVO;

    @NotNull(message = "新箱码信息不能为空")
    private List<String> newBoxCodeList;

}
