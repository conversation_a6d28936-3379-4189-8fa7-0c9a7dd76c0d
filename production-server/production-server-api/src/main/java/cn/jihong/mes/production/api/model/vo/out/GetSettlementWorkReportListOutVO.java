package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/8 15:56
 */
@Data
public class GetSettlementWorkReportListOutVO implements Serializable {


    /**
     * 生产工程单号
     */
    private String productTicketNo;


    /**
     * 工序名称
     */
    private String processName;


    /**
     * 生产数量
     */
    private BigDecimal realProductQuantity;


    /**
     * 报废数量
     */
    private BigDecimal scrapQuantity;

    /**
     * 损耗率
     */
    private BigDecimal attritionRate;

    /**
     * 工序顺序
     */
    private Integer processSeq;

}
