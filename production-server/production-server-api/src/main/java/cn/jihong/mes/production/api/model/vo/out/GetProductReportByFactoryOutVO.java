package cn.jihong.mes.production.api.model.vo.out;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetProductReportByFactoryOutVO {

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 班次
     */
    private Integer shift;
    private String shiftName;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计划产量
     */
    private BigDecimal plannedProductionCapacity;

    /**
     * 标准产能/H
     */
    private BigDecimal standardProductionCapacity;


    /**
     * 生产时长（H，实际）
     */
    private BigDecimal realHours;

    /**
     * 生产时长（H，班次）
     */
    private BigDecimal planHours;

    /**
     * 已生产
     */
    private BigDecimal realProductionCapacity;


    /**
     * 不良品数量
     */
    private BigDecimal defectiveProduct;

    /**
     * 损耗率（%）
     */
    private BigDecimal lossRate;

    /**
     * 生产计划达成率（%）
     */
    private BigDecimal planReachRate;

    /**
     * 计划产能利用率（%）
     */
    private BigDecimal planCapacityUtilRate;

    /**
     * 实际产能利用率（%）
     */
    private BigDecimal realCapacityUtilRate;

    /**
     * 设备时间稼动率（%）
     */
    private BigDecimal machineUtilRate;


    /**
     * 工序
     */
    private String process;

    /**
     * 班组人员
     */
    private String teamUsers;

    /**
     * 排产状态
     */
    private Integer planStatus;

    /**
     * 生产状态
     */
    private Integer productStatus;

    /**
     * 发送备注
     */
    private String sendRemark;

    /**
     * 发送人
     */
    private String sendName;

    /**
     * 设备利用率
     */
    private BigDecimal equipmentUtilizationRate;

    /**
     * 良品率
     */
    private BigDecimal yieldRate;

}
