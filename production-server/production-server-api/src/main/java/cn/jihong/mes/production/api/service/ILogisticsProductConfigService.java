package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.LogisticsProductConfigDTO;
import cn.jihong.mes.production.api.model.po.LogisticsProductConfigPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 物流产品配置信息 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ILogisticsProductConfigService extends IJiHongService<LogisticsProductConfigPO> {

    Pagination<LogisticsProductConfigDTO> getList(LogisticsProductConfigDTO logisticsProductConfigDTO);

    LogisticsProductConfigDTO getById(Long id);

    LogisticsProductConfigDTO getLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO);

    void saveLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO);

    void updateLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO);

    void delete(Long id);

    List<EnumDTO> getMaterialCategory(Long id);

    List<EnumDTO> getProcessType();
}
