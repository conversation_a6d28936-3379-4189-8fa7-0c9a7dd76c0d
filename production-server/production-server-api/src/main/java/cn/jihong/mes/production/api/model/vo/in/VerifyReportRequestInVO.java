package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-15 15:06
 */
@Data
public class VerifyReportRequestInVO implements Serializable {

    /**
     * 报工生产工单id 如果类型是生产则必传
     */
    @NotNull(message = "工单id不能为空")
    private Long reportedProductId;


    /**
     * 报工数量
     */
    @NotNull(message = "报工数量不能为空")
    private BigDecimal reportedQuantity;



}
