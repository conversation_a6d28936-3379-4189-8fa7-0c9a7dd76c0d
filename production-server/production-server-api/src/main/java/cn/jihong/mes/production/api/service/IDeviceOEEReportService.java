package cn.jihong.mes.production.api.service;

import java.util.List;
import java.util.Map;

import cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO;
import cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO;
import cn.jihong.mes.production.api.model.vo.in.DeviceOEEReportShowInVO;
import cn.jihong.mes.production.api.model.vo.in.GetOeeInVO;
import cn.jihong.mes.production.api.model.vo.out.DeviceOEEReportShowOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOeeOutVO;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByCompanyCodeOutVO;

/**
 * <AUTHOR>
 * @date 2025-03-22 16:55
 */
public interface IDeviceOEEReportService {

    List<DeviceOEEReportShowOutVO> deviceOEEReportShow(DeviceOEEReportShowInVO inVO);

    GetOeeOutVO getOee(GetOeeInVO inVO);

    List<GetOocqlTByCompanyCodeOutVO> getAllProcess();

}
