package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/22 11:03
 */
@Data
public class GetPageByDayInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 7864165120106160241L;

    /**
     * 工厂代码不能为空
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 工程单号
     */
    private String planTicketNo;


    /**
     * 生产日期
     */
    private String produceDate;


}
