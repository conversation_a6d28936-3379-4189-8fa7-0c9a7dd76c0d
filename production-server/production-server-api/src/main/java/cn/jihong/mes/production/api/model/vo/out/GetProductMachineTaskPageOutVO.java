package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/10 10:04
 */
@Data
public class GetProductMachineTaskPageOutVO implements Serializable {
    private static final long serialVersionUID = 727936408893294579L;

    /**
     * 机台任务类型: 1-生产 2-调机 3-会议
     */
    private Integer type;

    /**
     * 机台任务类型
     */
    private String typeName;

    /**
     * 机台任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;


    /**
     * 机台任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 上报人id
     */
    private Long reportPersonId;

    /**
     * 上报人
     */
    private String reportPersonName;


    /**
     * 班组成员id
     */
    private String teamUsers;

    /**
     * 班组成员名称
     */
    private String teamUsersName;

    /**
     * 工序
     */
    private String process;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 消耗小时
     */
    private BigDecimal consumingHours;

    private String lossType;

}
