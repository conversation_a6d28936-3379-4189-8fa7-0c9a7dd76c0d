//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.jihong.mes.production.api.model.enums;

import cn.jihong.common.exception.CommonException;
import cn.jihong.oa.approve.api.enums.BusinessType;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum ProductionBusinessType {
    MATERIAL_OVER_CLAIM(442L, "超领单", "materialOverClaim"),

    ;

    private Long workflowid;
    private String name;
    private String code;
    private static final Map<Long, ProductionBusinessType> businessTypeMap = (Map)Arrays.stream(values()).collect(Collectors.toMap(ProductionBusinessType::getWorkflowid, Function.identity()));
    private static final Map<String, ProductionBusinessType> businessTypeByCodeMap = (Map)Arrays.stream(values()).collect(Collectors.toMap(ProductionBusinessType::getCode, Function.identity()));

    private ProductionBusinessType(Long workflowid, String name, String code) {
        this.workflowid = workflowid;
        this.name = name;
        this.code = code;
    }

    public static boolean isWorkflowid(Long workflowid) {
        ProductionBusinessType businessType = (ProductionBusinessType)businessTypeMap.get(workflowid);
        return businessType != null;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Long getWorkflowid() {
        return this.workflowid;
    }

    public static ProductionBusinessType getByWorkflowid(Long workflowid) {
        ProductionBusinessType businessType = (ProductionBusinessType)businessTypeMap.get(workflowid);
        if (businessType == null) {
            throw new CommonException("暂不支持业务类型：%s", new Object[]{workflowid});
        } else {
            return businessType;
        }
    }

    public static ProductionBusinessType getByWorkflowCode(String workflowCode) {
        ProductionBusinessType businessType = (ProductionBusinessType)businessTypeByCodeMap.get(workflowCode);
        if (businessType == null) {
            throw new CommonException("暂不支持业务类型：%s", new Object[]{workflowCode});
        } else {
            return businessType;
        }
    }
}
