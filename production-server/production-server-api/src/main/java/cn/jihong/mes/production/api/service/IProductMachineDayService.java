package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ProductMachineDayDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineDayPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementPageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetPlaceTypeInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineDayOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 机台一日一结信息 服务类
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
public interface IProductMachineDayService extends IJiHongService<ProductMachineDayPO> {

    void saveProductMachineDay(ProductMachineDayDTO productMachineDayDTO);

    /**
     * 完成机台一日一结
     * @param productMachineDayDTO
     */
    void finishProductMachineDay(ProductMachineDayDTO productMachineDayDTO);

    /**
     * 分摊一单一结
     * @param productMachineDayDTO
     * @return
     */
    void apportionmentProductMachineDay(ProductMachineDayDTO productMachineDayDTO);



    Pagination<ProductMachineDayOutVO> getProductMachineDays(GetMaterialDailySettlementPageInVO getMaterialDailySettlementInVO);

    ProductMachineDayOutVO getProductMachineDay(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    List<EnumDTO> getOocqlTByType(GetPlaceTypeInVO getPlaceTypeInVO);

    ProductMachineDayOutVO getProductMachineDayApp(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    void updateIsFinish(Long id);
}
