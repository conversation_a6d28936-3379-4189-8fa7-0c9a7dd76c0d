package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-04-18 15:54
 */
@Data
public class GetProcessQuantityByPlanTicketNo implements Serializable {



    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 当前工序编码
     */
    private String processCode;

    /**
     * 单位
     */
    private String unit;

    /**
     * 报工良品数量
     */
    private BigDecimal totalGoodProductQuantity;

    /**
     * 报工不良品数量
     */
    private BigDecimal totalDefectiveProductQuantity;

    /**
     * 报工总数量
     */
    private BigDecimal totalReportQuantity;

    /**
     * 最后报工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

}
