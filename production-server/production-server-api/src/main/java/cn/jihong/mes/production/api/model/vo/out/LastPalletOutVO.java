package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class LastPalletOutVO implements Serializable {

    private Long id;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 栈板来源
     */
    private String palletSource;


    /**
     * 生产工程单号
     */
    private String productionOrder;


    /**
     * 生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionTime;

    /**
     * 上料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loadingTime;

    /**
     * 领料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    private Long createBy;

    private String createrName;

    /**
     * 班组人员id
     */
    private String teamUsers;
    private String teamUsersName;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次  1 白班  2 夜班
     */
    private Integer shift;


    /**
     * 是否存在旧的相同工序的栈板正在使用  0 否  1  是
     */
    private Integer hasOldPallet;

    /**
     * 栈板状态  1 在库  2 在产 3 暂存 4 备料
     */
    private Integer status;
    private String statusName;

}
