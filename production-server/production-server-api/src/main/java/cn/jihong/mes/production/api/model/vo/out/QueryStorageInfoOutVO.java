package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class QueryStorageInfoOutVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 栈板数量
     */
    private String palletCount;

    /**
     * 入库类型 1 按箱入库 2 按托入库
     */
    private Integer storeType;

    /**
     * 入库申请单号
     */
    private String storeApplyNo;

    /**
     * 入库状态 10 已申请 20 已拉货 30 已入库
     */
    private String storeStatus;

    /**
     * 已生产
     */
    private BigDecimal producedQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 申请人名称
     */
    private String applyUserName;


    private String storeNo;




    /**
     * 储位
     */
    private String storage;

    /**
     * 已入库数量
     */
    private Integer hadStoreCount;

    /**
     * 已入库数量
     */
    private Integer isAllIn;

    /**
     * 规格
     */
    private String standard;

    /**
     * 批次号
     */
    private String lotNo;

    /**
     * 栈板码
     */
    private String palletCode;

}
