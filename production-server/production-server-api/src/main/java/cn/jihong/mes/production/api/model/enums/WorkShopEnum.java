package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum WorkShopEnum {

    HUSHI_WATERMARK("SITE-06-1", "呼市水印车间"),
    HUSHI_OFFSET_PRINT("SITE-06-2", "呼市胶印车间"),
    HUSHI_TILE_WIRE("SITE-06-3", "呼市瓦线车间")

            ;

    private String code;

    private String name;

    WorkShopEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static WorkShopEnum getWorkShopEnum(String code) {
        for (WorkShopEnum value : WorkShopEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的车间");
    }

}
