package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetMachineGroupReportOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组ID
     */
    private Long groupId;
    private String groupName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;
    /**
     * 生产班次
     */
    private Integer shift;
    private String shiftName;

    private List<GetMachineGroupDetialReportOutVO> machineGroupDetialReportOutVOList;

    @Data
    public static class GetMachineGroupDetialReportOutVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 机台名称
         */
        private String machineName;
        /**
         * 工程单号
         */
        private String planTicketNo;


        /**
         * 工序
         */
        private String process;
        private String processCode;
        /**
         * 产品名称
         */
        private String productionName;
        /**
         * 计划产量
         */
        private String plannedProductionCapacity;
        /**
         * 计划产量单位
         */
        private String plannedProductionCapacityUnit;
        private String plannedProductionCapacityColor;

        /**
         * 实际产量(成品数量)
         */
        private BigDecimal realProductionCapacity;
        /**
         * 实际产量单位
         */
        private String realProductionCapacityUnit;
        /**
         * 不良品数量
         */
        private BigDecimal defectiveProduct;
        /**
         * 不良品数量单位
         */
        private String defectiveProductUnit;


        /**
         * 生产计划达成率（%）
         */
        private BigDecimal planReachRate;
        private String planReachRateStr;
        /**
         * 生产计划达成率颜色
         */
        private String planReachRateColor;

        /**
         * 损耗率（%）
         */
        private BigDecimal lossRate;
        private String lossRateStr;
        /**
         * 损耗率颜色
         */
        private String lossRateColor;


        /**
         * 班组人员
         */
        private String teamUsers;

        /**
         * 备注
         */
        private String remark;


        /**
         * 包含的任务id
         */
        private String includeProductTicketIds;

        /**
         * 用料列表
         */
        private List<UseMaterialOutVO> useMaterialList;

        /**
         * 超出标准损耗-用料列表
         */
        private List<UseMaterialOutVO> useMaterialOutStandardList;

        /**
         * 正扣料超出标准损耗-用料列表
         */
        private List<UseMaterialOutVO> usePositiveMaterialOutStandardList;


        @Data
        public static class UseMaterialOutVO implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 料名
             */
            private String materialName;

            /**
             * 料号
             */
            private String materialCode;

            /**
             * 用料料部位
             */
            private String materialPlace;

            /**
             * 消耗数量
             */
            private BigDecimal consumptionQuantity;

            /**
             * 单位
             */
            private String materialUnit;

            /**
             * 工程单单位
             */
            private String planTicketUnit;

            /**
             * 物料类别
             */
            private String materialType;

            /**
             * 物料使用类型： 10 正扣料 20 分摊 30 倒扣料
             */
            private Integer useType;

            /**
             * 物料使用类型名称： 10 正扣料 20 分摊 30 倒扣料
             */
            private String useTypeName;

            /**
             * 标准消耗
             */
            private BigDecimal standardLossRate;

            /**
             * 标准消耗文本
             */
            private String standardLossRateStr;

            /**
             * 实际消耗
             */
            private BigDecimal realLossRate;

            /**
             * 实际消耗文本
             */
            private String realLossRateStr;

            /**
             * 是否超过标准消耗  0：否  1：是
             */
            private String outStandard;

            /**
             * 是否 mes使用物料
             */
            private String mesUseMaterial;

            /**
             * 标准消耗与实际消耗占比数值
             */
            private BigDecimal standardLossAndRealLossRate;

            /**
             * 标准消耗与实际消耗占比文本
             */
            private String standardLossAndRealLossRateStr;

        }
     }


}
