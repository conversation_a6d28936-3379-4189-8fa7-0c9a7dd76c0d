package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum ErpDocTypeEnum {

    BUCKLE("", "扣料"),
    STRIPPING("asft314", "退料"),
    ;

    private String code;

    private String name;

    ErpDocTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ErpDocTypeEnum getErpDocTypeEnum(String code) {
        for (ErpDocTypeEnum value : ErpDocTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的公司");
    }



}
