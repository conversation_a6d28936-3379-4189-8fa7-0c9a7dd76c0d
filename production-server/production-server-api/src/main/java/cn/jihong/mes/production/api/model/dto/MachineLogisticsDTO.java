package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-12 13:46
 */
@Data
public class MachineLogisticsDTO implements Serializable {

    /**
     * 车间编码
     */
    private String workshopCode;

    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 网带列表
     */
    private List<MeshInfo> meshInfoList;

    @Data
    public static class MeshInfo implements Serializable{

        /**
         * 网带类型 （1：供料  2：出口）
         */
        private int type;

        /**
         * 网带编码
         */
        private String meshCode;

        /**
         * 网带工序
         */
        private String processCode;

    }


}
