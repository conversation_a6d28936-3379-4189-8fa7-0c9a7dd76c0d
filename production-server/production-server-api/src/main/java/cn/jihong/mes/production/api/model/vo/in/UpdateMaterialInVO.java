package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UpdateMaterialInVO implements Serializable {

    /**
     * 物料操作记录id
     */
    @NotNull(message = "物料id不能为空")
    private Long materialRecordId;

    /**
     * 消耗数量
     */
    @NotNull(message = "消耗数量不能为空")
    private BigDecimal consumptionQuantity;

}
