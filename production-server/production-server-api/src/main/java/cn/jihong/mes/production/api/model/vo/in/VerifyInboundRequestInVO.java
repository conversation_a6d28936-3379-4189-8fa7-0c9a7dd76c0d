package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-05-15 15:06
 */
@Data
public class VerifyInboundRequestInVO implements Serializable {

    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;


    /**
     * 单位
     */
    private String unit;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;


}
