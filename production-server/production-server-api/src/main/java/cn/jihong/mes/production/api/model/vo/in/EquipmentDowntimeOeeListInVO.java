package cn.jihong.mes.production.api.model.vo.in;


import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 设备停机代码表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
public class EquipmentDowntimeOeeListInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 停机代码
     */
    private String downtimeCode;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

}