package cn.jihong.mes.production.api.model.vo.out;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/9 16:47
 */
@Data
public class GetProductMachineTaskHistoryPageOutVO implements Serializable {
    private static final long serialVersionUID = 8358236245008522399L;


    /**
     * 报工操作记录id
     */
    private Long machineTaskHistoryId;

    /**
     * 报工人id
     */
    private Long reportedUserId;

    /**
     * 报工人名称
     */
    private String reportedUserName;

    /**
     * 班组成员id
     */
    private String teamUsers;

    /**
     * 班组成员名称
     */
    private String teamUsersName;

    /**
     * 工序
     */
    private String process;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity;

    /**
     * 报工时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportedTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 计件类型
     */
    private String pieceType;

    /**
     * OA审批流id
     */
    private Long workflowRequestId;

    /**
     * OA审批流状态
     */
    private Integer workflowRequestStatus;
}
