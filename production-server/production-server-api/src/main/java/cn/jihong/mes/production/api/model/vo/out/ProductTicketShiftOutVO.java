package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductTicketShiftOutVO implements Serializable {

    private Long id;

    /**
     * 公司code
     */
    private String companyCode;
    /**
     * 机台名称
     */
    private String machineName;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;
    /**
     * 生产班次
     */
    private Integer shift;

    /**
     * 生产计划工单号
     */
    private String planTicketNo;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 标准产能
     */
    private String standardProductionCapacity;
    /**
     * 计划产量
     */
    private String plannedProductionCapacity;
    /**
     * 实际产量
     */
    private String realProductionCapacity;
    /**
     * 计划生产时间
     */
    private BigDecimal planHours;
    /**
     * 实际生产时间
     */
    private BigDecimal realHours;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanStartTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanEndTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    private String processType;
    private String process;

}
