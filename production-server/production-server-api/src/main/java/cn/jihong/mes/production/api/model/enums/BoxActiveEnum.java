package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum BoxActiveEnum {

    PRINT(0, "打印"), ACTIVE(1, "激活"), DESTROY(2, "注销"),;

    private Integer code;

    private String name;

    BoxActiveEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BoxActiveEnum getBoxActiveEnum(String code) {
        for (BoxActiveEnum value : BoxActiveEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的公司");
    }


}
