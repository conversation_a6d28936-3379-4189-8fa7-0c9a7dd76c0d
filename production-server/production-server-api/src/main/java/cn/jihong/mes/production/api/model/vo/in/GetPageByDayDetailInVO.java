package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/22 11:03
 */
@Data
public class GetPageByDayDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 7864165120106160241L;

    /**
     * 工厂代码不能为空
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

    /**
     * 工序编码
     */
    @NotBlank(message = "工序编码不能为空")
    private String processCode;

    /**
     * 生产日期
     */
    private String produceDate;

}
