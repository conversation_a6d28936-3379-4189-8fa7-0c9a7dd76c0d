package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductMachineGroupRelationshipPO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 机组和机台关系表 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IProductMachineGroupRelationshipService extends IJiHongService<ProductMachineGroupRelationshipPO> {

    List<ProductMachineGroupRelationshipPO> getByGroupId(Long groupId);

    List<ProductMachineGroupRelationshipPO> getByMachineNames(List<String> machineNames);

    void removeByGroupId(Long id);

    List<ProductMachineGroupRelationshipPO> getByCompanyCode(String companySite);
}
