package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.LogisticsProductManualRecordPO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordPageInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordGroupOutVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * 物流产品手动出站记录服务接口
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
public interface ILogisticsProductManualRecordService extends IJiHongService<LogisticsProductManualRecordPO> {
    
    /**
     * 保存物流产品手动出站记录
     *
     * @param inVO 物流产品手动出站记录入参
     * @return 是否保存成功
     */
    boolean saveManualRecord(LogisticsProductManualRecordInVO inVO);
    
    /**
     * 根据工单号和栈板短码查询记录
     *
     * @param queryInVO 查询参数
     * @return 记录详情
     */
    LogisticsProductManualRecordOutVO getByPlanTicketNoAndShortCode(LogisticsProductManualRecordQueryInVO queryInVO);
    
    /**
     * 保存并处理记录
     *
     * @param saveInVO 保存参数
     * @return 处理结果
     */
    String saveAndProcess(LogisticsProductManualRecordSaveInVO saveInVO);
    
    /**
     * 分页查询分组记录
     *
     * @param pageInVO 分页查询参数
     * @return 分页结果
     */
    Pagination<LogisticsProductManualRecordGroupOutVO> pageGroup(LogisticsProductManualRecordPageInVO pageInVO);
} 