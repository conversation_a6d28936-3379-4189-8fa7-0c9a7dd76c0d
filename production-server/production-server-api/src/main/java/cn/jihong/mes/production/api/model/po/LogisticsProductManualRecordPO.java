package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流产品手动出站记录
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
@TableName("logistics_product_manual_record")
public class LogisticsProductManualRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PROCESS_CODE = "process_code";
    public static final String PRODUCT_NAME = "product_name";
    public static final String PRODUCT_NO = "product_no";
    public static final String PALLET_CODE = "pallet_code";
    public static final String PALLET_SHORT_CODE = "pallet_short_code";
    public static final String MATERIAL_CATEGORY = "material_category";
    public static final String MATERIAL_CATEGORY_NAME = "material_category_name";
    public static final String PALLET_COUNT = "pallet_count";
    public static final String STACK_LENGTH = "stack_length";
    public static final String STACK_WIDTH = "stack_width";
    public static final String PALLET_LENGTH = "pallet_length";
    public static final String PALLET_WIDTH = "pallet_width";
    public static final String PALLET_COLUMNS = "pallet_columns";
    public static final String CUSTOMER_SHORT_NAME = "customer_short_name";
    public static final String PRODUCT_SHORT_NAME = "product_short_name";
    public static final String MESH_CODE = "mesh_code";
    public static final String STATUS = "status";
    public static final String RESULT = "result";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String PLAN_COUNT = "plan_count";
    public static final String IDS_PALLET_ID = "ids_pallet_id";

    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 据点
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 工单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;

    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;

    /**
     * 工序code
     */
    @TableField(PROCESS_CODE)
    private String processCode;

    /**
     * 产品名称
     */
    @TableField(PRODUCT_NAME)
    private String productName;

    /**
     * 产品编号
     */
    @TableField(PRODUCT_NO)
    private String productNo;

    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;

    /**
     * 栈板短码
     */
    @TableField(PALLET_SHORT_CODE)
    private String palletShortCode;

    /**
     * 物料类别
     */
    @TableField(MATERIAL_CATEGORY)
    private String materialCategory;

    /**
     * 物料类别名称
     */
    @TableField(MATERIAL_CATEGORY_NAME)
    private String materialCategoryName;

    /**
     * 栈板一拖的数量
     */
    @TableField(PALLET_COUNT)
    private Integer palletCount;

    /**
     * 堆长度
     */
    @TableField(STACK_LENGTH)
    private Integer stackLength;

    /**
     * 堆宽度
     */
    @TableField(STACK_WIDTH)
    private Integer stackWidth;

    /**
     * 托盘长
     */
    @TableField(PALLET_LENGTH)
    private Integer palletLength;

    /**
     * 托盘宽
     */
    @TableField(PALLET_WIDTH)
    private Integer palletWidth;

    /**
     * 托盘列
     */
    @TableField(PALLET_COLUMNS)
    private Integer palletColumns;

    /**
     * 客户简称
     */
    @TableField(CUSTOMER_SHORT_NAME)
    private String customerShortName;

    /**
     * 产品简称
     */
    @TableField(PRODUCT_SHORT_NAME)
    private String productShortName;

    /**
     * 网待编号
     */
    @TableField(MESH_CODE)
    private String meshCode;

    /**
     * 状态：1 出站  2 已上网带
     */
    @TableField(STATUS)
    private Integer status;

    /**
     * 处理结果
     */
    @TableField(RESULT)
    private String result;

    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 计划产量
     */
    @TableField(PLAN_COUNT)
    private Integer planCount;

    /**
     * ids内部托盘id
     */
    @TableField(IDS_PALLET_ID)
    private String idsPalletId;
} 