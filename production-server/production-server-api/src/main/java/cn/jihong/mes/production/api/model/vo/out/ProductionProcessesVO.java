package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工序信息
 */
@Data
public class ProductionProcessesVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    private String mono;

    /**
     * 工序编号
     */
    private String processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 物料信息
     */
    private MaterialInfo materialInfo;

    /**
     * 机台信息
     */
    private List<MachineInfo> machineInfos;


    @Data
    public static class MachineInfo implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 设备编号
         */
        private String deviceNo;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 加工时间
         */
        private Date processingDate;

        /**
         * 报废数量
         */
        private BigDecimal sumErrorQty;

        /**
         * 产量
         */
        private BigDecimal sumInputQty;

        /**
         * 班组信息
         */
        private List<ProcessTeamMembersVO> processTeamMembersVOS;

    }


    @Data
    public static class MaterialInfo implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 原材料编号
         */
        private List<String> itemNos;

        /**
         * 原材料批号
         */
        private List<String> lotNos;

    }


}
