package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 物料操作记录信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Data
public class ProductMaterialOperationRecordsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 工单类型
     */
    private String ticketType;

    /**
     * 工单编号
     */
    private String ticketNumber;

    /**
     * 操作人员
     */
    private Long operator;

    /**
     * 原数量
     */
    private BigDecimal originalQuantity;

    /**
     * 消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料表id
     */
    private Long productMaterialId;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    /**
     * 剩余原因
     */
    private String remainingReason;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 请求id，用于记录请求到外部服务的id
     */
    private String requestId;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;
    private String materialUnitName;


    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 调拨单号
     */
    private String transferNo;

    /**
     * 扣料单号
     */
    private String deductionNo;

    /**
     * 仓位编号
     */
    private String warehouseNo;

    /**
     * 仓位库名称
     */
    private String warehouseName;

    /**
     * 储位编号
     */
    private String storageNo;

    /**
     * 储位名称
     */
    private String storageName;

}
