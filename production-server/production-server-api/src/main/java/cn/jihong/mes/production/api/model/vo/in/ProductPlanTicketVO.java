package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ProductPlanTicketVO implements Serializable {

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 生产工程单
     */
//    @NotBlank(message = "生产工程单不能为空")
    private String planTicketNo;

}
