package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum CompanyBoxEnum {

    COMPANY_ZWL("ZWL", "智物流"),
    COMPANY_TCBZ("TCBZ", "天成包装"),
    COMPANY_MDL("156", "麦当劳中国"),
    ;

    private String code;

    private String name;

    CompanyBoxEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CompanyBoxEnum getCompanyBoxEnum(String code) {
        for (CompanyBoxEnum value : CompanyBoxEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的公司");
    }

    public static String getCompany(String code) {
        switch (code) {
            case "156":
                return COMPANY_MDL.getCode();
            default:
                return COMPANY_ZWL.getCode();
        }
    }

}
