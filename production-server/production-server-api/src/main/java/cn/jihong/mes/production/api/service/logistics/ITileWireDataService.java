package cn.jihong.mes.production.api.service.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.logistics.api.model.vo.in.TileWireRealTimeDataPageInVO;
import cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetLatestTileWireDataInVO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025-03-14 11:19
 */
public interface ITileWireDataService {

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO
     * <AUTHOR>
     * @date: 2025-03-14 11:25
     */
    TileWireRealTimeDataOutVO getSingleTileWireRealTimeDataById(Long id);

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO>
     * <AUTHOR>
     * @date: 2025-03-14 11:25
     */
    Pagination<TileWireRealTimeDataOutVO> getTileWireRealTimeDataPage(TileWireRealTimeDataPageInVO inVO);

    /**
     * 获取瓦线最新生产数据
     * @param inVO
     * @return: cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO
     * <AUTHOR>
     * @date: 2025-03-14 11:25
     */
    TileWireRealTimeDataOutVO getLatestTileWireData(GetLatestTileWireDataInVO inVO);
}
