package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PalletUseOutVO implements Serializable {

    private Long id;

    /**
     * 公司code
     */
    private String companyCode;
    private String companyCodeName;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 生产工程单
     */
    private String productionOrder;
    /**
     * 领用工程单
     */
    private String requisitionOrder;

    /**
     * 栈板来源
     */
    private String palletSource;
    private String palletSourceName;

    /**
     * 生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionTime;

    /**
     * 来源班次
     */
    private Integer shiftSource;

    /**
     * 生产数量
     */
    private BigDecimal productQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 栈板状态 1 在库 2 在产
     */
    private String status;
    private String statusName;

    /**
     * 核销状态
     */
    private String writeOffStatus;
    private String writeOffStatusName;

    /**
     * 是否不良品
     */
    private Integer isDefective;

    /**
     * 是否重工
     */
    private Integer isRepeat;

    /**
     * 重工工序
     */
    private String repeatProcess;
    private String repeatProcessName;

}
