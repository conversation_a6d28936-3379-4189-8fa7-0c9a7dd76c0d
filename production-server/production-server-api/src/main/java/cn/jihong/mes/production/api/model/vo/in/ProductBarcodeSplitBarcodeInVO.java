package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 箱码号段明细
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
public class ProductBarcodeSplitBarcodeInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 箱码
     */
    private String barcodeNo;
    private String wmsBarcodeNo;

    /**
     * 物料编号
     */
    private String itemNumber;


    /**
     * 物品名称
     */
    private String itemName;


    /**
     * 规格
     */
    private String itemSpece;


    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date itemDate;

    /**
     * 仓库
     */
    private String warehouse;


    /**
     * 启用状态  0 未启用  1 已启用
     */
    private Integer enableStatus;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;

    /**
     * 开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;


}
