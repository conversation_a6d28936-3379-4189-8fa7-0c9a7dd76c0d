package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AddProductBoxBarcodeDetailInVO implements java.io.Serializable {

    /**
     * 箱码号段id
     */
    private Long productBarcodeId;

    /**
     * 箱码剩余数量
     */
    private Integer barcodeRemaining;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

    /**
     * 箱码申请数量
     */
    @NotNull(message = "箱码申请数量不能为空")
    private Integer applyBarcodeCount;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;


    /**
     * 料号
     */
    @NotBlank(message = "料号不能为空")
    private String productNo;


    /**
     * 生产日期
     */
    @NotNull(message = "生产日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;


    /**
     * 箱数量
     */
    @NotNull(message = "箱数量不能为空")
    private Integer boxSpece;


    /**
     * 批次识别码
     */
    @NotBlank(message = "批次识别码不能为空")
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    @NotBlank(message = "机台不能为空")
    private String machine;


    /**
     * 班次 1 2
     */
    @NotBlank(message = "班次不能为空")
    private String shift;


    /**
     * sku编码
     */
    private String skuCode;

}
