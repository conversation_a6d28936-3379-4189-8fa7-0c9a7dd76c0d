package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetLongStandbyMachineTaskTypeEnumOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetMachineTaskByNameOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetMachineTaskTypeEnumOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskPageOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 生产机台任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface IProductMachineTaskService extends IJiHongService<ProductMachineTaskPO> {


    VerifyReportRequestOutVO verifyReportRequest(VerifyReportRequestInVO inVO);

    /**
     * 开始机台任务
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2023/12/19 11:53
     */
    Boolean saveMachineTask(String key,SaveMachineTaskInVO inVO);


    /**
     * 稼动上报（人工上报）
     * @param key
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2025-06-04 16:58
     */
    Boolean operationReport(String key, OperationReportInVO inVO);


    /**
     * 稼动上报 （设备上报）
     * @param inVO
     * @return: cn.jihong.mes.production.api.model.po.ProductMachineTaskPO
     * <AUTHOR>
     * @date: 2025-07-02 14:41
     */
    ProductMachineTaskPO equipmentOperationReport(EquipmentOperationReportInVO inVO);


    /**
     * 更新设备稼动状态（设备上报）
     * @param inVO
     * @return: cn.jihong.mes.production.api.model.po.ProductMachineTaskPO
     * <AUTHOR>
     * @date: 2025-07-02 14:41
     */
    ProductMachineTaskPO updateEquipmentOperationReport(UpdateEquipmentOperationReportInVO inVO);

    /**
     * 停止机台任务
     * @param machineName
     * @param reportedQuantity
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024/2/23 14:55
     */
    Boolean stopMachineTask(String machineName,BigDecimal reportedQuantity,String reason);

    /**
     * 根据id结束机台任务
     * @param machineTaskId
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024-09-08 16:12
     */
    Boolean stopMachineTaskById(Long machineTaskId);


    /**
     * 更新机台任务
     * @param machineName
     * @param type
     * @param reportedProductId
     * @param reportedQuantity
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024-09-05 15:24
     */
    Boolean updateMachineTask(String machineName,Integer type,Long reportedProductId, BigDecimal reportedQuantity,String reason);

    /**
     * 获取机台任务根据机台名称
     * @param inVO
     * @return: cn.jihong.mes.production.api.model.vo.out.GetMachineTaskByNameOutVO
     * <AUTHOR>
     * @date: 2023/12/19 11:53
     */
    GetMachineTaskByNameOutVO getMachineTaskByName(GetMachineTaskByNameInVO inVO);

    /**
     * 报产量
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2023/12/19 13:38
     */
    Boolean finishMachineTask(String key,FinishMachineTaskInVO inVO);

    /**
     * 下机
     * @param productTicketId
     * @param planTicketNo
     * @param machineName
     * @return: void
     * <AUTHOR>
     * @date: 2025-07-10 14:11
     */
    void stopProductTicKet(Long productTicketId,String planTicketNo, String machineName);

    /**
     * 查询工单下的报工信息
     */
    List<ProductMachineTaskPO> getListByProductId(Long productId);

    /**
     * 查询工单下的报工信息  某个类型的时间
     */
    List<ProductMachineTaskPO> getListByProductId(Long productId,Integer type);

    /**
     * 查询工单下的报工信息
     */
    List<ProductMachineTaskPO> getByProductTicketIds(List<Long> productTicketIds);

    /**
     * 获取报工界面的机台状态
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetMachineTaskTypeEnumOutVO>
     * <AUTHOR>
     * @date: 2024/2/23 10:59
     */
    List<GetMachineTaskTypeEnumOutVO> getMachineTaskTypeEnum();


    /**
     * 获取长期待机的机台状态
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetLongStandbyMachineTaskTypeEnumOutVO>
     * <AUTHOR>
     * @date: 2024/2/23 10:59
     */
    List<GetLongStandbyMachineTaskTypeEnumOutVO> getLongStandbyMachineTaskTypeEnum();

    /**
     * 设备稼动记录
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskPageOutVO>
     * <AUTHOR>
     * @date: 2024/4/10 10:32
     */
    Pagination<GetProductMachineTaskPageOutVO> getProductMachineTaskPage(GetProductMachineTaskPageInVO inVO);


    /**
     * 获取机台任务
     * @param productTicketId
     * @return: cn.jihong.mes.production.api.model.po.ProductMachineTaskPO
     * <AUTHOR>
     * @date: 2024-09-13 15:04
     */
    List<ProductMachineTaskPO> getByProductTicketId(Long productTicketId,Integer type);

    void updateMachineTaskHistory(BigDecimal orgProducedQuantity, UpdateOutboundInVO updateOutboundInVO, ProductTicketPO productTicketPO);
}
