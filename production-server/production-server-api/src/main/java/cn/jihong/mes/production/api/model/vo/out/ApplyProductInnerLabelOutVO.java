package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ApplyProductInnerLabelOutVO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 客户编号
     */
    private String customerNo;


    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 生产工程单号
     */
    private String planTicketNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 料号
     */
    private String materialCode;


    /**
     * 箱码总数量
     */
    private Long barcodeTotal;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyyMMdd")
    private Date productionDate;


    /**
     * 保质期
     */
    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyyMMdd")
    private Date expirationDate;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;


    /**
     * 作废状态 0 作废   1 未作废
     */
    private Integer disableStatus;



}
