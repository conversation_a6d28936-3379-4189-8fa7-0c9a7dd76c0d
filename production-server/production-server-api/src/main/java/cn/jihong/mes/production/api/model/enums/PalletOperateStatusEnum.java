package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum PalletOperateStatusEnum {

    IN_LIBRARY(1, "在库"),
    IN_PRODUCTION(2, "在产"),
    TEMPORARY_STORAGE(3, "暂存"),
    PREPARE(4, "备料"),
    ;

    private Integer code;

    private String name;

    PalletOperateStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PalletOperateStatusEnum getPalletOperateStatusEnum(Integer code) {
        for (PalletOperateStatusEnum value : PalletOperateStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的操作类型");
    }
    
}
