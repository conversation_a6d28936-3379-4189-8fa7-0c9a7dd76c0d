package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-05-14 13:24
 */
@Data
public class SendOverProductionFlowInVO implements Serializable {

    /**
     * 业务id
     */
    @NotNull(message = "业务主键id不能为空")
    private Long ywId;

    /**
     * 触发类型 1：入库申请  2：报工申请
     */
    @NotBlank(message = "触发类型不能为空")
    private String cflx;

    /**
     * 工程单号
     */
    private String planTicketNo;


    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;


    /**
     * 超产原因
     */
    private String reason;

}
