package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/8 11:25
 */
@Data
public class GetProjectDailyReportsOutVO implements Serializable {
    private static final long serialVersionUID = -5489822600765543491L;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 工序code
     */
    private String processCode;


    /**
     * 工序
     */
    private String process;

    /**
     * 生产日期
     */
    private String produceDate;

    /**
     * 用料部位
     */
    private String materialPlace;

    /**
     * 开单用料
     */
    private BigDecimal billingMaterialQuantity;

    /**
     * 累计用料
     */
    private BigDecimal totalConsumptionQuantity;

    /**
     * 单位（用料）
     */
    private String materialUnit;

    /**
     * 单位（报工）
     */
    private String reportedUnit;

    /**
     * 应完成（pcs） 开单数量
     */
    private BigDecimal shouldCompletePcsQuantity;

    /**
     * 累计用料（pcs）
     */
    private BigDecimal totalConsumptionPcsQuantity;

    /**
     * 累计报工
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 累计报工（pcs）
     */
    private BigDecimal totalReportedPcsQuantity;

    /**
     * 累计出站
     */
    private BigDecimal totalProducedQuantity;

    /**
     * 累计出站（pcs）
     */
    private BigDecimal totalProducedPcsQuantity;

    /**
     * 累计不良品
     */
    private BigDecimal totalDefectiveProductsQuantity;

    /**
     * 累计不良品（pcs）
     */
    private BigDecimal totalDefectiveProductsPcsQuantity;

    /**
     * 开单损耗率
     */
    private BigDecimal billingAttritionRate;

    /**
     * 损耗率
     */
    private BigDecimal attritionRate;

}
