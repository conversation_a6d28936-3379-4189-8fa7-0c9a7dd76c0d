package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class GetProductBoxBarcodeOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 客户编号
     */
    private String customerNo;


    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 码段名称
     */
    private String barcodeName;


    /**
     * 箱码开始号
     */
    private String barcodeNoStart;


    /**
     * 箱码截止号
     */
    private String barcodeNoEnd;


    /**
     * 状态  0 禁用   1 启用
     */
    private Integer barcodeStatus;


    /**
     * 当前游标
     */
    private String barcodeCurrent;


    /**
     * 箱码总数量
     */
    private Integer barcodeTotal;


    /**
     * 箱码剩余数量
     */
    private Integer barcodeRemaining;

}
