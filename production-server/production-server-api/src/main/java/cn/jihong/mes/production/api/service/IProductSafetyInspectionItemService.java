package cn.jihong.mes.production.api.service;


import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductSafetyInspectionItemPO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionItemOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 安全点检项配置 服务接口
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface IProductSafetyInspectionItemService extends IJiHongService<ProductSafetyInspectionItemPO> {

    /**
     * 分页查询安全点检项配置
     *
     * @param inVO 查询条件
     * @return 分页结果
     */
    Pagination<ProductSafetyInspectionItemOutVO> page(ProductSafetyInspectionItemQueryInVO inVO);

    /**
     * 保存安全点检项配置
     *
     * @param inVO 保存参数
     * @return 保存结果
     */
    boolean save(ProductSafetyInspectionItemSaveInVO inVO);

    /**
     * 获取安全点检项配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    ProductSafetyInspectionItemOutVO getDetail(Long id);

    /**
     * 删除安全点检项配置
     *
     * @param id 配置ID
     * @return 删除结果
     */
    boolean delete(Long id);

    /**
     * 获取所有已启用的安全点检项
     *
     * @return 点检项列表
     */
    List<ProductSafetyInspectionItemOutVO> getEnableList();
} 