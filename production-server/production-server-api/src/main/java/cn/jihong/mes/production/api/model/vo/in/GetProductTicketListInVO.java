package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetProductTicketListInVO extends PageRequest implements Serializable {

    /**
     * 公司code
     */
    private String companyCode;
    /**
     * 机台名称
     */
    private String machineName;
    /**
     * 工程单号
     */
    private String planTicketNo;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;
    /**
     * 生产班次
     */
    private Integer shift;

    /**
     * 是否报工  0 否 1 是
     */
    private Integer isSignUp;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否APP报良品  0 否 1 是
     */
    private Integer isAppSignGood;

    /**
     * 是否APP报不良品  0 否 1 是
     */
    private Integer isAppSignBad;

}
