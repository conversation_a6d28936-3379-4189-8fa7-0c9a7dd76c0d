package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetRecordsByMainInfoOutVO implements Serializable {


    /**
     * 主要信息
     */
    private String mainInfo;


    /**
     * 业务类型 1 良品报工  2 不良品报工  3 更新物料  4 入库申请
     */
    private Integer businessType;


    /**
     * 响应数据
     */
    private String response;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    private Date createTime;



}
