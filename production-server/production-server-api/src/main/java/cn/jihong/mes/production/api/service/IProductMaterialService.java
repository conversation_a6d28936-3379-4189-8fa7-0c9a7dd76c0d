package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.dto.UpdateMaterialDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.service.IJiHongService;
import cn.jihong.wms.api.model.dto.UpdateBarcodeStoreDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 上料信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductMaterialService extends IJiHongService<ProductMaterialPO> {

    /**
     * 根据物料编号查询物料信息
     */
    MaterialInfoOutVO getMaterialByBarcodeNo(MaterialByBarcodeNoInVO materialByBarcodeNoInVO);

    /**
     * 根据物料类型查询当前用料信息
     */
    MaterialInfoOutVO getMaterialInfoByCode(GetMaterialInfoByCodeInVO getMaterialInfoByCodeInVO);

    /**
     * 保存物料信息
     */
    Long saveMaterialInfo(String redisLockKey, SaveMaterialInfoInVO saveMaterialInfoInVO);

    /**
     * 查询使用中的物料列表
     */
    List<MaterialInfoOutVO> getMaterialList(Long productTicketId);

    /**
     * 查询用料信息
     * 
     * @param inVO
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetListByMaterialCodeOutVO>
     * <AUTHOR>
     * @date: 2023/11/8 17:14
     */
    List<GetListByMaterialCodeOutVO> getListByMaterialCode(GetListByMaterialCodeInVO inVO);

    /**
     * 查询上料记录
     */
    Pagination<MaterialRecordsOutVO> getMaterialRecords(ProductTicketPageInVO productTicketPageInVO);

    /**
     * 获得上料记录
     */
    Pagination<MaterialRecordsOutVO> getMaterialRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);

    /**
     * 下料
     */
    DownMaterialOutVO downMaterial(DownMaterialInVO downMaterialInVO);

    /**
     * 暂存
     */
    String temporaryStorageMaterial(TemporaryStorageMaterialInVO inVO);

    /**
     * 换料
     */
    Long changeMaterial(ChangeMaterialInVO changeMaterialInVO);

    /**
     * 查询物料类别列表
     */
    List<MaterialTypeoutVO> getMaterialTypeList(Long productTicketId);

    /**
     * 根据物料id查询当前用料信息
     */
    MaterialInfoOutVO getMaterialInfo(Long id);

    /**
     * 查询使用中的物料列表 -- 需要增加物料类别
     */
    GetMaterialListByTypeOutVO getMaterialListByType(Long productTicketId, String materialType);

    /**
     * 获取物料信息列表
     * 
     * @param pageNum
     * @param pageSize
     * @param productTickIdList
     * @param materialCode
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductMaterialPO>
     * <AUTHOR>
     * @date: 2023/11/15 9:25
     */
    Pagination<ProductMaterialPO> getListByProductTickIds(Long pageNum, Long pageSize, List<Long> productTickIdList,
        String materialCode);

    /**
     * 查询物料使用列表
     */
    Pagination<MaterialUseOutVO> getMaterialUseList(GetMaterialListPageInVO getMaterialListPageInVO);

    /**
     * 查询物料使用详情
     */
    MaterialUseOutVO getMaterialUseDetial(Long id);

    /**
     * 查询物料使用详情列表
     */
    Pagination<MaterialUseRecordOutVO> getMaterialUseDetialList(ProductInfoPageInVO productInfoPageInVO);

    /**
     * 查询使用中的物料列表
     */
    List<MaterialInfoOutVO> getMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO);

    /**
     * 根据状态 查询的物料列表
     */
    List<MaterialInfoOutVO> getMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO, Integer status);

    /**
     * 查询暂存的物料列表
     */
    List<MaterialInfoOutVO> getStagingMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO);

    /**
     * 修改物料状态
     */
    Long updateMaterialStatus(UpdateMaterialStatusInVO updateMaterialStatusInVO);

    /**
     * 测试接口，推送erp
     */
    void updateToErpTest(Long productMaterialId);

    /**
     * 获得机台累计用料列表
     */
    List<MaterialTotalUseOutVO> getMaterialTotalUse(ProductTicketVO productTicketVO);

    /**
     * 更新物料使用数量
     */
    void updateMaterialCount(UpdateMaterialInVO updateMaterialInVO);

    DownMaterialOutVO downMaterial(String redisLockKey, DownMaterialInVO downMaterialInVO,
        ProductMaterialPO materialPO);

    void updateMateria(String lock_key, UpdateMaterialInVO updateMaterialInVO, ProductMaterialPO productMaterialPO,
        ProductTicketPO productTicketPO, BigDecimal consumptionQuantityOld, BigDecimal remainingQuantityOld);

    void temporaryStorageMaterial(String lock_key, TemporaryStorageMaterialInVO inVO, ProductMaterialPO materialPO);

    void changeMaterial(String lock_key, ChangeMaterialInVO changeMaterialInVO, ProductMaterialPO productMaterialPO);

//    void updateToWmsTest(String barcord, Integer flag, BigDecimal consumptionQuantity);

    void saveProductInterfaceRecords(ProductMaterialPO productMaterialPO, ProductTicketPO productTicketPO,
        ResponseCodeOutVO update, Long userId);

    Pagination<MaterialRecordsOutVO> getMaterialDownRecords(ProductTicketPageInVO productTicketPageInVO);

    ResponseCodeOutVO updateMaterial(ProductMaterialPO materialPO, ProductTicketPO productTicketPO , UpdateMaterialDTO updateMaterialDTO);

    public ResponseCodeOutVO updateMaterialToErpAndWms(ProductMaterialPO materialPO, ProductTicketPO productTicketPO, UpdateMaterialDTO updateMaterialDTO);


        List<GetMaterialDailySettlementOutVO> dailySettlementMaterial(DailySettlementMaterialInVO dailySettlementMaterialInVO);

    void dailySettlementMaterial(String redisLockKey,  ProductMaterialPO materialPO, ProductTicketPO productionTicket, Integer billingType);

    List<GetMaterialDailySettlementOutVO>
        getMaterialDailySettlement(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    /**
     * 批量扣减物料
     * 
     * @param productMachineMaterialApportionmentPOList
     * @param productTicketPO
     */
    void batchDeductionMaterial(List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOList,
        ProductTicketPO productTicketPO,String prefix);

    public List<UpdateBarcodeStoreDTO> setUpdateBarcodeStoreDTOS(
            List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOList, String nextIdStr,Integer flag);


    List<GetMaterialDailySettlementOutVO> getMaterialDailySettlementApp(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);
}
