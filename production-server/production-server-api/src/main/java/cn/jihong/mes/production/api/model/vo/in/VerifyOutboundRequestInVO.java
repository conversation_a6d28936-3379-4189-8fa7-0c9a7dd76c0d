package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-15 15:06
 */
@Data
public class VerifyOutboundRequestInVO implements Serializable {

    /**
     * 工单id
     */
    @NotNull(message = "工单id不能为空")
    private Long productTicketId;


    /**
     * 出站数量
     */
    @NotNull(message = "出站数量不能为空")
    private BigDecimal outboundQuantity;

    /**
     * 箱码
     */
    private String boxCode;



}
