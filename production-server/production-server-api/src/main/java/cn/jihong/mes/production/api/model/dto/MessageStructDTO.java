package cn.jihong.mes.production.api.model.dto;

import cn.jihong.common.model.dto.request.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * RabbitMq 消息结构体
 * 
 * <AUTHOR>
 * @date 2023/9/26 10:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageStructDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private String messageId;

    private UserInfo userInfo;

    private String createTime;

    private Integer bizCode;

    private T data;
}
