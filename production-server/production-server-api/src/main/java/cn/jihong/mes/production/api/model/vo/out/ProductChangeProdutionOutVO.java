package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
public class ProductChangeProdutionOutVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;


    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工单号
     */
    private String ticketRequestId;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 创建人id
     */
    private Long createBy;


    /**
     * 创建人员名称
     */
    private String createrName;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



}
