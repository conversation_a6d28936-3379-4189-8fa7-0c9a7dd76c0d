package cn.jihong.mes.production.api.service;


import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.mes.production.api.model.dto.ProductInterfaceRecordDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductOutboundPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.service.IJiHongService;
import cn.jihong.oa.erp.api.model.po.SfacTPO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;
import cn.jihong.wms.api.model.dto.UpdateBarcodeStoreDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 出站信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductOutboundService extends IJiHongService<ProductOutboundPO> {

    VerifyOutboundRequestOutVO verifyOutboundRequest(VerifyOutboundRequestInVO inVO);

//    /**
//     * 保存出站信息
//     */
//    String saveOutboundInfo(SaveOutboundInfoInVO saveOutboundInfoInVO);
    /**
     * 保存出站信息
     */
    String saveOutboundInfo(String redisLockKey,SaveOutboundInfoInVO saveOutboundInfoInVO);

    /**
     * 暂存出站和报不良信息
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024/4/11 14:43
     */
    Boolean temporaryStorageSaveOutboundAndDefective(TemporaryStorageSaveOutboundAndDefectiveInVO inVO);

    /**
     * 获取暂存出站和报不良信息
     * @param inVO
     * @return: cn.jihong.mes.production.api.model.vo.out.GetTemporaryStorageSaveOutboundAndDefectiveOutVO
     * <AUTHOR>
     * @date: 2024/4/12 16:04
     */
    GetTemporaryStorageSaveOutboundAndDefectiveOutVO getTemporaryStorageSaveOutboundAndDefective(GetTemporaryStorageSaveOutboundAndDefectiveInVO inVO);


    /**
     * 暂存推送wms和erp信息
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024/4/12 15:52
     */
    Boolean temporaryStoragePushWmsAndErp(TemporaryStoragePushWmsAndErpInVO inVO);


    /**
     * 获取暂存推送wms和erp信息
     * @param inVO
     * @return: cn.jihong.mes.production.api.model.vo.out.GetTemporaryStoragePushWmsAndErpOutVO
     * <AUTHOR>
     * @date: 2024/4/12 15:53
     */
    GetTemporaryStoragePushWmsAndErpOutVO getTemporaryStoragePushWmsAndErp(GetTemporaryStoragePushWmsAndErpInVO inVO);

    /**
     * 根据栈板码查询出站信息
     */
    ProductOutboundPO getOutboundByPalletCode(String palletCode);

    Pagination<OutboundInfoOutVO> getOutboundRecords(ProductTicketPageInVO productTicketPageInVO);

    /**
     * 创建一条栈板信息，但是这条栈板是未出站的
     * @param productTicketId
     * @param machineName
     * @param producedQuantity
     */
    ProductOutboundPO creatrPallet(Long productTicketId, String machineName, BigDecimal producedQuantity);

    /**
     * 查询栈板信息
     * @param productTicketId
     * @return {@link Object}
     */
    OutboundInfoOutVO getPallet(Long productTicketId);

    Pagination<OutboundInfoOutVO> getOutboundRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    OutboundInfoOutVO getPalletByMachineName(GetByMachineNameInVO getByMachineNameInVO);

    /**
     * 查询未出站的数据
     * @param productTicketId
     * @return
     */
    ProductOutboundPO getNoOutByProductTicketId(Long productTicketId);

    /**
     * 根据工单获取对应的工序的单位 -- 中文名称
     * @param productTicketId
     * @return
     */
    String getUnit(Long productTicketId);
    /**
     * 根据工单获取对应的工序的单位 -- 英文名称
     * @param productTicketPO
     * @return
     */
    String getUnitCode(ProductTicketPO productTicketPO);

    /**
     * 保存出站和报不良信息
     * @param saveOutboundAndDefectiveInfo
     * @return
     */
    SaveOutboundAndDefectiveInfoOutVO saveOutboundAndDefectiveInfo(SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo);


    UpdateBarcodeStoreDTO saveWmsMaterialInfo(SaveOutboundInfoInVO outboundInfo,
                                              ProductTicketPO productTicketPO, String docNo, String itemNo,
                                              String itemSpec, String storage, String storageName, String warehouse,
                                              String warehouseName, String lotNo, String productCode, String stockCode);

    List<ProductOutboundPO> getByProductTicketIds(List<Long> productTicketIds);


//    /**
//     * 推送wms和erp
//     * @param saveOutboundInfoInVO
//     * @return {@link String}
//     */
//    ResponseCodeOutVO pushWmsAndErp(SaveOutboundInfoInVO saveOutboundInfoInVO, String planTicketNo,SfacTPO sfacTPO);

    /**
     * 入库申请  --- 箱码入库
     * @param redisLockKey
     * @param pushWmsAndErpInVO
     */
    void inboundRequestByBox(String redisLockKey,PushWmsAndErpInVO pushWmsAndErpInVO);

    /**
     * 根据工程单号获取对应的单位
     * @param planTicketNo
     * @return {@link String}
     */
    String getUnitByPlanTicketNo(String planTicketNo);

    /**
     * 获得箱码规格
     * @param planTicketNo
     * @return
     */
    BigDecimal getBoxSpecs(String planTicketNo);

    /**
     * 推送wms和erp  --- 测试
     */
    void testA(String timeStamp, String planTicketNo);

    /**
     * 校验箱码是否使用过
     */
    String verifyBoxCode(String key,VerifyCaseCodeInVO verifyCaseCodeInVO);

    void updateOutbound(UpdateOutboundInVO updateOutboundInVO);

    void saveProductInterfaceRecords(ProductInterfaceRecordDTO productInterfaceRecordDTO);

    GetInboundQuantityOutVO getInboundQuantity(String planTicketNo);

    /**
     * 校验栈板码是否使用过
     * @param verifyPalletCodeInVO
     * @return
     */
    String verifyPalletCode(VerifyPalletCodeInVO verifyPalletCodeInVO);


    Object activeBoxCode(String barcode);

    String destroyBoxCodes(DestoryBoxInVO destoryBoxInVO);

    String destroyBoxCode(String boxCode);

    String changeBoxCode(ChangeBoxInVO changeBoxInVO);

    String inboundRequestByPallet(String redisLockKey, InboundRequestByPalletInVO inboundRequestByPalletInVO);


    ResponseCodeOutVO storageApplicationToErp(BarcodeDetailDTO barcodeDetailDTO,
                                                     SaveOutboundInfoInVO saveOutboundInfoInVO,
                                                     String planTicketNo, SfaaTVO sfaaTVO, SfacTPO sfacTPO);

    Date updateReportDate();
}
