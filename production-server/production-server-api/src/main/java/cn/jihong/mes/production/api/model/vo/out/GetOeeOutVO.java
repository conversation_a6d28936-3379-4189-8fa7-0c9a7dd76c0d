package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-05 15:13
 */
@Data
public class GetOeeOutVO implements Serializable {


    /**
     * 性能利用率
     */
    private BigDecimal performanceUtilizationRate;

    /**
     * 良品率
     */
    private BigDecimal goodProductRate;


    /**
     * 良品数
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 良品占比
     */
    private BigDecimal totalReportedRate = BigDecimal.ZERO;

    /**
     * 不良品数
     */
    private BigDecimal totalDefectiveProductQuantity;

    /**
     * 不良品占比
     */
    private BigDecimal totalDefectiveProductRate = BigDecimal.ZERO;

    /**
     * 进入数
     */
    private BigDecimal entryQuantity;



    /**
     * 实际速度
     */
    private BigDecimal actualSpeed;



    /**
     * 标准产能
     */
    private BigDecimal standardProductionCapacity;

    /**
     * 时间利用率A0
     */
    private BigDecimal timeUtilizationRate;

    /**
     * 战略损失
     */
    private BigDecimal strategicLossRate;

    /**
     * 计划损失
     */
    private BigDecimal planLossRate;

    /**
     * 操作损失
     */
    private BigDecimal operatingLossRate;

    /**
     * 可利用时间（分钟）
     */
    private BigDecimal totalCanUseMinutes;


    /**
     * 实际生产时间（分钟）
     */
    private BigDecimal totalDurationMinutes;

    /**
     * 生产状态下休息时间(分钟数)
     */
    private BigDecimal totalRestDurationMinutes;

    private BigDecimal totalDownTimeMinters;

    /**
     * 停机状态下休息时间(分钟数)
     */
    private BigDecimal totalRestDownTimeMinters;



    /**
     * oee
     */
    private BigDecimal oeeRate;

    /**
     * tee
     */
    private BigDecimal teeRate;

    /**
     * 损失列表
     */
    private List<GetOeeEquipmentDowntime> lossList;

}
