package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 箱码明细
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Getter
@Setter
@TableName("product_box_barcode_detail_detail")
public class ProductBoxBarcodeDetailDetailPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PRODUCT_BARCODE_ID = "product_barcode_id";
    public static final String PRODUCT_BARCODE_DETAIL_ID = "product_barcode_detail_id";
    public static final String BARCODE_NO = "barcode_no";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";
    public static final String PRODUCT_NAME = "product_name";
    public static final String MATERIAL_CODE = "material_code";
    public static final String SKU_CODE = "sku_code";
    public static final String PRODUCTION_DATE = "production_date";
    public static final String LOT_NO = "lot_no";
    public static final String BOX_NUM = "box_num";
    public static final String BATCH_CODE = "batch_code";
    public static final String MACHINE = "machine";
    public static final String SHIFT = "shift";
    public static final String EXPIRATION_DATE = "expiration_date";
    public static final String DISABLE_STATUS = "disable_status";
    public static final String ACTIVA = "activa";
    public static final String PRINT_STATUS = "print_status";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String CUSTOMER_NO = "customer_no";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 箱码号段id
     */
    @TableField(PRODUCT_BARCODE_ID)
    private Long productBarcodeId;

    @TableField(PRODUCT_BARCODE_DETAIL_ID)
    private Long productBarcodeDetailId;


    /**
     * 箱码开始号
     */
    @TableField(BARCODE_NO)
    private String barcodeNo;


    /**
     * 生产工程单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;


    /**
     * 产品名称
     */
    @TableField(PRODUCT_NAME)
    private String productName;


    /**
     * 料号
     */
    @TableField(MATERIAL_CODE)
    private String materialCode;

    /**
     * 麦当劳码
     */
    @TableField(SKU_CODE)
    private String skuCode;


    /**
     * 生产日期
     */
    @TableField(PRODUCTION_DATE)
    private Date productionDate;


    /**
     * 批次号
     */
    @TableField(LOT_NO)
    private String lotNo;


    /**
     * 箱数量
     */
    @TableField(BOX_NUM)
    private Long boxNum;


    /**
     * 批次识别码
     */
    @TableField(BATCH_CODE)
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    @TableField(MACHINE)
    private String machine;


    /**
     * 班次 01 02
     */
    @TableField(SHIFT)
    private String shift;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    @TableField(EXPIRATION_DATE)
    private String expirationDate;


    /**
     * 作废状态 0 作废   1 未作废
     */
    @TableField(DISABLE_STATUS)
    private Integer disableStatus;

    /**
     * 激活状态 0 未激活  1 已激活
     */
    @TableField(ACTIVA)
    private Integer activa;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    @TableField(PRINT_STATUS)
    private Integer printStatus;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 客户编号
     */
    @TableField(CUSTOMER_NO)
    private String customerNo;

}
