package cn.jihong.mes.production.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProductMachineDayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;


    /**
     * 班次
     */
    private Integer shift;


    /**
     * 是否日结  0 否  1 是
     */
    private Integer isFinish;

    private String process;

    private String processCode;


}
