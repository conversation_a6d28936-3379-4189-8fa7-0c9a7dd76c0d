package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum BarcodeOperateTypeEnum implements IntCodeEnum{

    PRINT("10", "打印"),
    RE_PRINT("20", "补打"),
    SPURT("30", "喷码"),
    RE_SPURT("40", "补喷"),
    ;

    private String code;

    private String name;

    BarcodeOperateTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BarcodeOperateTypeEnum getBarcodeOperateTypeEnum(String code) {
        for (BarcodeOperateTypeEnum value : BarcodeOperateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((BarcodeOperateTypeEnum) enumValue).getCode();
    }
}
