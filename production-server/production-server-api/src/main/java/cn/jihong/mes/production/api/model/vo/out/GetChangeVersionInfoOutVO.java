package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-09-08 17:11
 */
@Data
public class GetChangeVersionInfoOutVO implements Serializable {

    /**
     * 换版新旧版
     */
    private String changeVersionNewOld;

    /**
     * 换版类型列表
     */
    private List<ChangeVersionTypeInfo> changeVersionTypeList;



    @Data
   public static class ChangeVersionTypeInfo {

        /**
         * 换版类型
         */
        private String changeVersionType;

        /**
         * 换版单位
         */
        private String changeVersionUnit;
    }

}
