package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 系统字典表查询参数
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictQueryInVO extends PageRequest implements Serializable {

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典标签
     */
    private String dictLabel;
} 