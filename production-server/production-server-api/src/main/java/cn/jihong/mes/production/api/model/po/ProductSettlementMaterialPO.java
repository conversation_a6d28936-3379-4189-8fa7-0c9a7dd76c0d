package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 工程结算物料信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Getter
@Setter
@TableName("product_settlement_material")
public class ProductSettlementMaterialPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_TICKET_NO = "product_ticket_no";
    public static final String PROCESS_NAME = "process_name";
    public static final String MATERIAL_BARCODE_NO = "material_barcode_no";
    public static final String MATERIAL_CODE = "material_code";
    public static final String MATERIAL_NAME = "material_name";
    public static final String MATERIAL_UNIT = "material_unit";
    public static final String MATERIAL_TYPE = "material_type";
    public static final String PURCHASE_BATCH = "purchase_batch";

    public static final String PICKING_QUANTITY = "picking_quantity";
    public static final String CONSUMPTION_QUANTITY = "consumption_quantity";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 生产工程单号
     */
    @TableField(PRODUCT_TICKET_NO)
    private String productTicketNo;


    /**
     * 工序名称
     */
    @TableField(PROCESS_NAME)
    private String processName;

    /**
     * 物料编号
     */
    @TableField(MATERIAL_BARCODE_NO)
    private String materialBarcodeNo;


    /**
     * 物料code
     */
    @TableField(MATERIAL_CODE)
    private String materialCode;


    /**
     * 物料名称
     */
    @TableField(MATERIAL_NAME)
    private String materialName;


    /**
     * 物料单位
     */
    @TableField(MATERIAL_UNIT)
    private String materialUnit;


    /**
     * 物料类别
     */
    @TableField(MATERIAL_TYPE)
    private String materialType;

    /**
     * 采购批次
     */
    @TableField(PURCHASE_BATCH)
    private Integer purchaseBatch;

    /**
     * 领料数量
     */
    @TableField(PICKING_QUANTITY)
    private BigDecimal pickingQuantity;


    /**
     * 用料数量
     */
    @TableField(CONSUMPTION_QUANTITY)
    private BigDecimal consumptionQuantity;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
