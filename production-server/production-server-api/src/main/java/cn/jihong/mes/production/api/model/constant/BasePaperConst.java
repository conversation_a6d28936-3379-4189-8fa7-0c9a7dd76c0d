package cn.jihong.mes.production.api.model.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 原纸的常量
 * <AUTHOR>
 * @date 2024/02/05
 */
public class BasePaperConst {

    /**
     * 原纸类型
     */
    public static final String PAPER_TYPE = "130";
    /**
     * 原纸大类型
     */
    public static final String PAPER_BIG_TYPE = "13003";
    /**
     * 油墨
     */
    public static final String PRINTING_INK_BIG_TYPE = "13101";
    /**
     * 胶水及胶黏材料
     */
    public static final String GLUE_BIG_TYPE = "13103";
    /**
     * 柔印油墨(自调)
     */
    public static final String INK_SEMI_FINISHED_01 = "30801";
    /**
     * 胶印油墨(自调)
     */
    public static final String INK_SEMI_FINISHED_02 = "30802";
    /**
     * 水印油墨(自调)
     */
    public static final String INK_SEMI_FINISHED_03 = "30803";
    /**
     * 预印油墨(自调)
     */
    public static final String INK_SEMI_FINISHED_04 = "30804";
    /**
     * 食品级油墨(自调)
     */
    public static final String INK_SEMI_FINISHED_05 = "30805";

    /**
     * 白矿油
     */
    public static final String WHITE_MINERAL_OIL = "13305";

    /**
     * 塑料包装袋
     */
    public static final String PLASTIC_PACKAGING_BAGS_01 = "13201001";
    /**
     * 食品级塑料包装袋
     */
    public static final String PLASTIC_PACKAGING_BAGS_02 = "13201002";

    /**
     * 栈板
     */
    public static final String PALLET_TYPE = "PALLET";
    /**
     * 最大比例
     */
    public static final String MAX_PROPORTION = "1.05";
    /**
     * 最小比例
     */
    public static final String MIN_PROPORTION = "0.95";
    /**
     * 最大比例 百分比
     */
    public static final String MAX_PROPORTION_PERCENTAGE = "105";
    /**
     * 最小比例 百分比
     */
    public static final String MIN_PROPORTION_PERCENTAGE = "95";


    public static final Set<String> getInkTypes() {
        Set<String> inkTypes = new HashSet<>(Arrays.asList(
                BasePaperConst.PRINTING_INK_BIG_TYPE,
                BasePaperConst.GLUE_BIG_TYPE,
                BasePaperConst.INK_SEMI_FINISHED_01,
                BasePaperConst.INK_SEMI_FINISHED_02,
                BasePaperConst.INK_SEMI_FINISHED_03,
                BasePaperConst.INK_SEMI_FINISHED_04,
                BasePaperConst.INK_SEMI_FINISHED_05,
                BasePaperConst.WHITE_MINERAL_OIL,
                BasePaperConst.PLASTIC_PACKAGING_BAGS_01,
                BasePaperConst.PLASTIC_PACKAGING_BAGS_02
        ));
        return inkTypes;
    };
    

}
