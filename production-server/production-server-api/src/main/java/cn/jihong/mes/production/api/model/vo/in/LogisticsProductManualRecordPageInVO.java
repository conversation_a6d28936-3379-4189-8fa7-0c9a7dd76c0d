package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流产品手动出站记录分页查询入参
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsProductManualRecordPageInVO extends PageRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 网待编号
     */
    private String meshCode;
} 