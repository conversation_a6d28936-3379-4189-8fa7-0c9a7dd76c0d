package cn.jihong.mes.production.api.model.vo.in.packing;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 内标签解绑输入参数
 * 基于BarCodeTreePO解除内标签绑定关系
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class InnerLabelUnbindInVO implements Serializable {

    /**
     * 内标签码
     */
    @NotBlank(message = "内标签码不能为空")
    private String innerLabelCode;

    /**
     * 箱码（可选，用于二次校验）
     */
    private String boxCode;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 操作员ID
     */
    private Long operatorId;
}