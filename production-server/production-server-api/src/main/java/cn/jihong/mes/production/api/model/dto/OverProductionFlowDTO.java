package cn.jihong.mes.production.api.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.jihong.common.model.dto.BaseWorkflowFormDataDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-09-11 14:43
 */
@Data
public class OverProductionFlowDTO extends BaseWorkflowFormDataDTO implements Serializable {

    /**
     * 流程请求ID
     */
    private Long workRequestId;


    /**
     * 请求标题
     */
    private String requestTitle;

    /**
     * 公司据点
     */
    private String companySite;


    /**
     * 用户ID
     */
    private Long userId;


    /**
     * 工号
     */
    private String workcode;


    /**
     * 名字
     */
    private String name;


    /**
     * 公司/分部ID
     */
    private Long companyId;


    /**
     * 部门ID
     */
    private Long deptId;


    /**
     * 部门名称
     */
    private String deptName;


    /**
     * 公司/分部名
     */
    private String companyName;

    /**
     * 岗位ID
     */
    private Long jobTitleId;

    private String jobTitleName;


    /**
     * 申请日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate applyDate;


    /**
     * 申请人ID
     */
    private Long applicantUserId;


    /**
     * 申请人工号
     */
    private String applicantWorkCode;


    /**
     * 申请人部门
     */
    private Long applicantDepartmentId;


    /**
     * 申请人岗位
     */
    private Long applicantJobTitleId;


    /**
     * 紧急程度
     */
    private Integer requestLevel;


    /**
     * 状态
     */
    private String requestStatus;

    private Integer hasIt;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 更新人
     */
    private String updateBy;


    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * flag
     */
    private Integer flag;


    private String ywjd;

    private String xmscyb;

    private String xmsceb;

    /**
     * 申请人工号
     */
    private String sqr;

    /**
     * 工单号
     */
    private String gdh;

    /**
     * 产品名称
     */
    private String cpmc;

    /**
     * 开单数量
     */
    private BigDecimal kdsl;

    /**
     * 超产数量
     */
    private BigDecimal ccsl;

    /**
     * 单位
     */
    private String dw;

    /**
     * 超产原因
     */
    private String ccyy;

    /**
     * 业务ID
     */
    private Long ywid;

    /**
     * 触发类型 1：入库申请  2：报工申请
     */
    private String cflx;
}
