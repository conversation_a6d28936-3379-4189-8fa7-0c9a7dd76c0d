package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
public class ProductionTicketInfoOutVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序
     */
    private String processType;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 班次  1 白班  2 夜班
     */
    private Integer shift;

    /**
     * 生产计划工单单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartDate;


    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndDate;

    /**
     * 计划产量
     */
    private BigDecimal plannedProduct;
    private String plannedProductUnit;

    /**
     * 已生产
     */
    private BigDecimal realProduct;

    /**
     * 不良品数量
     */
    private BigDecimal defectiveProduct = BigDecimal.ZERO;
    /**
     * 未确认不良品数量
     */
    private BigDecimal unconfirDefectiveQuantity = BigDecimal.ZERO;

    /**
     * 备注
     */
    private String remark;

    /**
     * 班组人员id
     */
    private String teamUsers;

    /**
     * 班组人员名称
     */
    private String teamUsersName;

    /**
     * 创建人员名称
     */
    private String createrName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 是否报工  0 否 1 是
     */
    private Integer isSignUp;

    /**
     * 不良品是否报工  0 否 1 是
     */
    private Integer isDefectionSignUp;

    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity = BigDecimal.ZERO;
    private String reportedQuantityUnit;

    /**
     * 开单数量（PCS）
     */
    private BigDecimal billingPcsQuantity;
    private String billingPcsQuantityUnit;

    /**
     * 累计产量(报工数量)
     */
    private BigDecimal produceTotalQuantity;
    private String produceTotalQuantityUnit;

    /**
     * 计件类型
     */
    private String pieceType;

    /**
     * 是否倒扣料  0 否  1 是
     */
    private Integer isPour;

    /**
     * 是否分摊扣料成功  0 否  1 是
     */
    private Integer isDeductMaterial;

    /**
     * 是否倒扣料成功  0 否  1 是
     */
    private Integer isPourMaterial;

    /**
     * 结算类型：1 一单一结  2  一日一结
     */
    private Integer billingType;

    /**
     * 是否显示  0 否  1 是
     */
    private Integer show;

    /**
     * 生产批次
     */
    private String productionBatch;

    /**
     * 车间编码
     */
    private String workshopCode;

    /**
     * 供料网带编号
     */
    private String supplyMeshCode;

    /**
     * 出口网带编号
     */
    private String exitMeshCode;

    /**
     * 备料网带数量
     */
    private Integer prepareMeshCount;

    /**
     * 出站类型 出站类型 1 手动 2 自动
     */
    private Integer outboundType;

    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 良品报工单号
     */
    private String goodReportDoNo;

    /**
     * 不良品报工单号
     */
    private String defectionReportDoNo;

    /**
     * 扣料单号
     */
    private String updateMaterialDoNo;

}
