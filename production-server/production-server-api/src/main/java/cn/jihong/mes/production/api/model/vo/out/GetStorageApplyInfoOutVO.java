package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetStorageApplyInfoOutVO implements Serializable {

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 栈板码
     */
    private String palletCode;

    /**
     * 创建人
     */
    private Long createBy;
    private String createByName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 结果 0 失败  1 成功
     */
    private Integer result;

    /**
     * 入库申请单号
     */
    private String storageApplyNO;

    /**
     * 入库单号
     */
    private String storageNO;

    /**
     * 入库状态
     */
    private Integer storageStatus;
    private String storageStatusName;

    /**
     * 错误信息
     */
    private String errorInfo;


}
