package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-26 16:43
 */
@Data
public class UpdatePieceTypeInVO implements Serializable {

    private static final long serialVersionUID = -2539063441135045617L;


    @NotNull(message = "工单不能为空")
    private Long productTicketId;

    /**
     * 计件类型
     */
    @NotBlank(message = "计件类型不能为空")
    private String pieceType;


}
