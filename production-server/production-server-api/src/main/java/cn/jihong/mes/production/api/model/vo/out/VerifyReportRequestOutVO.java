package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-15 16:38
 */
@Data
public class VerifyReportRequestOutVO implements Serializable {


    private static final long serialVersionUID = -2291683325200544867L;
    /**
     * 已报工数量
     */
    private BigDecimal totalReportQuantity;


    /**
     * 可报工数量
     */
    private BigDecimal canReportQuantity;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;

    /**
     * mes尚未报erp的报工数
     */
    private BigDecimal mesReportQuantity;

    /**
     * true可报工,false无法报工
     */
    private Boolean result;

}
