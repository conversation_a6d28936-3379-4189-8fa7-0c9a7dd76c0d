package cn.jihong.mes.production.api.model.vo.in.packing;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 箱码内标签绑定输入参数
 * 基于BarCodeTreePO实现三级层次绑定：栈板码 → 箱码 → 内标签码
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class InnerLabelBindingInVO implements Serializable {

    /**
     * 箱码
     */
    @NotBlank(message = "箱码不能为空")
    private String boxCode;

    /**
     * 内标签码列表
     */
    @NotEmpty(message = "内标签列表不能为空")
    private List<String> innerLabelCodes;

    /**
     * 栈板码（可选，用于三级绑定）
     */
    private String palletCode;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 操作员ID
     */
    private Long operatorId;
}