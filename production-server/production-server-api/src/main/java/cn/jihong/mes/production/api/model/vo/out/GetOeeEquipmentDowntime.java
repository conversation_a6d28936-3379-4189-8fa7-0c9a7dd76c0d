package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-06 16:04
 */
@Data
public class GetOeeEquipmentDowntime implements Serializable {

    /**
     * 损失类型
     */
    private String lossType;

    /**
     * 停机次数
     */
    private Integer downTimeNum;

    /**
     * 停机分钟数
     */
    private BigDecimal downTimeMinters;

    /**
     * 停机状态下休息分钟数
     */
    private BigDecimal restDownTimeMinters;

    /**
     * 停机占比
     */
    private BigDecimal downTimeRate;

}
