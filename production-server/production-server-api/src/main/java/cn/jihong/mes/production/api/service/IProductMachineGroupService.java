package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductMachineGroupPO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupOperationHistoryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 机组信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IProductMachineGroupService extends IJiHongService<ProductMachineGroupPO> {

    List<ProductMachineGroupOutVO> getList();

    ProductMachineGroupOutVO getDetialById(Long id);

    Long addMachineGroup(ProductMachineGroupInVO productMachineGroupInVO);

    Long updateMachineGroup(ProductMachineGroupInVO productMachineGroupInVO);

    Pagination<ProductMachineOutVO> getMachineList(ProductMachineInVO productMachineInVO);

    void saveOperationHistory(ProductMachineGroupOperationHistoryInVO productMachineGroupOperationHistoryInVO);

    ProductMachineGroupOperationHistoryInVO getOperationHistory();

    void deleteById(Long id);
}
