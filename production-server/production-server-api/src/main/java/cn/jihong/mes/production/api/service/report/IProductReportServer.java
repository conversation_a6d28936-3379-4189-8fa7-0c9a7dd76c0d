package cn.jihong.mes.production.api.service.report;


import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 生产日报服务
 * <AUTHOR>
 * @date 2024/02/28
 */
public interface IProductReportServer {

    /**
     * 获得生产日报（工厂）
     */
    Pagination<GetProductReportByFactoryOutVO> getProductReportByFactory(GetProductReportByFactoryInVO getProductReportByFactoryInVO);


    List<GetMachineGroupReportOutVO> getMachineGroupReport(GetMachineGroupReportInVO getMachineGroupReportInVO);

    List<GetMachineGroupReportByH5OutVO> getMachineGroupReportByH5(GetMachineGroupReportByH5InVO inVO);

    List<ProductMachineGroupOutVO> getProductMachineGroupList(GetProductMachineGroupListInVO inVO);

    void sendMachineGroupReport(SendMachineGroupReportInVO sendMachineGroupReportInVO);

    List<GetMessageTypesByCompanyCodeOutVO> getMessageTypesByCompanyCode();

    Pagination<GetProductReportByFactoryOutVO> getProductPlanReport(GetProductReportByFactoryInVO getProductReportByFactoryInVO);
}
