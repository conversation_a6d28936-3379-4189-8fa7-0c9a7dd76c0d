package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApportionmentMaterialStandardConsumptionDTO implements java.io.Serializable {


    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    private List<ProcessDTO> processDTOs;

    @Data
    public static class ProcessDTO implements java.io.Serializable {

        /**
         * 工序
         */
        private String processCode;

        private List<MaterialDTO> materialDTOS;

        @Data
        public static class MaterialDTO implements java.io.Serializable {

            /**
             * 部位
             */
            private String place;
            /**
             * 物料code
             */
            private String materialCode;

            /**
             * 物料单位
             */
            private String materialUnit;

            /**
             * 物料使用类型
             */
            private String materialUseType;

            /**
             * 标准消耗
             */
            private BigDecimal standardConsumption;

        }

    }





}
