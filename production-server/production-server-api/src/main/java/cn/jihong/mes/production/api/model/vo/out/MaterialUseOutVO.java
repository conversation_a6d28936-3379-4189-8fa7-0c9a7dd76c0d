package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物料信息
 * 
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class MaterialUseOutVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 物料类别
     */
    private String materialType;
    private String materialTypeName;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 上料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 工厂code
     */
    private String companyCode;

    /**
     * 状态 2 使用中  3  下料
     */
    private Integer status;
    private String  statusName;

    /**
     * 采购批次
     */
    private String purchaseBatch;
}
