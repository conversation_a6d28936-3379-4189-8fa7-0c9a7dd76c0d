package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全点检记录查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductSafetyInspectionRecordQueryInVO extends PageRequest implements Serializable {

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 点检状态(0-未点检，1-已点检，2-未开机)
     */
    private Integer inspectionStatus;


} 