package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/22 11:04
 */
@Data
public class GetPageByDayOutVO implements Serializable {
    private static final long serialVersionUID = -7896083501818724296L;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 工序code
     */
    private String processCode;


    /**
     * 工序
     */
    private String process;

    /**
     * 生产日期
     */
    private String produceDate;

    /**
     * 单位（报工）
     */
    private String reportedUnit;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;

    /**
     * 开单数量（PCS）
     */
    private BigDecimal billingPcsQuantity;

    /**
     * 当日计划产量
     */
    private BigDecimal totalPlannedQuantity;

    /**
     * 当日计划产量（PCS）
     */
    private BigDecimal totalPlannedPcsQuantity;

    /**
     * 当日实际数量
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 当日实际数量（PCS）
     */
    private BigDecimal totalReportedPcsQuantity;

    /**
     * 当日计划完成率
     */
    private BigDecimal completeRate;

    /**
     * 当日不良品数量
     */
    private BigDecimal totalDefectiveProductsQuantity;

    /**
     * 当日不良品数量（PCS）
     */
    private BigDecimal totalDefectiveProductsPcsQuantity;

    /**
     * 工序开单损耗率
     */
    private BigDecimal billingAttritionRate;

    /**
     * 当日实际损耗率
     */
    private BigDecimal attritionRate;

}
