package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 一日一结机台物料消耗记录信息
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
public class ProductMachineMaterialRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 物料部件(用途)
     */
    private String materialPlace;

    /**
     * 物料部件名称（用途名称）
     */
    private String materialPlaceName;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 扣料单号
     */
    private String deductionNo;

    /**
     * 已经被扣除的数量
     */
    private BigDecimal lastConsumption = BigDecimal.ZERO;

    /**
     * 物料id
     */
    private Long materialId;


    /**
     * 物料操作id
     */
    private Long materialOperationId;

    private Integer useType;


    private String parts;

    private String partsName;

    private Long productTicketId;


    /**
     * 仓位编号
     */
    private String warehouseNo;

    /**
     * 仓位库名称
     */
    private String warehouseName;

    /**
     * 储位编号
     */
    private String storageNo;

    /**
     * 储位名称
     */
    private String storageName;

}
