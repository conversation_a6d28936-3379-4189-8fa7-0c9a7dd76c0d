package cn.jihong.mes.production.api.model.vo.out;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-15 16:38
 */
@Data
public class VerifyOutboundRequestOutVO implements Serializable {


    private static final long serialVersionUID = -2291683325200544867L;
    /**
     * 已出站数量
     */
    private BigDecimal totalOutboundQuantity;


    /**
     * 可出站数量
     */
    private BigDecimal canOutboundQuantity;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;


    /**
     * true可出站,false无法出站
     */
    private Boolean result;

}
