package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-09-08 16:22
 */
@Data
public class UpdateChangeVersionInVO implements Serializable {


    /**
     * 换版操作记录id
     */
    @NotNull(message = "换版操作记录id不能为空")
    private Long machineTaskHistoryId;

    /**
     * 换版类型
     */
    @NotBlank(message = "换版类型不能为空")
    private String changeVersionType;

    /**
     * 换版新旧版
     */
    @NotBlank(message = "新旧版不能为空")
    private String changeVersionNewOld;

    /**
     * 换版数量
     */
    @NotNull(message = "换版数量不能为空")
    private BigDecimal changeVersionQuantity;

}
