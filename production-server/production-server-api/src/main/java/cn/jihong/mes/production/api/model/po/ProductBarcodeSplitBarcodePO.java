package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条码拆分
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Getter
@Setter
@TableName("product_barcode_split_barcode")
public class ProductBarcodeSplitBarcodePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String BARCODE_NO = "barcode_no";
    public static final String ITEM_NUMBER = "item_number";
    public static final String ITEM_NAME = "item_name";
    public static final String ITEM_SPECE = "item_spece";
    public static final String ITEM_DATE = "item_date";
    public static final String ITEM_UNIT = "item_unit";
    public static final String ITEM_QUANTITY = "item_quantity";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String WAREHOUSE = "warehouse";
    public static final String WAREHOUSE_NAME = "warehouse_name";
    public static final String ITEM_UNIT_NAME = "item_unit_name";
    public static final String SOURCE_NO = "source_no";
    public static final String WMS_BARCODE_STORE_ID = "wms_barcode_store_id";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 条码
     */
    @TableField(BARCODE_NO)
    private String barcodeNo;


    /**
     * 物料编号
     */
    @TableField(ITEM_NUMBER)
    private String itemNumber;


    /**
     * 物品名称
     */
    @TableField(ITEM_NAME)
    private String itemName;


    /**
     * 规格
     */
    @TableField(ITEM_SPECE)
    private String itemSpece;


    /**
     * 日期
     */
    @TableField(ITEM_DATE)
    private Date itemDate;


    /**
     * 单位
     */
    @TableField(ITEM_UNIT)
    private String itemUnit;


    /**
     * 数量
     */
    @TableField(ITEM_QUANTITY)
    private BigDecimal itemQuantity;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;


    /**
     * 仓库
     */
    @TableField(WAREHOUSE)
    private String warehouse;


    /**
     * 仓库名称
     */
    @TableField(WAREHOUSE_NAME)
    private String warehouseName;


    /**
     * 单位名称
     */
    @TableField(ITEM_UNIT_NAME)
    private String itemUnitName;


    /**
     * 来源单号
     */
    @TableField(SOURCE_NO)
    private String sourceNo;

    /**
     * wms_id
     */
    @TableField(WMS_BARCODE_STORE_ID)
    private String wmsBarcodeStoreId;

}
