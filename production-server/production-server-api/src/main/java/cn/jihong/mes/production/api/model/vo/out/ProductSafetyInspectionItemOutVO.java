package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.util.Date;

/**
 * 安全点检项配置 出参
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
public class ProductSafetyInspectionItemOutVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 点检项编码
     */
    private String inspectionCode;

    /**
     * 点检项名称
     */
    private String inspectionName;

    /**
     * 点检描述
     */
    private String inspectionDesc;

    /**
     * 点检标准
     */
    private String inspectionStandard;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态(0-停用，1-启用)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
} 