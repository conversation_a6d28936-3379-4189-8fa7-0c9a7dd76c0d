package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum ProductConfigBusinessTypeEnum implements IntCodeEnum{

    BILLING_TYPE("10", "结算类型"),
    VERSION("20", "版本号"),
    ;

    private String code;

    private String name;

    ProductConfigBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductConfigBusinessTypeEnum getProductConfigBusinessTypeEnum(String code) {
        for (ProductConfigBusinessTypeEnum value : ProductConfigBusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的类型");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((ProductConfigBusinessTypeEnum) enumValue).getCode();
    }
}
