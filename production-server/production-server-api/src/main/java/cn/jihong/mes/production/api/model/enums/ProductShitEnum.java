package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum ProductShitEnum {



    DAY(1, "白班"),
    NIGHT(2, "夜班"),
    ;

    private Integer code;

    private String name;

    ProductShitEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductShitEnum getProductShitEnum(Integer code){
        for (ProductShitEnum value : ProductShitEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("班次异常");
    }
}
