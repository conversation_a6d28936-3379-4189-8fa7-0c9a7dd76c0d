package cn.jihong.mes.production.api.service;


import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductInterfaceRecordsPO;
import cn.jihong.mes.production.api.model.vo.in.GetRecordsByMainInfoInVO;
import cn.jihong.mes.production.api.model.vo.in.GetStorageApplyInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.GetRecordsByMainInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 接口调用记录 服务类
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
public interface IProductInterfaceRecordsService extends IJiHongService<ProductInterfaceRecordsPO> {

    List<ProductInterfaceRecordsPO> getByProductTicketId(Long id);

    Pagination<GetRecordsByMainInfoOutVO> getRecordsByMainInfo(GetRecordsByMainInfoInVO getRecordsByMainInfoInVO);

    Pagination<GetStorageApplyInfoOutVO> getStorageApplyInfo(GetStorageApplyInfoInVO getStorageApplyInfoInVO);

    List<ProductInterfaceRecordsPO> getByProductTicketIds(List<Long> ids);
}
