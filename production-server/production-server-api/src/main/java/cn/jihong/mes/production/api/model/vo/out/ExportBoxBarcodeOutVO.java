package cn.jihong.mes.production.api.model.vo.out;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class ExportBoxBarcodeOutVO implements java.io.Serializable {

    @ExcelProperty("第一段")
    private String first;

    @ExcelProperty("第二段")
    private String second;

    @ExcelProperty("第三段")
    private String third;

    @ExcelProperty("第四段")
    private String fourth;

    @ExcelProperty("第五段")
    private String fifth;

    @ExcelProperty("第六段")
    private String sixth;

    @ExcelProperty("第一段")
    private String seventh;

    @ExcelProperty("第二段")
    private String eighth;

    @ExcelProperty("第三段")
    private String ninth;

    @ExcelProperty("第四段")
    private String tenth;

    @ExcelProperty("第五段")
    private String eleventh;

    @ExcelProperty("第六段")
    private String twelfth;

}
