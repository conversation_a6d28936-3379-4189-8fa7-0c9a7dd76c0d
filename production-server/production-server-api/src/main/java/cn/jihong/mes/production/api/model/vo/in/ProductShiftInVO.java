package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class ProductShiftInVO extends ProductTicketVO implements Serializable {

    @NotNull(message = "班组人员不能为空")
    @Size(min = 1, message = "班组人员不能为空")
    @Valid
    private List<TeamUser> teamUsers;

    @Data
    public static class TeamUser {
        /**
         * 用户id
         */
        @NotBlank(message = "用户id不能为空")
        private String userId;
        private String userName;

        /**
         * 角色id
         */
        @NotBlank(message = "角色id不能为空")
        private String roleCode;

        /**
         * 角色名称
         */
        @NotBlank(message = "角色名称不能为空")
        private String roleName;
    }


}
