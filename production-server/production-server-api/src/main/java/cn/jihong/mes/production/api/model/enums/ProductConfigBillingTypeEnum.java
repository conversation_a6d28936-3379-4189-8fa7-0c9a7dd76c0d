package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum ProductConfigBillingTypeEnum implements IntCodeEnum{

    ONE_ORDER("1", "一单一结"),
    ONE_DAY("2", "一日一结"),
    ;

    private String code;

    private String name;

    ProductConfigBillingTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductConfigBillingTypeEnum getProductConfigBillingTypeEnum(String code) {
        for (ProductConfigBillingTypeEnum value : ProductConfigBillingTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的类型");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((ProductConfigBillingTypeEnum) enumValue).getCode();
    }
}
