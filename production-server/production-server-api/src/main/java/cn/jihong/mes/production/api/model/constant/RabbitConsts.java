package cn.jihong.mes.production.api.model.constant;


/**
 * MQ 常量
 * <AUTHOR>
 * @date 2023/10/20
 */
public class RabbitConsts {

    /**
     * Direct
     */
    public static String DIRECT = "Direct";
    /**
     * Direct交换机名称
     */
    public static String DIRECT_MODE_EXCHANGE = "mes.directExchange";
    /**
     * Direct队列名称
     */
    public static String DIRECT_MODE_QUEUE = "mes.directQueue";

    /**
     * Direct路由
     */
    public static String DIRECT_ROUTING_KEY = "mes.directRouting";

    // =======================================================================

    /**
     * Topic
     */
    public static String TOPIC = "Topic";
    /**
     * tipic交换机名称
     */
    public static String TOPIC_MODE_EXCHANGE = "mes.topicExchange";

    /**
     * tipic队列名称
     */
    public static String TOPIC_MODE_QUEUE = "mes.topicQueue";

    /**
     * tipic路由key
     */
    public static String TOPIC_ROUTING_KEY = "mes.topic.*";

// =======================================================================

    /**
     * Fanout
     */
    public static String FANOUT = "Fanout";

    // =============================死信队列=====================================

    /**
     * Dead
     */
    public static String DEAD = "Dead";

    /**
     * Dead队列名称
     */
    public static String DEAD_MODE_QUEUE = "mes.deadQueue";
    public static String DEAD_MODE_QUEUE_A = "mes.deadQueue.A";

    /**
     * Dead交换机名称
     */
    public static String DEAD_MODE_EXCHANGE = "mes.deadExchange";
    public static String DEAD_MODE_EXCHANGE_A = "mes.deadExchange.A";


    /**
     * Dead路由
     */
    public static String DEAD_ROUTING_KEY = "mes.deadRouting";
    public static String DEAD_ROUTING_KEY_A = "mes.deadRouting.A";

}
