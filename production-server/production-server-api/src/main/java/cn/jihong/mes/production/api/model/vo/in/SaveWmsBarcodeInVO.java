package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SaveWmsBarcodeInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String wmsBarcodeStoreId;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    private List<WmsBarcodeInfo> wmsBarcodeInfos;

    @Data
    public static class WmsBarcodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;

        /**
         * 单包数量
         */
        private BigDecimal itemQuantityOfPacks;
    }

}
