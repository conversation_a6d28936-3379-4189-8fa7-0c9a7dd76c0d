package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SaveOutboundAndDefectiveInfo implements Serializable {

    /**
     * 出站信息
     */
    @NotNull(message = "出站信息不能为空")
    @Valid
    private SaveOutboundInfoInVO outboundInfo;

    /**
     * 不良品信息
     */
    private SaveDefectiveProductsInfoInVO defectiveProductsInfo;

}
