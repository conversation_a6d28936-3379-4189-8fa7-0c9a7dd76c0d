package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.po.ProductSettlementMaterialPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementMaterialDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 * 工程结算物料信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface IProductSettlementMaterialService extends IJiHongService<ProductSettlementMaterialPO> {


    /**
     * 汇总工程结算物料信息
     * @param productTicketNo 生产工程单号
     * @param productTicketId 生产单id
     * @return: void
     * <AUTHOR>
     * @date: 2023/11/13 11:42
     */
    void collect(String productTicketNo,Long productTicketId);


    List<ProductSettlementMaterialPO> getList(String productTicketNo, String materialCode);

    /**
     * 查询工程结算物料信息
     * @param productTicketNo
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO>
     * <AUTHOR>
     * @date: 2023/11/8 16:06
     */
    List<GetSettlementMaterialListOutVO> getSettlementMaterialList(String productTicketNo);

    /**
     * 查询物料详情信息列表
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductMaterialPO>
     * <AUTHOR>
     * @date: 2023/11/14 17:54
     */
    Pagination<GetSettlementMaterialDetailOutVO> getSettlementMaterialDetail(GetSettlementMaterialDetailInVO inVO);

}
