package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 按月查询安全点检记录的输出参数
 */
@Data
public class ProductSafetyInspectionRecordMonthlyOutVO implements Serializable {

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 每天的点检记录
     */
    private List<DayRecord> dayRecordList;

    @Data
    public static class DayRecord implements Serializable {

        /**
         * 生产日期
         */
        private String produceDate;

        /**
         * 白班是否完成点检
         */
        private Boolean dayShiftCompleted;

        /**
         * 夜班是否完成点检
         */
        private Boolean nightShiftCompleted;
    }
} 