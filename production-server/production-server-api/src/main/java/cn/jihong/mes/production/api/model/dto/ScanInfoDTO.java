package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 扫描信息
 */
@Data
public class ScanInfoDTO implements java.io.Serializable {

    @NotBlank(message = "扫描信息不能为空")
    private String scan;

    /**
     * 扫描类型  1 扫描拉货  2 扫描入库
     */
    private Integer scanType;

    /**
     * 1 栈板码  2  箱码
     */
    private Integer barcodeType = 1;

}
