package cn.jihong.mes.production.api.model.vo.out;

import cn.jihong.mes.production.api.model.vo.in.ProductTicketVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物料信息
 * 
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class MaterialRecordsOutVO extends ProductTicketVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 工单编号
     */
    private String ticketNumber;
    /**
     * 生产工程单号
     */
    private String planTicketNo;
    /**
     * 工单号
     */
    private String ticketRequestId;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 物料入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date materialWarehousingTime;

    /**
     * 工序
     */
    private String process;

    /**
     * 上料数量
     */
    private BigDecimal loadingQuantity;

    /**
     * 上料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loadingTime;

    /**
     * 下料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date downLoadingTime;

    /**
     * 原数量
     */
    private BigDecimal originalQuantity;
    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;
    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 剩余原因
     */
    private String remainingReason;

    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 工单类型
     */
    private String ticketType;

    /**
     * 工厂code
     */
    private String companyCode;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;
    /**
     * 机台名称
     */
    private String machineName;
    /**
     * 流程类型
     */
    private String processType;
    /**
     * 班次
     */
    private String shift;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作人
     */
    private Long createBy;
    private String createrName;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    /**
     * 是否超领
     */
    private Integer overClaim;

    /**
     * OA审批流id
     */
    private Long workflowRequestId;

    /**
     * OA审批流状态  1:已归档 2:审批中
     */
    private Integer workflowRequestStatus;
}
