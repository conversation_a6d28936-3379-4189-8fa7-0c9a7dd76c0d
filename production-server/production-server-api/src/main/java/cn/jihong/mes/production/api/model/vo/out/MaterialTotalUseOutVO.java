package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物料信息
 * 
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class MaterialTotalUseOutVO implements Serializable {

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 使用部位
     */
    private String materialPlace;
    private String materialPlaceName;
    /**
     * 开单数量
     */
    private BigDecimal billingPcsQuantity;
    /**
     * 累计用量
     */
    private BigDecimal produceTotalUseQuantity;

    /**
     * OA审批流id
     */
    private Long workflowRequestId;

    /**
     * OA审批流状态  1:已归档 2:审批中
     */
    private Integer workflowRequestStatus;

}
