package cn.jihong.mes.production.api.model.vo.out;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

@Data
public class GetProductUseMaterialsOutVO implements java.io.Serializable {

    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 生产工程单号
     */
    private String planTicketNo;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 物料部件(用途)
     */
    private String materialPlace;

    /**
     * 物料部件名称（用途名称）
     */
    private String materialPlaceName;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 扣料单号
     */
    private String deductionNo;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 工序code
     */
    private String processCode;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 物料使用类型： 10 正扣料 20 分摊 30 倒扣料
     */
    private Integer useType;

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 物料操作id
     */
    private Long materialOperationId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品名称
     */
    private Long taskId;
    
    /**
     * 表标识：APPORTIONMENT 或 RECORD
     */
    private String tableType;
    
    /**
     * 是否可删除：true-可删除，false-不可删除
     */
    private Boolean deletable;
}
