package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.FirstCheckStatusDTO;
import cn.jihong.mes.production.api.model.po.ProductFirstDetectionPO;
import cn.jihong.mes.production.api.model.vo.in.CreateFirstDetectionInVO;
import cn.jihong.mes.production.api.model.vo.out.CreateFirstDetectionOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductFirstDetectionIngByTicketIdOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 首件记录信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductFirstDetectionService extends IJiHongService<ProductFirstDetectionPO> {

    /**
     * 创建首件
     */
    CreateFirstDetectionOutVO createFirstDetection(String key , CreateFirstDetectionInVO createFirstDetectionInVO);


    /**
     * 创建消息
     * @param bizCode
     * @param ticketId
     */
    void createMessage(Integer bizCode, String ticketId);

    /**
     * 更新首检状态
     *
     * @param firstCheckStatusDTO
     */
    void updateFirstCheckStatus(FirstCheckStatusDTO firstCheckStatusDTO);

    /**
     * 获取进行中的首件任务
     * @param ticketId
     * @return: cn.jihong.mes.production.api.model.po.ProductFirstDetectionPO
     * <AUTHOR>
     * @date: 2024/2/2 11:29
     */
    GetProductFirstDetectionIngByTicketIdOutVO getProductFirstDetectionIngByTicketId(Long ticketId);
}
