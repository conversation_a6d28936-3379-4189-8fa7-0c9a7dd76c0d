package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流产品配置信息
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
public class LogisticsProductConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    private Long id;

    /**
     * 据点
     */
    private String companyCode;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为")
    private String machineName;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

    /**
     * 工序code
     */
    @NotBlank(message = "工序code不能为空")
    private String processCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 物料类别
     */
    private String materialCategory;

    /**
     * 堆长度
     */
    private BigDecimal stackLength;

    /**
     * 堆宽度
     */
    private BigDecimal stackWidth;

    /**
     * 托盘长
     */
    private BigDecimal palletLength;

    /**
     * 托盘宽
     */
    private BigDecimal palletWidth;

    /**
     * 托盘列
     */
    private BigDecimal palletColumns;

    /**
     * 客户简称
     */
    private String customerShortName;

    /**
     * 产品简称
     */
    private String productShortName;

    /**
     * 栈板一拖的数量
     */
    private Integer palletCount;

    /**
     * 计划产量
     */
    private Integer planCount;

    /**
     * 托盘角度
     */
    private Integer palletAngle;

}
