package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.TemplateInfoDTO;
import cn.jihong.mes.production.api.model.po.ProductInnerLabelPO;
import cn.jihong.mes.production.api.model.vo.in.ApplyProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetTemplateInfosInVO;
import cn.jihong.mes.production.api.model.vo.out.ApplyProductInnerLabelOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetErpTicketInfoOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * 生产内标签表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IProductInnerLabelService extends IJiHongService<ProductInnerLabelPO> {


    void applyProductInnerLabel(String lock_key, ApplyProductInnerLabelInVO applyProductInnerLabelInVO);

    Pagination<ApplyProductInnerLabelOutVO> getList(GetProductInnerLabelInVO getProductInnerLabelInVO);

    void saveTemplateInfo(TemplateInfoDTO templateInfoDTO);

    TemplateInfoDTO getTemplateInfo(TemplateInfoDTO templateInfoDTO);

    GetErpTicketInfoOutVO getTicketDetailInfo(String planTicketNo);

    String getDate(String date,String month, String day);

    Pagination<TemplateInfoDTO> getTemplateInfos(GetTemplateInfosInVO getTemplateInfosInVO);

    void updateTemplateInfo(TemplateInfoDTO templateInfoDTO);

    void deleteTemplateInfo(TemplateInfoDTO templateInfoDTO);
}
