package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 工程结算报工信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Getter
@Setter
@TableName("product_settlement_work_report")
public class ProductSettlementWorkReportPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_TICKET_NO = "product_ticket_no";
    public static final String PROCESS_NAME = "process_name";
    public static final String REAL_PRODUCT_QUANTITY = "real_product_quantity";
    public static final String SCRAP_QUANTITY = "scrap_quantity";
    public static final String ATTRITION_RATE = "attrition_rate";
    public static final String PROCESS_SEQ = "process_seq";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 生产工程单号
     */
    @TableField(PRODUCT_TICKET_NO)
    private String productTicketNo;


    /**
     * 工序名称
     */
    @TableField(PROCESS_NAME)
    private String processName;


    /**
     * 真实生产数量
     */
    @TableField(REAL_PRODUCT_QUANTITY)
    private BigDecimal realProductQuantity;


    /**
     * 报废数量
     */
    @TableField(SCRAP_QUANTITY)
    private BigDecimal scrapQuantity;

    /**
     * 损耗率
     */
    @TableField(ATTRITION_RATE)
    private BigDecimal attritionRate;

    /**
     * 工序顺序
     */
    @TableField(PROCESS_SEQ)
    private Integer processSeq;

    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
