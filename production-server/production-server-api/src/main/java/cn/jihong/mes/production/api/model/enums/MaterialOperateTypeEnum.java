package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum MaterialOperateTypeEnum implements IntCodeEnum{

    UP_MATERIAL("10", "上料"),
    DOWN_MATERIAL("20", "下料"),
    CHANGE_MATERIAL("30", "换料"),
    TEMPORARY_STORAGE("40", "暂存"),
    DAILY_SETTLEMENT("50", "日结"),
    ;

    private String code;

    private String name;

    MaterialOperateTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MaterialOperateTypeEnum getMaterialOperateTypeEnum(String code) {
        for (MaterialOperateTypeEnum value : MaterialOperateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((MaterialOperateTypeEnum) enumValue).getCode();
    }
}
