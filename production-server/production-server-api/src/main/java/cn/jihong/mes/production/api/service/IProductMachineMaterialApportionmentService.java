package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.ApportionmentMaterialStandardConsumptionDTO;
import cn.jihong.mes.production.api.model.dto.DeleteMaterialRecordDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductUseMaterialsInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDailySettlementOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOneDayTaskSummaryOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductUseMaterialsOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;
import java.util.Map;

/**
 * 机台物料消耗使用信息 服务类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProductMachineMaterialApportionmentService extends IJiHongService<ProductMachineMaterialApportionmentPO> {

    List<GetMaterialDailySettlementOutVO> getMaterialDailyApportionment(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    List<GetMaterialDailySettlementOutVO> apportionmentMaterialDaily(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    Map<String, ApportionmentMaterialStandardConsumptionDTO> getTicketStandardConsumptionMap(List<GetOneDayTaskSummaryOutVO> taskSummary);

//    List<GetMaterialDailySettlementOutVO> apportionmentMaterialDaily2(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    void confirmMaterialDailyApportionment(String key,GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    List<GetMaterialDailySettlementOutVO> apportionmentUseMaterialInfo(Long id);

    List<GetMaterialDailySettlementOutVO> pourUseMaterialInfo(Long id);

    String confirmPourUseMaterialInfo(String key,Long id);

    void saveMaterialDailyApportionment(ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO);

    List<ProductMachineMaterialApportionmentPO> getByProductTicketId(Long id);

    ProductMachineMaterialApportionmentPO getByMaterialOperationRecordId(Long materialOperationRecordId);

    Pagination<GetProductUseMaterialsOutVO> useMaterials(GetProductUseMaterialsInVO getProductUseMaterialsInVO);

    /**
     * 批量删除用料信息，支持同时处理来自两个表的记录
     * @param deleteRecords 要删除的记录列表，包含ID和表类型
     */
    void deleteMaterials(List<DeleteMaterialRecordDTO> deleteRecords);
}
