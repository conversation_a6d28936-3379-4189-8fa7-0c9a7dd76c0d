package cn.jihong.mes.production.app.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

public class ExcelReader {

    // 用于暂存读取到的每一行数据
    public static List<String> data = new ArrayList<>();
    
    // 方法：读取Excel并处理单字段
    public void readExcel(String filePath) {
        // 创建一个监听器用于读取和处理数据
        EasyExcel.read(filePath, new AnalysisEventListener<LinkedHashMap<Integer, String>>() {

            @Override
            public void invoke(LinkedHashMap<Integer, String> row, AnalysisContext context) {
                // 假设只读取每一行的第一个字段
                String firstColumnValue = row.get(0);
                data.add(firstColumnValue);
                System.out.println("读取到的数据: " + firstColumnValue);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 在这里可以处理读取后的所有数据
                System.out.println("所有数据读取完成: " + data);
            }

        }).headRowNumber(0).sheet().doRead();
    }
}
