//package cn.jihong.mes.production.app.service;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.date.DatePattern;
//import cn.jihong.channel.api.model.vo.in.SyncEventActiveInVO;
//import cn.jihong.channel.api.model.vo.in.SyncEventDestroyInVO;
//import cn.jihong.channel.api.model.vo.out.SyncEventActiveOutVO;
//import cn.jihong.channel.api.model.vo.out.SyncEventDestroyOutVO;
//import cn.jihong.channel.api.service.ICaiNiaoKejiService;
//import cn.jihong.common.enums.BooleanEnum;
//import cn.jihong.common.exception.CommonException;
//import cn.jihong.common.model.dto.request.UserInfo;
//import cn.jihong.common.util.SecurityUtil;
//import cn.jihong.common.util.holder.UserContextHolder;
//import cn.jihong.mes.production.api.model.constant.CaiNiaoKeJiConst;
//import cn.jihong.mes.production.api.model.enums.BoxActiveEnum;
//import cn.jihong.mes.production.api.model.enums.CaiNiaoParamEnum;
//import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
//import cn.jihong.mes.production.api.model.vo.in.DestoryBoxInVO;
//import cn.jihong.mes.production.api.model.vo.in.PushWmsAndErpInVO;
//import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailDetailService;
//import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
//import cn.jihong.mes.production.api.service.IProductOutboundService;
//import cn.jihong.mes.production.app.ProductionBootstrap;
//import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
//import cn.jihong.oa.erp.api.service.ILsafTService;
//import cn.jihong.oa.erp.api.service.IMaterialsInfoService;
//import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
//import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockHttpServletRequest;
//import org.springframework.mock.web.MockHttpServletResponse;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//
//
//@Slf4j
//@SpringBootTest(classes = ProductionBootstrap.class)
//@RunWith(SpringJUnit4ClassRunner.class)
////@ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
//public class ProductOutboundServiceImplTest {
//
//    @DubboReference(url = "dubbo://************:20881")
//    private ISrmBarcodeDetailService srmBarcodeDetailService;
//    @DubboReference(url = "dubbo://**********:20884")
//    private ILsafTService iLsafTService;
//    @DubboReference(url = "dubbo://**********:20884")
//    private IInventoryToErpService iInventoryToErpService;
//    @Resource
//    private IProductBoxBarcodeService productBoxBarcodeService;
//    @Resource
//    private IProductOutboundService productOutboundService;
//    @Resource
//    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;
//    @DubboReference(url = "dubbo://**********:20900")
//    private ICaiNiaoKejiService caiNiaoKejiService;
//    @DubboReference(url = "dubbo://**********:20889")
//    private IMaterialsInfoService materialsInfoService;
//
//
//    private MockHttpServletRequest request;
//    private MockHttpServletResponse response;
//
//
//
//    @Test
//    public void test() throws Exception {
//
//        request = new MockHttpServletRequest();
//        response = new MockHttpServletResponse();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(16258L);
//        userInfo.setWorkcode("JHXG00858");
//        userInfo.setName("杨洲");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//
//        // 封装数据
//        PushWmsAndErpInVO pushWmsAndErpInVO = new PushWmsAndErpInVO();
//        pushWmsAndErpInVO.setPlanTicketNo("125-S299-24080000047");
//        pushWmsAndErpInVO.setCaseCode("4DCD0006001000000000C35A,4DCD0006001000000000C35B");
//        pushWmsAndErpInVO.setProducedQuantity(BigDecimal.valueOf(100));
//
//        UserInfo currentLoginUser = SecurityUtil.getCurrentLoginUser();
//        if (currentLoginUser == null) {
//            throw new CommonException("未登录");
//        }
//
//    }
//
//
//    @Test
//    public void destroyBoxCodes() {
//
//        request = new MockHttpServletRequest();
//        response = new MockHttpServletResponse();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-082");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//
//        DestoryBoxInVO destoryBoxInVO = new DestoryBoxInVO();
//        ExcelReader reader = new ExcelReader();
//        reader.readExcel("C:\\Users\\<USER>\\Desktop\\test.xlsx");  // 替换为你的Excel文件路径
//        List<String> data = ExcelReader.data;
//        destoryBoxInVO.setBoxCodes(data);
//
//        if (CollectionUtil.isEmpty(destoryBoxInVO.getBoxCodes())) {
//            throw new CommonException("请选择需要消箱的箱码");
//        }
//        Map<String, String> map = Maps.newHashMap();
//        destoryBoxInVO.getBoxCodes().forEach(boxCode -> {
//            try {
//                String msg = destroyBoxCode(boxCode);
//                map.put(boxCode, msg);
//            } catch (Exception e) {
//                log.error("消箱失败：" + boxCode, e);
//                map.put(boxCode, e.getMessage());
//            }
//        });
//        log.info("消箱结果：" + JSON.toJSONString(map));
//    }
//
//
//    private String destroyBoxCode(String boxCode) {
//        if (StringUtils.isBlank(boxCode)) {
//            throw new CommonException("箱码不能为空");
//        }
////        List<LsafTPO> sameCaseCodeList = iLsafTService.getByBoxCodes(Arrays.asList(boxCode));
////        if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
////            throw new CommonException("箱码已在erp使用：" + JSON.toJSONString(sameCaseCodeList.stream().map(lsafTPO -> lsafTPO.getLsaf001() + "入库申请单号:"+ lsafTPO.getLsaf009()).collect(Collectors.toList())));
////        }
//
//        // 调用菜鸟边缘系统，去消箱
//        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(boxCode);
//        if (productBoxBarcodeDetailDetailPO == null) {
//            throw new CommonException("箱码不存在");
//        }
//        if (productBoxBarcodeDetailDetailPO.getActiva().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))) {
//            throw new CommonException("箱码未激活，不能去消箱");
//        }
//        if (productBoxBarcodeDetailDetailPO.getActiva().equals(BoxActiveEnum.DESTROY.getCode())) {
//            throw new CommonException("箱码已消箱,不用重复消箱");
//        }
//
//        SyncEventDestroyInVO request = new SyncEventDestroyInVO();
//        request.setDeviceCode(SecurityUtil.getCompanySite());
//        request.setEventCode(CaiNiaoKeJiConst.EVENT_CODE_DESTROY);
//
//        SyncEventDestroyInVO.EventDetail eventDetail = new SyncEventDestroyInVO.EventDetail();
//        SyncEventDestroyInVO.SkuInfo skuInfo = new SyncEventDestroyInVO.SkuInfo();
//        skuInfo.setBatch(productBoxBarcodeDetailDetailPO.getBatchCode());
//        skuInfo.setProduceDate(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getProductionDate(), DatePattern.NORM_DATE_PATTERN));
//        skuInfo.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
//        skuInfo.setSkuName(productBoxBarcodeDetailDetailPO.getProductName());
//        skuInfo.setTinyBatch("");
//        skuInfo.setVendorSkuCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getVendorSkuCode());
//        skuInfo.setVendorSkuName(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getVendorSkuName());
//        eventDetail.setSkuInfo(skuInfo);
//        SyncEventDestroyInVO.Tag tag = new SyncEventDestroyInVO.Tag();
//        tag.setEpc(boxCode);
//        tag.setScanTime(cn.hutool.core.date.DateUtil.format(new Date(),DatePattern.NORM_DATETIME_PATTERN));
//        tag.setProduceDate(new Date());
//        tag.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
//        tag.setSkuName(productBoxBarcodeDetailDetailPO.getProductName());
//
//        List tagList = Lists.newArrayList();
//        tagList.add(tag);
//        eventDetail.setTagList(tagList);
//
//        request.setEventDetail(eventDetail);
//        request.setTenantCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getTenantCode());
//
//        SyncEventDestroyOutVO syncEventDestroyOutVO = caiNiaoKejiService.syncEventDestroy(request);
//        if (!"true".equals(syncEventDestroyOutVO.getSuccess())) {
//            throw new CommonException("消箱失败，原因：" + syncEventDestroyOutVO.getErrorMsg());
//        }
//
//        // 提取msg字段的内容
//        String msg = ProductBoxBarcodeDetailDetailServiceImpl.extractField(com.alibaba.fastjson.JSON.toJSONString(syncEventDestroyOutVO), "data");
//        // 提取成功和失败的个数
//        int successCount = ProductBoxBarcodeDetailDetailServiceImpl.extractCount(msg, "消箱成功(\\d+)个");
//        if (successCount > 0) {
//            log.info("消箱成功: " + boxCode);
//            log.info("更新本地箱码消箱状态: " + boxCode);
//            productBoxBarcodeDetailDetailService.destroyBox(boxCode);
//        } else {
//            throw new CommonException("消箱失败: " + boxCode);
//        }
//        return syncEventDestroyOutVO.getData();
//    }
//
//    @Test
//    public void activeBoxCodes(){
//        request = new MockHttpServletRequest();
//        response = new MockHttpServletResponse();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-082");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//
//        DestoryBoxInVO destoryBoxInVO = new DestoryBoxInVO();
//        ExcelReader reader = new ExcelReader();
//        reader.readExcel("C:\\Users\\<USER>\\Desktop\\test.xlsx");  // 替换为你的Excel文件路径
//        List<String> data = ExcelReader.data;
//        destoryBoxInVO.setBoxCodes(data);
//
//        if (CollectionUtil.isEmpty(destoryBoxInVO.getBoxCodes())) {
//            throw new CommonException("请选择需要消箱的箱码");
//        }
//        Map<String, String> map = Maps.newHashMap();
//        Map<String, String> map2 = Maps.newHashMap();
//        destoryBoxInVO.getBoxCodes().forEach(boxCode -> {
//            try {
//                SyncEventActiveOutVO msg = activeBoxCode(boxCode,map2);
//                map.put(boxCode, msg.getData().getMessage());
//            } catch (Exception e) {
//                log.error("激活失败：" + boxCode, e);
//                map.put(boxCode, e.getMessage());
//            }
//        });
//        log.info("激活结果：" + JSON.toJSONString(map));
//    }
//
//
//
//    private SyncEventActiveOutVO activeBoxCode(String barcode, Map<String, String> map2) {
//        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(barcode);
//        if (productBoxBarcodeDetailDetailPO == null) {
//            throw new CommonException("箱码不存在");
//        }
//
//        // 更新激活的skucode
//        String skuCode = map2.get(productBoxBarcodeDetailDetailPO.getMaterialCode());
//        if (StringUtils.isBlank(skuCode)) {
//            MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
//            if (materialsInfoDTO != null) {
//                map2.put(productBoxBarcodeDetailDetailPO.getMaterialCode(), materialsInfoDTO.getImaaua017());
//                skuCode = materialsInfoDTO.getImaaua017();
//            }
//        }
//        if (!StringUtils.equals(skuCode, productBoxBarcodeDetailDetailPO.getSkuCode())) {
//            log.info("条码skucode{}与erp中skucode{}不一致，更新为erp中的skucode", productBoxBarcodeDetailDetailPO.getSkuCode(),
//                    skuCode);
//            productBoxBarcodeDetailDetailPO.setSkuCode(skuCode);
//            productBoxBarcodeDetailDetailService.updateById(productBoxBarcodeDetailDetailPO);
//        }
//
//        SyncEventActiveInVO request = new SyncEventActiveInVO();
//        request.setDeviceCode(SecurityUtil.getCompanySite());
//        request.setEventCode(CaiNiaoKeJiConst.EVENT_CODE_ACTIVE);
//
//        SyncEventActiveInVO.SkuInfo skuInfo = new SyncEventActiveInVO.SkuInfo();
//        skuInfo.setBatch(productBoxBarcodeDetailDetailPO.getBatchCode());
//        skuInfo.setProduceDate(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getProductionDate(),DatePattern.NORM_DATE_PATTERN));
//        skuInfo.setDueDate(productBoxBarcodeDetailDetailPO.getExpirationDate());
//        skuInfo.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
//        skuInfo.setSkuName(productBoxBarcodeDetailDetailPO.getProductName());
//        skuInfo.setTinyBatch("");
//        skuInfo.setVendorSkuCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getVendorSkuCode());
//        skuInfo.setVendorSkuName(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getVendorSkuName());
//
//        SyncEventActiveInVO.EventDetail eventDetail = new SyncEventActiveInVO.EventDetail();
//        eventDetail.setSkuInfo(skuInfo);
//
//        SyncEventActiveInVO.Tag tag = new SyncEventActiveInVO.Tag();
//        tag.setEpc(barcode);
//        tag.setScanTime(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getUpdateTime(),DatePattern.NORM_DATETIME_PATTERN));
//
//        List tagList = Lists.newArrayList();
//        tagList.add(tag);
//        eventDetail.setTagList(tagList);
//
//        request.setEventDetail(eventDetail);
//        request.setTenantCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getTenantCode());
//
//
//
//        SyncEventActiveOutVO syncEventActiveOutVO = caiNiaoKejiService.syncEventActive(request);
//        if (!"true".equals(syncEventActiveOutVO.getSuccess())) {
//            throw new CommonException("激活失败，原因：" + syncEventActiveOutVO.getErrorMsg());
//        }
//        return syncEventActiveOutVO;
//    }
//
//}
