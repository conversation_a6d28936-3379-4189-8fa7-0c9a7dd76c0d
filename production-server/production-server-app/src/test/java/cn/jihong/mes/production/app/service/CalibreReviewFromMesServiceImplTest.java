package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.service.ICalibreReviewFromMesService;
import cn.jihong.mes.production.app.ProductionBootstrap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
public class CalibreReviewFromMesServiceImplTest {

    @Autowired
    private ICalibreReviewFromMesService calibreReviewFromMesService;

    @Test
    public void testA(){
//        QualityTraceabilityInVO qualityTraceabilityInVO = new QualityTraceabilityInVO();
//        qualityTraceabilityInVO.setPlanTicketNo("125-S299-24040000062");
//        List<GetProduceDetailsOutVO> produceDetails = calibreReviewFromMesService.getProduceDetails(qualityTraceabilityInVO);
//        List<GetMaterialDetailsOutVO> materialDetails = calibreReviewFromMesService.getMaterialDetails(qualityTraceabilityInVO);
//        System.out.println("1");
    }

}