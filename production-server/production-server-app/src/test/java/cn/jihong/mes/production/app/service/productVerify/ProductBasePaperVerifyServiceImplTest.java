package cn.jihong.mes.production.app.service.productVerify;

import cn.hutool.core.util.ObjectUtil;
import cn.jihong.mes.production.app.ProductionBootstrap;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.compress.utils.Lists;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
public class ProductBasePaperVerifyServiceImplTest {


    private static   void getProcessAttritionRate() {
        String json = "[{\n" +
                "\t\"sfcb001\": 0,\n" +
                "\t\"sfcb002\": 10,\n" +
                "\t\"sfcb003\": \"1020\",\n" +
                "\t\"sfcb004\": \"1\",\n" +
                "\t\"sfcb005\": \"1\",\n" +
                "\t\"sfcb007\": \"INIT\",\n" +
                "\t\"sfcb008\": \"0\",\n" +
                "\t\"sfcb009\": \"097\",\n" +
                "\t\"sfcb010\": \"1\",\n" +
                "\t\"sfcb011\": \"1020\"\n" +
                "},\n" +
                "{\n" +
                "\t\"sfcb001\": 0,\n" +
                "\t\"sfcb002\": 20,\n" +
                "\t\"sfcb003\": \"097\",\n" +
                "\t\"sfcb004\": \"1\",\n" +
                "\t\"sfcb005\": \"1\",\n" +
                "\t\"sfcb007\": \"1020\",\n" +
                "\t\"sfcb008\": \"1\",\n" +
                "\t\"sfcb009\": \"1090\",\n" +
                "\t\"sfcb010\": \"1\",\n" +
                "\t\"sfcb011\": \"097\"\n" +
                "},\n" +
                "{\n" +
                "\t\"sfcb001\": 0,\n" +
                "\t\"sfcb002\": 30,\n" +
                "\t\"sfcb003\": \"1090\",\n" +
                "\t\"sfcb004\": \"1\",\n" +
                "\t\"sfcb005\": \"1\",\n" +
                "\t\"sfcb007\": \"097\",\n" +
                "\t\"sfcb008\": \"1\",\n" +
                "\t\"sfcb009\": \"1110\",\n" +
                "\t\"sfcb010\": \"1\",\n" +
                "\t\"sfcb011\": \"1090\"\n" +
                "},\n" +
                "{\n" +
                "\t\"sfcb001\": 0,\n" +
                "\t\"sfcb002\": 40,\n" +
                "\t\"sfcb003\": \"1110\",\n" +
                "\t\"sfcb004\": \"1\",\n" +
                "\t\"sfcb005\": \"1\",\n" +
                "\t\"sfcb007\": \"1090\",\n" +
                "\t\"sfcb008\": \"1\",\n" +
                "\t\"sfcb009\": \"041\",\n" +
                "\t\"sfcb010\": \"1\",\n" +
                "\t\"sfcb011\": \"1110\"\n" +
                "\t\n" +
                "\n" +
                "},\n" +
                "{\n" +
                "\t\"sfcb001\": 0,\n" +
                "\t\"sfcb002\": 50,\n" +
                "\t\"sfcb003\": \"041\",\n" +
                "\t\"sfcb004\": \"1\",\n" +
                "\t\"sfcb005\": \"1\",\n" +
                "\t\"sfcb007\": \"1110\",\n" +
                "\t\"sfcb008\": \"1\",\n" +
                "\t\"sfcb009\": \"END\",\n" +
                "\t\"sfcb010\": \"0\",\n" +
                "\t\"sfcb011\": \"041\"\n" +
                "}]";

        List<SfcbTVO> sfcbTVOS = JSON.parseArray(json, SfcbTVO.class);
        Map<String, SfcbTVO> current =
                sfcbTVOS.stream().collect(Collectors.toMap(SfcbTVO::getSfcb003, Function.identity()));
        // 排序 获得当前所在的工艺，并获得之前的工艺
        List<SfcbTVO> sfcbTSort = Lists.newArrayList();
        SfcbTVO sfcbTVO = current.get("041");
        setSfcbTSort(current, sfcbTSort, sfcbTVO);
        System.out.println(sfcbTSort);
    }

    private static void setSfcbTSort(Map<String, SfcbTVO> currentMap, List<SfcbTVO> sfcbTSort, SfcbTVO sfcbTVO) {
        if (ObjectUtil.isNotNull(sfcbTVO)) {
            sfcbTSort.add(sfcbTVO);
            sfcbTVO = currentMap.get(sfcbTVO.getSfcb007());
            setSfcbTSort(currentMap, sfcbTSort, sfcbTVO);
        }

    }

    public static void main(String[] args) {
        ProductBasePaperVerifyServiceImplTest test = new ProductBasePaperVerifyServiceImplTest();
        test.getProcessAttritionRate();
    }
}