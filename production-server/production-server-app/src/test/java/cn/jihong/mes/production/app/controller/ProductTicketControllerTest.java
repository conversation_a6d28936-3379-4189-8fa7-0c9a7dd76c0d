package cn.jihong.mes.production.app.controller;

import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.ProductionBootstrap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
public class ProductTicketControllerTest {

    @Autowired
    private IProductTicketService productTicketService;

    public void updateProduct(Long productId, Integer newQuantity) {
        ProductTicketPO productTicketPO = productTicketService.getById(productId);

        productTicketPO.setStatus(newQuantity);

        productTicketService.updateById(productTicketPO);
    }

    @Test
    public void testOptimisticLocking() throws InterruptedException {
        final Long productId = 1L; // 测试的产品ID
        final int threadCount = 10; // 并发线程数

        // 创建固定大小的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        // 使用 CountDownLatch 控制所有线程几乎同时开始
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int quantity = i + 1; // 每个线程更新不同的数量
            executorService.execute(() -> {
                try {
                    updateProduct(productId, quantity);
                    System.out.println(Thread.currentThread().getName() + " 更新成功");
                } catch (Exception e) {
                    System.out.println(Thread.currentThread().getName() + " 更新失败: " + e.getMessage());
                } finally {
                    latch.countDown(); // 线程完成，计数器减1
                }
            });
        }

        // 等待所有线程完成
        latch.await();
        executorService.shutdown();


    }



}