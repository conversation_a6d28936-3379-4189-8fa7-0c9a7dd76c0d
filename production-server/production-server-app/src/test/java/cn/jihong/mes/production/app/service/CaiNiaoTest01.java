package cn.jihong.mes.production.app.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.HashMap;
import java.util.Map;

public class CaiNiaoTest01 {

    public static void main(String[] args) {
        JSONObject requestJson = JSONUtil.createObj();
        requestJson.set("deviceCode", "XGJH001");
        requestJson.set("eventCode", "active");
//        requestJson.set("eventCode", "line_active");
        JSONObject eventDetail = JSONUtil.createObj();
        JSONObject skuInfo = JSONUtil.createObj();
        skuInfo.set("batch", "1");

        skuInfo.set("produceDate", "2024-07-22");
        skuInfo.set("skuCode", "00000001");
        skuInfo.set("skuName", "麦当劳测试001");
        skuInfo.set("tinyBatch", "");
        skuInfo.set("vendorSkuCode", "jiHong");
        skuInfo.set("vendorSkuName", "吉宏");
        eventDetail.set("skuInfo", skuInfo);
        JSONArray tagList = JSONUtil.createArray();
        JSONObject tag = JSONUtil.createObj();
        tag.set("epc", "4DCD00060010000000000004");
        tag.set("scanTime", "2024-07-22 14:39:14");
        tagList.add(tag);
        eventDetail.set("tagList", tagList);
        requestJson.set("eventDetail", eventDetail);
        requestJson.set("tenantCode", "Jihonggroup");
        syncEventUpload(requestJson);
    }

    /**
     * 所有的作业事件，都走统一的物模型事件上报接口，只是不同的作业 eventCode 不同
     * @param requestJson
     */
    private static void syncEventUpload(JSONObject requestJson) {
        String api = "SYNC_EVENT_UPLOAD";
        String content = requestJson.toString();
        minilink(api, content);
    }


    public static String minilink(String api, String content) {
        String linkUrl = "http://116.62.173.190/gateway/link.do";
        String fromAppKey = "ji_hong"; // ji_hong zi_dan
        String fromCode = "zi_dan";
        String toAppKey = "galaxy";
        String toCode = "galaxy";
        String secret = "JiHong@2023!"; // JiHong@2023!   ZiDan@2023!
        String dataDigest = calculateDigest(content, secret);
        Map<String, Object> params = new HashMap<>();
        params.put("content", content);
        params.put("data_digest", dataDigest);
        params.put("api_name", api);
        params.put("from_appkey", fromAppKey);
        params.put("from_code", fromCode);
        params.put("to_appkey", toAppKey);
        params.put("to_code", toCode);
        System.out.println("Request url: " + linkUrl + ", api: " + api);
        System.out.println("Request body: " + JSONUtil.parseObj(content).toStringPretty());
        String body = HttpRequest.post(linkUrl).contentType("application/x-www-form-urlencoded").form(params).execute().body();
        System.out.println("Response body: " + JSONUtil.parseObj(body).toStringPretty());
        return body;
    }


    /**
     * • 计算签名值
     * •
     * • @param content 请求报文体
     * • @param secretkey from_appkey 配置的私钥
     * • @return
     */
    public static String calculateDigest(String content, String secretKey) {
        String text = content + secretKey;
        byte[] md5 = DigestUtils.md5(text);
        return Base64.encodeBase64String(md5);
    }

}
