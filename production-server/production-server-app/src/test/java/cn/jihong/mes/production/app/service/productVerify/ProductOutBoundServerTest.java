package cn.jihong.mes.production.app.service.productVerify;

import cn.jihong.mes.production.app.ProductionBootstrap;
import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
public class ProductOutBoundServerTest {

    @Autowired
    private ProductOutBoundServer productOutBoundServer;
    @Autowired
    private ProductPalletServer productPalletServer;
    @DubboReference
    private IInventoryToErpService iInventoryToErpService;

    @Test
    public void testA() {
//        productOutBoundServer.verify(1L,"125-S299-24010000103","华阳卫星1450-9印刷机:00114");
//        productPalletServer.verify(1L,"125-S299-24010000103","华阳卫星1450-9印刷机:00114");
//        Date curentDate = new Date();
//        String timeStamp = DateUtil.parseDateToStringCustom(curentDate,"yyyyMMddHHmmssSSS");
//        StorageApplicationToErpInDTO inDTO = new StorageApplicationToErpInDTO();
//        inDTO.setKey(UUID.randomUUID().toString().replaceAll("-", ""));
//        inDTO.setTimeStamp(timeStamp);
//        inDTO.setPlanTicketNo("125-S299-24010000104");
//        System.out.println("---storageApplicationToErp:{} ---"+ JSON.toJSONString(inDTO));
//        iInventoryToErpService.storageApplicationToErp(inDTO);
    }

}