package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.app.ProductionBootstrap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
// @ActiveProfiles(value = "dev")
@ActiveProfiles(value = "prod")
public class ProductStoreServiceImplTest {

    @Resource
    private ProductStoreServiceImpl productStoreService;


    @Test
    public void testSaveLogisticsPalletCode() {
        String str = "125-S342-25020000083," +
                "125-S342-25020000081," +
                "125-S342-25020000078," +
                "125-S342-25020000073," +
                "125-S342-25020000082," +
                "125-S342-25020000061," +
                "125-S342-25020000056," +
                "125-S342-25020000054," +
                "125-S342-25020000065," +
                "125-S342-25020000045," +
                "125-S342-25020000042," +
                "125-S342-25020000043," +
                "125-S342-25020000037," +
                "125-S342-25020000055," +
                "125-S342-25020000057," +
                "125-S342-25020000066," +
                "125-S342-25020000070," +
                "125-S342-25020000071," +
                "125-S342-25020000084," +
                "125-S342-25020000085," +
                "125-S342-25020000040," +
                "125-S342-25020000039," +
                "125-S342-25020000041," +
                "125-S342-25020000062," +
                "125-S342-25020000063," +
                "125-S342-25020000072," +
                "125-S342-25020000080," +
                "125-S342-25020000064," +
                "125-S342-25020000068," +
                "125-S342-25020000067," +
                "125-S342-25020000077," +
                "125-S342-25020000075," +
                "125-S342-25020000074," +
                "125-S342-25020000036," +
                "125-S342-25020000038," +
                "125-S342-25020000044," +
                "125-S342-25020000046," +
                "125-S342-25020000047," +
                "125-S342-25020000048," +
                "125-S342-25020000051," +
                "125-S342-25020000052," +
                "125-S342-25020000060," +
                "125-S342-25020000049," +
                "125-S342-25020000050," +
                "126-S342-25020001353," +
                "126-S342-25020001303," +
                "101-S342-25020000034," +
                "101-S342-25020000033," +
                "101-S342-25020000035," +
                "101-S342-25020000105," +
                "101-S342-25020000103," +
                "101-S342-25020000102," +
                "101-S342-25020000101";
        String[] split = str.split(",");
        for (String s : split) {
            try {
                String barcodeLotNo = productStoreService.getBarcodeLotNo(s);
            } catch (Exception e) {
                log.error("获取条码批次号失败", e);
            }
        }

    }

}