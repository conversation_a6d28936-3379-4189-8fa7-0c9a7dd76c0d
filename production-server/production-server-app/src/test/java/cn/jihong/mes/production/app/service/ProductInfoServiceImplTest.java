package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.po.ProductInterfaceRecordsPO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mes.production.api.service.IProductInfoService;
import cn.jihong.mes.production.api.service.IProductInterfaceRecordsService;
import cn.jihong.mes.production.api.service.IProductShiftService;
import cn.jihong.mes.production.app.ProductionBootstrap;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
public class ProductInfoServiceImplTest {

    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IProductInterfaceRecordsService productInterfaceRecordsService;
    @DubboReference(url = "dubbo://**********:20883")
    private IA01Service ia01Service;
    @Autowired
    private IProductShiftService productShiftService;

    @Test
    public void testA(){
//        productInfoService.createOrUpdateProductInfo(242L,null);
    }

    @Test
    public void testB() {
        // 获取需要的记录
        LambdaQueryWrapper<ProductInterfaceRecordsPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductInterfaceRecordsPO::getBusinessType, "1")
//                .eq(ProductInterfaceRecordsPO::getResponse, "101-S331-***********")
                .gt(ProductInterfaceRecordsPO::getCreateTime, "2024-07-01 00:00:00")
                .lt(ProductInterfaceRecordsPO::getCreateTime, "2024-07-08 18:03:40")
                .notLike(ProductInterfaceRecordsPO::getResponse, "没有");
        List<ProductInterfaceRecordsPO> list = productInterfaceRecordsService.list(lambdaQueryWrapper);

        // 取出所有的 productTicketId
        List<Long> productTicketIds = list.stream()
                .map(ProductInterfaceRecordsPO::getProductTicketId)
                .collect(Collectors.toList());

        // 批量获取所有 ProductShiftOutVO
        List<ProductShiftOutVO> allProductShiftOutVOS = productShiftService.getProductShift(productTicketIds);

        // 取出所有需要获取用户信息的 teamUserId
        List<Long> teamUserIds = allProductShiftOutVOS.stream()
                .filter(productShiftOutVO -> !productShiftOutVO.getTeamUsers().contains(","))
                .map(productShiftOutVO -> Long.valueOf(productShiftOutVO.getTeamUsers()))
                .collect(Collectors.toList());

        // 批量获取所有用户信息
        List<UserDTO> allUserInfo = ia01Service.getUserInfoByIds(teamUserIds);
        Map<Long, UserDTO> userMapById = allUserInfo.stream()
                .collect(Collectors.toMap(UserDTO::getId, Function.identity()));

        // 构建最后的 map
        Map<String, Map<String, String>> map = Maps.newHashMap();
        list.forEach(productInterfaceRecordsPO -> {
            Long productTicketId = productInterfaceRecordsPO.getProductTicketId();
            List<ProductShiftOutVO> productShiftOutVOS = allProductShiftOutVOS.stream()
                    .filter(productShiftOutVO -> productShiftOutVO.getProductTicketId().equals(productTicketId))
                    .collect(Collectors.toList());

//            HashMap<String, String> userMap = Maps.newHashMap();
            HashMap<String, String> userMap2 = Maps.newHashMap();
            HashMap<String, String> userMap3 = Maps.newHashMap();
            productShiftOutVOS.forEach(productShiftOutVO -> {
                if (productShiftOutVO.getTeamUsers().contains(",")) {
                    return;
                }
                UserDTO userDTO = userMapById.get(Long.valueOf(productShiftOutVO.getTeamUsers()));
                if (userDTO == null) {
                    throw new RuntimeException("获取不到用户信息");
                }
                if (userMap2.containsKey(productShiftOutVO.getRoleCode())) {
                    userMap3.put(productShiftOutVO.getRoleCode() + "-" + userMap2.get(productShiftOutVO.getRoleCode()), userMap2.get(productShiftOutVO.getRoleCode()));
                }
                userMap2.put(productShiftOutVO.getRoleCode(), userDTO.getWorkcode());
            });
            map.put(productInterfaceRecordsPO.getResponse(), userMap3);
        });

        System.out.println(JSON.toJSONString(map));

        map.entrySet().stream().forEach(stringMapEntry -> {
            String response = stringMapEntry.getKey();
            Map<String, String> userMap = stringMapEntry.getValue();
            userMap.entrySet().stream().forEach(stringStringEntry -> {
                String roleCode = stringStringEntry.getKey().substring(0, stringStringEntry.getKey().indexOf("-"));
                String workcode = stringStringEntry.getValue();
                System.out.println("insert into SFFE_T(SFFEENT,SFFESEQ,SFFE001, SFFEUA009, SFFEDOCNO) values ('100','0','" +  workcode + "','" +  roleCode + "','" +response + "')");
            });
        });

    }


}