//package cn.jihong.mes.production.app.service.logistics;
//
//import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
//import cn.jihong.mes.production.app.ProductionBootstrap;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import javax.annotation.Resource;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest(classes = ProductionBootstrap.class)
//@RunWith(SpringJUnit4ClassRunner.class)
//@ActiveProfiles(value = "dev")
//class LogisticsImplTest1 {
//
//    @Resource
//    private ILogisticsService iLogisticsService;
//
//    @Test
//    void buildErpOrderId() {
//    }
//
//    @Test
//    void meshDeviceDetails() {
//    }
//
//    @Test
//    void syncProductionPlanning() {
//    }
//
//    @Test
//    void callForMaterial() {
//    }
//
//    @Test
//    void returnOfMaterialMeshList() {
//        iLogisticsService.returnOfMaterialMeshList(1L);
//    }
//
//    @Test
//    void returnOfMaterial() {
//
//    }
//
//    @Test
//    void startCallMaterialByMeshId() {
//    }
//
//    @Test
//    void pauseCallMaterialByMeshId() {
//    }
//
//    @Test
//    void itemQueryByCondition() {
//    }
//
//    @Test
//    void internetTape() {
//    }
//}