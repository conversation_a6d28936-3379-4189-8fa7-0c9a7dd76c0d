package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.service.ICommonEnumService;
import cn.jihong.mes.production.app.util.EnumUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CommonEnumServiceImpl implements ICommonEnumService {

    /**
     * 获得枚举
     */
    @Override
    public List<EnumDTO> getEnumsByType(String enumType) {
        Class<?> enumClass = EnumUtils.getEnumClass(enumType);
        List<EnumDTO> enumValues = EnumUtils.getEnumValues(enumClass);
        return enumValues;
    }


}
