package cn.jihong.mes.production.app.mapper;


import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductMaterialOperationRecordsPO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.out.MaterialRecordsOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料操作记录信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface ProductMaterialOperationRecordsMapper extends JiHongMapper<ProductMaterialOperationRecordsPO> {

    Page<MaterialRecordsOutVO> getMaterialRecords(IPage page,
                                                  @Param("productTicketPageInVO") ProductTicketPageInVO productTicketPageInVO, @Param("operationType") String operationType, @Param("companyCode")String companyCode);

    Page<MaterialRecordsOutVO> getMaterialRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);

    List<ProductMaterialOperationRecordsDTO> getByProductTicketIds(
        @Param("productTicketIds") List<Long> productTicketIds, @Param("operationTypes") List<Integer> operationTypes);
}
