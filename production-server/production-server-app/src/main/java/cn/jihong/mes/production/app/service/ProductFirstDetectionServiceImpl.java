package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.calibre.review.server.api.model.dto.FirstTaskDTO;
import cn.jihong.calibre.review.server.api.model.vo.CreateFirstTaskOutVO;
import cn.jihong.calibre.review.server.api.model.vo.GetFirstTaskInspectionInVO;
import cn.jihong.calibre.review.server.api.model.vo.GetFirstTaskInspectionOutVO;
import cn.jihong.calibre.review.server.api.service.IQcFirstTaskService;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.constant.MessageInfoConsts;
import cn.jihong.mes.production.api.model.dto.FirstCheckStatusDTO;
import cn.jihong.mes.production.api.model.enums.FirstCheckStatusEnum;
import cn.jihong.mes.production.api.model.enums.ProductShitEnum;
import cn.jihong.mes.production.api.model.po.ProductFirstDetectionPO;
import cn.jihong.mes.production.api.model.po.ProductInfoPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.CreateFirstDetectionInVO;
import cn.jihong.mes.production.api.model.vo.out.CreateFirstDetectionOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductFirstDetectionIngByTicketIdOutVO;
import cn.jihong.mes.production.api.service.IProductFirstDetectionService;
import cn.jihong.mes.production.api.service.IProductInfoService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductFirstDetectionMapper;
import cn.jihong.mes.production.app.util.ThreadPoolUtil;
import cn.jihong.message.api.model.enums.MessageTypeEnum;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 首件记录信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@DubboService
public class ProductFirstDetectionServiceImpl extends
    JiHongServiceImpl<ProductFirstDetectionMapper, ProductFirstDetectionPO> implements IProductFirstDetectionService {

    @Resource
    private IProductTicketService productTicketService;

    @DubboReference
    private IQcFirstTaskService qcFirstTaskService;
    @Resource
    private IProductInfoService productInfoService;

    @DubboReference
    private IEnterpriseWeChatService iEnterpriseWeChatService;




    /**
     * ttl时间 10分钟
     */
//    private static String TTLTIMES = String.valueOf(10 * 60 * 1000);
    private static String TTLTIMES = String.valueOf(1 * 60 * 1000);

    @RedisLock
    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public CreateFirstDetectionOutVO createFirstDetection(String key,CreateFirstDetectionInVO createFirstDetectionInVO) {
        log.info("Seata全局事务id=================>{}", RootContext.getXID());
        ProductTicketPO productTicketPO = productTicketService.verify(createFirstDetectionInVO.getId());
        if (ObjectUtil.isNull(productTicketPO)) {
            throw new CommonException("查询不到对应工单信息");
        }
        // 校验
        verifyFirstTask(productTicketPO);

      //  ProductInfoPO productInfo = productInfoService.getProductInfo(productTicketPO);
        // 调整为调用 质检模块首检任务 校验
        GetFirstTaskInspectionInVO getFirstTaskInspectionInVO = new GetFirstTaskInspectionInVO();
        getFirstTaskInspectionInVO.setMachineName(productTicketPO.getMachineName());
        getFirstTaskInspectionInVO.setProductionPlanWorkerOrderNo(productTicketPO.getPlanTicketNo());
        getFirstTaskInspectionInVO.setProductionDate(productTicketPO.getProduceDate());
        getFirstTaskInspectionInVO.setProductionSerialNo(productTicketPO.getShift());
        getFirstTaskInspectionInVO.setCompanyCode(productTicketPO.getCompanyCode());
        GetFirstTaskInspectionOutVO firstTaskInspection = qcFirstTaskService.getFirstTaskInspection(getFirstTaskInspectionInVO);
        // 尚未创建过首件 或者 首件不通过
        if(Objects.isNull(firstTaskInspection) || (firstTaskInspection.getFinallyCheckResult() != null && BooleanEnum.FALSE.getCode().equals(firstTaskInspection.getFinallyCheckResult()))) {


            ThreadPoolUtil.getInstance().execute(()->{
                try {
                    // 2.通知进行首件
                    iEnterpriseWeChatService.sendComTextMessage(MessageTypeEnum.getCodeByCompanyAndParentCode(productTicketPO.getCompanyCode(), MessageTypeEnum.FIRST.getCode()),
                            "首件通知：生产单号：" + productTicketPO.getPlanTicketNo() + "，机台：" + productTicketPO.getMachineName());
                }catch (Exception e){
                    log.error("首件通知异常：{}",e.getMessage());
                    e.printStackTrace();
                }
            });
            // 3.创建MQ延时队列 --- xx 分钟后会继续通知
//            RabbitMqUtil.sendMessage(MQBizCodeEnum.FIRST_DETECTION.getCode(), RabbitConsts.DEAD,
//                    createFirstDetectionInVO.getId(), TTLTIMES);

            // 4.保存调用mq的记录
            ProductFirstDetectionPO productFirstDetectionPO = new ProductFirstDetectionPO();
            productFirstDetectionPO.setProductTicketId(createFirstDetectionInVO.getId());
            productFirstDetectionPO.setMachineName(productTicketPO.getMachineName());
            productFirstDetectionPO.setCheckStatus(FirstCheckStatusEnum.CREATE.getCode());
            productFirstDetectionPO.setCreateBy(SecurityUtil.getUserId());
            productFirstDetectionPO.setCompanyCode(productTicketPO.getCompanyCode());
            productFirstDetectionPO.setRoles(createFirstDetectionInVO.getRoles());
            save(productFirstDetectionPO);

            // 1.创建首件任务
            CreateFirstTaskOutVO firstTaskOutVO = createFirstCheck(productTicketPO, productFirstDetectionPO);

            // 创建/更新生产工程单信息  按照班次去获取
            ProductInfoPO productInfoPO = productInfoService.createOrUpdateProductInfo(createFirstDetectionInVO.getId(), null);
            // 创建完新的首件，需要将之前的首件结果置空
            productInfoService.updateFirstCheckResult(productInfoPO.getId());
            CreateFirstDetectionOutVO outVO = new CreateFirstDetectionOutVO();
            outVO.setQcFirstTaskId(firstTaskOutVO.getQcFirstTaskId());
            outVO.setSelectInspectionPeopleJobs(createFirstDetectionInVO.getRoles());
            return outVO;
        }else{
            if (firstTaskInspection.getFinallyCheckResult() != null && BooleanEnum.TRUE.getCode().equals(firstTaskInspection.getFinallyCheckResult())) {
                throw new CommonException("首件任务已通过，无需再起发起首检");
            }

            CreateFirstDetectionOutVO outVO = new CreateFirstDetectionOutVO();
            outVO.setQcFirstTaskId(firstTaskInspection.getId());
            outVO.setSelectInspectionPeopleJobs(firstTaskInspection.getSelectInspectionPeopleJobs());

            /*GetFirstTaskInspectionPeopleJobInVO inVO = new GetFirstTaskInspectionPeopleJobInVO();
            inVO.setProductionPlanWorkOrderNo(productTicketPO.getPlanTicketNo());
            inVO.setMachineName(productTicketPO.getMachineName());
            inVO.setSerialNo(productTicketPO.getShift());
            inVO.setProductionPlanDate(DateUtil.format(productTicketPO.getProduceDate(),DatePattern.NORM_DATE_PATTERN));

            GetFirstTaskInspectionPeopleJobOutVO firstTaskOutVO = qcFirstTaskService.getFirstTaskInspectionPeopleJob(inVO);
            return BeanUtil.copyProperties(firstTaskOutVO, CreateFirstDetectionOutVO.class);*/
            return outVO;
        }
    }

    private void verifyFirstTask( ProductTicketPO productTicketPO) {
        GetFirstTaskInspectionInVO inVO = new GetFirstTaskInspectionInVO();
        inVO.setMachineName(productTicketPO.getMachineName());
        inVO.setProductionDate(productTicketPO.getProduceDate());
        inVO.setProductionPlanWorkerOrderNo(productTicketPO.getPlanTicketNo());
        inVO.setProductionSerialNo(productTicketPO.getShift());
        inVO.setCompanyCode(productTicketPO.getCompanyCode());
        GetFirstTaskInspectionOutVO firstTaskInspectionOutVO = qcFirstTaskService.getFirstTaskInspection(inVO);

        // 可能存在历史首件信息
        if (Objects.nonNull(firstTaskInspectionOutVO)) {
            if(!FirstCheckStatusEnum.END.getCode().equals(firstTaskInspectionOutVO.getStatus())){
                throw new CommonException("已存在进行中的首件任务，请勿重复创建");
            }

            if (BooleanEnum.TRUE.getCode().equals(firstTaskInspectionOutVO.getFinallyCheckResult())) {
                throw new CommonException("首件任务已通过，无需再起发起首检");
            }
        }
    }


    private CreateFirstTaskOutVO createFirstCheck(ProductTicketPO productTicketPO, ProductFirstDetectionPO productFirstDetectionPO) {
        FirstTaskDTO firstTaskDTO = new FirstTaskDTO();
        firstTaskDTO.setFirstCheckId(productFirstDetectionPO.getId());
        firstTaskDTO.setProductTicketId(productTicketPO.getId());
        firstTaskDTO.setProductionWorkerOrderNo(productTicketPO.getTicketRequestId());
        firstTaskDTO.setProductionPlanWorkOrderNo(productTicketPO.getPlanTicketNo());
        firstTaskDTO.setProductionMachine(productTicketPO.getMachineName());
        firstTaskDTO.setProductionSerialNo(productTicketPO.getShift());
        firstTaskDTO.setStatus(Integer.valueOf(FirstCheckStatusEnum.CREATE.getCode()));
        firstTaskDTO.setProductionDate(productTicketPO.getProduceDate());
        firstTaskDTO.setProcessName(productTicketPO.getProcess());
        firstTaskDTO.setProcessType(productTicketPO.getProcessType());
        firstTaskDTO.setRoles(productFirstDetectionPO.getRoles());
        firstTaskDTO.setCompanyCode(productTicketPO.getCompanyCode());
        return qcFirstTaskService.createFirstTask(firstTaskDTO);
    }

    /**
     * 发送到群里
     * 
     * @param bizCode
     * @param ticketId
     */
    @Override
    public void createMessage(Integer bizCode, String ticketId) {
        // 查询当前的状态
        LambdaQueryWrapper<ProductFirstDetectionPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFirstDetectionPO::getProductTicketId, ticketId)
            .isNull(ProductFirstDetectionPO::getCheckResult);
        ProductFirstDetectionPO productFirstDetectionPO = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(productFirstDetectionPO)) {
            log.error("首件任务不存在,或者已完成");
            return;
        }

        ProductTicketPO productTicketPO = productTicketService.getById(ticketId);
        String messageInfo = "";
        String shiftName = ProductShitEnum.getProductShitEnum(productTicketPO.getShift()).getName();
        String productDate = DateUtil.format(productTicketPO.getProduceDate(), "yyyy-MM-dd");

        if (!FirstCheckStatusEnum.END.getCode().equals(productFirstDetectionPO.getCheckStatus())) {
            messageInfo = String.format(MessageInfoConsts.MESSAGE_INFO, productTicketPO.getCompanyCode(),
                    productTicketPO.getPlanTicketNo(), productTicketPO.getTicketRequestId(), productDate, shiftName,
                    productTicketPO.getProductName());
        }
        if (StringUtils.isNotBlank(messageInfo)) {
            log.info("发送的消息为：" + messageInfo);
            iEnterpriseWeChatService.sendMessage(messageInfo);
        }
    }


    /**
     * 更新首检状态
     *
     * @param firstCheckStatusDTO
     */
    @Override
    public void updateFirstCheckStatus(FirstCheckStatusDTO firstCheckStatusDTO) {
        LambdaUpdateWrapper<ProductFirstDetectionPO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(ProductFirstDetectionPO::getId, firstCheckStatusDTO.getFirstCheckId())
                .set(ProductFirstDetectionPO::getCheckStatus, firstCheckStatusDTO.getFirstCheckStatus())
                .set(StringUtils.isNotBlank(firstCheckStatusDTO.getFirstCheckResult()),ProductFirstDetectionPO::getCheckResult, firstCheckStatusDTO.getFirstCheckResult());
        update(lambdaUpdateWrapper);

        if (StringUtils.isNotBlank(firstCheckStatusDTO.getFirstCheckResult())) {
            // 把首件结果更新到productInfo中
            productInfoService.createOrUpdateProductInfo(firstCheckStatusDTO.getProductTicketId(),
                    firstCheckStatusDTO.getFirstCheckResult());
        }

        // 发送MQ消息
//        RabbitMqUtil.sendMessage(MQBizCodeEnum.FIRST_DETECTION.getCode(), RabbitConsts.DEAD,
//                firstCheckStatusDTO.getProductTicketId(), TTLTIMES);
    }


    @Override
    public GetProductFirstDetectionIngByTicketIdOutVO getProductFirstDetectionIngByTicketId(Long ticketId) {
        ProductTicketPO productTicketPO = productTicketService.getById(ticketId);
        GetFirstTaskInspectionInVO inVO = new GetFirstTaskInspectionInVO();
        inVO.setMachineName(productTicketPO.getMachineName());
        inVO.setProductionDate(productTicketPO.getProduceDate());
        inVO.setProductionPlanWorkerOrderNo(productTicketPO.getPlanTicketNo());
        inVO.setProductionSerialNo(productTicketPO.getShift());
        inVO.setCompanyCode(productTicketPO.getCompanyCode());
        GetFirstTaskInspectionOutVO firstTaskInspectionOutVO = qcFirstTaskService.getFirstTaskInspection(inVO);

        // 可能存在历史首件信息
        if (Objects.nonNull(firstTaskInspectionOutVO)) {
            if(firstTaskInspectionOutVO.getStatus().equals(FirstCheckStatusEnum.END.getCode())) {
                // 首件完成
                if (BooleanEnum.TRUE.getCode().equals(firstTaskInspectionOutVO.getFinallyCheckResult())) {
                    throw new CommonException("首件任务已通过，无需再起发起首检");
                }
            }else {
                // 首件进行中
                GetProductFirstDetectionIngByTicketIdOutVO outVO = new GetProductFirstDetectionIngByTicketIdOutVO();
                BeanUtil.copyProperties(firstTaskInspectionOutVO, outVO);
                outVO.setQcFirstTaskId(firstTaskInspectionOutVO.getId());
                outVO.setMachineName(firstTaskInspectionOutVO.getProductionMachine());
                outVO.setRoles(firstTaskInspectionOutVO.getSelectInspectionPeopleJobs());
                return outVO;
            }
        }
        return null;
    }
}
