package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.SysDictInVO;
import cn.jihong.mes.production.api.model.vo.in.SysDictQueryInVO;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mes.production.api.service.ISysDictService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统字典表
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/sysDict")
@ShenyuSpringMvcClient(path = "/sysDict/**")
public class SysDictController {

    @Resource
    private ISysDictService sysDictService;

    /**
     * 分页查询字典列表
     */
    @PostMapping("/page")
    public StandardResult<Pagination<SysDictOutVO>> page(@RequestBody @Valid SysDictQueryInVO queryInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.page(queryInVO));
    }

    /**
     * 根据ID获取字典详情
     */
    @GetMapping("/{id}")
    public StandardResult<SysDictOutVO> getById(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.getSysDictById(id));
    }

    /**
     * 新增字典
     */
    @PostMapping
    public StandardResult<Boolean> add(@RequestBody @Valid SysDictInVO dictInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.add(dictInVO));
    }

    /**
     * 修改字典
     */
    @PutMapping
    public StandardResult<Boolean> update(@RequestBody @Valid SysDictInVO dictInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.update(dictInVO));
    }

    /**
     * 删除字典
     */
    @DeleteMapping("/{id}")
    public StandardResult<Boolean> delete(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.delete(id));
    }

    /**
     * 批量删除字典
     */
    @DeleteMapping("/batch")
    public StandardResult<Boolean> batchDelete(@RequestBody List<Long> ids) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.batchDelete(ids));
    }

    /**
     * 根据字典类型查询字典列表
     */
    @GetMapping("/type/{dictType}")
    public StandardResult<List<SysDictOutVO>> listByType(@PathVariable String dictType) {
        return StandardResult.resultCode(OperateCode.SUCCESS, sysDictService.listByType(dictType));
    }
}

