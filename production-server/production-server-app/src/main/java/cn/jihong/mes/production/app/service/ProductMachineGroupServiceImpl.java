package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.po.ProductMachineGroupPO;
import cn.jihong.mes.production.api.model.po.ProductMachineGroupRelationshipPO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupOperationHistoryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO;
import cn.jihong.mes.production.api.service.IProductMachineGroupRelationshipService;
import cn.jihong.mes.production.api.service.IProductMachineGroupService;
import cn.jihong.mes.production.app.mapper.ProductMachineGroupMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 机组信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@DubboService
public class ProductMachineGroupServiceImpl extends JiHongServiceImpl<ProductMachineGroupMapper, ProductMachineGroupPO>
    implements IProductMachineGroupService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IProductMachineGroupRelationshipService productMachineGroupRelationshipService;
    @DubboReference
    private IProductionMachineService productionMachineService;

    @Override
    public List<ProductMachineGroupOutVO> getList() {
        List<ProductMachineGroupOutVO> list = baseMapper.getList(SecurityUtil.getCompanySite());
        list.stream().forEach(productMachineGroupOutVO -> {
            productMachineGroupOutVO.setCount(productMachineGroupOutVO.getProductMachineGroupRelationships().size());
        });
        return list;
    }

    @Override
    public ProductMachineGroupOutVO getDetialById(Long id) {
        ProductMachineGroupOutVO productMachineGroupOutVO = new ProductMachineGroupOutVO();
        ProductMachineGroupPO productMachineGroupPO = getById(id);
        if (productMachineGroupPO == null) {
            throw new CommonException("不存在该机组信息");
        }
        productMachineGroupOutVO.setId(productMachineGroupPO.getId());
        productMachineGroupOutVO.setCompanyCode(productMachineGroupPO.getCompanyCode());
        productMachineGroupOutVO.setMachineGroupName(productMachineGroupPO.getMachineGroupName());
        if (productMachineGroupPO != null) {
            List<ProductMachineGroupRelationshipPO> productMachineGroupRelationshipPOS =
                productMachineGroupRelationshipService.getByGroupId(productMachineGroupPO.getId());
            productMachineGroupOutVO.setProductMachineGroupRelationships(BeanUtil.copyToList(
                productMachineGroupRelationshipPOS, ProductMachineGroupOutVO.ProductMachineGroupRelationship.class));
        }
        return productMachineGroupOutVO;
    }

    @Override
    public Long addMachineGroup(ProductMachineGroupInVO productMachineGroupInVO) {
        List<ProductMachineGroupPO> productMachineGroupPOS = getByNmae(productMachineGroupInVO.getMachineGroupName());
        if (CollectionUtil.isNotEmpty(productMachineGroupPOS)) {
            throw new CommonException("存在相同的机组名称");
        }

        // 检查是否有重复的机器名称
        List<ProductMachineGroupRelationshipPO> machineNames = productMachineGroupRelationshipService
            .getByMachineNames(productMachineGroupInVO.getProductMachineGroupRelationships().stream()
                .map(ProductMachineGroupInVO.ProductMachineGroupRelationship::getMachineName)
                .collect(Collectors.toList()));
        if (CollectionUtil.isNotEmpty(machineNames)) {
            throw new CommonException("存在已被使用的机器名称：" + machineNames.stream()
                .map(ProductMachineGroupRelationshipPO::getMachineName).collect(Collectors.toList()));
        }

        ProductMachineGroupPO productMachineGroupPO = new ProductMachineGroupPO();
        productMachineGroupPO.setMachineGroupName(productMachineGroupInVO.getMachineGroupName());
        productMachineGroupPO.setCompanyCode(SecurityUtil.getCompanySite());
        productMachineGroupPO.setCreateBy(SecurityUtil.getUserId());
        productMachineGroupPO.setUpdateBy(SecurityUtil.getUserId());
        save(productMachineGroupPO);

        if (CollectionUtil.isNotEmpty(productMachineGroupInVO.getProductMachineGroupRelationships())) {
            List<ProductMachineGroupRelationshipPO> productMachineGroupRelationshipPOS = Lists.newArrayList();
            productMachineGroupInVO.getProductMachineGroupRelationships().forEach(productMachineGroupRelationship -> {
                ProductMachineGroupRelationshipPO productMachineGroupRelationshipPO =
                    new ProductMachineGroupRelationshipPO();
                productMachineGroupRelationshipPO.setMachineGroupId(productMachineGroupPO.getId());
                productMachineGroupRelationshipPO.setMachineName(productMachineGroupRelationship.getMachineName());
                productMachineGroupRelationshipPO.setCompanyCode(SecurityUtil.getCompanySite());
                productMachineGroupRelationshipPO.setCreateBy(SecurityUtil.getUserId());
                productMachineGroupRelationshipPO.setUpdateBy(SecurityUtil.getUserId());
                productMachineGroupRelationshipPOS.add(productMachineGroupRelationshipPO);
            });
            productMachineGroupRelationshipService.saveBatch(productMachineGroupRelationshipPOS);
        }
        return productMachineGroupPO.getId();
    }

    private List<ProductMachineGroupPO> getByNmae(String machineGroupName) {
        LambdaQueryWrapper<ProductMachineGroupPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineGroupPO::getMachineGroupName, machineGroupName);
        return list(lambdaQueryWrapper);
    }

    @Override
    public Long updateMachineGroup(ProductMachineGroupInVO productMachineGroupInVO) {
        ProductMachineGroupPO productMachineGroupPO = getById(productMachineGroupInVO.getId());
        productMachineGroupPO.setMachineGroupName(productMachineGroupInVO.getMachineGroupName());
        updateById(productMachineGroupPO);

        // 删除所有
        if (CollectionUtil.isEmpty(productMachineGroupInVO.getProductMachineGroupRelationships())) {
            List<ProductMachineGroupRelationshipPO> productMachineGroupRelationshipPOS =
                productMachineGroupRelationshipService.getByGroupId(productMachineGroupPO.getId());
            if (CollectionUtil.isNotEmpty(productMachineGroupRelationshipPOS)) {
                productMachineGroupRelationshipService.removeByIds(productMachineGroupRelationshipPOS.stream()
                    .map(ProductMachineGroupRelationshipPO::getId).collect(Collectors.toList()));
            }
            return productMachineGroupPO.getId();
        }

        // 更新关系表
        List<ProductMachineGroupRelationshipPO> addList = Lists.newArrayList();
        List<ProductMachineGroupRelationshipPO> removeList = Lists.newArrayList();
        List<ProductMachineGroupRelationshipPO> machineGroupRelationshipPOList =
            productMachineGroupRelationshipService.getByGroupId(productMachineGroupPO.getId());
        if (CollectionUtil.isNotEmpty(machineGroupRelationshipPOList)) {
            // 遍历旧的数据 旧的有，新的没有，则删除
            machineGroupRelationshipPOList.forEach(machineGroupRelationshipPO -> {
                if (!productMachineGroupInVO.getProductMachineGroupRelationships().stream()
                    .map(ProductMachineGroupInVO.ProductMachineGroupRelationship::getMachineName)
                    .collect(Collectors.toList()).contains(machineGroupRelationshipPO.getMachineName())) {
                    removeList.add(machineGroupRelationshipPO);
                }
            });
            // 遍历新的数据 新的有，旧的没有，则添加
            productMachineGroupInVO.getProductMachineGroupRelationships().stream()
                .map(ProductMachineGroupInVO.ProductMachineGroupRelationship::getMachineName).forEach(machineName -> {
                    if (!machineGroupRelationshipPOList.stream().map(ProductMachineGroupRelationshipPO::getMachineName)
                        .collect(Collectors.toList()).contains(machineName)) {
                        ProductMachineGroupRelationshipPO productMachineGroupRelationshipPO =
                            new ProductMachineGroupRelationshipPO();
                        productMachineGroupRelationshipPO.setMachineGroupId(productMachineGroupPO.getId());
                        productMachineGroupRelationshipPO.setMachineName(machineName);
                        productMachineGroupRelationshipPO.setCompanyCode(SecurityUtil.getCompanySite());
                        productMachineGroupRelationshipPO.setCreateBy(SecurityUtil.getUserId());
                        productMachineGroupRelationshipPO.setUpdateBy(SecurityUtil.getUserId());
                        addList.add(productMachineGroupRelationshipPO);
                    }
                });

            if (CollectionUtil.isNotEmpty(addList)) {
                // 检查是否有重复的机器名称
                List<ProductMachineGroupRelationshipPO> machineNames =
                    productMachineGroupRelationshipService.getByMachineNames(addList.stream()
                        .map(ProductMachineGroupRelationshipPO::getMachineName).collect(Collectors.toList()));
                if (CollectionUtil.isNotEmpty(machineNames)) {
                    throw new CommonException("存在已被使用的机器名称：" + machineNames.stream()
                        .map(ProductMachineGroupRelationshipPO::getMachineName).collect(Collectors.toList()));
                }
                productMachineGroupRelationshipService.saveBatch(addList);
            }
            productMachineGroupRelationshipService.removeByIds(removeList);
        }
        return productMachineGroupPO.getId();
    }

    @Override
    public Pagination<ProductMachineOutVO> getMachineList(ProductMachineInVO productMachineInVO) {
        // 不去重复
        List<String> usedMachineNames = productMachineGroupRelationshipService.getByCompanyCode(SecurityUtil.getCompanySite())
            .stream().map(ProductMachineGroupRelationshipPO::getMachineName).collect(Collectors.toList());

        IPage<ProductionMachinePO> page = productionMachineService.getMachineListByName(productMachineInVO.getPage(),
            productMachineInVO.getMachineName(),null);
        return Pagination.newInstance(page.getRecords().stream().map(productionMachinePO -> {
            ProductMachineOutVO productMachineOutVO = new ProductMachineOutVO();
            productMachineOutVO.setMachineName(productionMachinePO.getErpMachineName());
            if (usedMachineNames.contains(productionMachinePO.getErpMachineName())) {
                productMachineOutVO.setDisabled(true);
            } else {
                productMachineOutVO.setDisabled(false);
            }
            return productMachineOutVO;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public void saveOperationHistory(ProductMachineGroupOperationHistoryInVO productMachineGroupOperationHistoryInVO) {
        // 缓存操作历史
        redisTemplate.opsForValue().set(RedisCacheConstant.MACHINE_GROUP_OPERATION_HISTORY + SecurityUtil.getUserId(),
            JSON.toJSONString(productMachineGroupOperationHistoryInVO), 7, TimeUnit.DAYS);
    }

    @Override
    public ProductMachineGroupOperationHistoryInVO getOperationHistory() {
        String obj = (String)redisTemplate.opsForValue()
            .get(RedisCacheConstant.MACHINE_GROUP_OPERATION_HISTORY + SecurityUtil.getUserId());
        log.info("操作历史：{}", obj);
        if (obj != null) {
            return JSON.parseObject(obj, ProductMachineGroupOperationHistoryInVO.class);
        }
        return null;
    }

    @Override
    public void deleteById(Long id) {
        removeById(id);
        productMachineGroupRelationshipService.removeByGroupId(id);
    }

}
