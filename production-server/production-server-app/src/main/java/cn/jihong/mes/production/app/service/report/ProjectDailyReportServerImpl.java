package cn.jihong.mes.production.app.service.report;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.dto.CommonReplenishDTO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductMachineTaskService;
import cn.jihong.mes.production.api.service.report.IProjectDailyReportServer;
import cn.jihong.mes.production.app.mapper.ProductMachineTaskMapper;
import cn.jihong.mes.production.app.service.productVerify.ProductVerifyHelper;
import cn.jihong.oa.erp.api.model.dto.ReportUnitConvertDTO;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.SfbaTVO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/3/8 11:26
 */
@Slf4j
@DubboService
public class ProjectDailyReportServerImpl implements IProjectDailyReportServer {

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;

    @Resource
    private ProductMachineTaskMapper productMachineTaskMapper;

    @Resource
    private ProductVerifyHelper productVerifyHelper;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @DubboReference
    private ISfaaTService iSfaaTService;

    @DubboReference
    private ISfbaTService iSfbaTService;

    @DubboReference
    private IReportUnitConvertService iReportUnitConvertService;

    @DubboReference
    private IEcaaucTService ecaaucTService;

    @Override
    public Pagination<GetProjectDailyReportsOutVO> getProjectDailyReports(GetProjectDailyReportsInVO inVO) {

        if(inVO.getProduceDate() == null){
            inVO.setProduceDate(LocalDate.now().toString());
        }

        Page<GetProjectDailyReportsOutVO> page = productMachineTaskMapper.getProjectDailyReports(inVO.getPage(), inVO);
        page.getRecords().stream().peek(t->{

            try {

                if (t.getMaterialType() != null && t.getMaterialType().startsWith(BasePaperConst.PAPER_TYPE)) {
                    if (t.getMaterialUnit() != null) {
                        BigDecimal totalConsumptionPcsQuantity = productVerifyHelper.unitConversion(t.getPlanTicketNo(), t.getTotalConsumptionQuantity(), t.getMaterialUnit(), "PCS", t.getMachineName(), t.getMaterialType(), t.getMaterialCode(), t.getProcessCode());
                        t.setTotalConsumptionPcsQuantity(totalConsumptionPcsQuantity.setScale(2, RoundingMode.HALF_UP));
                    }

                    if (t.getReportedUnit() != null) {
                        BigDecimal totalReportedPcsQuantity = productVerifyHelper.unitConversion(t.getPlanTicketNo(), t.getTotalReportedQuantity(), t.getReportedUnit(), "PCS", t.getMachineName(), t.getMaterialType(), t.getMaterialCode(), t.getProcessCode());
                        t.setTotalReportedPcsQuantity(totalReportedPcsQuantity.setScale(2, RoundingMode.HALF_UP));

                        BigDecimal totalDefectiveProductsPcsQuantity = productVerifyHelper.unitConversion(t.getPlanTicketNo(), t.getTotalDefectiveProductsQuantity(), t.getReportedUnit(), "PCS", t.getMachineName(), t.getMaterialType(), t.getMaterialCode(), t.getProcessCode());
                        t.setTotalDefectiveProductsPcsQuantity(totalDefectiveProductsPcsQuantity.setScale(2, RoundingMode.HALF_UP));

                        BigDecimal totalProducedPcsQuantity = productVerifyHelper.unitConversion(t.getPlanTicketNo(), t.getTotalProducedQuantity(), t.getReportedUnit(), "PCS", t.getMachineName(), t.getMaterialType(), t.getMaterialCode(), t.getProcessCode());
                        t.setTotalProducedPcsQuantity(totalProducedPcsQuantity.setScale(2, RoundingMode.HALF_UP));
                    }
                } else {
                    log.info("materialType:{}", t.getMaterialType());
                }

            }catch (Exception e) {
               log.error("转换pcs异常：" + e.getMessage());
               e.printStackTrace();
            }

            SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(t.getPlanTicketNo());
            t.setShouldCompletePcsQuantity(sfaaTVO != null?sfaaTVO.getSfaa012():null);

            SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(t.getPlanTicketNo(), t.getProcessCode());
            t.setBillingAttritionRate(sfcbTVO != null?sfcbTVO.getSfcbud011():null);

            if(t.getMaterialPlace() != null && t.getMaterialCode() != null){
                List<SfbaTVO> placeSfbaTVOs = iSfbaTService.getPlaceSfbaTVO(t.getPlanTicketNo(), t.getProcessCode(), t.getMaterialCode(), t.getMaterialPlace());
                t.setBillingMaterialQuantity(placeSfbaTVOs.stream().map(SfbaTVO::getSfba013).reduce(BigDecimal.ZERO,BigDecimal::add));
            }else{
                log.info("materialPlace:{}",t.getMaterialPlace());
            }

            if(t.getAttritionRate()!=null){
                t.setAttritionRate(t.getAttritionRate().setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100))); // 转为 %
            }

        }).collect(Collectors.toList());
        return Pagination.newInstance(page.getRecords(),page);
    }


    @Override
    public Pagination<GetPageByDayOutVO> getPageByDay(GetPageByDayInVO inVO) {

        if(StringUtils.isBlank(inVO.getProduceDate())){
            inVO.setProduceDate(LocalDate.now().toString());
        }

        Page<GetPageByDayOutVO> page = productMachineTaskMapper.getPageByDay(inVO.getPage(), inVO);
        replenishByDay(page);
        return Pagination.newInstance(page.getRecords(),page);
    }

    @Override
    public Pagination<GetPageByDayDetailOutVO> getPageByDayDetail(GetPageByDayDetailInVO inVO) {
        if(StringUtils.isBlank(inVO.getProduceDate())){
            inVO.setProduceDate(LocalDate.now().toString());
        }

        Page<GetPageByDayDetailOutVO> page = productMachineTaskMapper.getPageByDayDetail(inVO.getPage(), inVO);
        replenishByDay(page);
        return Pagination.newInstance(page.getRecords(),page);
    }

    /**
     * 补充日内
     * @param page
     * @return: void
     * <AUTHOR>
     * @date: 2024/4/22 17:03
     */
    private <M extends GetPageByDayOutVO> void replenishByDay(Page<M> page) {
        page.getRecords().stream().peek(t->{
            CommonReplenishDTO dto = commonReplenish(t.getPlanTicketNo(), t.getProcessCode(), t.getProcess(), t.getReportedUnit(),
                    t.getTotalReportedQuantity(), t.getTotalDefectiveProductsQuantity(), t.getTotalPlannedQuantity());
            t.setTotalReportedPcsQuantity(dto.getTotalReportedPcsQuantity());
            t.setTotalDefectiveProductsPcsQuantity(dto.getTotalDefectiveProductsPcsQuantity());
            t.setTotalPlannedPcsQuantity(dto.getTotalPlannedPcsQuantity());
            t.setBillingAttritionRate(dto.getBillingAttritionRate());
            t.setBillingPcsQuantity(dto.getBillingPcsQuantity());

            if(t.getTotalReportedQuantity()!=null) {
                // 实际损耗率
                if(t.getTotalDefectiveProductsQuantity()!=null && t.getTotalReportedQuantity().compareTo(BigDecimal.ZERO)>0) {
                    t.setAttritionRate(t.getTotalDefectiveProductsQuantity().divide(t.getTotalReportedQuantity().add(t.getTotalDefectiveProductsQuantity()),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100))); // 转为 %
                }

                // 当日完成率
                if (t.getTotalPlannedQuantity()!=null && t.getTotalPlannedQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    t.setCompleteRate(t.getTotalReportedQuantity().divide(t.getTotalPlannedQuantity(),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                }
            }
        }).collect(Collectors.toList());
    }

    @Override
    public Pagination<GetPageByEndOutVO> getPageByEnd(GetPageByEndInVO inVO) {
        if(StringUtils.isBlank(inVO.getEndDate())){
            inVO.setEndDate(LocalDate.now().toString());
        }
        Map<String, SfaaTVO> workingNoMap = iSfaaTService.getWorkingListByCompanyCode(inVO.getCompanyCode()).stream().collect(Collectors.toMap(SfaaTVO::getSfaadocno, Function.identity()));
        List<String> workingPlanTicketNoList = new ArrayList<>(workingNoMap.keySet());
        Page<GetPageByEndOutVO> page;
        if(inVO.getWorkOrderStatus().equals(BooleanEnum.TRUE.getCode())){
            page = productMachineTaskMapper.getPageByEnd(inVO.getPage(), inVO, workingPlanTicketNoList);
        }else{
            page = productMachineTaskMapper.getPageByEnd(inVO.getPage(), inVO, null);
        }
        page.getRecords().stream().peek(t->{

            // // 得到最后一道工序
            List<GetProcessSeqByTickNoOutVO> list = iSfcbTService.getProcessSeqByTickNo(t.getPlanTicketNo()).stream().filter(x-> !"包装（股份）".equals(x.getProcessName())).sorted(Comparator.comparing(GetProcessSeqByTickNoOutVO::getProcessSeq).reversed()).collect(Collectors.toList());;
            GetProcessSeqByTickNoOutVO outVO;
                // 最后一道工序是 包装（股份） 不排产，取 最后第二道工序
            if(CollectionUtil.isNotEmpty(list)) {
                outVO = list.get(0);
            }else {
                log.error("获取末道工序为空:" + t.getPlanTicketNo());
                throw new CommonException("获取末道工序为空：" + t.getPlanTicketNo());
            }

            if(t.getProcessCodeConcat().contains(outVO.getSfcb003())) {
                String[] split = t.getProcessCodeConcat().split(",");
                for (int i = 0; i < split.length; i++) {
                    if (StringUtils.isNotBlank(split[i]) && split[i].equals(outVO.getSfcb003())) {
                        t.setProcessCode(outVO.getSfcb003());
                        t.setProcess(outVO.getProcessName());

                        /// 取单位 有误 直接取 机台任务表报工单位
                        EcaaucTPO ecaaucTPO = ecaaucTService.getByCompanyProcess(t.getCompanyCode(), t.getProcessCode());
                        if (ecaaucTPO != null) {
                            t.setReportedUnit(ecaaucTPO.getEcaauc009());
                        }

                        /*String[] temReportedUnitAry = t.getReportedUnitConcat().split(",");
                        // 因为报工可能为空 导致下标越界
                        if (i < temReportedUnitAry.length && temReportedUnitAry[i] != null) {
                            t.setReportedUnit(temReportedUnitAry[i]);
                        }*/

                        // 累加相同工程单下末道工序不同工单的 计划产量数量
                        if (StringUtils.isNotBlank(t.getTotalPlannedQuantityConcat())) {
                            String[] temPlannedQuantityAry = t.getTotalPlannedQuantityConcat().split(",");
                            // 因为计划产量可能为空 导致下标越界
                            if (i < temPlannedQuantityAry.length && temPlannedQuantityAry[i] != null) {
                                BigDecimal temPlannedQuantity = new BigDecimal(temPlannedQuantityAry[i]);
                                t.setTotalPlannedQuantity(t.getTotalPlannedQuantity() != null ? t.getTotalPlannedQuantity().add(temPlannedQuantity) : temPlannedQuantity);
                            }
                        }


                        // 累加相同工程单下末道工序不同工单的 已生产数量
                        if (StringUtils.isNotBlank(t.getTotalReportedQuantityConcat())) {
                            String[] temReportedQuantityAry = t.getTotalReportedQuantityConcat().split(",");
                            // 因为报工可能为空 导致下标越界
                            if (i < temReportedQuantityAry.length && temReportedQuantityAry[i] != null) {
                                BigDecimal temReportedQuantity = new BigDecimal(temReportedQuantityAry[i]);
                                t.setTotalReportedQuantity(t.getTotalReportedQuantity() != null ? t.getTotalReportedQuantity().add(temReportedQuantity) : temReportedQuantity);
                            }
                        }

                    }
                }
            }else {
                // mes还未做 工程单的末道工序
                t.setProcess(outVO.getProcessName());
                t.setProcessCode(outVO.getSfcb003());
            }

        }).collect(Collectors.toList());

        replenishByEnd(page);
        return Pagination.newInstance(page.getRecords(),page);
    }


    /**
     * 补充截止
     * @param page
     * @return: void
     * <AUTHOR>
     * @date: 2024/4/22 17:03
     */
    private <M extends GetPageByEndOutVO> void replenishByEnd(Page<M> page) {
        page.getRecords().stream().peek(t->{
            CommonReplenishDTO dto = commonReplenish(t.getPlanTicketNo(), t.getProcessCode(), t.getProcess(), t.getReportedUnit(),
                    t.getTotalReportedQuantity(), t.getTotalDefectiveProductsQuantity(), t.getTotalPlannedQuantity());
            t.setTotalReportedPcsQuantity(dto.getTotalReportedPcsQuantity());
            t.setTotalDefectiveProductsPcsQuantity(dto.getTotalDefectiveProductsPcsQuantity());
            t.setTotalPlannedPcsQuantity(dto.getTotalPlannedPcsQuantity());
            t.setBillingAttritionRate(dto.getBillingAttritionRate());
            t.setBillingPcsQuantity(dto.getBillingPcsQuantity());
            // 实际损耗率
            if(t.getTotalReportedQuantity()!=null && t.getTotalDefectiveProductsQuantity()!=null && t.getTotalReportedQuantity().compareTo(BigDecimal.ZERO)>0) {
                t.setAttritionRate(t.getTotalDefectiveProductsQuantity().divide(t.getTotalReportedQuantity(),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100))); // 转为 %
            }

            if(t.getBillingAttritionRate() != null && t.getBillingPcsQuantity() != null && t.getTotalDefectiveProductsPcsQuantity() != null) {
                BigDecimal shouldExcessPcsQuantity = t.getBillingAttritionRate().multiply(t.getBillingPcsQuantity());
                if(t.getTotalDefectiveProductsPcsQuantity().compareTo(shouldExcessPcsQuantity)>=0) { // 超损数量大于等于0 才显示
                    // 超损PCS数量
                    t.setExcessPcsQuantity(t.getTotalDefectiveProductsPcsQuantity().subtract(shouldExcessPcsQuantity).setScale(2, RoundingMode.HALF_UP));
                }else {
                    t.setExcessPcsQuantity(BigDecimal.ZERO);
                }
            }
        }).collect(Collectors.toList());
    }

    @Override
    public Pagination<GetPageByEndDetailOutVO> getPageByEndDetail(GetPageByEndDetailInVO inVO) {
        if(StringUtils.isBlank(inVO.getEndDate())){
            inVO.setEndDate(LocalDate.now().toString());
        }
        Page<GetPageByEndDetailOutVO> page = productMachineTaskMapper.getPageByEndDetail(inVO.getPage(), inVO);

        Map<String, Integer> processMap = iSfcbTService.getProcessSeqByTickNo(inVO.getPlanTicketNo()).stream().collect(Collectors.toMap(GetProcessSeqByTickNoOutVO::getSfcb003, GetProcessSeqByTickNoOutVO::getProcessSeq, (existing, replacement) -> existing));
        page.getRecords().stream().peek(t->{
            t.setProcessSeq(processMap.get(t.getProcessCode()) != null ? processMap.get(t.getProcessCode()) : 0);
        }).collect(Collectors.toList());
        replenishByEnd(page);

        // 按工序流程排序
        page.setRecords(page.getRecords().stream().sorted(Comparator.comparing(GetPageByEndDetailOutVO::getProcessSeq)).collect(Collectors.toList()));
        return Pagination.newInstance(page.getRecords(),page);
    }

    /**
     * 公共处理补充属性值
     * @return: cn.jihong.mes.production.api.model.dto.CommonReplenishDTO
     * <AUTHOR>
     * @date: 2024/4/22 17:02
     */
    private CommonReplenishDTO commonReplenish(String planTicketNo,String processCode,String process, String reportedUnit, BigDecimal totalReportedQuantity,
                                BigDecimal totalDefectiveProductsQuantity, BigDecimal totalPlannedQuantity) {
        CommonReplenishDTO t = new CommonReplenishDTO();
        try {
            if (reportedUnit != null) {
                ReportUnitConvertDTO dto = new ReportUnitConvertDTO();
                dto.setPlanTicketNo(planTicketNo);
                dto.setProcessCode(processCode);
                dto.setSourceUnit(reportedUnit);
                dto.setTargetUnit("PCS");
                if(totalReportedQuantity!=null) {
                    ReportUnitConvertDTO dto1 = new ReportUnitConvertDTO();
                    BeanUtil.copyProperties(dto,dto1);
                    dto1.setQuantity(totalReportedQuantity);
                    BigDecimal totalReportedPcsQuantity = iReportUnitConvertService.execute(dto1);
                    t.setTotalReportedPcsQuantity(totalReportedPcsQuantity!=null?totalReportedPcsQuantity.setScale(2, RoundingMode.HALF_UP):null);
                }
                if(totalDefectiveProductsQuantity!=null) {
                    ReportUnitConvertDTO dto2 = new ReportUnitConvertDTO();
                    BeanUtil.copyProperties(dto,dto2);
                    dto2.setQuantity(totalDefectiveProductsQuantity);
                    BigDecimal totalDefectiveProductsPcsQuantity = iReportUnitConvertService.execute(dto2);
                    t.setTotalDefectiveProductsPcsQuantity(totalDefectiveProductsPcsQuantity!=null?totalDefectiveProductsPcsQuantity.setScale(2, RoundingMode.HALF_UP):null);
                }
                if(totalPlannedQuantity != null) {
                    ReportUnitConvertDTO dto3 = new ReportUnitConvertDTO();
                    BeanUtil.copyProperties(dto,dto3);
                    dto3.setQuantity(totalPlannedQuantity);
                    BigDecimal totalPlannedPcsQuantity = iReportUnitConvertService.execute(dto3);
                    t.setTotalPlannedPcsQuantity(totalPlannedPcsQuantity!=null?totalPlannedPcsQuantity.setScale(2, RoundingMode.HALF_UP):null);
                }
            }
        }catch (Exception e) {
            log.error("转换pcs异常：" + e.getMessage());
            e.printStackTrace();
        }

        // 各个工序开单损耗率
        SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        t.setBillingAttritionRate(sfcbTVO != null?sfcbTVO.getSfcbud011():null);

        // 查询该工程单各个工序的开单数量PCS
        t.setBillingPcsQuantity(sfcbTVO != null?sfcbTVO.getSfcb027():null);
        /*Map<String, BigDecimal> map = iSfbaTService.getBillingQuantity(planTicketNo);
        if(Objects.nonNull(map.get(process))){
            t.setBillingPcsQuantity(map.get(process));
        }*/
        return t;
    }
}
