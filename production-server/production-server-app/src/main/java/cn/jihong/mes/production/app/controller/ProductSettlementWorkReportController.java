package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductSettlementWorkReportPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementWorkReportDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportListOutVO;
import cn.jihong.mes.production.api.service.IProductSettlementWorkReportService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工程结算报工信息
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@RestController
@RequestMapping("/productSettlementWorkReport")
@ShenyuSpringMvcClient(path = "/productSettlementWorkReport/**")
public class ProductSettlementWorkReportController {

    @Autowired
    private IProductSettlementWorkReportService iProductSettlementWorkReportService;

    /**
     * 查询报工信息
     * @param productTicketNo
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/8 16:02
     */
    @GetMapping("/getSettlementWorkReportList")
    public StandardResult<List<GetSettlementWorkReportListOutVO>> getSettlementWorkReportList(@RequestParam String productTicketNo){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementWorkReportService.getSettlementWorkReportList(productTicketNo));
    }

    /**
     * 查询报工详情信息列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/15 10:06
     */
    @PostMapping("/getSettlementWorkReportDetail")
    public StandardResult<Pagination<GetSettlementWorkReportDetailOutVO>> getSettlementWorkReportDetail(@RequestBody @Valid GetSettlementWorkReportDetailInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementWorkReportService.getSettlementWorkReportDetail(inVO));
    }
}

