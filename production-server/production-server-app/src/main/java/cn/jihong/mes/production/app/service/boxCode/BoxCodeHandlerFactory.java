package cn.jihong.mes.production.app.service.boxCode;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.service.IBoxCodeService;
import cn.jihong.mes.production.app.config.PushSwitchConfig;
import cn.jihong.oa.erp.api.model.po.PmaaTPO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.service.IPmaaTService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BoxCodeHandlerFactory {

    @Autowired
    private List<IBoxCodeService> boxCodeServices;

    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IPmaaTService pmaaTService;
    @Resource
    private PushSwitchConfig pushSwitchConfig;

    private static Map<String,IBoxCodeService> map = new HashMap<>(100);

    @PostConstruct
    public void init(){
        if(boxCodeServices == null) {
            return;
        }
        boxCodeServices.stream().forEach(boxCodeService->{
            map.put(boxCodeService.getCompanyCode(),boxCodeService);
        });
    }

    public IBoxCodeService getBoxCodeService(String planTicketNo) {
        // TODO  暂时写死
        if ("125-F001-24120000001".equals(planTicketNo)) {
             return map.get(CompanyBoxEnum.COMPANY_MDL.getCode());
        }
        if ("125-S298-25040000011".equals(planTicketNo)) {
             return map.get(CompanyBoxEnum.COMPANY_MDL.getCode());
        }
        // 包含这个据点
        if (CollectionUtil.isNotEmpty(pushSwitchConfig.getCompanyCodes())
                && pushSwitchConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())) {
            // 不是每个客户都有专门的码，默认就是走智物流
            String customerInfo = getCustomerInfo(planTicketNo);
            // 目前只有麦当劳一个，其他都是默认的的
            String company = CompanyBoxEnum.getCompany(customerInfo);
            return map.get(company);
        }

        return map.get(CompanyBoxEnum.COMPANY_ZWL.getCode());
    }




    private String getCustomerInfo(String planTicketNo) {
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNo);
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        // 获得客户集团信息
        PmaaTPO pmaaTPO = pmaaTService.getCustomerInfo(sfaaTVO.getSfaa009());
        if (pmaaTPO != null) {
            return pmaaTPO.getPmaa006();
        }
        log.error("客户集团信息不存在");
        return "ZWL";
    }


}
