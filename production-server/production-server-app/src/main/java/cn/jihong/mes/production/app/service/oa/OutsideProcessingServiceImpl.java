package cn.jihong.mes.production.app.service.oa;

import cn.jihong.common.constant.DateFormatConstant;
import cn.jihong.common.convertor.Convertor;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.WorkflowAttachmentDTO;
import cn.jihong.common.model.dto.WorkflowUserDTO;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.util.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.vo.out.OutsideProcessingVO;
import cn.jihong.oa.approve.api.enums.BusinessType;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OutsideProcessingDTO;
import cn.jihong.oa.approve.api.model.po.flow.OutsideProcessingPO;
import cn.jihong.oa.approve.api.service.IHrmResourceService;
import cn.jihong.oa.erp.api.model.vo.in.GetProductionProcessesVO;
import cn.jihong.oa.erp.api.model.vo.in.GetSupplierVO;
import cn.jihong.oa.erp.api.model.vo.out.GetSupplierOutVO;
import cn.jihong.oa.erp.api.service.IOaPmaalGyT2Service;
import cn.jihong.oa.erp.api.service.IOaSfcbViewService;
import cn.jihong.workflow.common.service.IWorkflowBizService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 外加工
 */
@Service
@Slf4j
public class OutsideProcessingServiceImpl implements IWorkflowBizService<OutsideProcessingPO, OutsideProcessingVO, OutsideProcessingDTO> {
    @DubboReference
    private IHrmResourceService hrmResourceService;

    @DubboReference
    private IOaSfcbViewService iOaSfcbViewService;

    @DubboReference
    private IOaPmaalGyT2Service iOaPmaalGyT2Service;

    @Override
    public OutsideProcessingDTO convert2OaFormData(OutsideProcessingVO outsideProcessingVO, WorkflowUserDTO workflowUserDTO) {

        if(outsideProcessingVO == null){
            log.warn("OutsideProcessingServiceImpl.convert2OaFormData formData is null");
            return null;
        }
        String creatorWorkCode = SecurityUtil.getWorkcode();
        String companyCode = SecurityUtil.getCompanySite();
        UserInfo creatorInfo = hrmResourceService.getUserInfo(creatorWorkCode);
        UserInfo applicantInfo = hrmResourceService.getUserInfo(workflowUserDTO.getWorkcode());
        //String orderNum = "SC01-"+ DateUtil.parseDateToStringCustom(new Date(), DateFormatConstant.NORMAL_NO_CONNECTOR);
        LocalDateTime nowDateTime = LocalDateTime.now();
        String nowDate = nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        OutsideProcessingDTO requestDTO = Convertor.copy(outsideProcessingVO, OutsideProcessingDTO.class);
        requestDTO.setSqr(applicantInfo.getId());
        requestDTO.setCjr(creatorInfo.getId().intValue());//必填
        //requestDTO.setDh(orderNum);
        requestDTO.setSqrq(nowDate);
        //requestDTO.setOrderNo(orderNum);
        requestDTO.setYgbh(applicantInfo.getWorkcode());
        requestDTO.setSsgs(applicantInfo.getCompanyId().intValue());
        requestDTO.setSsbm(applicantInfo.getDeptId().intValue());
        requestDTO.setGw(applicantInfo.getJobTitleId().intValue());
        requestDTO.setBz(outsideProcessingVO.getBz());
        requestDTO.setERPSite(companyCode);
        requestDTO.setCreatorID(creatorWorkCode);
        requestDTO.setProcessingTechnology(requestDTO.getProcessName());
        return requestDTO;
    }

    @Override
    public OutsideProcessingVO convert2FormData(OutsideProcessingDTO outsideProcessingDTO, List<WorkflowAttachmentDTO> list) {
        if(outsideProcessingDTO == null){
            log.warn("OutsideProcessingServiceImpl.convert2FormData formData is null");
            return null;
        }
        OutsideProcessingVO resultVO = Convertor.copy(outsideProcessingDTO, OutsideProcessingVO.class);
        if(StringUtil.isNotBlank(outsideProcessingDTO.getSupplier())){
            GetSupplierVO supplierVO = new GetSupplierVO();
            supplierVO.setPageNum(0);
            supplierVO.setPageSize(1);
            supplierVO.setErpId(outsideProcessingDTO.getERPSite());
            supplierVO.setSupplierNumer(outsideProcessingDTO.getSupplier());
            StandardResult<Pagination<GetSupplierOutVO>> supplier = iOaPmaalGyT2Service.getSupplier(supplierVO);
            AtomicReference<String> supplierName = new AtomicReference<>("");
            if( supplier!=null&& supplier.getData().getData()!=null){
                supplier.getData().getData().stream().forEach(obj->{
                    supplierName.set(obj.getPMAAL003());
                });
            }
            resultVO.setSupplierName(supplierName.toString());
        }
        if (StringUtil.isBlank(resultVO.getProcessName())) {
            resultVO.setProcessName(resultVO.getProcessingTechnology());
        }
        resultVO.setApplyDate(LocalDate.parse(outsideProcessingDTO.getSqrq()));

        return resultVO;
    }

    @Override
    public String checkParams(OutsideProcessingVO request) {
        if(StringUtil.isBlank(request.getProcessName())){
            throw new CommonException("工序名称不能为空");
        }
        if(StringUtil.isBlank(request.getWorkOrderId())){
            throw new CommonException("工作单号不能为空");
        }
        if(request.getExternalQuantity()==null){
            throw new CommonException("外发数量不能为空");
        }
        if(StringUtil.isBlank(request.getExpectsCompletionTime())){
            throw new CommonException("期望完成时间不能为空");
        }
//        if(request.getSupplier()==null){
//            throw new CommonException("供应商不能为空");
//        }
//        if(request.getProcessingUnitPrice()==null){
//            throw new CommonException("加工单价不能为空");
//        }
//        if(request.getSupplierType()==null){
//            throw new CommonException("供应商类型不能为空");
//        }
//        if(request.getTotalProcessingFee()==null){
//            throw new CommonException("总加工费不能为空");
//        }

        return "";
    }

    @Override
    public Long getWorkflowId() {
        return BusinessType.OUTSIDE_PROCESSING.getWorkflowid();
    }

    @Override
    public Class<OutsideProcessingVO> getFormDataClz() {
        return OutsideProcessingVO.class;
    }

    @Override
    public Class<OutsideProcessingDTO> getOaFormDataClz() {
        return OutsideProcessingDTO.class;
    }
}
