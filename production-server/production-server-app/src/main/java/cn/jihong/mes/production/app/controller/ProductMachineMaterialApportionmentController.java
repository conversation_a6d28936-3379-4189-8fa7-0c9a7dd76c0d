package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.DeleteMaterialRecordDTO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductUseMaterialsInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDailySettlementOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductUseMaterialsOutVO;
import cn.jihong.mes.production.api.service.IProductMachineMaterialApportionmentService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 机台物料消耗使用信息 前端控制器
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@RestController
@RequestMapping("/productMachineMaterialApportionment")
@ShenyuSpringMvcClient(path = "/productMachineMaterialApportionment/**")
public class ProductMachineMaterialApportionmentController {

    @Resource
    private IProductMachineMaterialApportionmentService productMachineMaterialApportionmentService;


    /**
     * 分摊物料信息 -- 废弃
     */
    @Deprecated
    @PostMapping("/apportionmentMaterialDaily")
    public StandardResult<List<GetMaterialDailySettlementOutVO>>
    apportionmentMaterialDaily(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.apportionmentMaterialDaily(getMaterialDailySettlementInVO));
    }


    /**
     * 查询分摊信息
     */
    @PostMapping("/getMaterialDailyApportionment")
    public StandardResult<List<GetMaterialDailySettlementOutVO>>
        getMaterialDailyApportionment(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.getMaterialDailyApportionment(getMaterialDailySettlementInVO));
    }

    /**
     * 确认分摊信息
     */
    @PostMapping("/confirmMaterialDailyApportionment")
    public StandardResult
    confirmMaterialDailyApportionment(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        String LOCK_KEY = RedisCacheConstant.MATERIAL_DAILY + getMaterialDailySettlementInVO.getMachineName();
        productMachineMaterialApportionmentService.confirmMaterialDailyApportionment(LOCK_KEY,getMaterialDailySettlementInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 分摊后用料信息
     */
    @GetMapping("/apportionmentUseMaterialInfo/{id}")
    public StandardResult<List<GetMaterialDailySettlementOutVO>>
    apportionmentUseMaterialInfo(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.apportionmentUseMaterialInfo(id));
    }


    /**
     * 倒扣料信息
     * @param id
     * @return
     */
    @GetMapping("/pourUseMaterialInfo/{id}")
    public StandardResult<List<GetMaterialDailySettlementOutVO>>
    pourUseMaterialInfo(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.pourUseMaterialInfo(id));
    }

    /**
     * 倒扣料信息确认
     * @param id
     * @return
     */
    @GetMapping("/confirmPourUseMaterialInfo/{id}")
    public StandardResult
    confirmPourUseMaterialInfo(@PathVariable("id") Long id) {
        String LOCK_KEY = RedisCacheConstant.POUR_USE_MATERIA + id;
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.confirmPourUseMaterialInfo(LOCK_KEY,id));
    }


    /**
     * 获得任务用料信息
     * @return
     */
    @PostMapping("/useMaterials")
    public StandardResult<Pagination<GetProductUseMaterialsOutVO>>
    useMaterials(@RequestBody @Valid GetProductUseMaterialsInVO getProductUseMaterialsInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineMaterialApportionmentService.useMaterials(getProductUseMaterialsInVO));
    }


    /**
     * 批量删除用料信息（增强版），支持同时处理多个表的记录
     * @param deleteRecords 要删除的记录列表，包含ID和表类型
     * @return
     */
    @PostMapping("/deleteMaterialRecords")
    public StandardResult deleteMaterialRecords(@RequestBody List<DeleteMaterialRecordDTO> deleteRecords) {
        productMachineMaterialApportionmentService.deleteMaterials(deleteRecords);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }
}

