package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.constant.SequenceConst;
import cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO;
import cn.jihong.mes.production.api.model.po.ProductBarcodeSplitBarcodeDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBarcodeSplitBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.PrintBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductBarcodeSplitBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveWmsBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.SpitWmsBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.PrintBarcodeOutVO;
import cn.jihong.mes.production.api.model.vo.out.SpitWmsBarcodeOutVO;
import cn.jihong.mes.production.api.service.IProductBarcodeSplitBarcodeDetailService;
import cn.jihong.mes.production.api.service.IProductBarcodeSplitBarcodeService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductBarcodeSplitBarcodeMapper;
import cn.jihong.mes.production.app.util.redis.OrderSequenceUtil;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.wms.api.model.dto.SrmBarcodeStoreDTO;
import cn.jihong.wms.api.model.po.SrmBarcodeDetailPO;
import cn.jihong.wms.api.model.po.SrmBarcodeStorePO;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import cn.jihong.wms.api.service.ISrmBarcodeStoreService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 条码拆分 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@DubboService
public class ProductBarcodeSplitBarcodeServiceImpl
    extends JiHongServiceImpl<ProductBarcodeSplitBarcodeMapper, ProductBarcodeSplitBarcodePO>
    implements IProductBarcodeSplitBarcodeService {


    @DubboReference(timeout = 300000,retries = 0)
    private ISrmBarcodeStoreService srmBarcodeStoreService;
    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @DubboReference
    private IA01Service a01Service;
    @Resource
    private IProductBarcodeSplitBarcodeDetailService productBarcodeSplitBarcodeDetailService;

    @Override
    public Pagination<ProductBarcodeSplitBarcodeDTO>
        queryWmsBarcode(ProductBarcodeSplitBarcodeInVO productBarcodeSplitBarcodeInVO) {
        SrmBarcodeStoreDTO srmBarcodeStoreDTO = new SrmBarcodeStoreDTO();
        srmBarcodeStoreDTO.setBarcodeNo(productBarcodeSplitBarcodeInVO.getBarcodeNo());
        srmBarcodeStoreDTO.setItemNo(productBarcodeSplitBarcodeInVO.getItemNumber());
        srmBarcodeStoreDTO.setItemName(productBarcodeSplitBarcodeInVO.getItemName());
        srmBarcodeStoreDTO.setItemSpec(productBarcodeSplitBarcodeInVO.getItemSpece());
        srmBarcodeStoreDTO.setLotDate(productBarcodeSplitBarcodeInVO.getItemDate());
        srmBarcodeStoreDTO.setWarehouseNo(productBarcodeSplitBarcodeInVO.getWarehouse());
        IPage<SrmBarcodeStoreDTO> srmBarcodeStorePage =
            srmBarcodeStoreService.getSrmBarcodeStorePage(srmBarcodeStoreDTO, productBarcodeSplitBarcodeInVO.getPage());
        if (srmBarcodeStorePage == null) {
            return null;
        }
        List<ProductBarcodeSplitBarcodeDTO> productBoxSpitBarcodeDTOS =
            srmBarcodeStorePage.getRecords().stream().map(s -> {
                ProductBarcodeSplitBarcodeDTO productBoxSpitBarcodeDTO = new ProductBarcodeSplitBarcodeDTO();
                productBoxSpitBarcodeDTO.setBarcodeNo(s.getBarcodeNo());
                productBoxSpitBarcodeDTO.setWmsBarcodeStoreId(s.getId());
                productBoxSpitBarcodeDTO.setCompanyCode(SecurityUtil.getCompanySite());
                productBoxSpitBarcodeDTO.setItemNumber(s.getItemNo());
                productBoxSpitBarcodeDTO.setItemName(s.getItemName());
                productBoxSpitBarcodeDTO.setItemSpece(s.getItemSpec());
                productBoxSpitBarcodeDTO.setItemDate(s.getLotDate());
                productBoxSpitBarcodeDTO.setItemUnit(s.getStockUnitNo());
                productBoxSpitBarcodeDTO.setItemUnitName(s.getStockUnitName());
                productBoxSpitBarcodeDTO.setItemQuantity(s.getCurStorNum());
                productBoxSpitBarcodeDTO.setWarehouse(s.getWarehouseNo());
                productBoxSpitBarcodeDTO.setWarehouseName(s.getWarehouseName());
                return productBoxSpitBarcodeDTO;
            }).collect(Collectors.toList());
        return Pagination.newInstance(productBoxSpitBarcodeDTOS, srmBarcodeStorePage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SpitWmsBarcodeOutVO splitWmsBarcode(SpitWmsBarcodeInVO spitWmsBarcodeInVO) {
        if (spitWmsBarcodeInVO.getSpitCount() > 100) {
            throw new CommonException("拆分数量不能大于100");
        }
        SpitWmsBarcodeOutVO spitWmsBarcodeOutVO = new SpitWmsBarcodeOutVO();
        SrmBarcodeStorePO srmBarcodeStorePO = srmBarcodeStoreService.getById(spitWmsBarcodeInVO.getWmsBarcodeStoreId());
        SrmBarcodeDetailPO srmBarcodeDetailPO =
            srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(srmBarcodeStorePO.getBarcodeNo());
        if (srmBarcodeDetailPO == null) {
            throw new CommonException("未找到条码明细信息，保存失败");
        }

        spitWmsBarcodeOutVO.setTotaliItemQuantity(srmBarcodeStorePO.getCurStorNum());
        ProductBarcodeSplitBarcodePO productBarcodeSplitBarcodePO =
            getByWmsBarcodeStoreId(spitWmsBarcodeInVO.getWmsBarcodeStoreId());
        if (productBarcodeSplitBarcodePO == null) {
            productBarcodeSplitBarcodePO = new ProductBarcodeSplitBarcodePO();
            productBarcodeSplitBarcodePO.setCreateBy(SecurityUtil.getUserId());
            productBarcodeSplitBarcodePO.setUpdateBy(SecurityUtil.getUserId());
        } else {
            productBarcodeSplitBarcodePO.setUpdateBy(SecurityUtil.getUserId());
            productBarcodeSplitBarcodePO.setUpdateTime(new Date());
        }

        productBarcodeSplitBarcodePO.setBarcodeNo(srmBarcodeStorePO.getBarcodeNo());
        productBarcodeSplitBarcodePO.setWmsBarcodeStoreId(srmBarcodeStorePO.getId());
        productBarcodeSplitBarcodePO.setCompanyCode(SecurityUtil.getCompanySite());
        productBarcodeSplitBarcodePO.setItemNumber(srmBarcodeStorePO.getItemNo());
        productBarcodeSplitBarcodePO.setItemName(srmBarcodeStorePO.getItemName());
        productBarcodeSplitBarcodePO.setItemSpece(srmBarcodeStorePO.getItemSpec());
        productBarcodeSplitBarcodePO.setItemDate(srmBarcodeStorePO.getLotDate());
        productBarcodeSplitBarcodePO.setItemUnit(srmBarcodeStorePO.getStockUnitNo());
        productBarcodeSplitBarcodePO.setItemUnitName(srmBarcodeStorePO.getStockUnitName());
        productBarcodeSplitBarcodePO.setItemQuantity(srmBarcodeStorePO.getCurStorNum());
        productBarcodeSplitBarcodePO.setWarehouse(srmBarcodeStorePO.getWarehouseNo());
        productBarcodeSplitBarcodePO.setWarehouseName(srmBarcodeStorePO.getWarehouseName());
        productBarcodeSplitBarcodePO.setSourceNo(srmBarcodeDetailPO.getSourceNo());

        saveOrUpdate(productBarcodeSplitBarcodePO);

        String barcodeNo = productBarcodeSplitBarcodePO.getBarcodeNo();
        // ORDER_SEQUENCE_PREFIX = "#MES#";

        List<ProductBarcodeSplitBarcodeDetailPO> productBarcodeSplitBarcodeDetailPOS = new ArrayList<>();
        for (int i = 0; i < spitWmsBarcodeInVO.getSpitCount(); i++) {
            Long sequence =
                OrderSequenceUtil.getAndIncrementOrderSequence(SequenceConst.ORDER_KEY + ":" + barcodeNo, SequenceConst.LOCK_KEY + ":" + barcodeNo);
            String barcode = String.format("%04d", sequence);
            ProductBarcodeSplitBarcodeDetailPO productBarcodeSplitBarcodeDetailPO =
                BeanUtil.copyProperties(spitWmsBarcodeInVO, ProductBarcodeSplitBarcodeDetailPO.class);
            productBarcodeSplitBarcodeDetailPO.setProductSpitBarcodeId(productBarcodeSplitBarcodePO.getId());
            productBarcodeSplitBarcodeDetailPO.setBarcodeNo(barcodeNo + SequenceConst.ORDER_SEQUENCE_PREFIX + barcode);
            productBarcodeSplitBarcodeDetailPO.setCreateBy(SecurityUtil.getUserId());
            productBarcodeSplitBarcodeDetailPO.setUpdateBy(SecurityUtil.getUserId());
            productBarcodeSplitBarcodeDetailPO.setCompanyCode(SecurityUtil.getCompanySite());
            productBarcodeSplitBarcodeDetailPO.setItemNumber(srmBarcodeStorePO.getItemNo());
            productBarcodeSplitBarcodeDetailPO.setItemName(srmBarcodeStorePO.getItemName());
            productBarcodeSplitBarcodeDetailPO.setItemSpece(srmBarcodeStorePO.getItemSpec());
            productBarcodeSplitBarcodeDetailPO.setItemDate(srmBarcodeStorePO.getLotDate());
            productBarcodeSplitBarcodeDetailPO.setItemUnit(srmBarcodeStorePO.getStockUnitNo());
            productBarcodeSplitBarcodeDetailPO.setItemUnitName(srmBarcodeStorePO.getStockUnitName());
            productBarcodeSplitBarcodeDetailPO.setItemQuantity(srmBarcodeStorePO.getCurStorNum());
            productBarcodeSplitBarcodeDetailPO.setWarehouse(srmBarcodeStorePO.getWarehouseNo());
            productBarcodeSplitBarcodeDetailPO.setWarehouseName(srmBarcodeStorePO.getWarehouseName());
            productBarcodeSplitBarcodeDetailPO.setSourceNo(srmBarcodeDetailPO.getSourceNo());
            productBarcodeSplitBarcodeDetailPOS.add(productBarcodeSplitBarcodeDetailPO);
        }
        productBarcodeSplitBarcodeDetailService.saveBatch(productBarcodeSplitBarcodeDetailPOS);
        List<ProductBarcodeSplitBarcodeDTO> productBarcodeSplitBarcodeDTOS =
            BeanUtil.copyToList(productBarcodeSplitBarcodeDetailPOS, ProductBarcodeSplitBarcodeDTO.class);
        spitWmsBarcodeOutVO.setProductBarcodeSplitBarcodeDTOList(productBarcodeSplitBarcodeDTOS);
        return spitWmsBarcodeOutVO;
    }

    @RedisLock
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveWmsBarcode(String redisLockKey,SaveWmsBarcodeInVO saveWmsBarcodeInVO) {
        // 校验数据是否正确
        SrmBarcodeStorePO srmBarcodeStorePO = srmBarcodeStoreService.getById(saveWmsBarcodeInVO.getWmsBarcodeStoreId());
        if (srmBarcodeStorePO == null) {
            throw new CommonException("未找到条码库存信息");
        }
        SrmBarcodeDetailPO srmBarcodeDetailPO =
            srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(srmBarcodeStorePO.getBarcodeNo());
        if (srmBarcodeDetailPO == null) {
            throw new CommonException("未找到条码明细信息，保存失败");
        }

        BigDecimal remainingQuantity = saveWmsBarcodeInVO.getRemainingQuantity();
        if (remainingQuantity == null || remainingQuantity.compareTo(BigDecimal.ZERO) < 0) {
            throw new CommonException("剩余数量不能小于0");
        }
        BigDecimal useQuantity = saveWmsBarcodeInVO.getWmsBarcodeInfos().stream()
            .map(SaveWmsBarcodeInVO.WmsBarcodeInfo::getItemQuantityOfPacks).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (srmBarcodeStorePO.getCurStorNum().compareTo(remainingQuantity.add(useQuantity)) != 0) {
            throw new CommonException("库存数量" + srmBarcodeStorePO.getCurStorNum() + ", 剩余数量 " + remainingQuantity
                + ", 已用数量 " + useQuantity + " 不相等");
        }

        // 懒得MAP处理了，慢点就慢点吧,单条处理
        List<ProductBarcodeSplitBarcodeDetailPO> productBarcodeSplitBarcodeDetailPOS = new ArrayList<>();
        List<SrmBarcodeStorePO> srmBarcodeStorePOS = new ArrayList<>();
        List<SrmBarcodeDetailPO> srmBarcodeDetailPOS = new ArrayList<>();
        saveWmsBarcodeInVO.getWmsBarcodeInfos().stream().forEach(wmsBarcodeInfo -> {
            ProductBarcodeSplitBarcodeDetailPO productBarcodeSplitBarcodeDetailPO =
                productBarcodeSplitBarcodeDetailService.getById(wmsBarcodeInfo.getId());
            productBarcodeSplitBarcodeDetailPO.setItemNumberOfPacks(wmsBarcodeInfo.getItemQuantityOfPacks() == null ? 0
                : Long.valueOf(String.valueOf(wmsBarcodeInfo.getItemQuantityOfPacks())));
            productBarcodeSplitBarcodeDetailPO.setEnableStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            productBarcodeSplitBarcodeDetailPOS.add(productBarcodeSplitBarcodeDetailPO);

            // 新增库存数量
            SrmBarcodeStorePO srmBarcodeStorePONew = new SrmBarcodeStorePO();
            BeanUtil.copyProperties(srmBarcodeStorePO, srmBarcodeStorePONew);
            srmBarcodeStorePONew.setId(null);
            srmBarcodeStorePONew.setBarcodeNo(productBarcodeSplitBarcodeDetailPO.getBarcodeNo());
            srmBarcodeStorePONew.setCurStorNum(wmsBarcodeInfo.getItemQuantityOfPacks());
            srmBarcodeStorePONew.setCreateDate(new Date());
            srmBarcodeStorePONew.setUpdateDate(new Date());
            srmBarcodeStorePONew.setCreateBy(SecurityUtil.getWorkcode());
            srmBarcodeStorePONew.setUpdateBy(SecurityUtil.getWorkcode());
            srmBarcodeStorePONew.setOwner(SecurityUtil.getWorkcode());
            srmBarcodeStorePONew.setSite(SecurityUtil.getCompanySite());
            srmBarcodeStorePOS.add(srmBarcodeStorePONew);

            SrmBarcodeDetailPO srmBarcodeDetailPONew = new SrmBarcodeDetailPO();
            BeanUtil.copyProperties(srmBarcodeDetailPO, srmBarcodeDetailPONew);
            srmBarcodeDetailPONew.setId(null);
            srmBarcodeDetailPONew.setBarcodeNo(productBarcodeSplitBarcodeDetailPO.getBarcodeNo());
            srmBarcodeDetailPONew.setBarcode(productBarcodeSplitBarcodeDetailPO.getBarcodeNo());
            srmBarcodeDetailPONew.setBarcodeTotal(String.valueOf(wmsBarcodeInfo.getItemQuantityOfPacks()));
            srmBarcodeDetailPONew.setCreateDate(new Date());
            srmBarcodeDetailPONew.setUpdateDate(new Date());
            srmBarcodeDetailPONew.setCreateBy(SecurityUtil.getWorkcode());
            srmBarcodeDetailPONew.setUpdateBy(SecurityUtil.getWorkcode());
            srmBarcodeDetailPONew.setOwner(SecurityUtil.getWorkcode());
            srmBarcodeDetailPONew.setSite(SecurityUtil.getCompanySite());
            srmBarcodeDetailPOS.add(srmBarcodeDetailPONew);
        });
        productBarcodeSplitBarcodeDetailService.updateBatchById(productBarcodeSplitBarcodeDetailPOS);

        // 更新库存数量 WMS
        srmBarcodeStoreService.saveBatch(srmBarcodeStorePOS);
        srmBarcodeStorePO.setUpdateDate(new Date());
        srmBarcodeStorePO.setUpdateBy(SecurityUtil.getWorkcode());
        srmBarcodeStorePO.setCurStorNum(remainingQuantity);
        srmBarcodeStoreService.updateById(srmBarcodeStorePO);

        srmBarcodeDetailService.saveBatch(srmBarcodeDetailPOS);
        srmBarcodeDetailPO.setUpdateDate(new Date());
        srmBarcodeDetailPO.setUpdateBy(SecurityUtil.getWorkcode());
        srmBarcodeDetailPO.setBarcodeTotal(String.valueOf(remainingQuantity));
        srmBarcodeDetailService.updateById(srmBarcodeDetailPO);

        return "SUCCESS";
    }

    @Override
    public Pagination<ProductBarcodeSplitBarcodeDTO>
        getPageList(ProductBarcodeSplitBarcodeInVO productBarcodeSplitBarcodeInVO) {
        IPage<ProductBarcodeSplitBarcodeDTO> page = baseMapper.getPageList(productBarcodeSplitBarcodeInVO.getPage(),
                SecurityUtil.getCompanySite() ,productBarcodeSplitBarcodeInVO);
        if (page.getRecords().isEmpty()) {
            return null;
        }

        // 补充字段
        List<Long> createBys = page.getRecords().stream().map(ProductBarcodeSplitBarcodeDTO::getCreateBy).collect(Collectors.toList());
        Map<Long, String> nameMap = a01Service.getUserInfoByIds(createBys).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));

        List<ProductBarcodeSplitBarcodeDTO> barcodeSplitBarcodeDTOS = page.getRecords().stream().map(productBarcodeSplitBarcodeDetailPO -> {
            ProductBarcodeSplitBarcodeDTO productBarcodeSplitBarcodeDTO = BeanUtil.copyProperties(productBarcodeSplitBarcodeDetailPO, ProductBarcodeSplitBarcodeDTO.class);
            String createByName = nameMap.get(productBarcodeSplitBarcodeDetailPO.getCreateBy());
            productBarcodeSplitBarcodeDTO.setCreateName(createByName);
            return productBarcodeSplitBarcodeDTO;
        }).collect(Collectors.toList());
        return Pagination.newInstance(barcodeSplitBarcodeDTOS, page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<PrintBarcodeOutVO> printBarcode(PrintBarcodeInVO printBarcodeInVO) {
        List<ProductBarcodeSplitBarcodeDetailPO> productBarcodeSplitBarcodeDetailPOS = productBarcodeSplitBarcodeDetailService.listByIds(printBarcodeInVO.getIds());
        if (productBarcodeSplitBarcodeDetailPOS.isEmpty()) {
            throw new CommonException("未找到条码拆分信息");
        }

        productBarcodeSplitBarcodeDetailPOS.stream().forEach(productBarcodeSplitBarcodeDetailPO -> {
            productBarcodeSplitBarcodeDetailPO.setPrintStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            productBarcodeSplitBarcodeDetailPO.setUpdateBy(SecurityUtil.getUserId());
            productBarcodeSplitBarcodeDetailPO.setUpdateTime(new Date());
        });
        productBarcodeSplitBarcodeDetailService.updateBatchById(productBarcodeSplitBarcodeDetailPOS);

        return productBarcodeSplitBarcodeDetailPOS.stream().map(p->{
            PrintBarcodeOutVO printBarcodeOutVO = new PrintBarcodeOutVO();
            printBarcodeOutVO.setBarcodeNo(p.getBarcodeNo());
            printBarcodeOutVO.setSourceNo(p.getSourceNo());
            return printBarcodeOutVO;
        }).collect(Collectors.toList());
    }

    private ProductBarcodeSplitBarcodePO getByWmsBarcodeStoreId(String wmsBarcodeStoreId) {
        LambdaQueryWrapper<ProductBarcodeSplitBarcodePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBarcodeSplitBarcodePO::getWmsBarcodeStoreId, wmsBarcodeStoreId)
            .eq(ProductBarcodeSplitBarcodePO::getCompanyCode, SecurityUtil.getCompanySite());
        return getOne(lambdaQueryWrapper);
    }

}
