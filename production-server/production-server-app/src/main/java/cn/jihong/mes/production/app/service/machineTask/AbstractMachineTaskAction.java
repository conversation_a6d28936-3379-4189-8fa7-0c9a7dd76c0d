package cn.jihong.mes.production.app.service.machineTask;

import cn.jihong.mes.production.api.service.IMachineTaskAction;
import cn.jihong.mes.production.app.factory.MachineTaskActionFactory;
import org.springframework.beans.factory.InitializingBean;

/**
 * <AUTHOR>
 * @date 2024-09-05 13:35
 */
public abstract class AbstractMachineTaskAction implements InitializingBean,IMachineTaskAction {

    @Override
    public void afterPropertiesSet(){
        Integer machineTaskCode = this.getMachineTaskCode();
        if(machineTaskCode != null) {
            MachineTaskActionFactory.registerStrategy(this.getMachineTaskCode(), this);
        }
    }


}
