package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductStoreBoxPO;
import cn.jihong.mes.production.api.service.IProductStoreBoxService;
import cn.jihong.mes.production.app.mapper.ProductStoreBoxMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.wms.api.model.dto.request.CheckInboundRequest;
import cn.jihong.wms.api.service.IFinishedInboundDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 入库箱码信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@DubboService
public class ProductStoreBoxServiceImpl extends JiHongServiceImpl<ProductStoreBoxMapper, ProductStoreBoxPO> implements IProductStoreBoxService {

    @DubboReference
    private IFinishedInboundDetailService finishedInboundDetailService;

    @Override
    public List<ProductStoreBoxPO> getByStoreId(Long storeId) {
        LambdaQueryWrapper<ProductStoreBoxPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductStoreBoxPO::getProductStoreId, storeId)
                .eq(ProductStoreBoxPO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ProductStoreBoxPO> getByBoxCode(String boxCode) {
        LambdaQueryWrapper<ProductStoreBoxPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductStoreBoxPO::getBoxCode, boxCode)
                .eq(ProductStoreBoxPO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ProductStoreBoxPO> getByBoxCodes(List<String> boxCodes) {
        LambdaQueryWrapper<ProductStoreBoxPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductStoreBoxPO::getBoxCode, boxCodes)
                .eq(ProductStoreBoxPO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public void verifyBoxCode(List<String> boxCodes) {
        List<ProductStoreBoxPO> byBoxCodes = getByBoxCodes(boxCodes);
        if (CollectionUtil.isNotEmpty(byBoxCodes)) {
            throw new CommonException("箱码已在入库申请表中存在[" +
                    byBoxCodes.stream().map(ProductStoreBoxPO::getBoxCode)
                            .collect(Collectors.joining(",")) + "]");
        }
    }

    @Override
    public void verifyBoxCodeFromWms(List<String> boxCodes) {
        CheckInboundRequest checkInboundRequest = new CheckInboundRequest();
        checkInboundRequest.setBoxCodeList(boxCodes);
        finishedInboundDetailService.checkInboundRequest(checkInboundRequest);
    }


}
