package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.mes.production.api.model.dto.ProductMachineTicketRelationDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineTicketRelationPO;
import cn.jihong.mes.production.api.service.IProductMachineTicketRelationService;
import cn.jihong.mes.production.app.mapper.ProductMachineTicketRelationMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 机台工单关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-05
 */
@DubboService
public class ProductMachineTicketRelationServiceImpl
    extends JiHongServiceImpl<ProductMachineTicketRelationMapper, ProductMachineTicketRelationPO>
    implements IProductMachineTicketRelationService {

    @Override
    public void createRelation(ProductMachineTicketRelationDTO productMachineTicketRelationDTO) {
        LambdaQueryWrapper<ProductMachineTicketRelationPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
            .eq(ProductMachineTicketRelationPO::getMachineName, productMachineTicketRelationDTO.getMachineName())
            .eq(ProductMachineTicketRelationPO::getStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()));
        ProductMachineTicketRelationPO productMachineTicketRelationPO = getOne(lambdaQueryWrapper);
        if (productMachineTicketRelationPO == null) {
            productMachineTicketRelationPO =
                BeanUtil.copyProperties(productMachineTicketRelationDTO, ProductMachineTicketRelationPO.class);
        }
        productMachineTicketRelationPO.setCurrentTicketId(productMachineTicketRelationDTO.getCurrentTicketId());
        productMachineTicketRelationPO.setStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        saveOrUpdate(productMachineTicketRelationPO);
    }

    @Override
    public void stopRelation(ProductMachineTicketRelationDTO productMachineTicketRelationDTO) {
        LambdaQueryWrapper<ProductMachineTicketRelationPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMachineTicketRelationPO::getMachineName,productMachineTicketRelationDTO.getMachineName())
                .eq(ProductMachineTicketRelationPO::getStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()));
        ProductMachineTicketRelationPO productMachineTicketRelationPO = getOne(lambdaQueryWrapper);
        if (productMachineTicketRelationPO == null) {
            return;
        }
        productMachineTicketRelationPO.setStatus(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        updateById(productMachineTicketRelationPO);

        // 新增一条未接结单的空数据
        ProductMachineTicketRelationPO productMachineTicketRelationPO1 = new ProductMachineTicketRelationPO();
        productMachineTicketRelationPO1.setCompanyCode(productMachineTicketRelationDTO.getCompanyCode());
        productMachineTicketRelationPO1.setMachineName(productMachineTicketRelationDTO.getMachineName());
        productMachineTicketRelationPO1.setLastTicketId(productMachineTicketRelationDTO.getCurrentTicketId());
        productMachineTicketRelationPO1.setStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        save(productMachineTicketRelationPO1);
    }

    @Override
    public ProductMachineTicketRelationPO getByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductMachineTicketRelationPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMachineTicketRelationPO::getCurrentTicketId,productTicketId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public ProductMachineTicketRelationPO getByMachineName(String machineName) {
        LambdaQueryWrapper<ProductMachineTicketRelationPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMachineTicketRelationPO::getMachineName,machineName)
                .eq(ProductMachineTicketRelationPO::getStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()));
        ProductMachineTicketRelationPO productMachineTicketRelationPO = getOne(lambdaQueryWrapper);
        if (productMachineTicketRelationPO == null) {
            return null;
        }
        return productMachineTicketRelationPO;
    }
}
