package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.vo.in.CreateFirstDetectionInVO;
import cn.jihong.mes.production.api.service.IProductFirstDetectionService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 首件记录信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productFirstDetection")
@ShenyuSpringMvcClient(path = "/productFirstDetection/**")
public class ProductFirstDetectionController {

    @Resource
    private IProductFirstDetectionService productFirstDetectionService;

    /**
     * 创建首件
     */
    @PostMapping("/createFirstDetection")
    public StandardResult createFirstDetection(@RequestBody @Valid CreateFirstDetectionInVO createFirstDetectionInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productFirstDetectionService.createFirstDetection(
            RedisCacheConstant.FIRST_CHECK + createFirstDetectionInVO.getId(), createFirstDetectionInVO));
    }



    /**
     * 获取进行中的首件任务
     * @param productTicketId
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2024/2/2 14:03
     */
    @GetMapping("/getProductFirstDetectionIng/{productTicketId}")
    public StandardResult getProductFirstDetectionIng(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productFirstDetectionService.getProductFirstDetectionIngByTicketId(productTicketId));
    }



}

