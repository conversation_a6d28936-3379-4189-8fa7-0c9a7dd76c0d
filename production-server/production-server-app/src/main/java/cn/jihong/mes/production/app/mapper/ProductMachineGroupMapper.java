package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductMachineGroupPO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机组信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface ProductMachineGroupMapper extends JiHongMapper<ProductMachineGroupPO> {

    List<ProductMachineGroupOutVO> getList(@Param("companyCode") String companyCode);
}
