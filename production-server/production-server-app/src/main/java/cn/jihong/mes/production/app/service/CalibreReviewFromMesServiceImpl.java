package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.QualityTraceabilityInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialUseDetailsInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDetailsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialUseDetailsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProduceDetailsOutVO;
import cn.jihong.mes.production.api.service.ICalibreReviewFromMesService;
import cn.jihong.mes.production.api.service.IProductStorageApplyService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.wms.api.model.po.SrmContainerTreePO;
import cn.jihong.wms.api.service.ISrmBarcodeChangeService;
import cn.jihong.wms.api.service.ISrmContainerTreeService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class CalibreReviewFromMesServiceImpl implements ICalibreReviewFromMesService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Autowired
    private ProductLastPalletServiceImpl productLastPalletService;
    @Autowired
    private ProductOutboundServiceImpl productOutboundService;
    @Autowired
    private IProductTicketService productTicketService;
    @Autowired
    private IProductStorageApplyService productStorageApplyService;

    @DubboReference
    private ISrmContainerTreeService srmContainerTreeService;
    @DubboReference
    private ISrmBarcodeChangeService srmBarcodeChangeService;

    @DubboReference
    private IA01Service ia01Service;

    @Override
    public List<GetProduceDetailsOutVO> getProduceDetails(QualityTraceabilityInVO boxDTO) {
        // 获得工程单号
        String planTicketNo = getPlanTikcetNo(boxDTO);
        return getProduceDetails(planTicketNo);
    }

    @Override
    public List<GetProduceDetailsOutVO> getProduceDetails(String planTicketNo) {
        List<GetProduceDetailsOutVO> getProduceDetailsOutVOS =
            productTicketService.getProduceDetailsCalibreReview(planTicketNo);
        List<Long> userIds = getProduceDetailsOutVOS.stream().flatMap(p -> p.getProduceLists().stream()).filter(pl -> !chinesePattern.matcher(pl.getTeamUsers()).find())
            .flatMap(pp -> Arrays.stream(pp.getTeamUsers().split(","))).map(Long::valueOf).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(userIds)) {
            Map<Long, String> userMap =
                    ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
            getProduceDetailsOutVOS.stream().flatMap(p -> p.getProduceLists().stream()).forEach(pp -> {
                if (chinesePattern.matcher(pp.getTeamUsers()).find()) {
                    return;
                }
                String userName = Arrays.stream(pp.getTeamUsers().split(",")).map(id -> userMap.get(Long.valueOf(id))).filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                pp.setTeamUsersName(userName);
            });
        } else {
            getProduceDetailsOutVOS.stream().flatMap(p -> p.getProduceLists().stream()).forEach(pp -> {
                pp.setTeamUsersName(pp.getTeamUsers());
            });
        }

        return getProduceDetailsOutVOS;
    }


    @Override
    public List<GetMaterialDetailsOutVO> getMaterialDetails(QualityTraceabilityInVO boxDTO) {
        // 获得工程单号
        String planTicketNo = getPlanTikcetNo(boxDTO);
        List<GetMaterialDetailsOutVO> getMaterialDetailsOutVOS = productTicketService.getMaterialDetailsCalibreReview(planTicketNo);
        return getMaterialDetailsOutVOS;
    }

    @Override
    public Pagination<GetMaterialUseDetailsOutVO> getMaterialUseDetails(GetMaterialUseDetailsInVO getMaterialUseDetailsInVO) {
        Page<GetMaterialUseDetailsOutVO> page =  productTicketService.getMaterialUseDetails(getMaterialUseDetailsInVO.getPage(),getMaterialUseDetailsInVO);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }


    private String getPlanTikcetNo(QualityTraceabilityInVO boxDTO) {
        if (StringUtils.isNotBlank(boxDTO.getPlanTicketNo())) {
            return boxDTO.getPlanTicketNo();
        } else if (StringUtils.isNotBlank(boxDTO.getPalletCode())) {
            String planTicketNo = productStorageApplyService.getPlanTicketNoByPalletCode(boxDTO.getPalletCode());
            if (StringUtils.isBlank(planTicketNo)) {
                throw new CommonException("查询不到对应栈板信息：" + boxDTO.getPalletCode());
            }
            return planTicketNo;
        } else if (StringUtils.isNotBlank(boxDTO.getBoxCode())) {
            SrmContainerTreePO srmContainerTreePO = srmContainerTreeService.getByBarcode(boxDTO.getBoxCode());
            if (srmContainerTreePO == null) {
                throw new CommonException("查询不到对应箱码信息：" + boxDTO.getBoxCode());
            }
            SrmContainerTreePO srmContainerTree = srmContainerTreeService.getById(srmContainerTreePO.getParentId());

            String planTicketNo = productStorageApplyService.getPlanTicketNoByPalletCode(srmContainerTree.getCode());
            if (StringUtils.isBlank(planTicketNo)) {
                throw new CommonException("查询不到对应箱码信息：" + boxDTO.getBoxCode());
            }
            return planTicketNo;
        } else {
            throw new CommonException("查询参数必填一个");
        }
    }

}
