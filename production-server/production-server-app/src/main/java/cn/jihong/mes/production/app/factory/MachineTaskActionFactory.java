package cn.jihong.mes.production.app.factory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.jihong.mes.production.api.service.IMachineTaskAction;

/**
 * <AUTHOR>
 * @date 2024-09-05 13:50
 */
public class MachineTaskActionFactory {

    public static Map<Integer, IMachineTaskAction> machineTaskActionContext = new ConcurrentHashMap<>();

    public static void registerStrategy(Integer code,IMachineTaskAction iMachineTaskAction){
        machineTaskActionContext.put(code,iMachineTaskAction);
    }

    public static IMachineTaskAction getImpl(Integer code){
        return machineTaskActionContext.get(code);
    }

}
