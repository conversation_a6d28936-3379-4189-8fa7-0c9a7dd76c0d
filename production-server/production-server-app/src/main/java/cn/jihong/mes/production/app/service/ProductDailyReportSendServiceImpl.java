package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.po.ProductDailyReportSendPO;
import cn.jihong.mes.production.app.mapper.ProductDailyReportSendMapper;
import cn.jihong.mes.production.api.service.IProductDailyReportSendService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;


/**
 * 生产日报发送 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@DubboService
public class ProductDailyReportSendServiceImpl extends JiHongServiceImpl<ProductDailyReportSendMapper, ProductDailyReportSendPO> implements IProductDailyReportSendService {


    @Override
    public List<ProductDailyReportSendPO> getList(List<Long> groupIds, String produceDate, Integer shit) {
        LambdaQueryWrapper<ProductDailyReportSendPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductDailyReportSendPO::getProduceDate,produceDate)
                .eq(ProductDailyReportSendPO::getShift,shit)
                .in(ProductDailyReportSendPO::getMachineGroupId,groupIds)
                .orderByDesc(ProductDailyReportSendPO::getCreateTime);
        return list(wrapper);
    }
}
