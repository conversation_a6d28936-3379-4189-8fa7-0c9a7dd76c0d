package cn.jihong.mes.production.app.config;

import org.apache.dubbo.config.ProviderConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

@Configuration
public class DubboConfiguration {

    @Value("${dubbo.preIP}")
    private String preIP;

    @Bean
    public ProviderConfig providerConfig() throws SocketException {
        ProviderConfig providerConfig = new ProviderConfig();
        providerConfig.setHost(getLocalIpAddress(preIP)); // 指定以""开头的IP地址
        return providerConfig;
    }

    private String getLocalIpAddress(String ipPrefix) throws SocketException {
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface networkInterface = interfaces.nextElement();
            if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp()) {
                continue;
            }
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress address = addresses.nextElement();
                if (!address.isLoopbackAddress() && !address.isLinkLocalAddress() && address.isSiteLocalAddress()) {
                    String ipAddress = address.getHostAddress();
                    if (ipAddress.startsWith(ipPrefix)) {
                        return ipAddress;
                    }
                }
            }
        }
        throw new RuntimeException("没有找到"+ipPrefix+"开头的ip，请检查本地ip，并在配置里配置dubbo.preIP");
    }
}
