package cn.jihong.mes.production.app.controller.oa;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.jihong.mes.production.api.model.vo.in.VerifyOutboundRequestInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyReportRequestInVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyOutboundRequestOutVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyReportRequestOutVO;
import cn.jihong.mes.production.api.service.IProductMachineTaskService;
import cn.jihong.mes.production.api.service.IProductOutboundService;
import cn.jihong.oa.approve.api.enums.BusinessType;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.mes.production.api.model.dto.OverProductionFlowDTO;
import cn.jihong.mes.production.api.model.enums.ProductionBusinessType;
import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.vo.in.SendOverProductionFlowInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyInboundRequestInVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO;
import cn.jihong.mes.production.api.service.IProductStoreService;
import cn.jihong.mes.production.app.service.ProductStoreServiceImpl;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OaOverProductionDTO;
import cn.jihong.workflow.common.biz.IWorkflowAttachmentService;
import cn.jihong.workflow.common.controller.BaseWorkflowActionController;
import cn.jihong.workflow.common.service.IWfService;

/**
 * 超产单OA流程
 * <AUTHOR>
 * @date 2024-09-11 11:35
 */
@RestController
@RequestMapping("/overProductionFlow")
@ShenyuSpringMvcClient(path = "/overProductionFlow/**")
public class OverProductionFlowController extends BaseWorkflowActionController<ProductStorePO, OverProductionFlowDTO, OaOverProductionDTO, ProductStoreServiceImpl> {


    @Resource
    private IProductStoreService iProductStoreService;

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;

    @Resource
    private IProductOutboundService iProductOutboundService;


    @Autowired
    public OverProductionFlowController(IWfService wfService, ProductStoreServiceImpl service, IWorkflowAttachmentService workflowAttachmentService) {
        super(wfService, service, workflowAttachmentService);
    }

    @Override
    public Long getWorkflowId() {
        return BusinessType.OVER_PRODUCTION.getWorkflowid();
    }


    /**
     * 发起OA超产单流程
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.workflow.common.model.request.SubmitWorkflowRequest>
     * <AUTHOR>
     * @date: 2024-09-12 18:15
     */
    @PostMapping("/sendOverProductionFlow")
    public StandardResult<Object>  sendOverProductionFlow(@RequestBody @Valid SendOverProductionFlowInVO inVO){
        return iProductStoreService.sendOverProductionFlow(inVO);
    }

    /**
     * 验证入库申请
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO>
     * <AUTHOR>
     * @date: 2025-05-15 16:45
     */
    @PostMapping("/verifyInboundRequest")
    public StandardResult<VerifyInboundRequestOutVO> verifyInboundRequest(@RequestBody @Valid VerifyInboundRequestInVO inVO){
        return StandardResult.ok(iProductStoreService.verifyInboundRequest(inVO));
    }

    /**
     * 验证报工申请
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO>
     * <AUTHOR>
     * @date: 2025-05-15 16:45
     */
    @PostMapping("/verifyReportRequest")
    public StandardResult<VerifyReportRequestOutVO> verifyReportRequest(@RequestBody @Valid VerifyReportRequestInVO inVO){
        return StandardResult.ok(iProductMachineTaskService.verifyReportRequest(inVO));
    }

    /**
     * 验证出站申请
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO>
     * <AUTHOR>
     * @date: 2025-05-15 16:45
     */
    @PostMapping("/verifyOutboundRequest")
    public StandardResult<VerifyOutboundRequestOutVO> verifyOutboundRequest(@RequestBody @Valid VerifyOutboundRequestInVO inVO){
        return StandardResult.ok(iProductOutboundService.verifyOutboundRequest(inVO));
    }
}
