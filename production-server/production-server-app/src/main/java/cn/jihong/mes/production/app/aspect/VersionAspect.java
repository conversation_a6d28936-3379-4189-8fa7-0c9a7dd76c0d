package cn.jihong.mes.production.app.aspect;

import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.dto.request.HeadersInfo;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductConfigPO;
import cn.jihong.mes.production.api.service.IProductConfigService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class VersionAspect {

    private final IProductConfigService productConfigService;

    private Map<String,String> minimumVersion = Maps.newHashMap();

    @Autowired
    public VersionAspect(IProductConfigService productConfigService) {
        this.productConfigService = productConfigService;
    }

    @Pointcut("execution(* cn.jihong..*.app.controller..*.*(..))")
    public void controller() {
    }

    @Before("controller()")
    public void before(JoinPoint point) throws Throwable{
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HeadersInfo headersInfo = (HeadersInfo) request.getAttribute("requestHeadersContext");
        Map<String, String> headers = headersInfo.getHeaders();
        String from = headers.get("from");
        String version = headers.get("version");

        // 版本号
        String minVersion = minimumVersion.get(SecurityUtil.getCompanySite());
        if (minVersion == null) {
            // 从数据库获取最低版本号
            minVersion = productConfigService.getMinimumVersion();
            minimumVersion.put(SecurityUtil.getCompanySite(), minVersion);
        }

        if (request.getRequestURI().contains("/getProductionTicketNo")
                || request.getRequestURI().contains("/getProductPlanList")
                || request.getRequestURI().contains("/createAndProcessor")
                || request.getRequestURI().contains("/saveMaterialInfo")
                || request.getRequestURI().contains("/changeMaterial")
                || request.getRequestURI().contains("/productMaterial/downMaterial")
                || request.getRequestURI().contains("/saveLastPalletInfo")
                || request.getRequestURI().contains("/downPallet")
                || request.getRequestURI().contains("/createFirstDetection")
                || request.getRequestURI().contains("/saveMachineTask")
                || request.getRequestURI().contains("/stopMachineTask")
                || request.getRequestURI().contains("/finishMachineTask")
        ) {
            if (version == null || version.isEmpty()) {
                throw new CommonException("版本号过低，请先升级app版本");
            }
        }

        if ("app".equals(from)) {
            // 比较版本号，确保用户使用的版本符合要求
            if (compareVersion(version, minVersion) < 0) {
                log.info("用户使用app版本：" + version + "，最低版本：" + minVersion);
                throw new CommonException("版本号过低，请先升级app版本");
            }
            log.info("用户使用app版本：" + version + "，最低版本：" + minVersion);
        } else {
//            log.info("不需要比较版本号");
        }
    }

    // 比较两个版本号的大小
    private int compareVersion(String version, String minimumVersion) {
        String[] v1 = version.split("\\.");
        String[] v2 = minimumVersion.split("\\.");
        int length = Math.max(v1.length, v2.length);

        for (int i = 0; i < length; i++) {
            int num1 = i < v1.length ? Integer.parseInt(v1[i]) : 0;
            int num2 = i < v2.length ? Integer.parseInt(v2[i]) : 0;
            if (num1 != num2) {
                return num1 - num2;
            }
        }
        return 0;
    }


    public void refreshMinimumVersion() {
        // 从数据库获取最低版本号
        List<ProductConfigPO> allMinimumVersion = productConfigService.getAllMinimumVersion();
        allMinimumVersion.stream().forEach(
            productConfigPO -> minimumVersion.replace(productConfigPO.getCompanyCode(), productConfigPO.getVersion()));
    }
}