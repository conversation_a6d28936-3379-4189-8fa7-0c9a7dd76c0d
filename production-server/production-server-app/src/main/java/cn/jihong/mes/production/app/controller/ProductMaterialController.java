package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductMaterialService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 上料信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productMaterial")
@ShenyuSpringMvcClient(path = "/productMaterial/**")
public class ProductMaterialController {

    @Resource
    private IProductMaterialService productMaterialService;

    /**
     * 根据物料编号查询物料信息
     */
    @PostMapping("/getMaterialByBarcodeNo")
    public StandardResult<MaterialInfoOutVO>
        getMaterialByBarcodeNo(@RequestBody @Valid MaterialByBarcodeNoInVO materialByBarcodeNoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.getMaterialByBarcodeNo(materialByBarcodeNoInVO));
    }

    /**
     * 查询物料类别列表
     */
    @GetMapping("/getMaterialTypeList/{productTicketId}")
    public StandardResult<List<MaterialTypeoutVO>> getMaterialTypeList(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.getMaterialTypeList(productTicketId));
    }

    /**
     * 根据物料id查询当前用料信息
     */
    @GetMapping("/getMaterialInfo/{id}")
    public StandardResult<MaterialInfoOutVO> getMaterialInfo(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMaterialService.getMaterialInfo(id));
    }


    /**
     * 根据物料类别查询当前用料信息 --- 废弃
     */
    @Deprecated
    @PostMapping("/getMaterialInfoByCode")
    public StandardResult<MaterialInfoOutVO>
        getMaterialInfoByCode(@RequestBody @Valid GetMaterialInfoByCodeInVO getMaterialInfoByCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.getMaterialInfoByCode(getMaterialInfoByCodeInVO));
    }

    /**
     * 查询用料信息
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/8 17:16
     */
    @GetMapping("/getListByMaterialCode")
    public StandardResult getListByMaterialCode(@RequestBody GetListByMaterialCodeInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productMaterialService.getListByMaterialCode(inVO));
    }

    /**
     * 查询使用中的物料列表
     */
    @PostMapping("/getMaterialList")
    public StandardResult<List<MaterialInfoOutVO>>
        getMaterialList(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.getMaterialListByMachineName(getByMachineNameInVO));
    }

    /**
     * 查询暂存的物料列表
     */
    @PostMapping("/getStagingMaterialList")
    public StandardResult<List<MaterialInfoOutVO>>
    getStagingMaterialList(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getStagingMaterialListByMachineName(getByMachineNameInVO));
    }


    /**
     * 查询使用中的物料列表 -- 需要增加物料类别
     */
    @GetMapping("/getMaterialList/{productTicketId}/{materialType}")
    public StandardResult<GetMaterialListByTypeOutVO> getMaterialListByType(@PathVariable Long productTicketId, @PathVariable String materialType) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMaterialService.getMaterialListByType(productTicketId,materialType));
    }

    /**
     * 查询上料记录
     */
    @PostMapping("/getMaterialRecords")
    public StandardResult<Pagination<MaterialRecordsOutVO>>
        getMaterialRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.getMaterialRecords(productTicketPageInVO));
    }

    /**
     * 查询下料记录
     */
    @PostMapping("/getMaterialDownRecords")
    public StandardResult<Pagination<MaterialRecordsOutVO>>
    getMaterialDownRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialDownRecords(productTicketPageInVO));
    }

    /**
     * 查询物料使用列表
     */
    @PostMapping("/getMaterialUseList")
    public StandardResult<Pagination<MaterialUseOutVO>>
    getMaterialUseList(@RequestBody @Valid GetMaterialListPageInVO getMaterialListPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialUseList(getMaterialListPageInVO));
    }

    /**
     * 查询物料使用详情
     */
    @GetMapping("/getMaterialUseDetial/{id}")
    public StandardResult<MaterialUseOutVO>
    getMaterialUseDetial(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialUseDetial(id));
    }

    /**
     * 查询物料使用详情列表
     */
    @PostMapping("/getMaterialUseDetialList")
    public StandardResult<Pagination<MaterialUseRecordOutVO>>
    getMaterialUseDetialList(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialUseDetialList(productInfoPageInVO));
    }

    /**
     * 获得机台累计用料列表
     */
    @PostMapping("/getMaterialTotalUse")
    public StandardResult<List<MaterialTotalUseOutVO>> getMaterialTotalUse(@RequestBody @Valid ProductTicketVO productTicketVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialTotalUse(productTicketVO));
    }

    /**
     * 获得机台日结信息 PC
     */
    @PostMapping("/getMaterialDailySettlementPC")
    public StandardResult<List<GetMaterialDailySettlementOutVO>> getMaterialDailySettlement(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialDailySettlement(getMaterialDailySettlementInVO));
    }

    /**
     * 获得机台日结信息 APP
     */
    @PostMapping("/getMaterialDailySettlement")
    public StandardResult<List<GetMaterialDailySettlementOutVO>> getMaterialDailySettlementApp(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.getMaterialDailySettlementApp(getMaterialDailySettlementInVO));
    }


    // ====================================================================================

    /**
     * 上料
     */
    @PostMapping("/saveMaterialInfo")
    public StandardResult saveMaterialInfo(@RequestBody @Valid SaveMaterialInfoInVO saveMaterialInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.saveMaterialInfo(saveMaterialInfoInVO.getUpMaterialInfo().getMaterialBarcodeNo(),saveMaterialInfoInVO));
    }


    /**
     * 下料
     */
    @PostMapping("/downMaterial")
    public StandardResult downMaterial(@RequestBody @Valid DownMaterialInVO downMaterialInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMaterialService.downMaterial(downMaterialInVO));
    }

    /**
     * 暂存物料
     */
    @PostMapping("/temporaryStorageMaterial")
    public StandardResult temporaryStorageMaterial(@RequestBody @Valid TemporaryStorageMaterialInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMaterialService.temporaryStorageMaterial(inVO));
    }

    /**
     * 换料
     */
    @PostMapping("/changeMaterial")
    public StandardResult changeMaterial(@RequestBody @Valid ChangeMaterialInVO changeMaterialInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.changeMaterial(changeMaterialInVO));
    }

    /**
     * 修改物料状态
     */
    @PostMapping("/updateMaterialStatus")
    public StandardResult updateMaterialStatus(@RequestBody @Valid UpdateMaterialStatusInVO updateMaterialStatusInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMaterialService.updateMaterialStatus(updateMaterialStatusInVO));
    }


    /**
     * 更新物料使用数量
     */
    @PostMapping("/updateMaterialCount")
    public StandardResult
    updateMaterialCount(@RequestBody @Valid UpdateMaterialInVO updateMaterialInVO) {
        productMaterialService.updateMaterialCount(updateMaterialInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


    /**
     * 物料日结
     */
    @PostMapping("/dailySettlementMaterial")
    public StandardResult<List<GetMaterialDailySettlementOutVO>>
        dailySettlementMaterial(@RequestBody @Valid DailySettlementMaterialInVO dailySettlementMaterialInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMaterialService.dailySettlementMaterial(dailySettlementMaterialInVO));
    }

    // ====================================================================================


    /**
     * 测试接口，推送erp
     */
    @GetMapping("/updateToErpTest/{productMaterialId}")
    public StandardResult updateToErpTest(@PathVariable Long productMaterialId) {
        productMaterialService.updateToErpTest(productMaterialId);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

//    /**
//     * 测试接口，推送wms
//     */
//    @PostMapping("/updateToWmsTest")
//    public StandardResult updateToWmsTest(@RequestBody UpdateToWmsTestInVO update) {
//        productMaterialService.updateToWmsTest(update.getBarcord(), update.getFlag(), update.getConsumptionQuantity());
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }

}
