package cn.jihong.mes.production.app.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProductionShiftSetDTO;
import cn.jihong.mes.api.model.po.ProductionShiftDetailPO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.api.service.IProductionShiftDetailService;
import cn.jihong.mes.api.service.IProductionShiftSetService;
import cn.jihong.mes.production.api.model.dto.CalMinutesDTO;
import cn.jihong.mes.production.api.model.dto.GetOeeDTO;
import cn.jihong.mes.production.api.model.enums.ShiftNoEnum;
import cn.jihong.mes.production.api.model.vo.in.GetOeeInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.util.ShiftTimeCalculatorUtil;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByCompanyCodeOutVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.jihong.logistics.api.model.vo.out.GetRunTimeByMonthOut;
import cn.jihong.logistics.api.service.ITileWireEquipmentStateService;
import cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO;
import cn.jihong.mes.production.api.model.vo.in.DeviceOEEReportShowInVO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-03-22 16:56
 */
@Slf4j
@DubboService
public class DeviceOEEReportServiceImpl implements IDeviceOEEReportService {

    @Resource
    private IProductTicketService iProductTicketService;

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;

    @Resource
    private IProductMachineTaskHistoryService iProductMachineTaskHistoryService;

    @DubboReference
    private ITileWireEquipmentStateService iTileWireEquipmentStateService;

    @Resource
    private IProductionShiftDetailService iProductionShiftDetailService;

    @Resource
    private IEquipmentDowntimeService iEquipmentDowntimeService;

    @DubboReference
    private IProductionShiftSetService iProductionShiftSetService;

    @DubboReference
    private IOocqlTService iOocqlTService;

    @Resource
    private IProductionMachineService iProductionMachineService;

    @Override
    public List<DeviceOEEReportShowOutVO> deviceOEEReportShow(DeviceOEEReportShowInVO inVO) {
        String yearMonth = DateUtil.format(inVO.getYearMonth(), DatePattern.NORM_MONTH_PATTERN);
        List<DeviceOEEReportDTO> deviceOEEReportShowOutVOList = iProductTicketService.deviceOEEReportShow(SecurityUtil.getCompanySite(), inVO.getMachineName(), inVO.getProcessName(),yearMonth );
        Map<String, Map<String, List<DeviceOEEReportDTO>>> map = deviceOEEReportShowOutVOList.stream().collect(Collectors.groupingBy(
                DeviceOEEReportDTO::getMachineName,
                Collectors.groupingBy(
                        DeviceOEEReportDTO::getProcessName,
                        Collectors.toList()
                )
        ));
        List<DeviceOEEReportShowOutVO> list = new ArrayList<>();
        map.forEach((machineName,processMap)->{
            processMap.forEach((processName,temList)->{
                DeviceOEEReportShowOutVO outVO = new DeviceOEEReportShowOutVO();
                outVO.setMachineName(machineName);
                outVO.setProcessName(processName);
                List<DeviceOEEReportShowOutVO.DayRate> dayRateList = new ArrayList<>();

                if("瓦楞纸板生产线:00001".equals(machineName)){
                    List<GetRunTimeByMonthOut> runTimeByMonth = iTileWireEquipmentStateService.getRunTimeByMonth(machineName, yearMonth);
                    Map<String, BigDecimal> tileWireProduceDataMap = runTimeByMonth.stream().collect(Collectors.toMap(GetRunTimeByMonthOut::getProduceDate, GetRunTimeByMonthOut::getTotalHours));
                    temList.forEach(t->{
                        if(tileWireProduceDataMap.containsKey(t.getProduceDate())) {
                            log.info("瓦线运行情况:{}", JSON.toJSONString(runTimeByMonth));
                            BigDecimal totalHours = tileWireProduceDataMap.get(t.getProduceDate());
                            DeviceOEEReportShowOutVO.DayRate dayRate = new DeviceOEEReportShowOutVO.DayRate();
                            dayRate.setProduceDate(t.getProduceDate());
                            dayRate.setTimeActuationRate(totalHours.divide(BigDecimal.valueOf(24),4,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                            dayRate.setGoodProductRate(Objects.nonNull(t.getGoodProductRate()) ? t.getGoodProductRate().setScale(2,RoundingMode.HALF_UP) : null);
                            if(t.getStandardProductionCapacity() != null && BigDecimal.ZERO.compareTo(t.getStandardProductionCapacity().multiply(totalHours)) != 0) {
                                dayRate.setPerformanceRate(t.getTotalReportedQuantity().divide(t.getStandardProductionCapacity().multiply(totalHours),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                                if(dayRate.getTimeActuationRate()!=null && dayRate.getGoodProductRate()!=null && dayRate.getPerformanceRate()!=null) {
                                    dayRate.setOeeRate(dayRate.getTimeActuationRate().multiply(dayRate.getGoodProductRate()).multiply(dayRate.getPerformanceRate()).divide(BigDecimal.valueOf(10000),2,RoundingMode.HALF_UP));
                                }
                            }
                            dayRateList.add(dayRate);
                        }
                    });
                } else {
                    temList.forEach(t -> {
                        DeviceOEEReportShowOutVO.DayRate dayRate = new DeviceOEEReportShowOutVO.DayRate();
                        dayRate.setProduceDate(t.getProduceDate());
                        dayRate.setTimeActuationRate(Objects.nonNull(t.getTimeActuationRate()) ? t.getTimeActuationRate().setScale(2,RoundingMode.HALF_UP) : null);
                        dayRate.setPerformanceRate(Objects.nonNull(t.getPerformanceRate()) ? t.getPerformanceRate().setScale(2,RoundingMode.HALF_UP) : null);
                        dayRate.setGoodProductRate(Objects.nonNull(t.getGoodProductRate()) ? t.getGoodProductRate().setScale(2,RoundingMode.HALF_UP) : null);
                        dayRate.setOeeRate(Objects.nonNull(t.getOeeRate()) ? t.getOeeRate().setScale(2,RoundingMode.HALF_UP) : null);
                        dayRateList.add(dayRate);
                    });
                }
                outVO.setDayRateList(dayRateList);
                list.add(outVO);

            });
        });
     return list;
    }



    @Override
    public GetOeeOutVO getOee(GetOeeInVO inVO) {
        List<String> machineNameList = new ArrayList<>();
        if(StringUtil.isNotBlank(inVO.getProcessCode())){
            List<ProductionMachineOutVO> productionMachineOutVOList = iProductionMachineService.getProductionMachineByProcessCode(inVO.getProcessCode());
            if(CollectionUtil.isEmpty(productionMachineOutVOList)){
                throw new CommonException("筛选的工序尚未在机台配置");
            }
            machineNameList = new ArrayList<>(productionMachineOutVOList.stream()
                    .map(ProductionMachineOutVO::getErpMachineName)
                    .collect(Collectors.toCollection(HashSet::new)));
        }

        if(StringUtil.isNotBlank(inVO.getMachineName())){
            if(CollectionUtil.isNotEmpty(machineNameList) && !machineNameList.contains(inVO.getMachineName())){
                throw new CommonException("筛选工序下的机台,不包含当前筛选的机台");
            }
            machineNameList.add(inVO.getMachineName());
        }

        String companyCode = SecurityUtil.getCompanySite();
        inVO.setCompanyCode(companyCode);
        BigDecimal calculateDays = calculateDaysBetween(inVO.getStartDate(), inVO.getEndDate());
        log.info("可利用生产天数：{}",calculateDays);
        List<ProductionShiftSetVO> shiftSetList = iProductionShiftSetService.getList(new ProductionShiftSetDTO());

        // 每天可利用分钟数
        BigDecimal everyDayCanUseMinutes = BigDecimal.ZERO;

        ProductionShiftSetVO productionShiftSetVO = shiftSetList.stream().filter(t -> t.getCompanyCode().equals(companyCode)).findFirst().orElseThrow(() -> new CommonException("尚未设置班次时间"));

        List<ProductionShiftDetailPO> shiftDetailPOList = iProductionShiftDetailService.getShiftsByCompanyCode(companyCode)
                .stream().filter(t->t.getMainId().equals(productionShiftSetVO.getId())).collect(Collectors.toList());
        if (inVO.getShift() != null) {
            shiftDetailPOList = shiftDetailPOList.stream().filter(t -> t.getSerialNo().equals(inVO.getShift())).collect(Collectors.toList());
        }

        log.info("每天的可利用生产时间:{}",JSON.toJSONString(shiftDetailPOList));
        for (ProductionShiftDetailPO productionShiftDetailPO : shiftDetailPOList) {
            String[] split = productionShiftDetailPO.getTimeSlot().split(",");
            for (String timeSlot : split) {
                if (StringUtil.isNotBlank(timeSlot)) {
                    BigDecimal minutesBetween = calculateMinutesBetween(timeSlot);
                    everyDayCanUseMinutes = everyDayCanUseMinutes.add(minutesBetween);
                }
            }
        }

        log.info("每天可利用的分钟数:{}",everyDayCanUseMinutes);

        ProductionShiftDetailPO dayProductionShiftDetailPO = shiftDetailPOList.stream().filter(t -> t.getSerialNo().equals(ShiftNoEnum.DAY_SHIFT.getCode())).findFirst().orElse(null);
        if(dayProductionShiftDetailPO == null){
            dayProductionShiftDetailPO = shiftDetailPOList.get(0);
        }
        String startTime = dayProductionShiftDetailPO.getTimeSlot().split("-")[0] + ":00";



        ProductionShiftDetailPO graveyardProductionShiftDetailPO = shiftDetailPOList.stream().filter(t -> t.getSerialNo().equals(ShiftNoEnum.GRAVEYARD_SHIFT.getCode())).findFirst().orElse(null);
        if(graveyardProductionShiftDetailPO == null){
            graveyardProductionShiftDetailPO = shiftDetailPOList.get(0);
        }
        LocalDate date = LocalDate.parse(inVO.getEndDate());
        LocalDate nextDay = date.plusDays(1);
        String nextDayStr = nextDay.format(DateTimeFormatter.ISO_LOCAL_DATE);
        // 取班次的最晚时间
        String[] split1 = graveyardProductionShiftDetailPO.getTimeSlot().split("-");

        String endTime = split1[split1.length - 1] + ":00";


        List<String> timeSlotList = shiftDetailPOList.stream().map(ProductionShiftDetailPO::getTimeSlot).collect(Collectors.toList());

        // 可利用生产时间
        BigDecimal totalCanUseMinutes = everyDayCanUseMinutes.multiply(calculateDays);

        GetOeeDTO dto = BeanUtil.copyProperties(inVO,GetOeeDTO.class);
        dto.setMachineNameList(machineNameList);

        // 取班次的最早时间
        dto.setStartTime(inVO.getStartDate() + ' ' + startTime);
        dto.setEndTime(nextDayStr + ' ' + endTime);
        log.info("oee查询时间范围：{} {}",dto.getStartTime(),dto.getEndTime());

        List<GetOeeBaseDataOutVO> oeeBaseDataList = iProductTicketService.getOeeBaseData(dto);
        GetOeeOutVO oeeOutVO = new GetOeeOutVO();
        if(CollectionUtil.isEmpty(oeeBaseDataList)){
            oeeOutVO.setTotalReportedRate(BigDecimal.ZERO);
            oeeOutVO.setTotalReportedQuantity(BigDecimal.ZERO);
            oeeOutVO.setTotalDefectiveProductQuantity(BigDecimal.ZERO);
            oeeOutVO.setTotalDurationMinutes(BigDecimal.ZERO);
            oeeOutVO.setActualSpeed(BigDecimal.ZERO);
            oeeOutVO.setGoodProductRate(BigDecimal.ZERO);
            oeeOutVO.setStandardProductionCapacity(BigDecimal.ZERO);
        }else {

            // 在生产状态下 夹杂早班晚班中间的休息分钟数
            BigDecimal totalRestDurationMinutes  = ShiftTimeCalculatorUtil.calculateTotalMinutes(BeanUtil.copyToList(oeeBaseDataList, CalMinutesDTO.class), timeSlotList);
            BigDecimal durationMinutes = oeeBaseDataList.stream().map(GetOeeBaseDataOutVO::getDurationMinutes).reduce(BigDecimal.ZERO, BigDecimal::add);

            oeeOutVO.setTotalRestDurationMinutes(totalRestDurationMinutes);
            // 实际生产分钟数 = 生产时间分钟数 - 夹杂休息时间分钟数
            oeeOutVO.setTotalDurationMinutes(durationMinutes.subtract(totalRestDurationMinutes));

            oeeOutVO.setTotalReportedQuantity(oeeBaseDataList.stream().map(GetOeeBaseDataOutVO::getReportedQuantity).reduce(BigDecimal.ZERO,BigDecimal::add));
            oeeOutVO.setTotalDefectiveProductQuantity(oeeBaseDataList.stream().map(GetOeeBaseDataOutVO::getDefectiveProduct).reduce(BigDecimal.ZERO,BigDecimal::add));
            if(oeeOutVO.getTotalDurationMinutes().compareTo(BigDecimal.ZERO) > 0) {
                oeeOutVO.setActualSpeed(oeeOutVO.getTotalReportedQuantity().divide(oeeOutVO.getTotalDurationMinutes(),4,RoundingMode.HALF_UP));
            }else {
                oeeOutVO.setActualSpeed(BigDecimal.ZERO);
            }
            BigDecimal totalQuantity = oeeOutVO.getTotalReportedQuantity().add(oeeOutVO.getTotalDefectiveProductQuantity());
            if(totalQuantity.compareTo(BigDecimal.ZERO) > 0){
                oeeOutVO.setGoodProductRate(oeeOutVO.getTotalReportedQuantity().divide(totalQuantity,4,RoundingMode.HALF_UP));
            }else {
                oeeOutVO.setGoodProductRate(BigDecimal.ZERO);
            }
            oeeOutVO.setStandardProductionCapacity(oeeBaseDataList.stream()
                    .map(GetOeeBaseDataOutVO::getStandardProductionCapacity)
                    .filter(Objects::nonNull)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO));
        }

        oeeOutVO.setTotalCanUseMinutes(totalCanUseMinutes);
        // 进入数
        oeeOutVO.setEntryQuantity(oeeOutVO.getTotalReportedQuantity().add(oeeOutVO.getTotalDefectiveProductQuantity()));
        if(oeeOutVO.getEntryQuantity()!=null && oeeOutVO.getEntryQuantity().compareTo(BigDecimal.ZERO)>0) {
            oeeOutVO.setTotalReportedRate(oeeOutVO.getTotalReportedQuantity().divide(oeeOutVO.getEntryQuantity(), 4, RoundingMode.HALF_UP));
            oeeOutVO.setTotalDefectiveProductRate(oeeOutVO.getTotalDefectiveProductQuantity().divide(oeeOutVO.getEntryQuantity(), 4, RoundingMode.HALF_UP));
        }else {
            oeeOutVO.setTotalReportedRate(BigDecimal.ZERO);
            oeeOutVO.setTotalDefectiveProductRate(BigDecimal.ZERO);
        }
        // 时间利用率 A0 = 实际生产时间 / 可利用时间
        oeeOutVO.setTimeUtilizationRate(oeeOutVO.getTotalDurationMinutes().divide(totalCanUseMinutes,4,RoundingMode.HALF_UP));


        // 停机列表
        List<GetOeeBaseEquipmentDowntime> baseDowntimeList = iEquipmentDowntimeService.getOeeBaseEquipmentDowntimeList(dto).stream().filter(t -> StringUtil.isNotBlank(t.getLossType())).collect(Collectors.toList());
        Map<String, List<GetOeeBaseEquipmentDowntime>> lossTypeMap = baseDowntimeList.stream()
                .collect(Collectors.groupingBy(GetOeeBaseEquipmentDowntime::getLossType));
        List<GetOeeEquipmentDowntime> downtimeList = new ArrayList<>();
        lossTypeMap.forEach((lossType,temList)->{
            // 在停机状态下 夹杂早班晚班中间的休息分钟数
            BigDecimal restDownTimeMinters  = ShiftTimeCalculatorUtil.calculateTotalMinutes(BeanUtil.copyToList(temList, CalMinutesDTO.class), timeSlotList);
            // 停机时间分钟数
            BigDecimal downTimeMinters = temList.stream().map(GetOeeBaseEquipmentDowntime::getDownTimeMinters).reduce(BigDecimal.ZERO, BigDecimal::add);

            GetOeeEquipmentDowntime oeeEquipmentDowntime = new GetOeeEquipmentDowntime();
            oeeEquipmentDowntime.setLossType(lossType);
            oeeEquipmentDowntime.setDownTimeNum(temList.size());
            oeeEquipmentDowntime.setRestDownTimeMinters(restDownTimeMinters);

            // 实际停机分钟数 = 停机时间分钟数 - 夹杂休息时间分钟数
            oeeEquipmentDowntime.setDownTimeMinters(downTimeMinters.subtract(restDownTimeMinters));
            downtimeList.add(oeeEquipmentDowntime);
        });

   //     List<GetOeeEquipmentDowntime> downtimeList = iEquipmentDowntimeService.getOeeEquipmentDowntimeList(dto).stream().filter(t-> StringUtil.isNotBlank(t.getLossType())).collect(Collectors.toList());
        oeeOutVO.setTotalRestDownTimeMinters(downtimeList.stream().map(GetOeeEquipmentDowntime::getRestDownTimeMinters).reduce(BigDecimal.ZERO,BigDecimal::add));
        oeeOutVO.setTotalDownTimeMinters(downtimeList.stream().map(GetOeeEquipmentDowntime::getDownTimeMinters).reduce(BigDecimal.ZERO,BigDecimal::add));
        oeeOutVO.setLossList(downtimeList);
        BigDecimal finalTotalCanUseMinutes = totalCanUseMinutes;
        downtimeList.stream().peek(t->{
            t.setDownTimeRate(t.getDownTimeMinters().divide(finalTotalCanUseMinutes,4, RoundingMode.HALF_UP));
        }).collect(Collectors.toList());


        List<String> strategicLossA2List = Arrays.asList("无计划停机", "检验停机");
        BigDecimal timeUtilizationMinutesA2 = downtimeList.stream().filter(t -> strategicLossA2List.contains(t.getLossType())).map(GetOeeEquipmentDowntime::getDownTimeMinters).reduce(BigDecimal.ZERO,BigDecimal::add);

        // 时间利用率 A2 = 实际生产时间 / (可利用时间 - 无计划停机时间 - 检验停机时间)
        BigDecimal timeUtilizationRateA2 = oeeOutVO.getTotalDurationMinutes().divide(totalCanUseMinutes.subtract(timeUtilizationMinutesA2),4,RoundingMode.HALF_UP);

        // 战略损失 = （无计划停机+计划异常停机时间）/ 可利用生产时间
        List<String> strategicLossList = Arrays.asList("无计划停机", "计划异常停机");
        BigDecimal strategicLossRate = downtimeList.stream().filter(t -> strategicLossList.contains(t.getLossType())).map(GetOeeEquipmentDowntime::getDownTimeRate).reduce(BigDecimal.ZERO,BigDecimal::add);
        oeeOutVO.setStrategicLossRate(strategicLossRate);

        // 计划损失 = （交接班停机+设备维护停机+人员休息停机+换版/换单停机）/ 可利用生产时间
        List<String> planLossList = Arrays.asList("交接班停机", "设备维护停机","人员休息停机","换版/换单停机");
        BigDecimal planLossRate = downtimeList.stream().filter(t -> planLossList.contains(t.getLossType())).map(GetOeeEquipmentDowntime::getDownTimeRate).reduce(BigDecimal.ZERO,BigDecimal::add);
        oeeOutVO.setPlanLossRate(planLossRate);

        // 操作损失 = （故障停机+短停机+能源异常停机+原辅料不足+检验停机）/ 可利用生产时间
        List<String> operatingLossList = Arrays.asList("故障停机", "短停机","能源异常停机","原辅料不足","检验停机");
        BigDecimal operatingLossRate = downtimeList.stream().filter(t -> operatingLossList.contains(t.getLossType())).map(GetOeeEquipmentDowntime::getDownTimeRate).reduce(BigDecimal.ZERO,BigDecimal::add);
        oeeOutVO.setOperatingLossRate(operatingLossRate);

        // P-性能开动率 = 实际速度 / 理论速度 (分钟级计算)
        if(oeeOutVO.getStandardProductionCapacity()!=null && oeeOutVO.getStandardProductionCapacity().compareTo(BigDecimal.ZERO)>0) {
            oeeOutVO.setPerformanceUtilizationRate(oeeOutVO.getActualSpeed().divide(oeeOutVO.getStandardProductionCapacity().divide(BigDecimal.valueOf(60), 4, RoundingMode.HALF_UP), 4, RoundingMode.HALF_UP));
        }else {
            oeeOutVO.setPerformanceUtilizationRate(BigDecimal.ZERO);
        }

        // OEE-设备综合效率 = A2 × P × Q (时间利用率A2 * P-性能开动率 * Q-合格品率)
        oeeOutVO.setOeeRate(timeUtilizationRateA2.multiply(oeeOutVO.getPerformanceUtilizationRate()).multiply(oeeOutVO.getGoodProductRate()).setScale(4,RoundingMode.HALF_UP));

        // TEE-全面设备效率 = A × P × Q (时间利用率A * P-性能开动率 * Q-合格品率)
        oeeOutVO.setTeeRate(oeeOutVO.getTimeUtilizationRate().multiply(oeeOutVO.getPerformanceUtilizationRate()).multiply(oeeOutVO.getGoodProductRate()).setScale(4,RoundingMode.HALF_UP));

        return oeeOutVO;
    }


    public static BigDecimal calculateDaysBetween(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        return BigDecimal.valueOf(Math.abs(ChronoUnit.DAYS.between(start, end))).add(BigDecimal.ONE); // 取绝对值
    }

    /**
     * 计算时间范围字符串的分钟数差（例如 "19:00-23:30" -> 270分钟）
     *
     * @param timeRange 时间范围字符串，格式为 "HH:mm-HH:mm"
     * @return 分钟数差（如果结束时间小于开始时间，则视为跨天）
     */
    public static BigDecimal calculateMinutesBetween(String timeRange) {
        String[] times = timeRange.split("-");
        if (times.length != 2) {
            throw new IllegalArgumentException("时间格式无效，应为 HH:mm-HH:mm");
        }

        LocalTime startTime = LocalTime.parse(times[0], DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime endTime = LocalTime.parse(times[1], DateTimeFormatter.ofPattern("HH:mm"));

        // 处理跨天情况（如 00:30-06:00）
        if (endTime.isBefore(startTime)) {
            return BigDecimal.valueOf(ChronoUnit.MINUTES.between(startTime, LocalTime.MAX) + 1  // 当天剩余分钟
                    + ChronoUnit.MINUTES.between(LocalTime.MIN, endTime));        // 次日分钟
        } else {
            return BigDecimal.valueOf(ChronoUnit.MINUTES.between(startTime, endTime));
        }
    }

    @Override
    public List<GetOocqlTByCompanyCodeOutVO> getAllProcess() {
        return iOocqlTService.getOocqlTByCompanyCode(SecurityUtil.getCompanySite());
    }
}
