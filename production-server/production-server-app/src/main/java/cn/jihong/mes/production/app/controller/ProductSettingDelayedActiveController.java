package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.ProductSettingDelayedActiveDTO;
import cn.jihong.mes.production.api.service.IProductSettingDelayedActiveService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 工单配置箱码延迟激活 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@RestController
@RequestMapping("/productSettingDelayedActive")
@ShenyuSpringMvcClient(path = "/productSettingDelayedActive/**")
public class ProductSettingDelayedActiveController {

    @Resource
    private IProductSettingDelayedActiveService productSettingDelayedActiveService;

    /**
     * 获取工单配置箱码延迟激活列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/getPage")
    public StandardResult<Pagination<ProductSettingDelayedActiveDTO>> getPage(@RequestBody @Valid PageRequest pageRequest) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productSettingDelayedActiveService.getPage(pageRequest));
    }


    /**
     * 新增工单配置箱码延迟激活
     * @param productSettingDelayedActiveDTO
     * @return
     */
    @PostMapping("/add")
    public StandardResult add(@RequestBody @Valid ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productSettingDelayedActiveService.add(productSettingDelayedActiveDTO));
    }

    /**
     * 修改工单配置箱码延迟激活
     * @param productSettingDelayedActiveDTO
     * @return
     */
    @PostMapping("/update")
    public StandardResult update(@RequestBody @Valid ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productSettingDelayedActiveService.update(productSettingDelayedActiveDTO));
    }

    /**
     * 删除工单配置箱码延迟激活
     * @param id
     * @return
     */
    @PostMapping("/delete/{id}")
    public StandardResult delete(@PathVariable("id") Long id) {
        productSettingDelayedActiveService.removeById(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


}

