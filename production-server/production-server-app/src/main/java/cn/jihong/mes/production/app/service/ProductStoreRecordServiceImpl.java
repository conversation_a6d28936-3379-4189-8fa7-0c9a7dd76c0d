package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductStoreRecordDTO;
import cn.jihong.mes.production.api.model.enums.StoreOperationEnum;
import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.po.ProductStoreRecordPO;
import cn.jihong.mes.production.api.service.IProductStoreRecordService;
import cn.jihong.mes.production.app.mapper.ProductStoreRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 入库表操作记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@DubboService
public class ProductStoreRecordServiceImpl extends JiHongServiceImpl<ProductStoreRecordMapper, ProductStoreRecordPO> implements IProductStoreRecordService {

    @Override
    public void createRecord(ProductStoreRecordDTO productStoreRecordDTO) {
        ProductStoreRecordPO productStoreRecordPO = BeanUtil.copyProperties(productStoreRecordDTO, ProductStoreRecordPO.class);
        productStoreRecordPO.setId(null);
        productStoreRecordPO.setCompanyCode(SecurityUtil.getCompanySite());
        productStoreRecordPO.setCreateBy(SecurityUtil.getUserId());
        productStoreRecordPO.setUpdateBy(SecurityUtil.getUserId());
        save(productStoreRecordPO);
    }

    @Override
    public void createRecords(List<ProductStoreRecordDTO> productStoreRecordDTOs) {
        List<ProductStoreRecordPO> productStoreRecordPOS = productStoreRecordDTOs.stream().map(productStoreRecordDTO -> {
            ProductStoreRecordPO productStoreRecordPO = BeanUtil.copyProperties(productStoreRecordDTO, ProductStoreRecordPO.class);
            productStoreRecordPO.setId(null);
            productStoreRecordPO.setCompanyCode(SecurityUtil.getCompanySite());
            productStoreRecordPO.setCreateBy(SecurityUtil.getUserId());
            productStoreRecordPO.setUpdateBy(SecurityUtil.getUserId());
            return productStoreRecordPO;
        }).collect(Collectors.toList());
        saveBatch(productStoreRecordPOS);
    }

    @Override
    public void deleteApplyNo(List<ProductStorePO> productStorePOS) {
        List<ProductStoreRecordPO> productStoreRecordPOS = productStorePOS.stream().map(productStorePO -> {
            ProductStoreRecordPO productStoreRecordPO = new ProductStoreRecordPO();
            productStoreRecordPO.setId(null);
            productStoreRecordPO.setProductStoreId(productStorePO.getId());
            productStoreRecordPO.setPalletCode(productStorePO.getPalletCode());
            productStoreRecordPO.setOperation(StoreOperationEnum.DELETE.getCode());
            productStoreRecordPO.setCompanyCode(SecurityUtil.getCompanySite());
            productStoreRecordPO.setOperatorName(SecurityUtil.getUserName());
            productStoreRecordPO.setCreateBy(SecurityUtil.getUserId());
            productStoreRecordPO.setUpdateBy(SecurityUtil.getUserId());
            return productStoreRecordPO;
        }).collect(Collectors.toList());
        saveBatch(productStoreRecordPOS);
    }
}
