package cn.jihong.mes.production.app.controller.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.logistics.api.model.vo.in.TileWireRealTimeDataPageInVO;

import cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO;
import cn.jihong.logistics.api.service.ITileWireRealTimeDataService;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetLatestTileWireDataInVO;
import cn.jihong.mes.production.api.service.logistics.ITileWireDataService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 瓦线实时信息
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@RestController
@RequestMapping("/tileWireRealTimeData")
@ShenyuSpringMvcClient(path = "/tileWireRealTimeData/**")
public class TileWireRealTimeDataController {

    @Resource
    private ITileWireDataService iTileWireDataService;

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.TileWireRealTimeDataOutVO>
     */
    @GetMapping("/getSingleTileWireRealTimeDataById/{id}")
    public StandardResult<TileWireRealTimeDataOutVO> getSingleTileWireRealTimeDataById(@PathVariable("id") Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,iTileWireDataService.getSingleTileWireRealTimeDataById(id));
    }

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.TileWireRealTimeDataOutVO>>
     */
    @PostMapping("/getTileWireRealTimeDataPage")
    public StandardResult<Pagination<TileWireRealTimeDataOutVO>> getTileWireRealTimeDataPage(@RequestBody @Valid TileWireRealTimeDataPageInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iTileWireDataService.getTileWireRealTimeDataPage(inVO));
    }

    /** 
     * 获取瓦线最新生产数据
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO> 
     * <AUTHOR>
     * @date: 2025-03-14 11:25
     */
    @PostMapping("/getLatestTileWireData")
    public StandardResult<TileWireRealTimeDataOutVO> getLatestTileWireData(@RequestBody @Valid GetLatestTileWireDataInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iTileWireDataService.getLatestTileWireData(inVO));
    }


}

