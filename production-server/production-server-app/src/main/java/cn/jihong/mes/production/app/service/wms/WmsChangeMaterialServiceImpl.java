//package cn.jihong.mes.production.app.service.wms;
//
//import cn.hutool.core.date.DatePattern;
//import cn.hutool.core.lang.Snowflake;
//import cn.jihong.common.enums.BooleanEnum;
//import cn.jihong.common.exception.CommonException;
//import cn.jihong.common.util.SecurityUtil;
//import cn.jihong.wms.api.model.dto.OutboundBoxCodeDTO;
//import cn.jihong.wms.api.model.dto.SrmBarcodeStoreDTO;
//import cn.jihong.wms.api.model.po.SrmBarStoreChangeBodyPO;
//import cn.jihong.wms.api.model.po.SrmBarStoreChangeHeadPO;
//import cn.jihong.wms.api.model.po.SrmBarcodeChangePO;
//import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
//import cn.jihong.wms.api.service.ISrmBarcodeStoreService;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.text.ParseException;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.util.Date;
//
//@Slf4j
//@Service
//public class WmsChangeMaterialServiceImpl {
//
//    private static final String DEPT_NAME = "新MES";
//
//    @DubboReference
//    private ISrmBarcodeStoreService srmBarcodeStoreService;
//    @DubboReference
//    private ISrmBarcodeDetailService srmBarcodeDetailService;
//
//    @SneakyThrows
//    public void outboundBoxCode(String barcord, BigDecimal materialQuantity, String flag) {
//        Snowflake snowflake = new Snowflake(1,1);
//        String nextIdStr = snowflake.nextIdStr();
//        SrmBarcodeStoreDTO srmBarcodeStoreDTO =
//                srmBarcodeStoreService.getXBCSrmBarcodeByBarcodeNo(barcord);
//        if (srmBarcodeStoreDTO == null) {
//            throw new CommonException("查询不到对应线边仓物料信息");
//        }
//
//        OutboundBoxCodeDTO outboundBoxCodeDTO = new OutboundBoxCodeDTO();
//        outboundBoxCodeDTO.setMaterialQuantity(materialQuantity);
//        outboundBoxCodeDTO.setFlag(flag);
//
//        SrmBarcodeChangePO srmBarcodeChangePO = new SrmBarcodeChangePO();
//        srmBarcodeChangePO.setEnt("100");
//        srmBarcodeChangePO.setSite(SecurityUtil.getCompanySite());
//        srmBarcodeChangePO.setCreateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarcodeChangePO.setCreateDate(new java.util.Date());
//        srmBarcodeChangePO.setUpdateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarcodeChangePO.setUpdateDate(new java.util.Date());
//        srmBarcodeChangePO.setDelFlag(BooleanEnum.FALSE.getCode());
//        srmBarcodeChangePO.setCreateOffice("新MES提交，自动审核通过");
//        srmBarcodeChangePO.setDeptName(DEPT_NAME);
//        srmBarcodeChangePO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarcodeChangePO.setStatus("Y");
//        srmBarcodeChangePO.setRemarks("新MES提交，做出站动作");
//        srmBarcodeChangePO.setBarcodeNo(srmBarcodeStoreDTO.getBarcodeNo());
//        srmBarcodeChangePO.setWarehouseNo(srmBarcodeStoreDTO.getWarehouseNo());
//        srmBarcodeChangePO.setStorageSpacesNo(srmBarcodeStoreDTO.getWarehouseNoId());
//        srmBarcodeChangePO.setLotNo(srmBarcodeStoreDTO.getLotNo());
//        srmBarcodeChangePO.setQty(materialQuantity);
//        srmBarcodeChangePO.setDeptName(DEPT_NAME);
//        srmBarcodeChangePO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarcodeChangePO.setReferenceQty(BigDecimal.ZERO);
//        srmBarcodeChangePO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
//        // srm_barcode_change条码异动档
//        srmBarcodeChangePO.setWarehouseName(srmBarcodeStoreDTO.getWarehouseName());
//        srmBarcodeChangePO.setStorageSpacesName(srmBarcodeStoreDTO.getStorageSpacesName());
//        srmBarcodeChangePO.setBadStorage(srmBarcodeStoreDTO.getBadStorage());
//        srmBarcodeChangePO.setPlatform(srmBarcodeStoreDTO.getPlatform());
//        try {
//            srmBarcodeChangePO
//                    .setVldDate(cn.jihong.common.util.DateUtil.parseDate("9998-12-31", DatePattern.NORM_DATE_PATTERN));
//        } catch (ParseException e) {
//            log.error("异常信息", e);
//        }
//        srmBarcodeChangePO.setChagType(-1);
//        srmBarcodeChangePO.setSourceNo(nextIdStr);
//        srmBarcodeChangePO.setSourceItemNo(BigDecimal.ONE);
//        srmBarcodeChangePO.setTargetNo(nextIdStr);
//        srmBarcodeChangePO.setTargetItemNo(BigDecimal.ONE);
//        srmBarcodeChangePO.setPdaOpCode(SecurityUtil.getWorkcode());
//        try {
//            srmBarcodeChangePO.setScanDate(cn.jihong.common.util.DateUtil
//                    .parseDate(LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER), DatePattern.NORM_DATE_PATTERN));
//            srmBarcodeChangePO.setGenTime(LocalDateTime.now().format(DatePattern.NORM_TIME_FORMATTER));
//        } catch (ParseException e) {
//            log.error("异常信息", e);
//        }
//        srmBarcodeChangePO.setIsCharged("Y");
//        srmBarcodeChangePO.setChagNumUni(srmBarcodeStoreDTO.getStockUnitNo());
//        srmBarcodeChangePO.setExchagRate(BigDecimal.ONE);
//
//        srmBarcodeChangePO.setErpChgCode("C");
//        srmBarcodeChangePO.setBarChgType("Z");
//        srmBarcodeChangePO.setCustomerNo(SecurityUtil.getCompanySite());
//        srmBarcodeChangePO.setCustomerName(SecurityUtil.getCompanyName());
//        srmBarcodeChangePO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
//        srmBarcodeChangePO.setStuffCode(srmBarcodeStoreDTO.getItemNo());
//        srmBarcodeChangePO.setItemName(srmBarcodeStoreDTO.getItemName());
//        srmBarcodeChangePO.setItemSpec(srmBarcodeStoreDTO.getItemSpec());
//        srmBarcodeChangePO.setIsVldDate("N");
//        srmBarcodeChangePO.setEffectiveDay(0);
//        srmBarcodeChangePO.setInNearDay(0);
//        srmBarcodeChangePO.setInNearControl("1");
//        srmBarcodeChangePO.setOutNearDay(0);
//        srmBarcodeChangePO.setOutNearControl("1");
//        srmBarcodeChangePO.setWarningDay(0);
//        srmBarcodeChangePO.setEffectiveType("2");
//        srmBarcodeChangePO.setContainCode(srmBarcodeStoreDTO.getContainCode());
//        srmBarcodeChangePO.setErpChgNo(srmBarcodeStoreDTO.getErpChgNo());
//        srmBarcodeChangePO.setErpChgLine(srmBarcodeStoreDTO.getErpChgLine());
//        srmBarcodeChangePO.setErpChgOrder(srmBarcodeStoreDTO.getErpChgOrder());
//        srmBarcodeChangePO.setStatus("Y");
//        srmBarcodeChangePO.setBarcode(srmBarcodeStoreDTO.getBarcodeNo());
//        srmBarcodeChangePO.setReferenceUnitNo(srmBarcodeStoreDTO.getReferenceUnitNo());
//        srmBarcodeChangePO.setReferenceUnitName(srmBarcodeStoreDTO.getReferenceUnitName());
//        srmBarcodeChangePO.setReferenceQty(srmBarcodeStoreDTO.getReferenceQty());
//        srmBarcodeChangePO.setPostDate(new Date());
//        srmBarcodeChangePO.setStockKeep(BigDecimal.ZERO);
//        srmBarcodeChangePO.setSourceBarStatus(srmBarcodeStoreDTO.getSourceBarStatus());
//        srmBarcodeChangePO.setTargetBarStatus(srmBarcodeStoreDTO.getTargetBarStatus());
//        srmBarcodeChangePO.setDeptName(DEPT_NAME);
//        srmBarcodeChangePO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarcodeChangePO.setReferenceQty(BigDecimal.ZERO);
//        srmBarcodeChangePO.setErpLotNo(srmBarcodeStoreDTO.getErpLotNo());
//
//        // ===========================================================
//        SrmBarStoreChangeHeadPO srmBarStoreChangeHeadPO = new SrmBarStoreChangeHeadPO();
//        srmBarStoreChangeHeadPO.setEnt("100");
//        srmBarStoreChangeHeadPO.setSite(SecurityUtil.getCompanySite());
//        srmBarStoreChangeHeadPO.setCreateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeHeadPO.setCreateDate(new java.util.Date());
//        srmBarStoreChangeHeadPO.setUpdateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeHeadPO.setUpdateDate(new java.util.Date());
//        srmBarStoreChangeHeadPO.setDelFlag(BooleanEnum.FALSE.getCode());
//        srmBarStoreChangeHeadPO.setCreateOffice("新MES提交，自动审核通过");
//        srmBarStoreChangeHeadPO.setDeptName(DEPT_NAME);
//        srmBarStoreChangeHeadPO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeHeadPO.setStatus("Y");
//        srmBarStoreChangeHeadPO.setRemarks("新MES提交，做出站动作");
//
//        // ===========================================================
//        SrmBarStoreChangeBodyPO srmBarStoreChangeBodyPO = new SrmBarStoreChangeBodyPO();
//        srmBarStoreChangeBodyPO.setEnt("100");
//        srmBarStoreChangeBodyPO.setSite(SecurityUtil.getCompanySite());
//        srmBarStoreChangeBodyPO.setCreateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeBodyPO.setCreateDate(new java.util.Date());
//        srmBarStoreChangeBodyPO.setUpdateBy(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeBodyPO.setUpdateDate(new java.util.Date());
//        srmBarStoreChangeBodyPO.setDelFlag(BooleanEnum.FALSE.getCode());
//        srmBarStoreChangeBodyPO.setCreateOffice("新MES提交，自动审核通过");
//        srmBarStoreChangeBodyPO.setDeptName(DEPT_NAME);
//        srmBarStoreChangeBodyPO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
//        srmBarStoreChangeBodyPO.setStatus("Y");
//        srmBarStoreChangeBodyPO.setRemarks("新MES提交，做出站动作");
//        srmBarStoreChangeBodyPO.setBarcodeNo(srmBarcodeStoreDTO.getBarcodeNo());
//        srmBarStoreChangeBodyPO.setWarehouseNo(srmBarcodeStoreDTO.getWarehouseNo());
//        srmBarStoreChangeBodyPO.setStorageSpacesNo(srmBarcodeStoreDTO.getWarehouseNoId());
//        srmBarStoreChangeBodyPO.setLotNo(srmBarcodeStoreDTO.getLotNo());
//        srmBarStoreChangeBodyPO.setQty(materialQuantity);
//        srmBarStoreChangeBodyPO.setReferenceQty(BigDecimal.ZERO);
//        srmBarStoreChangeBodyPO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
//        srmBarStoreChangeBodyPO.setChagType(Integer.valueOf(flag));
//        srmBarStoreChangeBodyPO.setReferenceUnitNo(srmBarcodeStoreDTO.getReferenceUnitNo());
//
//
//        outboundBoxCodeDTO.setSrmBarcodeChangePO(srmBarcodeChangePO);
//        outboundBoxCodeDTO.setSrmBarStoreChangeHeadPO(srmBarStoreChangeHeadPO);
//        outboundBoxCodeDTO.setSrmBarStoreChangeBodyPO(srmBarStoreChangeBodyPO);
//        srmBarcodeDetailService.outboundBoxCode(outboundBoxCodeDTO);
//    }
//
//
//}
