package cn.jihong.mes.production.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 金山物流配置开关
 */
@Data
@Component
@ConfigurationProperties(prefix = "logistics")
@RefreshScope
public class JinshanLogisticsConfig {

    /**
     * 据点
     */
    private List<String> companyCodes;

}
