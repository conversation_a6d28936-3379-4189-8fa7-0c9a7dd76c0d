package cn.jihong.mes.production.app.service.mq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.jihong.mes.production.api.model.constant.RabbitConsts;
import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.service.IRabbitMQService;

@Service
public class TopicRabbitMQServiceImpl implements IRabbitMQService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public String getRabbitMQType() {
        return RabbitConsts.TOPIC;
    }

    @Override
    public void sendMessage(MessageStructDTO msg) {
        rabbitTemplate.convertAndSend(RabbitConsts.TOPIC_MODE_EXCHANGE, RabbitConsts.TOPIC_ROUTING_KEY, msg);
    }


}
