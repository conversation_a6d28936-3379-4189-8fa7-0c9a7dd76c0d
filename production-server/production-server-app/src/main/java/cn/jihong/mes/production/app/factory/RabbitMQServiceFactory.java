/**
 * @company 杭州吉喵云科技有限公司(www.gillmall.com)
 * @copyright Copyright (c) 2012-2022
 */
package cn.jihong.mes.production.app.factory;

import cn.jihong.mes.production.api.service.IRabbitMQService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class RabbitMQServiceFactory {

	@Autowired(required = false)
	private List<IRabbitMQService> rabbitMQServices;

	private Map<String, IRabbitMQService> rabbitMQServiceMap = new HashMap<>(100);

	@PostConstruct
	public void init() {
		if(rabbitMQServices == null) {
			return;
		}
		for (IRabbitMQService rabbitMQService : rabbitMQServices) {
			rabbitMQServiceMap.put(rabbitMQService.getRabbitMQType(), rabbitMQService);
		}
	}

	/**
	 * 根据业务类型code获取详情实现类
	 *
	 * @param rabbitMQTypeCode
	 * @return {@link IRabbitMQService}
	 */
	public IRabbitMQService getRabbitMQService(String rabbitMQTypeCode) {
		IRabbitMQService rabbitMQService = rabbitMQServiceMap.get(rabbitMQTypeCode);
		return rabbitMQService;
	}
}
