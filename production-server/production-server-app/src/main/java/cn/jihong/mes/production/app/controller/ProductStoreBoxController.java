package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.service.IProductStoreBoxService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 入库箱码信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@RestController
@RequestMapping("/productStoreBox")
@ShenyuSpringMvcClient(path = "/productStoreBox/**")
public class ProductStoreBoxController {

    @Resource
    private IProductStoreBoxService productStoreBoxService;

    /**
     * 校验箱码是否使用过
     * @param boxCodes
     * @return
     */
    @PostMapping("/verifyBoxCode")
    public StandardResult<String> verifyBoxCode(@RequestBody List<String> boxCodes) {
        productStoreBoxService.verifyBoxCode(boxCodes);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

}

