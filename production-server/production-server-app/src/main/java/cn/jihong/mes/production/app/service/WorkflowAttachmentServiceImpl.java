package cn.jihong.mes.production.app.service;

import cn.jihong.common.enums.AttachmentSourceTypeEnum;
import cn.jihong.common.enums.FileSourceTypeEnum;
import cn.jihong.common.model.dto.WorkflowAttachmentDTO;
import cn.jihong.common.model.dto.request.oa.WorkflowDownloadFileRequest;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.WorkflowAttachmentFilePO;
import cn.jihong.mes.production.api.model.po.WorkflowRequestAttachmentFilePO;
import cn.jihong.mes.production.app.mapper.WorkflowAttachmentFileMapper;
import cn.jihong.mes.production.app.mapper.WorkflowRequestAttachmentFileMapper;
import cn.jihong.workflow.common.biz.IWorkflowAttachmentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkflowAttachmentServiceImpl implements IWorkflowAttachmentService {

    @Resource
    private WorkflowAttachmentFileMapper workflowAttachmentFileMapper;

    @Resource
    WorkflowRequestAttachmentFileMapper workflowRequestAttachmentFileMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertFile(WorkflowAttachmentDTO workflowAttachmentDTO) {

        log.info("WorkflowAttachmentServiceImpl.uploadFile start;");
        WorkflowAttachmentFilePO po = new WorkflowAttachmentFilePO();
        Long userId = SecurityUtil.getUserId();
        po.setCreateBy(userId);
        po.setUpdateBy(userId);
        po.setFileUrl(workflowAttachmentDTO.getFileUrl());
        po.setFileType(workflowAttachmentDTO.getFileType());
        po.setFileName(workflowAttachmentDTO.getFileName());
        po.setFileSourceType(FileSourceTypeEnum.JH_NEW_OA.getFileSourceType());
        workflowAttachmentFileMapper.insert(po);
        workflowAttachmentDTO.setId(po.getId());
    }

    @Override
    public WorkflowAttachmentDTO getAttachmentFileInfo(Long fileId) {
        log.info("WorkflowAttachmentServiceImpl.getAttachmentFileInfo start; fileId = [{}]", fileId);
        WorkflowAttachmentFilePO po = workflowAttachmentFileMapper.selectById(fileId);
        if(po == null) {

            return null;
        }

        WorkflowRequestAttachmentFilePO requestAttachmentPO = workflowRequestAttachmentFileMapper.getWorkflowRequestAttachmentPO(fileId);
        WorkflowAttachmentDTO dto = new WorkflowAttachmentDTO();
        dto.setId(po.getId());
        dto.setFileUrl(po.getFileUrl());
        dto.setFileName(po.getFileName());
        dto.setFileSourceType(po.getFileSourceType());
        dto.setFileType(po.getFileType());
        dto.setAttachmentSourceType(AttachmentSourceTypeEnum.ATTACHMENT.getSourceType());
        if(requestAttachmentPO != null) {

            Integer attachmentSourceType = Optional.ofNullable(requestAttachmentPO.getAttachmentSourceType()).orElse(AttachmentSourceTypeEnum.ATTACHMENT.getSourceType());
            dto.setAttachmentSourceType(attachmentSourceType);
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(WorkflowDownloadFileRequest request) {

        log.info("WorkflowAttachmentServiceImpl.getAttachmentFileInfo start; request = [{}]", request);
        workflowAttachmentFileMapper.deleteById(request.getId());

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put(WorkflowRequestAttachmentFilePO.FILE_ID, request.getId());
        workflowRequestAttachmentFileMapper.deleteByMap(paramMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertWorkflowRequestAttachmentList(Long requestId, List<WorkflowAttachmentDTO> list, Integer attachmentSourceType) {
        if(list == null) {

            return;
        }

        // 空列表 清除附件
        if(CollectionUtil.isEmpty(list)) {

            LambdaQueryWrapper<WorkflowRequestAttachmentFilePO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getRequestId, requestId);
            queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getAttachmentSourceType, attachmentSourceType);
            List<WorkflowRequestAttachmentFilePO> poList = workflowRequestAttachmentFileMapper.selectList(queryWrapper);
            if(CollectionUtil.isEmpty(poList)) {

                return;
            }

            workflowRequestAttachmentFileMapper.delete(queryWrapper);
            return;
        }

        // 删除不在列表中得附件
        List<Long> fileIdList = list.stream().filter(o -> o.getId() != null).map(o -> o.getId()).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<WorkflowRequestAttachmentFilePO> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(WorkflowRequestAttachmentFilePO :: getRequestId, requestId);
        deleteWrapper.eq(WorkflowRequestAttachmentFilePO :: getAttachmentSourceType, attachmentSourceType);
        deleteWrapper.notIn(CollectionUtil.isNotEmpty(fileIdList), WorkflowRequestAttachmentFilePO :: getFileId, fileIdList);
        List<WorkflowRequestAttachmentFilePO> deletePoList = workflowRequestAttachmentFileMapper.selectList(deleteWrapper);
        if(CollectionUtil.isNotEmpty(deletePoList)) {

            workflowRequestAttachmentFileMapper.delete(deleteWrapper);
        }

        // 过滤已存在附件
        LambdaQueryWrapper<WorkflowRequestAttachmentFilePO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getRequestId, requestId);
        queryWrapper.in(WorkflowRequestAttachmentFilePO :: getFileId, fileIdList);
        queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getAttachmentSourceType, attachmentSourceType);
        List<WorkflowRequestAttachmentFilePO> poList = workflowRequestAttachmentFileMapper.selectList(queryWrapper);
        if(CollectionUtil.isNotEmpty(poList)) {
            Map<Long, List<WorkflowRequestAttachmentFilePO>> listMap = poList.stream().collect(Collectors.groupingBy(WorkflowRequestAttachmentFilePO :: getFileId));
            Iterator<WorkflowAttachmentDTO> it = list.iterator();
            while (it.hasNext()) {
                WorkflowAttachmentDTO next = it.next();
                if(listMap.containsKey(next.getId())) {

                    it.remove();
                }
            }
        }

        // 插入新附件
        if(CollectionUtil.isNotEmpty(list)) {

            for (WorkflowAttachmentDTO workflowAttachmentDTO : list) {
                WorkflowRequestAttachmentFilePO po = new WorkflowRequestAttachmentFilePO();
                po.setRequestId(requestId);
                po.setAttachmentSourceType(attachmentSourceType);
                po.setFileId(workflowAttachmentDTO.getId());
                workflowRequestAttachmentFileMapper.insert(po);
            }
        }
    }

    @Override
    public List<WorkflowAttachmentDTO> getAttachmentList(Long requestId) {

        LambdaQueryWrapper<WorkflowRequestAttachmentFilePO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getRequestId, requestId);
//        queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getAttachmentSourceType, AttachmentSourceTypeEnum.ATTACHMENT.getSourceType());
        List<WorkflowRequestAttachmentFilePO> poList = workflowRequestAttachmentFileMapper.selectList(queryWrapper);
        if(CollectionUtil.isEmpty(poList)) {

            return Lists.newArrayList();
        }

        Map<Long, WorkflowRequestAttachmentFilePO> requestFileMap = poList.stream().collect(Collectors.toMap(WorkflowRequestAttachmentFilePO :: getFileId, o -> o, (o1, o2) -> o1));
        List<Long> fileIdList = poList.stream().map(o -> o.getFileId()).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<WorkflowAttachmentFilePO> queryWrapper1 = Wrappers.lambdaQuery();
        queryWrapper1.in(WorkflowAttachmentFilePO :: getId, fileIdList);
        queryWrapper1.orderByAsc(WorkflowAttachmentFilePO :: getId);
        List<WorkflowAttachmentFilePO> poList2 = workflowAttachmentFileMapper.selectList(queryWrapper1);
        Map<Long, WorkflowAttachmentFilePO> fileMap = poList2.stream().collect(Collectors.toMap(WorkflowAttachmentFilePO::getId, Function.identity()));
        ArrayList<WorkflowAttachmentDTO> resultList = Lists.newArrayList();
        poList.stream().filter(f -> fileMap.containsKey(f.getFileId())).forEach(po -> {

            WorkflowAttachmentFilePO o = fileMap.get(po.getFileId());
            WorkflowAttachmentDTO dto = new WorkflowAttachmentDTO();
            dto.setId(o.getId());
            dto.setFileType(o.getFileType());
            dto.setFileSourceType(o.getFileSourceType());
            dto.setFileName(o.getFileName());
            dto.setAttachmentSourceType(po.getAttachmentSourceType());

            resultList.add(dto);
        });
        return resultList;
    }
}
