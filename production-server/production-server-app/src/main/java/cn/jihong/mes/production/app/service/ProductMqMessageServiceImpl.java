package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.model.po.ProductMqMessagePO;
import cn.jihong.mes.production.app.mapper.ProductMqMessageMapper;
import cn.jihong.mes.production.api.service.IProductMqMessageService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.annotation.DubboService;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@DubboService
public class ProductMqMessageServiceImpl extends JiHongServiceImpl<ProductMqMessageMapper, ProductMqMessagePO> implements IProductMqMessageService {

    @Override
    public void saveMqMessage(MessageStructDTO messageStructDTO, String message) {
        ProductMqMessagePO productMqMessagePO = new ProductMqMessagePO();
        productMqMessagePO.setMessageId(messageStructDTO.getMessageId());
        productMqMessagePO.setCompanyCode(messageStructDTO.getUserInfo().getCompanySite());
        productMqMessagePO.setUserInfo(JSON.toJSONString(messageStructDTO.getUserInfo()));
        productMqMessagePO.setBizCode(String.valueOf(messageStructDTO.getBizCode()));
        productMqMessagePO.setRequestData(JSON.toJSONString(messageStructDTO.getData()));
        productMqMessagePO.setResponseData(message);
        save(productMqMessagePO);
    }
}
