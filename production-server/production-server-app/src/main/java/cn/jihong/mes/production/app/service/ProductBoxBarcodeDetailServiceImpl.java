package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.jihong.channel.api.model.dto.YinKeLiDTO;
import cn.jihong.channel.api.service.IYinKeLiService;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeRecordDTO;
import cn.jihong.mes.production.api.model.enums.BarcodeOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetOldBoxCodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailDetailService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeRecordService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductBoxBarcodeDetailMapper;
import cn.jihong.mes.production.app.service.boxCode.BoxCodeHandlerFactory;
import cn.jihong.mes.production.api.service.IBoxCodeService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.model.po.PmaaTPO;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.service.IImaalTService;
import cn.jihong.oa.erp.api.service.IMaterialsInfoService;
import cn.jihong.oa.erp.api.service.IPmaaTService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import cn.jihong.wms.api.model.dto.SrmBarcodeDetailDTO;
import cn.jihong.wms.api.model.vo.SrmBarcodeDetailVO;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 箱码号段明细 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@DubboService
public class ProductBoxBarcodeDetailServiceImpl extends JiHongServiceImpl<ProductBoxBarcodeDetailMapper, ProductBoxBarcodeDetailPO> implements IProductBoxBarcodeDetailService {

    @Autowired
    private IProductBoxBarcodeService productBoxBarcodeService;

    @Autowired
    private IProductBoxBarcodeRecordService productBoxBarcodeRecordService;
    @Autowired
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;
    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;

    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IPmaaTService pmaaTService;
    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @DubboReference
    private IImaalTService iImaalTService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;
    @DubboReference
    private IYinKeLiService yinKeLiService;

    @RedisLock
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long applyBoxBarcodeDetail(String lockKey, AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO) {
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(addProductBoxBarcodeDetailInVO.getPlanTicketNo());
        if (!boxCodeService.getCompanyCode().equals(addProductBoxBarcodeDetailInVO.getCustomerNo())){
            throw new CommonException("工单号与客户号不匹配" + boxCodeService.getCompanyCode() + ":" + addProductBoxBarcodeDetailInVO.getCustomerNo());
        }
        return boxCodeService.applyBoxBarcodeDetail(addProductBoxBarcodeDetailInVO);
    }

    @Override
    public void verify(String planTicketNo, String customerNo) {
        // 校验客户与工单的客户是否相同
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNo);
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        // 获得客户集团信息
        PmaaTPO pmaaTPO = pmaaTService.getCustomerInfo(sfaaTVO.getSfaa009());
        if (pmaaTPO != null) {
            if (!customerNo.equals(pmaaTPO.getPmaa006())) {
                throw new CommonException("选择的客户集团与工单号不匹配");
            }
        }
    }

    @RedisLock
    @Transactional(rollbackFor = CommonException.class)
    @Override
    public Long update(String lockKey, UpdateProductBoxBarcodeDetailInVO updateProductBoxBarcodeDetailInVO) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(updateProductBoxBarcodeDetailInVO.getId());
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要更新的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            throw new CommonException("该箱码已作废，请勿更新");
        }
        if (productBoxBarcodeDetailPO.getPrintStatus().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            throw new CommonException("该箱码已打印，请勿更新");
        }
        ProductBoxBarcodePO productBoxBarcodePO =
                productBoxBarcodeService.getById(productBoxBarcodeDetailPO.getProductBarcodeId());
        verify(updateProductBoxBarcodeDetailInVO.getPlanTicketNo(), productBoxBarcodePO.getCustomerNo());

        productBoxBarcodeDetailPO.setPlanTicketNo(updateProductBoxBarcodeDetailInVO.getPlanTicketNo());
        productBoxBarcodeDetailPO.setProductName(updateProductBoxBarcodeDetailInVO.getProductName());
        productBoxBarcodeDetailPO.setMaterialCode(updateProductBoxBarcodeDetailInVO.getProductNo());
        productBoxBarcodeDetailPO.setBoxNum(updateProductBoxBarcodeDetailInVO.getBoxSpece());
        productBoxBarcodeDetailPO.setProductionDate(updateProductBoxBarcodeDetailInVO.getProductionDate());
        productBoxBarcodeDetailPO.setBatchCode(updateProductBoxBarcodeDetailInVO.getBatchCode());
        productBoxBarcodeDetailPO.setPlanTicketNo(updateProductBoxBarcodeDetailInVO.getPlanTicketNo());
        productBoxBarcodeDetailPO.setProductName(updateProductBoxBarcodeDetailInVO.getProductName());
        productBoxBarcodeDetailPO.setMaterialCode(updateProductBoxBarcodeDetailInVO.getProductNo());
        productBoxBarcodeDetailPO.setBoxNum(updateProductBoxBarcodeDetailInVO.getBoxSpece());
        productBoxBarcodeDetailPO.setProductionDate(updateProductBoxBarcodeDetailInVO.getProductionDate());
        productBoxBarcodeDetailPO.setLotNo(DateUtil.format(updateProductBoxBarcodeDetailInVO.getProductionDate(),
                DatePattern.PURE_DATE_PATTERN));
        productBoxBarcodeDetailPO.setBatchCode(updateProductBoxBarcodeDetailInVO.getBatchCode());
        productBoxBarcodeDetailPO.setMachine(updateProductBoxBarcodeDetailInVO.getMachine());
        productBoxBarcodeDetailPO.setShift(updateProductBoxBarcodeDetailInVO.getShift());
//        productBoxBarcodeDetailPO.setExpirationDate(updateProductBoxBarcodeDetailInVO.getExpirationDate());
        updateById(productBoxBarcodeDetailPO);
        return productBoxBarcodeDetailPO.getId();
    }
    
    @Transactional(rollbackFor = CommonException.class)
    @Override
    public void disableBoxBarcode(Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要启用的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            productBoxBarcodeDetailPO.setDisableStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        } else {
            productBoxBarcodeDetailPO.setDisableStatus(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        }
        updateById(productBoxBarcodeDetailPO);
    }


    @RedisLock
    @Transactional(rollbackFor = CommonException.class)
    @Override
    public List<String> printBoxBarcode(String key, Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要打印的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            throw new CommonException("该箱码已作废，请勿打印");
        }
        if (productBoxBarcodeDetailPO.getPrintStatus().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))){
            throw new CommonException("该箱码已打印，请勿重复打印");
        }

        if (StringUtil.isEmpty(productBoxBarcodeDetailPO.getPlanTicketNo())) {
            return printBoxBarcodeWithoutPlanTicketNo(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.PRINT);
        }

        // 更新状态
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(productBoxBarcodeDetailPO.getPlanTicketNo());
        return boxCodeService.getBarcodeList(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.PRINT);
    }

    @Override
    public List<String> printPMJBoxBarcode(String key, Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要打印的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            throw new CommonException("该箱码已作废，请勿喷码");
        }
        if (productBoxBarcodeDetailPO.getSpurtStatus().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))){
            throw new CommonException("该箱码已喷码，请勿重复喷码");
        }

        if (StringUtil.isEmpty(productBoxBarcodeDetailPO.getPlanTicketNo())) {
            return printBoxBarcodeWithoutPlanTicketNo(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.SPURT);
        }

        // 更新状态
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(productBoxBarcodeDetailPO.getPlanTicketNo());
        return boxCodeService.getBarcodeList(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.SPURT);
    }

    @Override
    public List<String> rePrintPMJBoxBarcode(String key, Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要打印的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            throw new CommonException("该箱码已作废，请勿喷码");
        }

        if (StringUtil.isEmpty(productBoxBarcodeDetailPO.getPlanTicketNo())) {
            return printBoxBarcodeWithoutPlanTicketNo(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.RE_SPURT);
        }

        // 更新状态
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(productBoxBarcodeDetailPO.getPlanTicketNo());
        return boxCodeService.getBarcodeList(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.RE_SPURT);
    }

    private List<String> printBoxBarcodeWithoutPlanTicketNo(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO,
                                                            BarcodeOperateTypeEnum barcodeOperateTypeEnum) {
        // 更新状态
        productBoxBarcodeDetailPO.setPrintStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productBoxBarcodeDetailService.updateById(productBoxBarcodeDetailPO);

        // 获取条码列表
        List<String> barcodeList = Lists.newArrayList();
        String barcodeNoStart = productBoxBarcodeDetailPO.getBarcodeNoStart();
        BigInteger startHex = new BigInteger(barcodeNoStart.substring(barcodeNoStart.length() - 13), 16);
        for (int i = 1; i <= productBoxBarcodeDetailPO.getBarcodeTotal(); i++) {
            String barcode = String.format("%013X", startHex);
            barcode = barcodeNoStart.substring(0, barcodeNoStart.length() - 13) + barcode;
            barcodeList.add(barcode);
            startHex = startHex.add(BigInteger.ONE);
        }
        log.info("打印条码列表：{}", barcodeList);

        // 保存条码记录
        ProductBoxBarcodeRecordDTO productBoxBarcodeRecordDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeRecordDTO.class);
        productBoxBarcodeRecordDTO.setOperationType(barcodeOperateTypeEnum.getCode());
        productBoxBarcodeRecordDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
        productBoxBarcodeRecordDTO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordDTO.setUpdateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordService.saveProductBoxBarcodeRecord(productBoxBarcodeRecordDTO);

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByBarcodeNos(barcodeList);
        Set<String> barcodeSet = productBoxBarcodeDetailDetailPOS.stream().map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toSet());

        // 保存条码详情
        List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOS = barcodeList.stream().map(barcode -> {
            if (barcodeSet.contains(barcode)) {
                return null;
            }
            ProductBoxBarcodeDetailDetailDTO productBoxBarcodeDetailDetailDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeDetailDetailDTO.class);
            productBoxBarcodeDetailDetailDTO.setId(null);
            productBoxBarcodeDetailDetailDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
            productBoxBarcodeDetailDetailDTO.setBarcodeNo(barcode);
            productBoxBarcodeDetailDetailDTO.setCustomerNo(productBoxBarcodeDetailPO.getCustomerNo());
            productBoxBarcodeDetailDetailDTO.setSkuCode(productBoxBarcodeDetailPO.getSkuCode());
            return productBoxBarcodeDetailDetailDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        productBoxBarcodeDetailDetailService.saveProductBoxBarcodeDetailDetailPOs(productBoxBarcodeDetailDetailDTOS);

        return barcodeList;

    }

    @Override
    public Pagination<GetProductBoxBarcodeDetailOutVO> getList(GetProductBoxBarcodeDetailInVO getProductBoxBarcodeDetailInVO) {
        Page<GetProductBoxBarcodeDetailOutVO> page = baseMapper.getList(getProductBoxBarcodeDetailInVO.getPage(), getProductBoxBarcodeDetailInVO);
        if (CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null);
        }

        page.getRecords().stream().forEach(record -> {
            record.setCustomerName(CompanyBoxEnum.getCompanyBoxEnum(record.getCustomerNo()).getName());
        });
        return Pagination.newInstance(page);
    }

    @Override
    public GetProductBoxBarcodeDetailOutVO getBoxBarcodeDetail(Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO != null){
            GetProductBoxBarcodeDetailOutVO getProductBoxBarcodeDetailOutVO = new GetProductBoxBarcodeDetailOutVO();
            getProductBoxBarcodeDetailOutVO.setId(productBoxBarcodeDetailPO.getId());
            getProductBoxBarcodeDetailOutVO.setCompanyCode(productBoxBarcodeDetailPO.getCompanyCode());
            getProductBoxBarcodeDetailOutVO.setBarcodeNoStart(productBoxBarcodeDetailPO.getBarcodeNoStart());
            getProductBoxBarcodeDetailOutVO.setBarcodeNoEnd(productBoxBarcodeDetailPO.getBarcodeNoEnd());
            getProductBoxBarcodeDetailOutVO.setBarcodeTotal(productBoxBarcodeDetailPO.getBarcodeTotal());
            getProductBoxBarcodeDetailOutVO.setBarcodeRemaining(productBoxBarcodeDetailPO.getBarcodeRemaining());
            getProductBoxBarcodeDetailOutVO.setPlanTicketNo(productBoxBarcodeDetailPO.getPlanTicketNo());
            getProductBoxBarcodeDetailOutVO.setProductName(productBoxBarcodeDetailPO.getProductName());
            getProductBoxBarcodeDetailOutVO.setMaterialCode(productBoxBarcodeDetailPO.getMaterialCode());
            getProductBoxBarcodeDetailOutVO.setBoxNum(productBoxBarcodeDetailPO.getBoxNum());
            getProductBoxBarcodeDetailOutVO.setProductionDate(productBoxBarcodeDetailPO.getProductionDate());
            getProductBoxBarcodeDetailOutVO.setBatchCode(productBoxBarcodeDetailPO.getBatchCode());
            getProductBoxBarcodeDetailOutVO.setMachine(productBoxBarcodeDetailPO.getMachine());
            getProductBoxBarcodeDetailOutVO.setShift(productBoxBarcodeDetailPO.getShift());
            getProductBoxBarcodeDetailOutVO.setExpirationDate(productBoxBarcodeDetailPO.getExpirationDate());
            getProductBoxBarcodeDetailOutVO.setPrintStatus(productBoxBarcodeDetailPO.getPrintStatus());
            getProductBoxBarcodeDetailOutVO.setDisableStatus(productBoxBarcodeDetailPO.getDisableStatus());

            ProductBoxBarcodePO productBoxBarcodePO = productBoxBarcodeService.getById(productBoxBarcodeDetailPO.getProductBarcodeId());
            if (productBoxBarcodePO != null){
                getProductBoxBarcodeDetailOutVO.setBarcodeName(productBoxBarcodePO.getBarcodeName());
                getProductBoxBarcodeDetailOutVO.setCustomerNo(productBoxBarcodePO.getCustomerNo());
                getProductBoxBarcodeDetailOutVO.setCustomerName(productBoxBarcodePO.getCustomerName());
            }

            return getProductBoxBarcodeDetailOutVO;
        }
        return null;
    }

    @RedisLock
    @Transactional(rollbackFor = CommonException.class)
    @Override
    public List<String> rePrintBoxBarcode(String lock_key, Integer id) {
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = getById(id);
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("未找到要打印的箱码");
        }
        if (productBoxBarcodeDetailPO.getDisableStatus().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))){
            throw new CommonException("该箱码已作废，请勿打印");
        }

        if (StringUtil.isEmpty(productBoxBarcodeDetailPO.getPlanTicketNo())) {
            return printBoxBarcodeWithoutPlanTicketNo(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.PRINT);
        }

        // 更新状态
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(productBoxBarcodeDetailPO.getPlanTicketNo());
        return boxCodeService.getBarcodeList(productBoxBarcodeDetailPO, BarcodeOperateTypeEnum.RE_PRINT);
    }

    @Override
    public List<String> getLotNosByBarcodes(List<String> barcodes) {
        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodeDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .and(wrapper -> {
                    // 使用in语句，遍历每一个条码进行条件构建
                    for (String barcode : barcodes) {
                                wrapper.or(subWrapper -> subWrapper
                                        .le(ProductBoxBarcodeDetailPO::getBarcodeNoStart, barcode)
                                        .ge(ProductBoxBarcodeDetailPO::getBarcodeNoEnd, barcode));
                            }
                        }
                );
        // 执行查询并返回批次号列表
        return list(lambdaQueryWrapper).stream()
                .map(ProductBoxBarcodeDetailPO::getLotNo)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<ProductBoxBarcodeDetailPO> getByBarcodes(List<String> barcodes) {
        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodeDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .and(wrapper -> {
                            // 使用in语句，遍历每一个条码进行条件构建
                            for (String barcode : barcodes) {
                                wrapper.or(subWrapper -> subWrapper
                                        .le(ProductBoxBarcodeDetailPO::getBarcodeNoStart, barcode)
                                        .ge(ProductBoxBarcodeDetailPO::getBarcodeNoEnd, barcode));
                            }
                        }
                );
        // 执行查询并返回批次号列表
        return list(lambdaQueryWrapper);
    }

    @Override
    public ProductBoxBarcodeDetailPO getByBarcode(String barcode) {
        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodeDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .le(ProductBoxBarcodeDetailPO::getBarcodeNoStart, barcode)
                .ge(ProductBoxBarcodeDetailPO::getBarcodeNoEnd, barcode);
        // 执行查询并返回批次号列表
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<ProductBoxBarcodeDetailDetailDTO> getDetail(Long id) {
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByDetailId(id);
        return BeanUtil.copyToList(productBoxBarcodeDetailDetailPOS, ProductBoxBarcodeDetailDetailDTO.class);
    }



    @Override
    public String getNextBarcode(String barcodeNoStart, BigInteger currentHex, BigInteger count) {
        // 计算当前条码的下一个条码
        BigInteger nextHex = currentHex.add(count);
        // 将十进制数值转换为16进制字符串
        String nextBarcode = String.format("%013X", nextHex);
        // 补全到16位
        nextBarcode = barcodeNoStart.substring(0, barcodeNoStart.length() - 13) + nextBarcode;
        return nextBarcode;
    }

    @Override
    public Long applyBoxBarcodeDetailNoInfo(String lock_key,
        ApplyBoxBarcodeDetailNoInfoInVO applyBoxBarcodeDetailNoInfoInVO) {
        ProductBoxBarcodePO productBoxBarcodePO =
            productBoxBarcodeService.getById(applyBoxBarcodeDetailNoInfoInVO.getProductBarcodeId());

        if (productBoxBarcodePO.getBarcodeRemaining()
            .compareTo(applyBoxBarcodeDetailNoInfoInVO.getApplyBarcodeCount()) < 0) {
            throw new CommonException("申请数量超出剩余数量，请检查");
        }
        String barcodeNoStart = productBoxBarcodePO.getBarcodeNoStart();
        String barcodeNoEnd = productBoxBarcodePO.getBarcodeNoEnd();
        // 将16进制的字符串转换为十进制数值进行比较
        BigInteger startHex = new BigInteger(barcodeNoStart.substring(barcodeNoStart.length() - 13), 16);
        BigInteger endHex = new BigInteger(barcodeNoEnd.substring(barcodeNoEnd.length() - 13), 16);

        String barcodeCurrent = null;
        if (productBoxBarcodePO.getBarcodeCurrent() == null) {
            barcodeCurrent = productBoxBarcodePO.getBarcodeNoStart();
        } else {
            barcodeCurrent = productBoxBarcodePO.getBarcodeCurrent();
        }
        BigInteger currentHex = new BigInteger(barcodeCurrent.substring(barcodeCurrent.length() - 13), 16);
        // 计算下一个条码
        // 获得后续第N个条码
        String nextNBarcode = productBoxBarcodeDetailService.getNextBarcode(barcodeNoStart, currentHex,
            new BigInteger(String.valueOf(applyBoxBarcodeDetailNoInfoInVO.getApplyBarcodeCount())));
        String lastNBarcode = productBoxBarcodeDetailService.getNextBarcode(barcodeNoStart, currentHex,
            new BigInteger(String.valueOf(applyBoxBarcodeDetailNoInfoInVO.getApplyBarcodeCount() - 1)));

        productBoxBarcodePO.setBarcodeCurrent(nextNBarcode);
        productBoxBarcodePO.setBarcodeRemaining(
            productBoxBarcodePO.getBarcodeRemaining() - applyBoxBarcodeDetailNoInfoInVO.getApplyBarcodeCount());
        productBoxBarcodeService.updateById(productBoxBarcodePO);

        // 将16进制的字符串转换为十进制数值进行比较
        BigInteger startDetailHex = new BigInteger(barcodeCurrent.substring(barcodeCurrent.length() - 13), 16);
        BigInteger endDetailHex = new BigInteger(lastNBarcode.substring(lastNBarcode.length() - 13), 16);

        // 明细开始要大于等于号段开始 明细结束要小于等于号段结束
        boolean flag = startDetailHex.compareTo(startHex) >= 0 && endDetailHex.compareTo(endHex) <= 0;
        if (!flag) {
            throw new CommonException("条码范围不在码段范围内，请检查");
        }

        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodeDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        String finalBarcodeCurrent = barcodeCurrent;
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
            .eq(ProductBoxBarcodeDetailPO::getProductBarcodeId, applyBoxBarcodeDetailNoInfoInVO.getProductBarcodeId())
            .and(wrapper -> wrapper.le(ProductBoxBarcodeDetailPO::getBarcodeNoStart, lastNBarcode)
                .ge(ProductBoxBarcodeDetailPO::getBarcodeNoEnd, finalBarcodeCurrent));

        List<ProductBoxBarcodeDetailPO> list = productBoxBarcodeDetailService.list(lambdaQueryWrapper);
        if (cn.jihong.common.util.CollectionUtil.isNotEmpty(list)) {
            throw new CommonException("该客户已存在该箱码范围，请勿重复添加");
        }

        // 计算条数
        BigInteger totalCount = endDetailHex.subtract(startDetailHex).add(BigInteger.ONE);
        if (totalCount.compareTo(BigInteger.ZERO) <= 0) {
            throw new CommonException("条码范围不合法，请检查");
        }

        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = new ProductBoxBarcodeDetailPO();
        productBoxBarcodeDetailPO.setCompanyCode(SecurityUtil.getCompanySite());
        productBoxBarcodeDetailPO.setProductBarcodeId(applyBoxBarcodeDetailNoInfoInVO.getProductBarcodeId());
        productBoxBarcodeDetailPO.setBarcodeNoStart(barcodeCurrent);
        productBoxBarcodeDetailPO.setBarcodeNoEnd(lastNBarcode);
        productBoxBarcodeDetailPO.setBarcodeTotal(applyBoxBarcodeDetailNoInfoInVO.getApplyBarcodeCount());
        productBoxBarcodeDetailPO.setBarcodeRemaining(productBoxBarcodePO.getBarcodeRemaining());
        productBoxBarcodeDetailPO.setCustomerNo(productBoxBarcodePO.getCustomerNo());

        productBoxBarcodeDetailPO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeDetailPO.setUpdateBy(SecurityUtil.getUserId());

        productBoxBarcodeDetailService.save(productBoxBarcodeDetailPO);
        return productBoxBarcodeDetailPO.getId();
    }

    @Override
    public GetOldBoxCodeDetailOutVO getOldBoxCodeDetail(GetOldBoxCodeDetailInVO getOldBoxCodeDetailInVO) {
        SrmBarcodeDetailDTO srmBarcodeDetailDTO = new SrmBarcodeDetailDTO();
        srmBarcodeDetailDTO.setBarcode(getOldBoxCodeDetailInVO.getBoxCode());
        List<SrmBarcodeDetailVO> srmBarcodeDetail = srmBarcodeDetailService.getSrmBarcodeDetail(srmBarcodeDetailDTO);
        if (CollectionUtil.isEmpty(srmBarcodeDetail)) {
            throw new CommonException("未找到该箱码信息");
        }
        SrmBarcodeDetailVO srmBarcodeDetailVO = srmBarcodeDetail.get(0);

        GetOldBoxCodeDetailOutVO getOldBoxCodeDetailOutVO = new GetOldBoxCodeDetailOutVO();
        if (StringUtil.isBlank(srmBarcodeDetailVO.getSourceNo())) {
            throw new CommonException("该箱码未找到工单信息");
        }
        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(srmBarcodeDetailVO.getSourceNo());
        if (boxCodeService != null) {
            getOldBoxCodeDetailOutVO.setCustomerNo(boxCodeService.getCompanyCode());
        }
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(srmBarcodeDetailVO.getSourceNo());
        ImaalTVO imaalTVO = iImaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTVO != null) {
            getOldBoxCodeDetailOutVO.setProductName(imaalTVO.getImaal003());
        }

        getOldBoxCodeDetailOutVO.setPlanTicketNo(srmBarcodeDetailVO.getSourceNo());

        getOldBoxCodeDetailOutVO.setMaterialCode(srmBarcodeDetailVO.getItemNo());
        if (StringUtil.isNotBlank(srmBarcodeDetailVO.getBarcodeTotal())) {
            getOldBoxCodeDetailOutVO.setBoxNum(Long.valueOf(srmBarcodeDetailVO.getBarcodeTotal()));
        }
        getOldBoxCodeDetailOutVO.setProductionDate(srmBarcodeDetailVO.getLotDate());
        getOldBoxCodeDetailOutVO.setLotNo(srmBarcodeDetailVO.getLotNo());
        getOldBoxCodeDetailOutVO.setBatchCode(srmBarcodeDetailVO.getJ());
        getOldBoxCodeDetailOutVO.setMachine(srmBarcodeDetailVO.getK());
        getOldBoxCodeDetailOutVO.setShift(srmBarcodeDetailVO.getL());
        getOldBoxCodeDetailOutVO.setExpirationDate(srmBarcodeDetailVO.getM());

        return getOldBoxCodeDetailOutVO;

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String copyOldBoxCodeToNewBoxCode(CopyOldBoxCodeToNewBoxCodeInVO copyOldBoxCodeToNewBoxCodeInVO) {
        GetOldBoxCodeDetailOutVO oldBoxCodeDetailOutVO = copyOldBoxCodeToNewBoxCodeInVO.getOldBoxCodeDetailOutVO();
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS =
            productBoxBarcodeDetailDetailService.getByBarcodeNos(copyOldBoxCodeToNewBoxCodeInVO.getNewBoxCodeList());
        List<String> barcodeList = productBoxBarcodeDetailDetailPOS.stream()
            .map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).distinct().collect(Collectors.toList());
        List<String> notInBarcodes = Lists.newArrayList();
        Map<String, ProductBoxBarcodeDetailDetailPO> detailPOMap = productBoxBarcodeDetailDetailPOS.stream()
            .collect(Collectors.toMap(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, Function.identity()));
        MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(oldBoxCodeDetailOutVO.getMaterialCode());

        List<ProductBoxBarcodeDetailDetailPO> pos = Lists.newArrayList();
        copyOldBoxCodeToNewBoxCodeInVO.getNewBoxCodeList().stream().forEach(newBoxCode -> {
            if (barcodeList.contains(newBoxCode)) {
                ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = detailPOMap.get(newBoxCode);
                if (!productBoxBarcodeDetailDetailPO.getCompanyCode().equals(SecurityUtil.getCompanySite())) {
                    throw new CommonException(newBoxCode + "该条码不属于当前据点");
                }
                if (StringUtil.isNotEmpty(productBoxBarcodeDetailDetailPO.getPlanTicketNo())) {
                    throw new CommonException("箱码"+newBoxCode+"不是空白箱码无法被旧箱码替换");
                }
                productBoxBarcodeDetailDetailPO.setBoxNum(oldBoxCodeDetailOutVO.getBoxNum());
                productBoxBarcodeDetailDetailPO.setPlanTicketNo(oldBoxCodeDetailOutVO.getPlanTicketNo());
                productBoxBarcodeDetailDetailPO.setCustomerNo(oldBoxCodeDetailOutVO.getCustomerNo());
                productBoxBarcodeDetailDetailPO.setProductName(oldBoxCodeDetailOutVO.getProductName());
                productBoxBarcodeDetailDetailPO.setMaterialCode(oldBoxCodeDetailOutVO.getMaterialCode());
                productBoxBarcodeDetailDetailPO.setProductionDate(oldBoxCodeDetailOutVO.getProductionDate());
                productBoxBarcodeDetailDetailPO.setLotNo(oldBoxCodeDetailOutVO.getLotNo());
                productBoxBarcodeDetailDetailPO.setBatchCode(oldBoxCodeDetailOutVO.getBatchCode());
                productBoxBarcodeDetailDetailPO.setMachine(oldBoxCodeDetailOutVO.getMachine());
                productBoxBarcodeDetailDetailPO.setShift(oldBoxCodeDetailOutVO.getShift());
                productBoxBarcodeDetailDetailPO.setExpirationDate(oldBoxCodeDetailOutVO.getExpirationDate());
                productBoxBarcodeDetailDetailPO.setUpdateBy(SecurityUtil.getUserId());
                productBoxBarcodeDetailDetailPO.setUpdateTime(new Date());
                if (materialsInfoDTO != null) {
                    if (StringUtil.isBlank(materialsInfoDTO.getImaaua017()) || StringUtil.isBlank(materialsInfoDTO.getImaaua018())) {
                        throw new CommonException("找不到工单的麦中8位码信息请联系相关人员维护后再次申请");
                    }
                    productBoxBarcodeDetailDetailPO.setSkuCode(materialsInfoDTO.getImaaua017());
                }
                pos.add(productBoxBarcodeDetailDetailPO);
            } else {
                notInBarcodes.add(newBoxCode);
            }
        });

        if (CollectionUtil.isNotEmpty(notInBarcodes)) {
            throw new CommonException("新箱码中有不存在的条码：" + StringUtil.join(notInBarcodes, ","));
        }

        // 保存
        productBoxBarcodeDetailDetailService.updateBatchById(pos);
        List<String> activeFailList = Lists.newArrayList();
        IBoxCodeService boxCodeService =
                boxCodeHandlerFactory.getBoxCodeService(oldBoxCodeDetailOutVO.getPlanTicketNo());
        // 激活
        pos.stream().forEach(productBoxBarcodeDetailDetailPO -> {
            boxCodeService.activeBoxCode(productBoxBarcodeDetailDetailPO.getBarcodeNo());
        });


        if (CollectionUtil.isNotEmpty(activeFailList)) {
            return "激活失败：" + StringUtil.join(activeFailList, ",");
        } else {
            return "替换并激活成功";
        }
    }

    @RedisLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String printPMJBoxBarcode(String lock_key, PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        List<String> list = productBoxBarcodeDetailService.printPMJBoxBarcode(lock_key, Math.toIntExact(printPMJBoxBarcodeInVO.getId()));
        YinKeLiDTO yinKeLiDTO = new YinKeLiDTO();
        yinKeLiDTO.setIp(printPMJBoxBarcodeInVO.getIp());
        yinKeLiDTO.setPort(printPMJBoxBarcodeInVO.getPort());
        yinKeLiDTO.setMessage(list.stream().collect(Collectors.joining(",")));
        return yinKeLiService.print(yinKeLiDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String firstPrintPMJBoxBarcode(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        YinKeLiDTO yinKeLiDTO = new YinKeLiDTO();
        yinKeLiDTO.setIp(printPMJBoxBarcodeInVO.getIp());
        yinKeLiDTO.setPort(printPMJBoxBarcodeInVO.getPort());
        yinKeLiDTO.setMessage("JHXGAB12CD34EF56GH78JL01");
        return yinKeLiService.print(yinKeLiDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String rePrintPMJBoxBarcode(String lock_key, PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        List<String> list = productBoxBarcodeDetailService.rePrintPMJBoxBarcode(lock_key, Math.toIntExact(printPMJBoxBarcodeInVO.getId()));
        YinKeLiDTO yinKeLiDTO = new YinKeLiDTO();
        yinKeLiDTO.setIp(printPMJBoxBarcodeInVO.getIp());
        yinKeLiDTO.setPort(printPMJBoxBarcodeInVO.getPort());
        yinKeLiDTO.setMessage(list.stream().collect(Collectors.joining(",")));
        return yinKeLiService.print(yinKeLiDTO);
    }

    @Override
    public Object getClintList() {
        return yinKeLiService.getClintList();
    }

    @Override
    public void printOpen(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        YinKeLiDTO yinKeLiDTO = new YinKeLiDTO();
        yinKeLiDTO.setIp(printPMJBoxBarcodeInVO.getIp());
        yinKeLiDTO.setPort(printPMJBoxBarcodeInVO.getPort());
        yinKeLiService.open(yinKeLiDTO);
    }

    @Override
    public void printClose(PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        YinKeLiDTO yinKeLiDTO = new YinKeLiDTO();
        yinKeLiDTO.setIp(printPMJBoxBarcodeInVO.getIp());
        yinKeLiDTO.setPort(printPMJBoxBarcodeInVO.getPort());
        yinKeLiService.close(yinKeLiDTO);
    }


}
