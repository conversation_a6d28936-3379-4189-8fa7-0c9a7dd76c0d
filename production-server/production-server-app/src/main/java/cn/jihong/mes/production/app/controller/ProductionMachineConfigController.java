package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 机台配置表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@RestController
@RequestMapping("/productionMachineConfig")
@ShenyuSpringMvcClient(path = "/productionMachineConfig/**")
public class ProductionMachineConfigController {

    @DubboReference
    private IProductionMachineConfigService productionMachineConfigService;


    /**
     * 判断机台是否需要机位
     * @param machineConfigInVO
     * @return
     */
    @PostMapping("/getMachineParts")
    public StandardResult getMachineParts(@RequestBody @Valid MachineConfigInVO machineConfigInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productionMachineConfigService.getMachineParts(machineConfigInVO));
    }

    /**
     * 获得结算方式   1 一单一结   2  一日一结
     */
    @PostMapping("/getBillingType")
    public StandardResult<Integer> getBillingType(@RequestBody @Valid MachineConfigInVO machineConfigInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productionMachineConfigService.getBillingType(machineConfigInVO));
    }


//    /**
//     * 获取车间列表
//     * @param
//     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.mes.api.model.vo.WorkshopOutVO>>
//     * <AUTHOR>
//     * @date: 2025-03-11 14:59
//     */
//    @GetMapping("/getAllWorkshop")
//    public StandardResult<List<WorkshopOutVO>> getAllWorkshop() {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productionMachineConfigService.getAllWorkshop());
//    }



}

