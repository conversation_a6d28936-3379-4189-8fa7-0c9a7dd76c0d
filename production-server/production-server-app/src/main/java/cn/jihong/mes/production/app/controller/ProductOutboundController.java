package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.po.ProductOutboundPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetInboundQuantityOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetTemporaryStoragePushWmsAndErpOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetTemporaryStorageSaveOutboundAndDefectiveOutVO;
import cn.jihong.mes.production.api.model.vo.out.OutboundInfoOutVO;
import cn.jihong.mes.production.api.service.IProductOutboundService;
import cn.jihong.mes.production.app.service.boxCode.CompanyMDLBoxCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 出站信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@RestController
@RequestMapping("/productOutbound")
@ShenyuSpringMvcClient(path = "/productOutbound/**")
public class ProductOutboundController {

    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private CompanyMDLBoxCodeService companyMDLBoxCodeService;

    /**
     * 根据id获取出站详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.po.ProductOutboundPO>
     * <AUTHOR>
     * @date: 2023/11/21 10:41
     */
    @GetMapping("/getSingleById/{id}")
    public StandardResult<ProductOutboundPO> getSingleById(@PathVariable Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,productOutboundService.getById(id));
    }

//    /**
//     * 保存出站和报不良信息
//     */
//    @PostMapping("/saveOutboundAndDefectiveInfo")
//    public StandardResult saveOutboundAndDefectiveInfo(@RequestBody @Valid SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productOutboundService.saveOutboundAndDefectiveInfo(saveOutboundAndDefectiveInfo));
//    }

    /**
     * 更新出站数量
     */
    @PostMapping("/updateOutbound")
    public StandardResult updateOutbound(@RequestBody @Valid UpdateOutboundInVO updateOutboundInVO) {
        productOutboundService.updateOutbound(updateOutboundInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


//    /**
//     * 保存出站信息(废弃)
//     */
//    @PostMapping("/saveOutboundInfo")
//    public StandardResult saveOutboundInfo(@RequestBody @Valid SaveOutboundInfoInVO saveOutboundInfoInVO) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productOutboundService.saveOutboundInfo(saveOutboundInfoInVO));
//    }

    /**
     * 暂存出站和报不良信息
     */
    @PostMapping("/temporaryStorageSaveOutboundAndDefective")
    public StandardResult<Boolean> temporaryStorageSaveOutboundAndDefective(@RequestBody TemporaryStorageSaveOutboundAndDefectiveInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.temporaryStorageSaveOutboundAndDefective(inVO));
    }

    /**
     * 获取暂存出站和报不良信息
     */
    @PostMapping("/getTemporaryStorageSaveOutboundAndDefective")
    public StandardResult<GetTemporaryStorageSaveOutboundAndDefectiveOutVO> getTemporaryStorageSaveOutboundAndDefective(@RequestBody @Valid GetTemporaryStorageSaveOutboundAndDefectiveInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getTemporaryStorageSaveOutboundAndDefective(inVO));
    }

//    /**
//     * 推送wms和erp 入库申请 -- 按箱入库
//     */
//    @PostMapping("/pushWmsAndErp")
//    public StandardResult pushWmsAndErp(@RequestBody @Valid PushWmsAndErpInVO pushWmsAndErpInVO) {
//        String LOCK_KEY = RedisCacheConstant.STORAGE  + pushWmsAndErpInVO.getPalletCode();
//        productOutboundService.pushWmsAndErp(LOCK_KEY,pushWmsAndErpInVO);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }

//    /**
//     * 推送wms和erp 入库申请 -- 按箱入库  批量
//     */
//    @PostMapping("/pushWmsAndErps")
//    public StandardResult pushWmsAndErps(@RequestBody @Valid BatchPushWmsAndErpInVO batchPushWmsAndErpInVO) {
//        String LOCK_KEY = RedisCacheConstant.STORAGE  + batchPushWmsAndErpInVO.getPushWmsAndErpInVOs().get(0).getPalletCode();
//        productOutboundService.pushWmsAndErps(LOCK_KEY,batchPushWmsAndErpInVO);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }

//    /**
//     * 入库申请 -- 按托入库
//     */
//    @PostMapping("/inboundRequestByPallet")
//    public StandardResult inboundRequestByPallet(@RequestBody @Valid InboundRequestByPalletInVO inboundRequestByPalletInVO) {
//        String LOCK_KEY = RedisCacheConstant.STORAGE  + inboundRequestByPalletInVO.getPlanTicketNo();
//        productOutboundService.inboundRequestByPallet(LOCK_KEY,inboundRequestByPalletInVO);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }

    /**
     * 暂存推送wms和erp信息
     */
    @PostMapping("/temporaryStoragePushWmsAndErp")
    public StandardResult<Boolean> temporaryStoragePushWmsAndErp(@RequestBody TemporaryStoragePushWmsAndErpInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productOutboundService.temporaryStoragePushWmsAndErp(inVO));
    }

    /**
     * 获取暂存推送wms和erp信息
     */
    @PostMapping("/getTemporaryStoragePushWmsAndErp")
    public StandardResult<GetTemporaryStoragePushWmsAndErpOutVO> getTemporaryStoragePushWmsAndErp(@RequestBody @Valid GetTemporaryStoragePushWmsAndErpInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getTemporaryStoragePushWmsAndErp(inVO));
    }


    /**
     * 校验箱码是否使用过
     */
    @PostMapping("/verifyBoxCode")
    public StandardResult<String> verifyBoxCode(@RequestBody @Valid VerifyCaseCodeInVO verifyCaseCodeInVO) {
        String LOCK_KEY = RedisCacheConstant.VERIFY_BOX_CODE + verifyCaseCodeInVO.getBoxCode();
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productOutboundService.verifyBoxCode(LOCK_KEY,verifyCaseCodeInVO));
    }

    /**
     * 激活箱码
     */
    @PostMapping("/activeBoxCode")
    public StandardResult<String> activeBoxCode(@RequestBody @Valid VerifyCaseCodeInVO verifyCaseCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                companyMDLBoxCodeService.activeBoxCode(verifyCaseCodeInVO.getBoxCode()));
    }

    /**
     * 消箱 多个
     */
    @PostMapping("/destroyBoxCodes")
    public StandardResult<String> destroyBoxCodes(@RequestBody @Valid DestoryBoxInVO destoryBoxInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productOutboundService.destroyBoxCodes(destoryBoxInVO));
    }

    /**
     * 消箱  单个
     */
    @PostMapping("/destroyBoxCode")
    public StandardResult<String> destroyBoxCode(@RequestBody @Valid DestoryBoxInVO destoryBoxInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productOutboundService.destroyBoxCode(destoryBoxInVO.getBoxCode()));
    }

    /**
     * 换箱
     */
    @PostMapping("/changeBoxCode")
    public StandardResult<String> changeBoxCode(@RequestBody @Valid ChangeBoxInVO changeBoxInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productOutboundService.changeBoxCode(changeBoxInVO));
    }


    /**
     * 校验栈板码是否使用过
     */
    @PostMapping("/verifyPalletCode")
    public StandardResult<String> verifyPalletCode(@RequestBody @Valid VerifyPalletCodeInVO VerifyPalletCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productOutboundService.verifyPalletCode(VerifyPalletCodeInVO));
    }


    /**
     * 查询出站记录
     */
    @PostMapping("/getOutboundRecords")
    public StandardResult<Pagination<OutboundInfoOutVO>>
        getOutboundRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productOutboundService.getOutboundRecords(productTicketPageInVO));
    }

    /**
     * 查询栈板信息(未出站的栈板信息)
     */
    @PostMapping("/getPallet")
    public StandardResult<OutboundInfoOutVO> getPallet(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getPalletByMachineName(getByMachineNameInVO));
    }


    /**
     * 根据工单id获取对应的工序的单位
     * @param productTicketId
     * @return
     */
    @GetMapping("/getUnit/{productTicketId}")
    public StandardResult<String> getUnit(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getUnit(productTicketId));
    }

    /**
     * 根据工程单号获取对应的单位
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getUnitByPlanTicketNo/{planTicketNo}")
    public StandardResult<String> getUnitByPlanTicketNo(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getUnitByPlanTicketNo(planTicketNo));
    }

    /**
     * 获得箱码规格
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getBoxSpecs/{planTicketNo}")
    public StandardResult<BigDecimal> getBoxSpecs(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getBoxSpecs(planTicketNo));
    }


    /**
     * 获得入库数量
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getInboundQuantity/{planTicketNo}")
    public StandardResult<GetInboundQuantityOutVO> getInboundQuantity(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productOutboundService.getInboundQuantity(planTicketNo));
    }


    /**
     * 推送erp  --- 测试  入库申请
     */
    @GetMapping("/testA/{timeStamp}/{planTicketNo}")
    public StandardResult testA(@PathVariable String timeStamp,@PathVariable String planTicketNo) {
        productOutboundService.testA(timeStamp,planTicketNo);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }



}
