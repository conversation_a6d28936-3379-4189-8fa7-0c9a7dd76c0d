package cn.jihong.mes.production.app.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @Description
 * @Date 2023/7/21 11:10
 * @Version 1.0
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "aliyun.oss")
public class AliyunOssProperty {

    private String accessId;

    private String accessKey;

    @Value("${aliyun.oss.endpoint.hz}")
    private String hzEndpoint;

    @Value("${aliyun.oss.buckets.commission.bucketName}")
    private String commissionBucketName;

    @Value("${aliyun.oss.buckets.commission.templateUrl}")
    private String commissionTemplateUrl;

    @Value("${aliyun.oss.buckets.commission.userCommissionSummaryTemplateUrl}")
    private String userCommissionSummaryTemplateUrl;

    @Value("${aliyun.oss.buckets.commission.productScrapTemplateUrl}")
    private String productScrapTemplateUrl;
}
