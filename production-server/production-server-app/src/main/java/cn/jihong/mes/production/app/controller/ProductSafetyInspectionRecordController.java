package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordMonthlyInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordMonthlyOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordOutVO;
import cn.jihong.mes.production.api.service.IProductSafetyInspectionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 安全点检记录 控制器
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@RestController
@RequestMapping("/production/safety/inspection/record")
@ShenyuSpringMvcClient(path = "/production/safety/inspection/record/**")
public class ProductSafetyInspectionRecordController {

    @Resource
    private IProductSafetyInspectionRecordService productSafetyInspectionRecordService;

//    /**
//     * 分页查询安全点检记录
//     *
//     * @param inVO 查询参数
//     * @return 分页结果
//     */
//    @PostMapping("/page")
//    public StandardResult<Pagination<ProductSafetyInspectionRecordOutVO>> page(
//            @RequestBody @Valid ProductSafetyInspectionRecordQueryInVO inVO) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productSafetyInspectionRecordService.page(inVO));
//    }
//
//    /**
//     * 获取安全点检记录详情
//     *
//     * @param id 记录ID
//     * @return 记录详情
//     */
//    @GetMapping("/{id}")
//    public StandardResult<ProductSafetyInspectionRecordOutVO> getDetail(@PathVariable("id") Long id) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productSafetyInspectionRecordService.getDetail(id));
//    }

    /**
     * 保存安全点检记录
     *
     * @param inVO 保存参数
     * @return 保存结果
     */
    @PostMapping
    public StandardResult<Boolean> save(@RequestBody @Valid ProductSafetyInspectionRecordSaveInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
            productSafetyInspectionRecordService.save(inVO));
    }

//    /**
//     * 删除安全点检记录
//     *
//     * @param id 记录ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/{id}")
//    public StandardResult<Boolean> delete(@PathVariable("id") Long id) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productSafetyInspectionRecordService.delete(id));
//    }

//    /**
//     * 根据机台名称、日期和班次获取点检记录
//     *
//     * @param machineName 机台名称
//     * @param produceDate 生产日期
//     * @param shift 班次
//     * @return 点检记录
//     */
//    @GetMapping("/getByMachineDateShift")
//    public StandardResult<ProductSafetyInspectionRecordOutVO> getByMachineDateShift(
//            @RequestParam("machineName") String machineName,
//            @RequestParam("produceDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date produceDate,
//            @RequestParam("shift") Integer shift) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productSafetyInspectionRecordService.getByMachineDateShift(machineName, produceDate, shift));
//    }

//    /**
//     * 获取某日期的所有机台点检记录
//     *
//     * @param produceDate 生产日期
//     * @return 点检记录列表
//     */
//    @GetMapping("/listByDate")
//    public StandardResult<List<ProductSafetyInspectionRecordOutVO>> getListByDate(
//            @RequestParam("produceDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date produceDate) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productSafetyInspectionRecordService.getListByDate(produceDate));
//    }

    /**
     * 按月查询安全点检记录
     *
     * @param inVO 查询参数
     * @return 点检记录列表
     */
    @PostMapping("/getMonthlyRecords")
    public StandardResult<List<ProductSafetyInspectionRecordMonthlyOutVO>> getMonthlyRecords(
            @RequestBody @Valid ProductSafetyInspectionRecordMonthlyInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
            productSafetyInspectionRecordService.getMonthlyRecords(inVO));
    }

    /**
     * 判断当天指定班次是否可以提交点检记录
     * @return true-可以提交，false-不可提交
     */
    @PostMapping("/canSubmitTodayShift")
    public StandardResult<Boolean> canSubmitTodayShift(@RequestBody ProductTicketVO productTicketVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
            productSafetyInspectionRecordService.canSubmitTodayShift(productTicketVO));
    }

    /**
     * 获取当前时间对应的生产日期和班次
     *
     * @return 当前班次信息
     */
    @GetMapping("/getCurrentShiftInfo")
    public StandardResult<ProductSafetyInspectionRecordOutVO> getCurrentShiftInfo() {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
            productSafetyInspectionRecordService.getCurrentShiftInfo());
    }
} 