package cn.jihong.mes.production.app.service;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeRecordDTO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeRecordPO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeRecordService;
import cn.jihong.mes.production.app.mapper.ProductBoxBarcodeRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;

/**
 * 条码操作记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@DubboService
public class ProductBoxBarcodeRecordServiceImpl extends JiHongServiceImpl<ProductBoxBarcodeRecordMapper, ProductBoxBarcodeRecordPO> implements IProductBoxBarcodeRecordService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveProductBoxBarcodeRecord(ProductBoxBarcodeRecordDTO productBoxBarcodeRecordDTO) {
        ProductBoxBarcodeRecordPO productBoxBarcodeRecordPO =
            BeanUtil.copyProperties(productBoxBarcodeRecordDTO, ProductBoxBarcodeRecordPO.class);
        productBoxBarcodeRecordPO.setId(null);
        save(productBoxBarcodeRecordPO);
    }
}
