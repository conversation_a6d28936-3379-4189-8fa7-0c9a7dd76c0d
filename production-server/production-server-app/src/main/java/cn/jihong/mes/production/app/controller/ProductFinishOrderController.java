package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.SaveFinishOrderInfoInVO;
import cn.jihong.mes.production.api.service.IProductFinishOrderService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 结单信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productFinishOrder")
@ShenyuSpringMvcClient(path = "/productFinishOrder/**")
public class ProductFinishOrderController {

    @Resource
    private IProductFinishOrderService productFinishOrderService;

   /* *//**
     * 结单
     *//*
    @PostMapping("/saveFinishOrderInfo")
    public StandardResult saveFinishOrderInfo(@RequestBody @Valid SaveFinishOrderInfoInVO saveFinishOrderInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productFinishOrderService.saveFinishOrderInfo(saveFinishOrderInfoInVO));
    }*/

}

