package cn.jihong.mes.production.app.controller.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetMeshListInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.ItemQueryByConditionInVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetMeshListOutVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.ItemQueryByConditionOutVO;
import cn.jihong.mes.production.api.service.IMeshService;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 网带
 * <AUTHOR>
 * @date 2025-03-11 15:11
 */
@RestController
@RequestMapping("/mesh")
@ShenyuSpringMvcClient(path = "/mesh/**")
public class MeshController {

    @Resource
    private IMeshService iMeshService;

    /**
     * 根据车间获取网带列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.OutboundStackerOutVO>>
     */
    @PostMapping("/getMeshList")
    public StandardResult<List<GetMeshListOutVO>> getMeshList(@RequestBody @Valid GetMeshListInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iMeshService.getMeshList(inVO));
    }
}
