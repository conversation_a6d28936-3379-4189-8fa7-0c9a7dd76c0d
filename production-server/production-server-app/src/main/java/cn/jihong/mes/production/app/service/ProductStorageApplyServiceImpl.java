package cn.jihong.mes.production.app.service;


import org.apache.dubbo.config.annotation.DubboService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.po.ProductStorageApplyPO;
import cn.jihong.mes.production.api.model.vo.in.GetStorageApplyInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO;
import cn.jihong.mes.production.api.service.IProductStorageApplyService;
import cn.jihong.mes.production.app.mapper.ProductStorageApplyMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;

/**
 * 入库申请表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@DubboService
public class ProductStorageApplyServiceImpl extends JiHongServiceImpl<ProductStorageApplyMapper, ProductStorageApplyPO> implements IProductStorageApplyService {

    @Override
    public Page<GetStorageApplyInfoOutVO> getStorageApplyInfo(GetStorageApplyInfoInVO getStorageApplyInfoInVO) {
        return baseMapper.getStorageApplyInfo(getStorageApplyInfoInVO.getPage(),getStorageApplyInfoInVO);
    }

    @Override
    public String getPlanTicketNoByPalletCode(String palletCode) {
        return baseMapper.getPlanTicketNoByPalletCode(palletCode);
    }
}
