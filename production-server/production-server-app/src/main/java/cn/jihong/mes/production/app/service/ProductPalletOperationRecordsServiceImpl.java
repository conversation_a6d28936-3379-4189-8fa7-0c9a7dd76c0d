package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.enums.PalletOperateTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductPalletOperationRecordsPO;
import cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO;
import cn.jihong.mes.production.api.service.IProductPalletOperationRecordsService;
import cn.jihong.mes.production.app.event.ProductPalletEvent;
import cn.jihong.mes.production.app.mapper.ProductPalletOperationRecordsMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 栈板操作记录信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Service
public class ProductPalletOperationRecordsServiceImpl
    extends JiHongServiceImpl<ProductPalletOperationRecordsMapper, ProductPalletOperationRecordsPO>
    implements IProductPalletOperationRecordsService {

    private ApplicationEventPublisher eventPublisher;

    @Autowired
    public void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Override
    public void saveOrUpdatePalletOperationRecords(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) {
        save(BeanUtil.copyProperties(productPalletOperationRecordsDTO, ProductPalletOperationRecordsPO.class));
    }

    @Override
    public void work(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO,String code) {
        ProductPalletEvent productEvent =
                new ProductPalletEvent(this, code, productPalletOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    @Override
    public void signingUpWork(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) {
        ProductPalletEvent productEvent =
                new ProductPalletEvent(this, PalletOperateTypeEnum.SIGNING_UP_WORK.getCode(), productPalletOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    @Override
    public void outBound(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) {
        ProductPalletEvent productEvent =
                new ProductPalletEvent(this, PalletOperateTypeEnum.OUT_BOUND.getCode(), productPalletOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    @Override
    public void downPallet(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) {
        ProductPalletEvent productEvent =
                new ProductPalletEvent(this, PalletOperateTypeEnum.DOWN_PALLET.getCode(), productPalletOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    @Override
    public void upPallet(ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO) {
        ProductPalletEvent productEvent =
                new ProductPalletEvent(this, PalletOperateTypeEnum.UP_PALLET.getCode(), productPalletOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    @Override
    public Page<LastPalletRecordsOutVO> getLastPalletRecords(IPage page, Long productTicketId) {
        return baseMapper.getLastPalletRecords(page, productTicketId, SecurityUtil.getCompanySite());
    }

    @Override
    public Page<LastPalletRecordsOutVO> getLastPalletRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        return baseMapper.getLastPalletRecordsByTicketBase(page, productTicketBaseDTO);
    }

    @Override
    public List<ProductPalletOperationRecordsDTO> getByProductTicketIds(List<Long> productTicketIds, List<Integer> operationTypes) {
        return baseMapper.getByProductTicketIds(productTicketIds, operationTypes);
    }

    @Override
    public ProductPalletOperationRecordsPO getOutBoundByPalletId(Long palletId) {
        LambdaQueryWrapper<ProductPalletOperationRecordsPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductPalletOperationRecordsPO::getProductPalletId,palletId)
                .eq(ProductPalletOperationRecordsPO::getOperationType,PalletOperateTypeEnum.OUT_BOUND.getCode())
                .orderByDesc(ProductPalletOperationRecordsPO::getId);
        List<ProductPalletOperationRecordsPO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public Page<LastPalletRecordsOutVO> getDownPalletRecords(IPage page, Long productTicketId) {
        return baseMapper.getDownPalletRecords(page, productTicketId, SecurityUtil.getCompanySite());
    }
}
