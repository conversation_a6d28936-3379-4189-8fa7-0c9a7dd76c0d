package cn.jihong.mes.production.app.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductMachineTaskHistoryService;
import cn.jihong.mes.production.api.service.IProductMachineTaskService;

/**
 * 生产机台任务
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@RestController
@RequestMapping("/productMachineTask")
@ShenyuSpringMvcClient(path = "/productMachineTask/**")
public class ProductMachineTaskController {

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;


    @Resource
    private IProductMachineTaskHistoryService iProductMachineTaskHistoryService;

    /**
     * 开始机台任务
     */
    @PostMapping("/saveMachineTask")
    public StandardResult<Boolean> saveMachineTask(@RequestBody @Valid SaveMachineTaskInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.saveMachineTask(RedisCacheConstant.SAVE_MACHINE_TASK + inVO.getMachineName(),inVO));
    }


    /**
     * 稼动上报(人工)
     */
    @PostMapping("/operationReport")
    public StandardResult<Boolean> operationReport(@RequestBody @Valid OperationReportInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.operationReport(RedisCacheConstant.OPERATION_REPORT + inVO.getMachineName(),inVO));
    }

    /**
     * 稼动上报(设备)
     */
    @PostMapping("/equipmentOperationReport")
    public StandardResult<ProductMachineTaskPO> equipmentOperationReport(@RequestBody @Valid EquipmentOperationReportInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.equipmentOperationReport(inVO));
    }


    /**
     * 停止机台任务
     */
    @PostMapping("/stopMachineTask")
    public StandardResult<Boolean> stopMachineTask(@RequestBody @Valid StopMachineTaskInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.stopMachineTask(inVO.getMachineName(),null,null));
    }


    /**
     * 获取工作中的机台任务根据机台名称
     */
    @PostMapping("/getMachineTaskByName")
    public StandardResult<GetMachineTaskByNameOutVO> getMachineTaskByName(@RequestBody @Valid GetMachineTaskByNameInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.getMachineTaskByName(inVO));
    }

    /**
     * 下机
     */
    @PostMapping("/finishMachineTask")
    public StandardResult<Boolean> finishMachineTask(@RequestBody @Valid FinishMachineTaskInVO inVO) {
        //结束机台状态
        inVO.setFinished(BooleanEnum.TRUE.getCode());
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.finishMachineTask(RedisCacheConstant.FINISH_MACHINE_TASK + inVO.getId(),inVO));
    }

    /**
     * 获取报工界面的机台状态
     */
    @GetMapping("/getMachineTaskTypeEnum")
    public StandardResult<List<GetMachineTaskTypeEnumOutVO>> getMachineTaskTypeEnum() {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.getMachineTaskTypeEnum());
    }


    /**
     * 获取长期待机的机台状态
     */
    @GetMapping("/getLongStandbyMachineTaskTypeEnum")
    public StandardResult<List<GetLongStandbyMachineTaskTypeEnumOutVO>> getLongStandbyMachineTaskTypeEnum() {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMachineTaskService.getLongStandbyMachineTaskTypeEnum());
    }

    /**
     * 设备稼动记录
     */
    @PostMapping("/getProductMachineTaskPage")
    public StandardResult<Pagination<GetProductMachineTaskPageOutVO>> getProductMachineTaskPage(@RequestBody @Valid GetProductMachineTaskPageInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskService.getProductMachineTaskPage(inVO));
    }

    /**
     * 报工记录
     */
    @PostMapping("/getProductMachineTaskHistoryPage")
    public StandardResult<Pagination<GetProductMachineTaskHistoryPageOutVO>> getProductMachineTaskHistoryPage(@RequestBody @Valid GetProductMachineTaskHistoryPageInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskHistoryService.getProductMachineTaskHistoryPage(inVO));
    }

    /**
     * 修改报工记录
     */
    @PostMapping("/updateMachineTaskHistory")
    public StandardResult<Boolean> updateMachineTaskHistory(@RequestBody @Valid UpdateMachineTaskHistoryInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskHistoryService.updateMachineTaskHistory(inVO));
    }


    /**
     * 获取换版信息
     */
    @PostMapping("/getChangeVersionInfo")
    public StandardResult<List<GetChangeVersionInfoOutVO>> getChangeVersionInfo(@RequestBody @Valid GetChangeVersionInfoInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskHistoryService.getChangeVersionInfo(inVO));
    }


    /**
     * 换版记录
     */
    @PostMapping("/getChangeVersionHistoryPage")
    public StandardResult<Pagination<GetChangeVersionHistoryPageOutVO>> getChangeVersionHistoryPage(@RequestBody @Valid GetChangeVersionHistoryPageInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskHistoryService.getChangeVersionHistoryPage(inVO));
    }

    /**
     * 修改换版记录
     */
    @PostMapping("/updateChangeVersion")
    public StandardResult<Boolean> updateChangeVersion(@RequestBody @Valid UpdateChangeVersionInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductMachineTaskHistoryService.updateChangeVersion(inVO));
    }

}

