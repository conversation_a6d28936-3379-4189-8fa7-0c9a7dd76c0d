package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.LogisticsProductConfigDTO;
import cn.jihong.mes.production.app.service.LogisticsProductConfigServiceImpl;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 物流产品配置信息
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@RequestMapping("/logisticsProductConfig")
@ShenyuSpringMvcClient(path = "/logisticsProductConfig/**")
public class LogisticsProductConfigController {

    @Resource
    private LogisticsProductConfigServiceImpl logisticsProductConfigService;

    /**
     * 获取物流产品配置信息
     *
     * @param logisticsProductConfigDTO 物流产品配置信息
     * @return 物流
     *
     */
    @RequestMapping("/getLogisticsProductConfig")
    public StandardResult<LogisticsProductConfigDTO>
        getLogisticsProductConfig(@RequestBody @Valid LogisticsProductConfigDTO logisticsProductConfigDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            logisticsProductConfigService.getLogisticsProductConfig(logisticsProductConfigDTO));
    }

    /**
     * 获取物流产品配置信息列表
     * @return
     */
    @RequestMapping("/getList")
    public StandardResult<Pagination<LogisticsProductConfigDTO>> getList(@RequestBody @Valid LogisticsProductConfigDTO logisticsProductConfigDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, logisticsProductConfigService.getList(logisticsProductConfigDTO));
    }

    /**
     * 保存物流产品配置信息
     * @param logisticsProductConfigDTO
     * @return
     */
    @RequestMapping("/saveLogisticsProductConfig")
    public StandardResult<LogisticsProductConfigDTO>
        saveLogisticsProductConfig(@RequestBody @Valid LogisticsProductConfigDTO logisticsProductConfigDTO) {
        logisticsProductConfigService.saveLogisticsProductConfig(logisticsProductConfigDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 更新物流产品配置信息
     * @param logisticsProductConfigDTO
     * @return
     */
    @RequestMapping("/updateLogisticsProductConfig")
    public StandardResult<LogisticsProductConfigDTO>
        updateLogisticsProductConfig(@RequestBody @Valid LogisticsProductConfigDTO logisticsProductConfigDTO) {
        logisticsProductConfigService.updateLogisticsProductConfig(logisticsProductConfigDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 删除物流产品配置信息
     * @param id
     * @return
     */
    @PostMapping("/delete/{id}")
    public StandardResult delete(@PathVariable Long id) {
        logisticsProductConfigService.delete(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 获得物料类别
     */
    @GetMapping("/materialCategory/{id}")
    public StandardResult<List<EnumDTO>> getMaterialCategory(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,logisticsProductConfigService.getMaterialCategory(id));
    }


    /**
     * 获得工序列表信息
     * @return
     */
    @GetMapping("/getProcessType")
    public StandardResult<List<EnumDTO>> getProcessType() {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                logisticsProductConfigService.getProcessType());
    }


}
