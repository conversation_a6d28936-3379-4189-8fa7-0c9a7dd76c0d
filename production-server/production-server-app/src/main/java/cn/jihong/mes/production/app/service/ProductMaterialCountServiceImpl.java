package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.dto.ProductMaterialCountDTO;
import cn.jihong.mes.production.api.model.po.ProductMaterialCountPO;
import cn.jihong.mes.production.api.service.IProductMaterialCountService;
import cn.jihong.mes.production.app.mapper.ProductMaterialCountMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机台上料和上栈板的数量 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Service
public class ProductMaterialCountServiceImpl extends JiHongServiceImpl<ProductMaterialCountMapper, ProductMaterialCountPO>
    implements IProductMaterialCountService {
    
    @DubboReference
    private IProductionMachineService productionMachineService;
    

    @Override
    public ProductMaterialCountDTO getPalletCountCount(String processtype, String palletSource) {
        LambdaQueryWrapper<ProductMaterialCountPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMaterialCountPO::getProcessType, processtype)
                .eq(ProductMaterialCountPO::getProcess,palletSource)
                .eq(ProductMaterialCountPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        // 使用MyBatis-Plus的分页查询，取第一页，一页一条数据
        Page<ProductMaterialCountPO> page = new Page<>(1, 1);
        // 使用Mapper接口的selectPage方法进行查询
        Page<ProductMaterialCountPO> result = page(page, lambdaQueryWrapper);

        // 获取查询到的记录列表中的第一条，即最新的一条记录
        ProductMaterialCountPO latestEntity = result.getRecords().isEmpty() ? null : result.getRecords().get(0);
        if (ObjectUtil.isNotNull(latestEntity)) {
            return BeanUtil.copyProperties(latestEntity,ProductMaterialCountDTO.class);
        }
        return null;
    }



    @Override
    public ProductMaterialCountDTO getMaterialCountCount(String processtype, String materialType,String materialPlace) {
        LambdaQueryWrapper<ProductMaterialCountPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMaterialCountPO::getProcessType, processtype)
                .eq(ProductMaterialCountPO::getMaterialType,materialType)
                .eq(ProductMaterialCountPO::getMaterialPlace,materialPlace)
                .eq(ProductMaterialCountPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        Page<ProductMaterialCountPO> page = new Page<>(1, 1);
        Page<ProductMaterialCountPO> result = page(page, lambdaQueryWrapper);

        // 获取查询到的记录列表中的第一条，即最新的一条记录
        ProductMaterialCountPO latestEntity = result.getRecords().isEmpty() ? null : result.getRecords().get(0);
        if (ObjectUtil.isNotNull(latestEntity)) {
            return BeanUtil.copyProperties(latestEntity,ProductMaterialCountDTO.class);
        }
        return null;
    }



    @Override
    public List<ProductMaterialCountDTO> GetList() {
        LambdaQueryWrapper<ProductMaterialCountPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMaterialCountPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()))
                .eq(ProductMaterialCountPO::getCompanyCode, SecurityUtil.getCompanySite());
        List<ProductMaterialCountPO> list = list(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(list)) {
            return BeanUtil.copyToList(list,ProductMaterialCountDTO.class);
        }
        return null;
    }

    @Override
    public void doAdd(ProductMaterialCountDTO ProductMaterialCountDTO) {
        ProductionMachineOutVO productionMachineOutVO = validate(ProductMaterialCountDTO);
        ProductMaterialCountPO ProductMaterialCountPO =
            BeanUtil.copyProperties(ProductMaterialCountDTO, ProductMaterialCountPO.class);
        ProductMaterialCountPO.setCompanyCode(productionMachineOutVO.getCompanyCode());
        save(ProductMaterialCountPO);
    }

    private ProductionMachineOutVO validate(ProductMaterialCountDTO ProductMaterialCountDTO) {
        if (StringUtils.isBlank(ProductMaterialCountDTO.getMachineName())) {
            throw new CommonException("机台名称不能为空");
        }
        if (StringUtils.isNotBlank(ProductMaterialCountDTO.getProcess())
            && StringUtils.isNotBlank(ProductMaterialCountDTO.getMaterialCode())) {
            throw new CommonException("不能同时添加上料和上栈板");
        }
        if (ProductMaterialCountDTO.getPalletCount() != null && ProductMaterialCountDTO.getMaterialCount() != null) {
            throw new CommonException("不能同时添加上料和上栈板数量");
        }
        if (StringUtils.isNotBlank(ProductMaterialCountDTO.getProcess())
            && ProductMaterialCountDTO.getPalletCount() == null) {
            throw new CommonException("上栈板数量不能为空");
        }
        if (StringUtils.isNotBlank(ProductMaterialCountDTO.getMaterialCode())
            && ProductMaterialCountDTO.getMaterialCount() == null) {
            throw new CommonException("上料数量不能为空");
        }
        List<ProductionMachineOutVO> productionMachineOutVOS =
                productionMachineService.getProductionMachineByErpName(ProductMaterialCountDTO.getMachineName());
        if (CollectionUtil.isEmpty(productionMachineOutVOS)){
            throw new CommonException("机台不存在");
        }
        return productionMachineOutVOS.get(0);
    }

    @Override
    public void doUpdate(ProductMaterialCountDTO ProductMaterialCountDTO) {
        ProductionMachineOutVO productionMachineOutVO = validate(ProductMaterialCountDTO);
        ProductMaterialCountPO ProductMaterialCountPO = BeanUtil.copyProperties(ProductMaterialCountDTO,
            ProductMaterialCountPO.class);
        ProductMaterialCountPO.setCompanyCode(productionMachineOutVO.getCompanyCode());
        updateById(ProductMaterialCountPO);
    }

    @Override
    public void doDelete(Integer id) {
        removeById(id);
    }
}
