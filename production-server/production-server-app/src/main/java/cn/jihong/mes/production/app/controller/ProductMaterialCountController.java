package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.ProductMaterialCountDTO;
import cn.jihong.mes.production.api.service.IProductMaterialCountService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产工程单信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@RestController
@RequestMapping("/ProductMaterialCount")
@ShenyuSpringMvcClient(path = "/ProductMaterialCount/**")
public class ProductMaterialCountController {

    @Resource
    private IProductMaterialCountService ProductMaterialCountService;

    @GetMapping("/list")
    public StandardResult<List<ProductMaterialCountDTO>> list() {
        return StandardResult.resultCode(OperateCode.SUCCESS, ProductMaterialCountService.GetList());
    }

    @PostMapping("/add")
    public StandardResult add(@RequestBody ProductMaterialCountDTO ProductMaterialCountDTO) {
        ProductMaterialCountService.doAdd(ProductMaterialCountDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    @PostMapping("/update")
    public StandardResult update(@RequestBody ProductMaterialCountDTO ProductMaterialCountDTO) {
        ProductMaterialCountService.doUpdate(ProductMaterialCountDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    @GetMapping("/delete/{id}")
    public StandardResult delete(@PathVariable("id") Integer id) {
        ProductMaterialCountService.doDelete(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }




}
