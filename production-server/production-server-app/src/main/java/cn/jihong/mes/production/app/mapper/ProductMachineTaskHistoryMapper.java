package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductMachineTaskHistoryPO;
import cn.jihong.mes.production.api.model.vo.in.GetChangeVersionHistoryPageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductMachineTaskHistoryPageInVO;
import cn.jihong.mes.production.api.model.vo.out.GetChangeVersionHistoryPageOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskHistoryPageOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 生产机台任务操作历史 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
public interface ProductMachineTaskHistoryMapper extends JiHongMapper<ProductMachineTaskHistoryPO> {

    IPage<GetProductMachineTaskHistoryPageOutVO> getProductMachineTaskHistoryPage(IPage page,
        @Param("inVO") GetProductMachineTaskHistoryPageInVO inVO, @Param("companyCode") String companyCode);


    IPage<GetChangeVersionHistoryPageOutVO> getChangeVersionHistoryPage(IPage page,
        @Param("inVO") GetChangeVersionHistoryPageInVO inVO, @Param("companyCode") String companyCode);


}
