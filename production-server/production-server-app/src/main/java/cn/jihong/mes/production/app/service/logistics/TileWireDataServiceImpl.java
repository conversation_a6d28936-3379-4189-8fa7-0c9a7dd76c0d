package cn.jihong.mes.production.app.service.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.logistics.api.model.vo.in.TileWireRealTimeDataPageInVO;
import cn.jihong.logistics.api.model.vo.out.TileWireRealTimeDataOutVO;
import cn.jihong.logistics.api.service.ITileWireRealTimeDataService;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetLatestTileWireDataInVO;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.api.service.logistics.ITileWireDataService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-14 11:21
 */
@DubboService
public class TileWireDataServiceImpl implements ITileWireDataService {

    @DubboReference
    private ITileWireRealTimeDataService iTileWireRealTimeDataService;

    @Resource
    private IProductTicketService iProductTicketService;

    @Override
    public TileWireRealTimeDataOutVO getSingleTileWireRealTimeDataById(Long id) {
        return iTileWireRealTimeDataService.getSingleTileWireRealTimeDataById(id);
    }

    @Override
    public Pagination<TileWireRealTimeDataOutVO> getTileWireRealTimeDataPage(TileWireRealTimeDataPageInVO inVO) {
        return iTileWireRealTimeDataService.getTileWireRealTimeDataPage(inVO);
    }

    @Override
    public TileWireRealTimeDataOutVO getLatestTileWireData(GetLatestTileWireDataInVO inVO) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(inVO.getProductTicketId());
        return iTileWireRealTimeDataService.getLatestTileWireData(productTicketPO.getPlanTicketNo() + "@" + productTicketPO.getProcess());
    }
}
