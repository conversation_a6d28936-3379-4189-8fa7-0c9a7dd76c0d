package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductSettlementWorkReportPO;
import cn.jihong.mes.production.api.model.vo.in.GetListByDefectiveSourceNameInVO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementWorkReportDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportListOutVO;
import cn.jihong.mes.production.api.service.IProductDefectiveProductsService;
import cn.jihong.mes.production.api.service.IProductLastPalletService;
import cn.jihong.mes.production.api.service.IProductSettlementWorkReportService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductDefectiveProductsMapper;
import cn.jihong.mes.production.app.mapper.ProductSettlementWorkReportMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 工程结算报工信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@DubboService
public class ProductSettlementWorkReportServiceImpl extends JiHongServiceImpl<ProductSettlementWorkReportMapper, ProductSettlementWorkReportPO> implements IProductSettlementWorkReportService {


    @Resource
    private IProductLastPalletService iProductLastPalletService;

    @Resource
    private IProductSettlementWorkReportService iProductSettlementWorkReportService;

    @Resource
    private IProductTicketService iProductTicketService;

    @Resource
    private IProductDefectiveProductsService iProductDefectiveProductsService;

    @Resource
    private ProductDefectiveProductsMapper productDefectiveProductsMapper;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Override
    public void collect(String productTicketNo,Long productTicketId,String processName, Map<String, Integer> processSeqMap) {
        // 旧的结算记录
        List<GetSettlementWorkReportListOutVO> outVOList = iProductSettlementWorkReportService.getSettlementWorkReportList(productTicketNo);
        List<ProductSettlementWorkReportPO> settlementWorkReportPOList = BeanUtil.copyToList(outVOList, ProductSettlementWorkReportPO.class);
        Map<String, ProductSettlementWorkReportPO> settlementWorkReportMap = settlementWorkReportPOList.stream().collect(Collectors.toMap(ProductSettlementWorkReportPO::getProcessName,t->t));

        GetListByDefectiveSourceNameInVO inVO = new GetListByDefectiveSourceNameInVO();
        inVO.setProductTicketId(productTicketId);
        // 获取这个生产工单的 不良品记录
        List<GetListByDefectiveSourceNameOutVO> defectiveSourceList = iProductDefectiveProductsService.getListByDefectiveSourceName(inVO);
        if(CollectionUtil.isNotEmpty(defectiveSourceList)) {
            /*Map<String, List<GetListByDefectiveSourceNameOutVO>> processNameMap = defectiveSourceList.stream().collect(Collectors.groupingBy(GetListByDefectiveSourceNameOutVO::getDefectiveProductsSourceName));
            processNameMap.keySet().forEach(processName->{
                List<GetListByDefectiveSourceNameOutVO> temList = processNameMap.get(processName);*/
                ProductSettlementWorkReportPO workReportPO = settlementWorkReportMap.get(processName);
                if(Objects.isNull(workReportPO)){
                    GetListByDefectiveSourceNameOutVO defectiveSourceOutVO = defectiveSourceList.get(0);
                    workReportPO = new ProductSettlementWorkReportPO();
                    BeanUtil.copyProperties(defectiveSourceOutVO,workReportPO);
                    workReportPO.setProductTicketNo(productTicketNo);
                    workReportPO.setProcessName(processName);
                    workReportPO.setProcessSeq(processSeqMap.get(processName));
                    workReportPO.setRealProductQuantity(defectiveSourceOutVO.getRealProduct());
                    workReportPO.setCreateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
                }else{
                    workReportPO.setUpdateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
                    workReportPO.setUpdateTime(new Date());
                }
                workReportPO.setScrapQuantity(defectiveSourceList.stream().map(GetListByDefectiveSourceNameOutVO::getDefectiveProductsQuantity).reduce(BigDecimal.ZERO,BigDecimal::add));
                workReportPO.setAttritionRate(workReportPO.getScrapQuantity().divide(workReportPO.getRealProductQuantity(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                saveOrUpdate(workReportPO);
            /*});*/
        }
    }


    @Override
    public List<GetSettlementWorkReportListOutVO> getSettlementWorkReportList(String productTicketNo) {
        List<GetSettlementWorkReportListOutVO> list = baseMapper.getSettlementWorkReportList(productTicketNo);

        List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(productTicketNo);
        Map<String, Integer> sfcbMap = processSeqList.stream().collect(Collectors.toMap(GetProcessSeqByTickNoOutVO::getProcessName,GetProcessSeqByTickNoOutVO::getProcessSeq));
        list = list.stream().peek(x->x.setProcessSeq(sfcbMap.get(x.getProcessName()))).sorted(Comparator.comparing(GetSettlementWorkReportListOutVO::getProcessSeq)).collect(Collectors.toList());

        /*LambdaQueryWrapper<ProductSettlementWorkReportPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductSettlementWorkReportPO::getProductTicketNo,productTicketNo);
        wrapper.orderByAsc(ProductSettlementWorkReportPO::getProcessSeq);*/
        return list;
    }

    @Override
    public Pagination<GetSettlementWorkReportDetailOutVO> getSettlementWorkReportDetail(GetSettlementWorkReportDetailInVO inVO) {
        Page<GetSettlementWorkReportDetailOutVO> page = baseMapper.getSettlementWorkReportDetail(inVO.getPage(), inVO);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }
}
