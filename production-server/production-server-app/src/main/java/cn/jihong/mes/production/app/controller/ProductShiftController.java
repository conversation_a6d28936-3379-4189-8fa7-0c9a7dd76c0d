package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.GetDefaultRoleListsInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefaultRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mes.production.api.service.IProductShiftService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 班次信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@RestController
@RequestMapping("/productShift")
@ShenyuSpringMvcClient(path = "/productShift/**")
public class ProductShiftController {

    @Resource
    private IProductShiftService productShiftService;

    /**
     * 保存班组信息
     */
    @PostMapping("/saveProductShift")
    public StandardResult saveProductShift(@RequestBody @Valid ProductShiftInVO productShiftInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productShiftService.saveProductShift(productShiftInVO));
    }

    /**
     * 查询班组信息
     */
    @PostMapping("/getProductShift")
    public StandardResult<Pagination<ProductShiftOutVO>>
        getProductShift(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productShiftService.getProductShift(productTicketPageInVO));
    }

    /**
     * 更新班组信息
     */
    @PostMapping("/updateProductShift")
    public StandardResult updateProductShift(@RequestBody @Valid ProductShiftInVO productShiftInVO) {
        productShiftService.updateProductShift(productShiftInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 获得角色、岗位列表
     * @return
     */
    @GetMapping("/getRoleLists")
    public StandardResult<List<GetRoleListsOutVO>> getRoleLists() {
        return StandardResult.resultCode(OperateCode.SUCCESS, productShiftService.getRoleLists());
    }


    /**
     * 默认获得角色、岗位列表
     * @return
     */
    @PostMapping("/getDefaultRoleLists")
    public StandardResult<List<GetDefaultRoleListsOutVO>> getDefaultRoleLists(@RequestBody @Valid GetDefaultRoleListsInVO getDefaultRoleListsInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productShiftService.getDefaultRoleLists(getDefaultRoleListsInVO));
    }


    /**
     * 根据工序获取班组信息
     * @return
     */
    @GetMapping("/getResponsibleShift/{planTicketId}")
    public StandardResult<List<ProductShiftInVO.TeamUser>> getResponsibleShift(@PathVariable Long planTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productShiftService.getResponsibleShift(planTicketId));
    }




}

