package cn.jihong.mes.production.app.controller.oa;


import cn.jihong.mes.production.api.model.vo.out.OutsideProcessingVO;
import cn.jihong.mes.production.app.service.oa.OutsideProcessingServiceImpl;
import cn.jihong.oa.approve.api.enums.BusinessType;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OutsideProcessingDTO;
import cn.jihong.oa.approve.api.model.po.flow.OutsideProcessingPO;
import cn.jihong.workflow.common.biz.IWorkflowAttachmentService;
import cn.jihong.workflow.common.controller.BaseWorkflowActionController;
import cn.jihong.workflow.common.service.IWfService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/outsideProcessing")
@ShenyuSpringMvcClient(path = "/outsideProcessing/**")
public class OutsideProcessingController extends BaseWorkflowActionController<OutsideProcessingPO, OutsideProcessingVO, OutsideProcessingDTO, OutsideProcessingServiceImpl> {


    public OutsideProcessingController(IWfService wfService, OutsideProcessingServiceImpl service, IWorkflowAttachmentService workflowAttachmentService) {
        super(wfService, service, workflowAttachmentService);
    }

    @Override
    public Long getWorkflowId() {
        return BusinessType.OUTSIDE_PROCESSING.getWorkflowid();
    }
}
