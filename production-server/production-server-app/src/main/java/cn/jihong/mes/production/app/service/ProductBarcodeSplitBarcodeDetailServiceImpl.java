package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.po.ProductBarcodeSplitBarcodeDetailPO;
import cn.jihong.mes.production.app.mapper.ProductBarcodeSplitBarcodeDetailMapper;
import cn.jihong.mes.production.api.service.IProductBarcodeSplitBarcodeDetailService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 条码拆分明细 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@DubboService
public class ProductBarcodeSplitBarcodeDetailServiceImpl extends JiHongServiceImpl<ProductBarcodeSplitBarcodeDetailMapper, ProductBarcodeSplitBarcodeDetailPO> implements IProductBarcodeSplitBarcodeDetailService {

}
