package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeOutVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
import cn.jihong.mes.production.app.mapper.ProductBoxBarcodeMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.math.BigInteger;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@DubboService
public class ProductBoxBarcodeServiceImpl extends JiHongServiceImpl<ProductBoxBarcodeMapper, ProductBoxBarcodePO> implements IProductBoxBarcodeService {

    @Override
    public Pagination<GetProductBoxBarcodeOutVO> getProductBoxBarcodeList(GetProductBoxBarcodeInVO getProductBoxBarcodeInVO) {
        LambdaQueryWrapper<ProductBoxBarcodePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper
                .eq(ProductBoxBarcodePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtils.isNotBlank(getProductBoxBarcodeInVO.getCustomerNo()), ProductBoxBarcodePO::getCustomerNo, getProductBoxBarcodeInVO.getCustomerNo())
                .eq(getProductBoxBarcodeInVO.getBarcodeStatus() != null, ProductBoxBarcodePO::getBarcodeStatus, getProductBoxBarcodeInVO.getBarcodeStatus())
                .gt(StringUtils.isNotBlank(getProductBoxBarcodeInVO.getBarcodeNo()), ProductBoxBarcodePO::getBarcodeNoStart, getProductBoxBarcodeInVO.getBarcodeNo())
                .lt(StringUtils.isNotBlank(getProductBoxBarcodeInVO.getBarcodeNo()), ProductBoxBarcodePO::getBarcodeNoEnd, getProductBoxBarcodeInVO.getBarcodeNo())
        .orderByDesc(ProductBoxBarcodePO::getId)
                ;
        IPage page = page(getProductBoxBarcodeInVO.getPage(), lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<GetProductBoxBarcodeOutVO> getProductBoxBarcodeOutVOS = BeanUtil.copyToList(page.getRecords(), GetProductBoxBarcodeOutVO.class);
            return Pagination.newInstance(getProductBoxBarcodeOutVOS,page);
        }
        return Pagination.newInstance(null);
    }

    @Override
    public void enableBoxBarcode(Integer id) {
        ProductBoxBarcodePO productBoxBarcodePO = getById(id);
        if (productBoxBarcodePO == null) {
            throw new CommonException("未找到要启用的箱码");
        }

        LambdaUpdateWrapper<ProductBoxBarcodePO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper
                .eq(ProductBoxBarcodePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtils.isNotBlank(productBoxBarcodePO.getCustomerNo()), ProductBoxBarcodePO::getCustomerNo, productBoxBarcodePO.getCustomerNo())
                .eq(ProductBoxBarcodePO::getBarcodeStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()))
                .set(ProductBoxBarcodePO::getBarcodeStatus, Integer.valueOf(BooleanEnum.FALSE.getCode()))
        ;
        update(lambdaUpdateWrapper);

        if (productBoxBarcodePO.getBarcodeStatus().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))){
            productBoxBarcodePO.setBarcodeStatus(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        } else {
            productBoxBarcodePO.setBarcodeStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        updateById(productBoxBarcodePO);
    }

    @Override
    public Long addBoxBarcode(AddProductBoxBarcodeInVO addProductBoxBarcodeInVO) {

        LambdaUpdateWrapper<ProductBoxBarcodePO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper
                .eq(ProductBoxBarcodePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtils.isNotBlank(addProductBoxBarcodeInVO.getCustomerNo()), ProductBoxBarcodePO::getCustomerNo, addProductBoxBarcodeInVO.getCustomerNo())
                .eq(ProductBoxBarcodePO::getBarcodeStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()))
                .set(ProductBoxBarcodePO::getBarcodeStatus, Integer.valueOf(BooleanEnum.FALSE.getCode()))
        ;
        update(lambdaUpdateWrapper);

        String customerNo = addProductBoxBarcodeInVO.getCustomerNo();
        String barcodeNoStart = addProductBoxBarcodeInVO.getBarcodeNoStart();
        String barcodeNoEnd = addProductBoxBarcodeInVO.getBarcodeNoEnd();

        // 将16进制的字符串转换为十进制数值进行比较
        BigInteger startHex = new BigInteger(barcodeNoStart.substring(barcodeNoStart.length() - 13), 16);
        BigInteger endHex = new BigInteger(barcodeNoEnd.substring(barcodeNoEnd.length() - 13), 16);

        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductBoxBarcodePO::getCustomerNo, customerNo)
                .and(wrapper -> wrapper
                        .le(ProductBoxBarcodePO::getBarcodeNoStart, barcodeNoEnd)
                        .ge(ProductBoxBarcodePO::getBarcodeNoEnd, barcodeNoStart)
                );

        List<ProductBoxBarcodePO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new CommonException("该客户已存在该箱码范围，请勿重复添加");
        }

        // 计算条数
        BigInteger totalCount = endHex.subtract(startHex).add(BigInteger.ONE);
        if (totalCount.compareTo(BigInteger.ZERO) <= 0) {
            throw new CommonException("条码范围不合法，请检查");
        }

        // 如果没有重叠，则添加新的记录
        ProductBoxBarcodePO productBoxBarcodePO = new ProductBoxBarcodePO();
        productBoxBarcodePO.setCompanyCode(SecurityUtil.getCompanySite());
        productBoxBarcodePO.setCustomerNo(customerNo);
        productBoxBarcodePO.setCustomerName(addProductBoxBarcodeInVO.getCustomerName());
        productBoxBarcodePO.setBarcodeNoStart(barcodeNoStart);
        productBoxBarcodePO.setBarcodeNoEnd(barcodeNoEnd);
        productBoxBarcodePO.setBarcodeTotal(totalCount.intValue());
        productBoxBarcodePO.setBarcodeRemaining(totalCount.intValue());
        productBoxBarcodePO.setBarcodeName(barcodeNoStart);
        productBoxBarcodePO.setBarcodeStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
//        productBoxBarcodePO.setBarcodeCurrent(barcodeNoStart);
        productBoxBarcodePO.setCreateBy(Long.valueOf(SecurityUtil.getUserId()));
        productBoxBarcodePO.setUpdateBy(Long.valueOf(SecurityUtil.getUserId()));
        save(productBoxBarcodePO);
        return productBoxBarcodePO.getId();
    }

    @Override
    public List<GetProductBoxBarcodeDetailOutVO> getListByBarcodes(List<String> caseCodes) {
        return baseMapper.getListByBarcodes(SecurityUtil.getCompanySite(),caseCodes);
    }

    @Override
    public GetProductBoxBarcodeOutVO getBoxBarcode(Integer id) {
        ProductBoxBarcodePO productBoxBarcodePO = getById(id);
        return BeanUtil.copyProperties(productBoxBarcodePO, GetProductBoxBarcodeOutVO.class);
    }

    @Override
    public GetProductBoxBarcodeOutVO getEnableBoxBarcode(String customerNo) {
        if (CompanyBoxEnum.COMPANY_ZWL.getCode().equals(customerNo)) {
            return new GetProductBoxBarcodeOutVO();
        } else {
            LambdaQueryWrapper<ProductBoxBarcodePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper
                    .eq(ProductBoxBarcodePO::getCompanyCode, SecurityUtil.getCompanySite())
                    .eq(StringUtils.isNotBlank(customerNo), ProductBoxBarcodePO::getCustomerNo, customerNo)
                    .eq(ProductBoxBarcodePO::getBarcodeStatus, Integer.valueOf(BooleanEnum.TRUE.getCode()))
            ;
            List<ProductBoxBarcodePO> list = list(lambdaQueryWrapper);
            if (CollectionUtil.isEmpty(list)) {
                throw new CommonException("该客户没有启用的箱码");
            }
            return BeanUtil.copyProperties(list.get(0), GetProductBoxBarcodeOutVO.class);
        }

    }


}
