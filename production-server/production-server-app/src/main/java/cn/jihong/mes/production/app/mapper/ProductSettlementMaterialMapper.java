package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.po.ProductSettlementMaterialPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementMaterialDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialDetailOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

import java.util.List;

/**
 * <p>
 * 工程结算物料信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Repository
public interface ProductSettlementMaterialMapper extends JiHongMapper<ProductSettlementMaterialPO> {

    List<GetSettlementMaterialListOutVO> getSettlementMaterialList(@Param("productTicketNo") String productTicketNo);


    Page<GetSettlementMaterialDetailOutVO> getSettlementMaterialDetail(IPage page, @Param("inVo") GetSettlementMaterialDetailInVO inVo);
}
