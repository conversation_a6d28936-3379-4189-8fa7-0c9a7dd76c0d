package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.service.IProductConfigService;
import cn.jihong.mes.production.app.aspect.VersionAspect;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 生产配置表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@RestController
@RequestMapping("/productConfig")
@ShenyuSpringMvcClient(path = "/productConfig/**")
public class ProductConfigController {

    @Resource
    private IProductConfigService productTicketService;
    @Autowired
    private VersionAspect versionAspect;

//    /**
//     * 获得结算方式   1 一单一结   2  一日一结
//     */
//    @GetMapping("/getBillingType")
//    public StandardResult<Integer> getBillingType() {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getBillingType());
//    }


    /**
     * 刷新版本号缓存
     * @return
     */
    @PostMapping("/refreshVersion")
    public StandardResult<String> refreshMinimumVersion() {
        versionAspect.refreshMinimumVersion();
        return StandardResult.resultCode(OperateCode.SUCCESS,"版本号缓存已刷新");
    }

}
