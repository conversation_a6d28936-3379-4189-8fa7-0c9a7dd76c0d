package cn.jihong.mes.production.app.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.GetFlowProcessListInVO;
import cn.jihong.mes.production.api.model.vo.out.GetFlowProcessListOutVO;
import cn.jihong.mes.production.api.service.IFlowProcessService;

/**
 * 流转流程
 * <AUTHOR>
 * @date 2025-04-18 15:33
 */
@RestController
@RequestMapping("/flowProcess")
@ShenyuSpringMvcClient(path = "/flowProcess/**")
public class FlowProcessController {


    @Resource
    private IFlowProcessService iFlowProcessService;



    /**
     * 流转列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.mes.production.api.model.vo.out.DeviceOEEReportShowOutVO>>
     * <AUTHOR>
     * @date: 2025-03-22 17:07
     */
    @PostMapping("/getFlowProcessList")
    public StandardResult<List<GetFlowProcessListOutVO>> getFlowProcessList(@RequestBody @Valid GetFlowProcessListInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iFlowProcessService.getFlowProcessList(inVO));
    }

}
