package cn.jihong.mes.production.app.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.packing.BoxCodeCheckHistoryInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.BoxCodeValidationInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.DetectionDataUploadInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.PalletizeResultInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.StationStatusInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.InnerLabelBindingInVO;
import cn.jihong.mes.production.api.model.vo.in.packing.InnerLabelUnbindInVO;
import cn.jihong.mes.production.api.model.vo.out.packing.BoxCodeCheckHistoryOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.BoxCodeInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.BoxCodeValidationOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.DetectionDataUploadOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.PalletizeResultOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.StationStatusOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.InnerLabelBindingOutVO;
import cn.jihong.mes.production.api.model.vo.out.packing.InnerLabelQueryOutVO;

import java.util.List;
import cn.jihong.mes.production.api.service.IPackingBoxCodeService;

/**
 * 包装线箱码信息表 前端控制器
 * 负责处理自动包装生产线的各个工站业务
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/packingBoxCode")
@ShenyuSpringMvcClient(path = "/packingBoxCode/**")
public class PackingBoxCodeController {

    @Resource
    private IPackingBoxCodeService packingBoxCodeService;

    /**
     * 箱码校验接口
     * 所有工站首次扫码时调用，校验箱码有效性并返回箱码所带数据
     */
    @PostMapping("/validateBoxCode")
    public StandardResult<BoxCodeValidationOutVO> validateBoxCode(@RequestBody @Valid BoxCodeValidationInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.validateBoxCode(vo));
    }

    /**
     * 检测数据上传接口
     * 称重站/金探站完成检测后调用，工站检测结果上报
     */
    @PostMapping("/uploadDetectionData")
    public StandardResult<DetectionDataUploadOutVO> uploadDetectionData(@RequestBody @Valid DetectionDataUploadInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.uploadDetectionData(vo));
    }

    /**
     * 组托结果接口
     * 机械臂组托完成后调用，获取箱托绑定信息，生成出站数据，自动提交入库申请
     */
    @PostMapping("/palletizeResult")
    public StandardResult<PalletizeResultOutVO> palletizeResult(@RequestBody @Valid PalletizeResultInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.palletizeResult(vo));
    }

    /**
     * 工站状态查询接口
     * 查询当前工站的运行状态和统计信息
     */
    @PostMapping("/getStationStatus")
    public StandardResult<StationStatusOutVO> getStationStatus(@RequestBody @Valid StationStatusInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.getStationStatus(vo));
    }

    /**
     * 根据箱码查询详细信息
     * 用于追溯和问题排查
     */
    @GetMapping("/getBoxCodeInfo/{boxCode}")
    public StandardResult<BoxCodeInfoOutVO> getBoxCodeInfo(@PathVariable String boxCode) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.getBoxCodeInfo(boxCode));
    }

    /**
     * 查询箱码检测历史
     * 获取指定箱码在各工站的检测记录
     */
    @PostMapping("/getBoxCodeCheckHistory")
    public StandardResult<BoxCodeCheckHistoryOutVO> getBoxCodeCheckHistory(@RequestBody @Valid BoxCodeCheckHistoryInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.getBoxCodeCheckHistory(vo));
    }

    // ==================== 内标签绑定相关接口 ====================

    /**
     * 箱码内标签绑定接口
     * 将内标签码与箱码建立绑定关系，支持三级层次：栈板码 → 箱码 → 内标签码
     * 同时从ERP接口获取并更新产品信息（重量、内包数量等）
     */
    @PostMapping("/bindInnerLabels")
    public StandardResult<InnerLabelBindingOutVO> bindInnerLabels(@RequestBody @Valid InnerLabelBindingInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.bindInnerLabels(vo));
    }

    /**
     * 查询箱码绑定的内标签
     * 获取指定箱码下的所有内标签绑定信息
     */
    @GetMapping("/getInnerLabels/{boxCode}")
    public StandardResult<List<InnerLabelQueryOutVO>> getInnerLabels(@PathVariable String boxCode) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.getInnerLabels(boxCode));
    }

    /**
     * 解绑内标签
     * 解除内标签与箱码的绑定关系
     */
    @PostMapping("/unbindInnerLabel")
    public StandardResult<Boolean> unbindInnerLabel(@RequestBody @Valid InnerLabelUnbindInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                packingBoxCodeService.unbindInnerLabel(vo));
    }
}

