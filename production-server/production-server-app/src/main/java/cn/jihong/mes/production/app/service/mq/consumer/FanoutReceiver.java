//package cn.jihong.mes.production.app.service.mq.consumer;
//
//import org.springframework.amqp.rabbit.annotation.RabbitHandler;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Component;
//
//import java.util.Map;
//
//
//@Component
//@RabbitListener(queues = "fanout.A")
//public class FanoutReceiver {
//
//    @RabbitHandler
//    public void process(Map testMessage) {
//        System.out.println("FanoutReceiverA消费者收到消息  : " +testMessage.toString());
//    }
//
//}
