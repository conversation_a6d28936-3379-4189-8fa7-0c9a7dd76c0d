package cn.jihong.mes.production.app.service.report;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.BeanUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.dto.ApportionmentMaterialStandardConsumptionDTO;
import cn.jihong.mes.production.api.model.enums.MaterialUseTypeEnum;
import cn.jihong.mes.production.api.model.enums.ProductShitEnum;
import cn.jihong.mes.production.api.model.enums.UnitEnum;
import cn.jihong.mes.production.api.model.po.ProductDailyReportSendPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductDailyReportSendService;
import cn.jihong.mes.production.api.service.IProductMachineGroupService;
import cn.jihong.mes.production.api.service.IProductMachineMaterialApportionmentService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.api.service.report.IProductReportServer;
import cn.jihong.message.api.model.po.MessageTypePO;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.message.api.service.IMessageTypeService;
import cn.jihong.oa.erp.api.model.dto.ImaaTNewDTO;
import cn.jihong.oa.erp.api.model.dto.MrbaTDTO;
import cn.jihong.oa.erp.api.model.dto.ReportUnitConvertDTO;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.SfbaTVO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductReportServerImpl implements IProductReportServer {

    @Resource
    private IProductTicketService productTicketService;

    @DubboReference
    private IEnterpriseWeChatService enterpriseWeChatService;
    @DubboReference
    private ISfcbTService sfcbTService;
    @DubboReference
    private IEcaaucTService ecaaucTService;

    @DubboReference
    private IImaaTNewService imaaTNewService;

    @DubboReference
    private IMessageTypeService iMessageTypeService;

    @Resource
    private IProductMachineMaterialApportionmentService iProductMachineMaterialApportionmentService;

    @DubboReference
    private IReportUnitConvertService reportUnitConvertService;

    @DubboReference
    private ISfaaTService sfaaTService;

    @DubboReference
    private ISfbaTService iSfbaTService;

    @Resource
    private IProductDailyReportSendService iProductDailyReportSendService;

    @Resource
    private IProductMachineGroupService productMachineGroupService;

    @DubboReference
    private IImaalTService imaalTService;
    @DubboReference
    private IMrbaTService mrbaTService;

    /**
     * 获得生产日报（工厂）
     */
    @Override
    public Pagination<GetProductReportByFactoryOutVO>
        getProductReportByFactory(GetProductReportByFactoryInVO getProductReportByFactoryInVO) {
        if (StringUtils.isBlank(getProductReportByFactoryInVO.getCompanyCode())) {
            getProductReportByFactoryInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }
        // 基础数据
        Page<GetProductReportByFactoryOutVO> productReportByFactoryOutVOS =
            productTicketService.getReportByFactory(getProductReportByFactoryInVO);

        if (CollectionUtil.isEmpty(productReportByFactoryOutVOS.getRecords())) {
            return Pagination.newInstance(null);
        }
        // 设备利用率=实际产能(实际当班生产数量) / （标准产能 × 生产时间H）
        List<String> machineNames = productReportByFactoryOutVOS.getRecords().stream().map(GetProductReportByFactoryOutVO::getMachineName).distinct().collect(Collectors.toList());
        List<MrbaTDTO> mrbaTDTOS = mrbaTService.getListByMachineNameList(SecurityUtil.getCompanySite(), machineNames);
        if (CollectionUtils.isNotEmpty(mrbaTDTOS)) {
            Map<String, String> mrbaud015Map = mrbaTDTOS.stream().collect(Collectors.toMap(MrbaTDTO::getMrba001, MrbaTDTO::getMrbaud015, (v1, v2) -> v2));
            productReportByFactoryOutVOS.getRecords().stream().forEach(p->{
                String[] split = p.getMachineName().split(":");
                String machineName = split[1];
                if (mrbaud015Map.get(machineName) != null) {
                    BigDecimal standardCapacity = BigDecimal.valueOf(Double.parseDouble(mrbaud015Map.get(machineName)));
                    BigDecimal multiply = standardCapacity.multiply(p.getRealHours());
                    if (multiply != null && multiply.compareTo(BigDecimal.ZERO) > 0) {
                        p.setEquipmentUtilizationRate(p.getRealProductionCapacity().divide(multiply, RoundingMode.HALF_UP));
                    }
                }
            });
        }


        List<GetProductReportByFactoryOutVO> list =
            productReportByFactoryOutVOS.getRecords().stream().map(productReportByFactoryOutVO -> {

                productReportByFactoryOutVO
                    .setShiftName(ProductShitEnum.getProductShitEnum(productReportByFactoryOutVO.getShift()).getName());
                return productReportByFactoryOutVO;
            }).collect(Collectors.toList());

        return Pagination.newInstance(list, productReportByFactoryOutVOS.getTotal(),
            productReportByFactoryOutVOS.getPages());

    }

    @Override
    public List<ProductMachineGroupOutVO> getProductMachineGroupList(GetProductMachineGroupListInVO inVO) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        UserInfo userInfo = new UserInfo();
        userInfo.setCompanySite(inVO.getToken());
  //      request.setAttribute("userContext",JSONUtil.toBean(URLDecoder.decode(inVO.getToken(), Charset.defaultCharset()), UserInfo.class) );
        request.setAttribute("userContext",userInfo);
        return productMachineGroupService.getList();
    }

    @Override
    public List<GetMachineGroupReportByH5OutVO> getMachineGroupReportByH5(GetMachineGroupReportByH5InVO inVO) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        UserInfo userInfo = new UserInfo();
        userInfo.setCompanySite(inVO.getToken());
        request.setAttribute("userContext",userInfo);

        GetMachineGroupReportInVO temInVO = new GetMachineGroupReportInVO();
        temInVO.setProduceDate(DateUtil.parse(inVO.getProduceDate(), DatePattern.NORM_DATE_PATTERN));
        temInVO.setShift(inVO.getShit());
        temInVO.setGroupIds(inVO.getGroupIds());
        List<GetMachineGroupReportOutVO> machineGroupReportList = getMachineGroupReport(temInVO);
        List<GetMachineGroupReportByH5OutVO> list = cn.hutool.core.bean.BeanUtil.copyToList(machineGroupReportList, GetMachineGroupReportByH5OutVO.class);

        // H5访问 补充发送人和备注
        Map<String, ProductDailyReportSendPO> sendMap = new HashMap<>();
        List<ProductDailyReportSendPO> sendList = iProductDailyReportSendService.getList(inVO.getGroupIds(), inVO.getProduceDate(), inVO.getShit());
        sendMap.putAll(sendList.stream().collect(Collectors.toMap(t -> t.getPlanTicketNo() + t.getMachineName() + t.getProcessCode(), Function.identity(), (a, b) -> a)));
        list.forEach(m->{
            m.getMachineGroupDetialReportOutVOList().forEach(x->{
                ProductDailyReportSendPO productDailyReportSendPO = sendMap.get(x.getPlanTicketNo() + x.getMachineName() + x.getProcessCode());
                if(productDailyReportSendPO!=null){
                    m.setSendName(productDailyReportSendPO.getSendName());
                    x.setRemark(productDailyReportSendPO.getSendRemark());
                }
            });
        });
        return list;
    }

    @Override
    public List<GetMachineGroupReportOutVO> getMachineGroupReport(GetMachineGroupReportInVO getMachineGroupReportInVO) {
        if (CollectionUtil.isEmpty(getMachineGroupReportInVO.getGroupIds())) {
            throw new CommonException("至少选择一个机组");
        }
        List<GetMachineGroupReportOutVO> machineGroupReport = productTicketService.getMachineGroupReport(getMachineGroupReportInVO);
        if (CollectionUtil.isEmpty(machineGroupReport)) {
            return new ArrayList<>();
        }
        List<GetOneDayTaskSummaryOutVO> oneDayTaskSummaryOutVOList = new ArrayList<>();
        machineGroupReport.forEach(n-> n.getMachineGroupDetialReportOutVOList().forEach(o->{
            GetOneDayTaskSummaryOutVO oneDayTaskSummaryOutVO = new GetOneDayTaskSummaryOutVO();
            oneDayTaskSummaryOutVO.setPlanTicketNo(o.getPlanTicketNo());
            oneDayTaskSummaryOutVO.setProductName(o.getProductionName());
            oneDayTaskSummaryOutVOList.add(oneDayTaskSummaryOutVO);
        }));
        // 获取所有工程单的物料标准消耗率
        Map<String, ApportionmentMaterialStandardConsumptionDTO> ticketMaterialPlacesMap = iProductMachineMaterialApportionmentService.getTicketStandardConsumptionMap(oneDayTaskSummaryOutVOList);

        machineGroupReport.stream().forEach(report -> {
            List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO> machineGroupDetialReportOutVOList =
                report.getMachineGroupDetialReportOutVOList();

            List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO> list = machineGroupDetialReportOutVOList.stream().filter(detail -> {
                if ("0".equals(detail.getPlannedProductionCapacity())
                        && detail.getRealProductionCapacity().compareTo(BigDecimal.ZERO) == 0
                        && detail.getDefectiveProduct().compareTo(BigDecimal.ZERO) == 0
                ) {
                    return false;
                }

                // 良品和不良品都为0，不需要用料情况
                if (detail.getRealProductionCapacity().compareTo(BigDecimal.ZERO) == 0
                        && detail.getDefectiveProduct().compareTo(BigDecimal.ZERO) == 0) {
                    detail.setUseMaterialList(new ArrayList<>());
                    detail.setUseMaterialOutStandardList(new ArrayList<>());
                    detail.setUsePositiveMaterialOutStandardList(new ArrayList<>());
                    return true;
                }
                // 用料情况
                useMaterialCondition(detail, ticketMaterialPlacesMap,getMachineGroupReportInVO.getUseMaterialType());

                return true;
            }).collect(Collectors.toList());
            report.setMachineGroupDetialReportOutVOList(list);
        });


        // 查询开单损耗率
        List<String> planTicketNos = machineGroupReport.stream().flatMap(
                report -> report.getMachineGroupDetialReportOutVOList().stream()
        ).map(GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO::getPlanTicketNo).distinct().collect(Collectors.toList());
        List<SfcbTVO> sfcbTVOS = sfcbTService.getByTickNos(planTicketNos);
        // 开单损耗率 乘以了100后的数据
        Map<String, BigDecimal> billingAttRate = sfcbTVOS.stream().collect(Collectors
            .toMap(sfcbTVO -> sfcbTVO.getSfcbdocno() + ":" + sfcbTVO.getSfcb003(), sfcbTVO -> sfcbTVO.getSfcbud011()));
        
        // 获得单位
        List<String> processCodes =
            machineGroupReport.stream().flatMap(report -> report.getMachineGroupDetialReportOutVOList().stream())
                .map(GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO::getProcessCode).distinct()
                .collect(Collectors.toList());
        List<EcaaucTPO> ecaaucTPOS =
            ecaaucTService.getListBySiteAndProcess(SecurityUtil.getCompanySite(), processCodes);
        Map<String, String> unitMap = ecaaucTPOS.stream().collect(Collectors.toMap(EcaaucTPO::getEcaauc001, EcaaucTPO::getEcaauc009, (k1, k2) -> k1));

        machineGroupReport.stream().forEach(report -> {
            report.setProduceDate(getMachineGroupReportInVO.getProduceDate());
            report.setShift(getMachineGroupReportInVO.getShift());
            report.setShiftName(ProductShitEnum.getProductShitEnum(getMachineGroupReportInVO.getShift()).getName());
            report.getMachineGroupDetialReportOutVOList().stream().forEach(detail -> {
                if (detail.getPlanReachRate() == null) {
                    detail.setPlanReachRate(BigDecimal.ZERO);
                }
                if (detail.getPlannedProductionCapacity() == null || new BigDecimal(detail.getPlannedProductionCapacity()).compareTo(BigDecimal.ZERO) == 0) {
                    detail.setPlannedProductionCapacity("未排产");
                    detail.setPlanReachRateStr("未排产");
                    detail.setPlanReachRateColor("warning");
                    detail.setPlannedProductionCapacityColor("warning");
                } else {
                    detail.setPlanReachRateStr(detail.getPlanReachRate().multiply(new BigDecimal(100L)).setScale(2, RoundingMode.HALF_UP)+"%");
                }
                if (detail.getLossRate() == null || detail.getLossRate().compareTo(BigDecimal.ZERO) == 0) {
                    detail.setLossRate(BigDecimal.ZERO);
                    detail.setLossRateStr("0.00%");
                } else {
                    detail.setLossRateStr(detail.getLossRate().multiply(new BigDecimal(100L)).setScale(2, RoundingMode.HALF_UP)+"%");
                }
                // 设置颜色
//                计划完成率<85% or >115%
                if (detail.getPlanReachRate().compareTo(new BigDecimal("0.85")) < 0 || detail.getPlanReachRate().compareTo(new BigDecimal("1.15")) > 0) {
                    detail.setPlanReachRateColor("warning");
                } else {
                    detail.setPlanReachRateColor("info");
                }
                // 损耗率 >erp定义的开单损耗率(不同工序损耗率不同)
                BigDecimal bigDecimal = billingAttRate.get(detail.getPlanTicketNo() + ":" + detail.getProcessCode());
                log.info("开单损耗率：{}，erp定义的开单损耗率：{}", detail.getLossRate(), bigDecimal);
                if (detail.getLossRate().multiply(new BigDecimal(100L)).compareTo(bigDecimal) > 0) {
                    detail.setLossRateColor("warning");
                } else {
                    detail.setLossRateColor("info");
                }

                // 设置单位
                String unit = unitMap.get(detail.getProcessCode());
                if (unit != null) {
                    detail.setRealProductionCapacityUnit(UnitEnum.getUnitEnum(unit).getName());
                    detail.setDefectiveProductUnit(UnitEnum.getUnitEnum(unit).getName());
                }

                if (detail.getRealProductionCapacity() == null) {
                    detail.setRealProductionCapacity(BigDecimal.ZERO);
                }

                detail.setRemark("");
            });
        });
        return machineGroupReport;
    }

    private void useMaterialCondition(GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO detail, Map<String, ApportionmentMaterialStandardConsumptionDTO> ticketMaterialPlacesMap,Integer materialUseType) {
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(detail.getPlanTicketNo());
        String planTicketUnit = sfaaTVO.getSfaa013();

        List<Long> includeProductTicketIdList = Arrays.stream(detail.getIncludeProductTicketIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        // 该工程单下 所有的用料列表
        Map<String,GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO> temUseMaterialsOutVOMap = new HashMap<>();

        includeProductTicketIdList.forEach(productTicketId->{
            GetProductUseMaterialsInVO useMaterialsInVO = new GetProductUseMaterialsInVO();
            useMaterialsInVO.setId(productTicketId);
            useMaterialsInVO.setPageNum(1);
            useMaterialsInVO.setPageSize(Integer.MAX_VALUE);
            Pagination<GetProductUseMaterialsOutVO> useMaterialsOutVOPagination = iProductMachineMaterialApportionmentService.useMaterials(useMaterialsInVO);
            for(GetProductUseMaterialsOutVO t : useMaterialsOutVOPagination.getData())    {
                // 料号 部位和扣料类型 作为维度
                String key = t.getMaterialCode() + t.getMaterialPlace() + t.getUseType();
                GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO tem = temUseMaterialsOutVOMap.get(key);
                if (tem != null) {
                    tem.setConsumptionQuantity(tem.getConsumptionQuantity().add(t.getConsumptionQuantity()));
                } else {
                    GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO useMaterialOutVO = new GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO();
                    BeanUtil.copyProperties(t, useMaterialOutVO);
                    useMaterialOutVO.setUseTypeName(MaterialUseTypeEnum.getMaterialUseTypeEnum(t.getUseType().toString()).getName());
                    useMaterialOutVO.setOutStandard(BooleanEnum.FALSE.getCode());
                    useMaterialOutVO.setMesUseMaterial(BooleanEnum.TRUE.getCode());
                    temUseMaterialsOutVOMap.put(key, useMaterialOutVO);
                }
            }
        });
        List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO> useMaterialsOutVOList = new ArrayList<>(temUseMaterialsOutVOMap.values());
        detail.setUseMaterialList(useMaterialsOutVOList);

        // 获取 对应物料的用料信息 还有 标准消耗率
        ApportionmentMaterialStandardConsumptionDTO.ProcessDTO processDTO = ticketMaterialPlacesMap.get(detail.getPlanTicketNo()).getProcessDTOs().stream().filter(x -> x.getProcessCode().equals(detail.getProcessCode())).findFirst().orElse(null);


        if(processDTO!=null) {
            // 查询erp bom表信息 补充物料
            queryBomAndFillMaterial(detail,processDTO, useMaterialsOutVOList);
        }

        useMaterialsOutVOList.forEach(useMaterials->{
            useMaterials.setPlanTicketUnit(planTicketUnit);
            BigDecimal totalQuantity = detail.getRealProductionCapacity().add(detail.getDefectiveProduct());
            ReportUnitConvertDTO reportUnitConvertDTO = new ReportUnitConvertDTO();
            reportUnitConvertDTO.setTargetUnit(planTicketUnit);

            EcaaucTPO ecaaucTPO = ecaaucTService.getByCompanyProcess(SecurityUtil.getCompanySite(), detail.getProcessCode());
            reportUnitConvertDTO.setSourceUnit(ecaaucTPO.getEcaauc009());

            reportUnitConvertDTO.setPlanTicketNo(detail.getPlanTicketNo());
            reportUnitConvertDTO.setProcessCode(detail.getProcessCode());
            reportUnitConvertDTO.setQuantity(totalQuantity);
            BigDecimal convertQuantity = reportUnitConvertService.execute(reportUnitConvertDTO);
            AssertUtil.isNotNull(convertQuantity, "物料单位转换异常");
            log.info("消耗物料数量：{},工单单位转化后数量：{}", useMaterials.getConsumptionQuantity(), convertQuantity);

            BigDecimal realConsumption = BigDecimal.ZERO;
            if(convertQuantity.compareTo(BigDecimal.ZERO) > 0) {
                 realConsumption = useMaterials.getConsumptionQuantity().divide(convertQuantity, 10, RoundingMode.HALF_UP);
            }
            useMaterials.setRealLossRate(realConsumption);
            useMaterials.setRealLossRateStr(realConsumption.toPlainString() + " " + useMaterials.getMaterialUnit() + "/" + planTicketUnit);

            if(processDTO!=null && CollectionUtil.isNotEmpty(processDTO.getMaterialDTOS())) {
                List<ApportionmentMaterialStandardConsumptionDTO.ProcessDTO.MaterialDTO> materialDTOList = processDTO.getMaterialDTOS().stream().filter(m -> (m.getMaterialCode().equals(useMaterials.getMaterialCode()) && m.getPlace().equals(useMaterials.getMaterialPlace()))).collect(Collectors.toList());
                BigDecimal standardConsumption = materialDTOList.stream().map(ApportionmentMaterialStandardConsumptionDTO.ProcessDTO.MaterialDTO::getStandardConsumption).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                useMaterials.setStandardLossRate(standardConsumption);
                useMaterials.setStandardLossRateStr(standardConsumption.toPlainString() + " " + useMaterials.getMaterialUnit() + "/" + planTicketUnit);

                useMaterials.setStandardLossAndRealLossRate(standardConsumption.compareTo(BigDecimal.ZERO)==0?null:realConsumption.divide(standardConsumption, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2));
                useMaterials.setStandardLossAndRealLossRateStr(useMaterials.getStandardLossAndRealLossRate()== null?"%":useMaterials.getStandardLossAndRealLossRate() + "%");
                if (realConsumption.compareTo(standardConsumption) > 0) {
                    useMaterials.setOutStandard(BooleanEnum.TRUE.getCode());
                }
            }
        });
        detail.setUseMaterialOutStandardList(detail.getUseMaterialList().stream().filter(t->BooleanEnum.TRUE.getCode().equals(t.getOutStandard())).collect(Collectors.toList()));
        detail.setUsePositiveMaterialOutStandardList(detail.getUseMaterialOutStandardList().stream().filter(t->MaterialUseTypeEnum.POSITIVE_MATERIAL.getCode().equals(t.getUseType().toString())).collect(Collectors.toList()));
    }

    /** 
     * 查询erp bom表信息 补充物料
     * @param processDTO	
     * @param useMaterialsOutVOList	 
     * @return: void
     * <AUTHOR>
     * @date: 2024-12-17 13:21
     */
    private void queryBomAndFillMaterial(GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO detail,ApportionmentMaterialStandardConsumptionDTO.ProcessDTO processDTO, List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO> useMaterialsOutVOList) {
        // 获取 物料在bom中的使用类型
        Map<String, String> bomMaterialUsedTypeMap = getBomMaterialUsedType(detail.getPlanTicketNo(), detail.getProcessCode());
        List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO> addMaterialList = new ArrayList<>();

        Set<String> inkTypes = BasePaperConst.getInkTypes();

        processDTO.getMaterialDTOS().forEach(t->{
            GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO temMaterial = useMaterialsOutVOList.stream().filter(x -> x.getMaterialCode().equals(t.getMaterialCode())).findFirst().orElse(null);
            if(temMaterial == null){ // 如果bom物料表中有 mes没有用料 则实际用料数量为0
                GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO addMaterial = new GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO();
                addMaterial.setMaterialCode(t.getMaterialCode());
                addMaterial.setMaterialPlace(t.getPlace());
                addMaterial.setConsumptionQuantity(BigDecimal.ZERO);
                addMaterial.setMaterialUnit(t.getMaterialUnit());
                addMaterial.setMesUseMaterial(BooleanEnum.FALSE.getCode());
                addMaterial.setOutStandard(BooleanEnum.FALSE.getCode());
                String materialType = bomMaterialUsedTypeMap.get(t.getMaterialCode());
                if (inkTypes.contains(materialType)) {
                    log.info("工单号：{}  物料：{} ，类别：{} 油墨或胶水，则为倒扣料！", detail.getPlanTicketNo(), t.getMaterialCode(), materialType);
                    addMaterial.setUseType(MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode());
                }else {
                    log.info("工单号：{}  物料：{} ，类别：{} 不是油墨或胶水，则为正扣料！", detail.getPlanTicketNo(), t.getMaterialCode(), materialType);
                    addMaterial.setUseType(MaterialUseTypeEnum.POSITIVE_MATERIAL.getIntCode());
                }
                addMaterial.setUseTypeName(MaterialUseTypeEnum.getMaterialUseTypeEnum(addMaterial.getUseType().toString()).getName());
                addMaterialList.add(addMaterial);
            }
        });
        if(CollectionUtil.isNotEmpty(addMaterialList)) {
            List<String> addMateialCodeList = addMaterialList.stream().map(GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO::getMaterialCode).collect(Collectors.toList());
            Map<String, String> addMaterilaMap = imaalTService.getImaalTList(addMateialCodeList).stream().collect(Collectors.toMap(ImaalTVO::getImaal001, ImaalTVO::getImaal003, (a, b) -> a));
            addMaterialList.forEach(n -> {
                n.setMaterialName(addMaterilaMap.get(n.getMaterialCode()));
            });
            useMaterialsOutVOList.addAll(addMaterialList);
        }
    }

    // bom获取用料类型
    private Map<String, String> getBomMaterialUsedType(String planTicketNo,String processCode){
        List<SfbaTVO> sfbaTVOS = iSfbaTService.getByProductTicketNo(planTicketNo);

        // 获得物料类型
        List<ImaaTNewDTO> imaaTDTOS = imaaTNewService.getNameByProductNos(sfbaTVOS.stream().filter(sfbaTVO ->
                sfbaTVO.getSfba003().equals(processCode)).map(SfbaTVO::getSfba006).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(imaaTDTOS)) {
            log.error("工单号：{} 未找到对应的物料类型！不需要倒扣料", planTicketNo);
            return new HashMap<>();
        }
        log.info("查询到的物料类型：{}" , JSON.toJSONString(imaaTDTOS));
        Map<String, String> imaaTMap = imaaTDTOS.stream().collect(Collectors.toMap(ImaaTNewDTO::getImaa001, ImaaTNewDTO::getImaaua004, (a, b) -> a));
        log.info("查询到的物料类型map：{}" ,JSON.toJSONString(imaaTMap));
        return imaaTMap;
    }

    @Override
    public void sendMachineGroupReport(SendMachineGroupReportInVO sendMachineGroupReportInVO) {
        String sendNo = UUID.randomUUID().toString().replaceAll("-", "");
        List<GetMachineGroupReportOutVO> machineGroupReport = sendMachineGroupReportInVO.getMachineGroupReport();
        if (CollectionUtil.isEmpty(machineGroupReport)) {
            throw new CommonException("日报数据不能为空");
        }

        machineGroupReport.stream().forEach(report -> {

            report.getMachineGroupDetialReportOutVOList().forEach(t->{
                if(t.getPlanReachRate().compareTo(new BigDecimal(0.95)) <= 0 && StringUtils.isBlank(t.getRemark())){
                    throw new CommonException("生产计划完成率<95%，请在备注中输入未完成原因。");
                }
            });

            if (report.getMachineGroupDetialReportOutVOList().size() > 6) {
                // 每6个切割成一个新的List
                List<List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO>> partitionedLists =
                    splitList(report.getMachineGroupDetialReportOutVOList(), 6);
                partitionedLists.forEach(partitionList -> {
                    GetMachineGroupReportOutVO getMachineGroupReportOutVO = new GetMachineGroupReportOutVO();
                    BeanUtil.copyProperties(report, getMachineGroupReportOutVO);
                    getMachineGroupReportOutVO.setMachineGroupDetialReportOutVOList(partitionList);
                    String msg = sendMsg(getMachineGroupReportOutVO);
                    List<String> segments = splitStringByBytes(msg, 4096);
                    for (String segment : segments) {
                        sendMachineGroupReportInVO.getWeChatTeamCodes().stream().distinct().forEach(teamCode -> {
                            enterpriseWeChatService.robotSendMarkDownMessage(teamCode, segment);
                        });
                    }
                });
            } else {
                String msg = sendMsg(report);
                List<String> segments = splitStringByBytes(msg, 4096);
                for (String segment : segments) {
                    sendMachineGroupReportInVO.getWeChatTeamCodes().stream().distinct().forEach(teamCode -> {
                        enterpriseWeChatService.robotSendMarkDownMessage(teamCode, segment);
                    });
                }
            }

            // 保存发送信息
            saveSendInfo(report, sendNo);
        });
    }

    /**
     * 保存发送信息
     * @param report
     * @param sendNo
     * @return: void
     * <AUTHOR>
     * @date: 2024-12-04 14:20
     */
    private void saveSendInfo(GetMachineGroupReportOutVO report, String sendNo) {
        report.getMachineGroupDetialReportOutVOList().forEach(x->{
            ProductDailyReportSendPO productDailyReportSendPO = new ProductDailyReportSendPO();
            productDailyReportSendPO.setCompanyCode(SecurityUtil.getCompanySite());
            productDailyReportSendPO.setMachineGroupId(report.getGroupId());
            productDailyReportSendPO.setSendNo(sendNo);
            productDailyReportSendPO.setProduceDate(DateUtil.format(report.getProduceDate(),DatePattern.NORM_DATE_PATTERN));
            productDailyReportSendPO.setShift(report.getShift());
            productDailyReportSendPO.setMachineName(x.getMachineName());
            productDailyReportSendPO.setPlanTicketNo(x.getPlanTicketNo());
            productDailyReportSendPO.setProcessCode(x.getProcessCode());
            productDailyReportSendPO.setIncludeProductTicketIds(x.getIncludeProductTicketIds());
            productDailyReportSendPO.setSendRemark(x.getRemark());
            productDailyReportSendPO.setSendBy(SecurityUtil.getUserId());
            productDailyReportSendPO.setSendName(SecurityUtil.getUserName());
            productDailyReportSendPO.setCreateTime(LocalDateTime.now());
            productDailyReportSendPO.setCreateBy(SecurityUtil.getUserId());
            iProductDailyReportSendService.save(productDailyReportSendPO);
        });
    }


    private String sendMsg(GetMachineGroupReportOutVO report) {
        String header = "日期：${productDate}\n" +
                "班次：${shiftName}\n" +
                "发送人：${userName}\n" +
                "\n";
        GetMachineGroupReportOutVO getMachineGroupReportOutVO = report;
        header = header.replace("${productDate}", DateUtil.format(getMachineGroupReportOutVO.getProduceDate(), DatePattern.NORM_DATE_PATTERN) )
                .replace("${shiftName}", ProductShitEnum.getProductShitEnum(getMachineGroupReportOutVO.getShift()).getName())
                .replace("${userName}", SecurityUtil.getUserName());

        String template1 = "**【${machineName}】**\n" +
                ">工序：<font color=\"info\">${process}</font>\n" +
                ">工程号：<font color=\"info\">${planTicketNo}</font>\n" +
                ">生产产品：<font color=\"info\">${productionName}</font>\n" +
                ">计划产量：<font color=${plannedProductionCapacityColor}>${plannedProductionCapacity}</font>\n" +
                ">成品数量：<font color=\"info\">${realProductionCapacity}</font>\n" +
                ">不良品数量：<font color=\"info\">${defectiveProduct}</font>\n" +
                ">计划完成率：<font color=${planReachRateColor}>${planReachRateStr}</font>\n";
        String template2 = ">损耗率：<font color=${lossRateColor}>${lossRateStr}</font>\n" +
                ">班组人员：<font color=\"info\">${teamUsers}</font>\n" +
                ">备注：<font color=\"info\">${remark}</font>\n";

        StringBuilder result = new StringBuilder();

        result.append("**").append(report.getGroupName()).append("**\n");
        List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO> details = report.getMachineGroupDetialReportOutVOList();

        //    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        //   HttpServletRequest request = attributes.getRequest();
        // String token = request.getHeader(HttpHeaders.AUTHORIZATION);

        for (int i = 0; i < details.size(); i++) {
            GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO detail = details.get(i);

            StringBuilder useMaterialTemplateBuilder = new StringBuilder();
            // 超出标准消耗的物料
            if(CollectionUtil.isNotEmpty(detail.getUsePositiveMaterialOutStandardList())){
                detail.getUsePositiveMaterialOutStandardList().forEach(t->{
                    useMaterialTemplateBuilder.append(">物料名称：<font color=\"info\">").append(t.getMaterialName()).append("</font>\n")
                            .append(">消耗总量：<font color=\"warning\">").append(t.getConsumptionQuantity()).append(" ").append(t.getMaterialUnit()).append("</font>\n")
                            .append(">真实消耗：<font color=\"warning\">").append(t.getRealLossRateStr()).append("</font>\n")
                            .append(">标准消耗：<font color=\"warning\">").append(t.getStandardLossRateStr()).append("</font>\n")
                            .append(">投入产出比：<font color=\"warning\">").append(t.getStandardLossAndRealLossRateStr()).append("</font>\n");
                });
            }

            // bom表中配置 但mes没有使用物料列表
            List<GetMachineGroupReportOutVO.GetMachineGroupDetialReportOutVO.UseMaterialOutVO> mesNoUseMaterialList = detail.getUseMaterialList().stream().filter(t -> t.getMesUseMaterial().equals(BooleanEnum.FALSE.getCode())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(mesNoUseMaterialList)){
                mesNoUseMaterialList.forEach(t-> useMaterialTemplateBuilder.append(">物料名称：<font color=\"info\">").append(t.getMaterialName()).append("</font>\n")
                        .append(">消耗总量：<font color=\"warning\">").append(t.getConsumptionQuantity()).append(" ").append(t.getMaterialUnit()).append("</font>\n")
                        .append(">真实消耗：<font color=\"warning\">").append(t.getRealLossRateStr()).append("</font>\n")
                        .append(">标准消耗：<font color=\"warning\">").append(t.getStandardLossRateStr()).append("</font>\n")
                        .append(">投入产出比：<font color=\"warning\">").append(t.getStandardLossAndRealLossRateStr()).append("</font>\n"));
            }

            String machineReport = (template1 + useMaterialTemplateBuilder + template2)
                    .replace("${machineName}", detail.getMachineName())
                    .replace("${process}", detail.getProcess())
                    .replace("${planTicketNo}", detail.getPlanTicketNo())
                    .replace("${productionName}", detail.getProductionName())
                    .replace("${plannedProductionCapacityColor}", detail.getPlannedProductionCapacity() == null || detail.getPlannedProductionCapacity().equals("未排产") ||  new BigDecimal(detail.getPlannedProductionCapacity()).compareTo(BigDecimal.ZERO) == 0 ? "warning" : "info")
                    .replace("${plannedProductionCapacity}", detail.getPlannedProductionCapacity() == null || detail.getPlannedProductionCapacity().equals("未排产") || new BigDecimal(detail.getPlannedProductionCapacity()).compareTo(BigDecimal.ZERO) == 0 ? "未排产" : detail.getPlannedProductionCapacity().toString())
                    .replace("${realProductionCapacity}", detail.getRealProductionCapacity() == null ? "0" : detail.getRealProductionCapacity().toString())
                    .replace("${planReachRateStr}", detail.getPlannedProductionCapacity() == null || detail.getPlannedProductionCapacity().equals("未排产") || new BigDecimal(detail.getPlannedProductionCapacity()).compareTo(BigDecimal.ZERO) == 0 || detail.getPlanReachRateStr() == null ? "未排产" : detail.getPlanReachRateStr())
                    .replace("${planReachRateColor}", detail.getPlanReachRateColor())
                    .replace("${defectiveProduct}", detail.getDefectiveProduct() == null ? "0" : detail.getDefectiveProduct().toString())
                    .replace("${lossRateStr}", detail.getLossRateStr() == null ? "0" : detail.getLossRateStr())
                    .replace("${lossRateColor}", detail.getLossRateColor())
                    .replace("${teamUsers}", detail.getTeamUsers())
                    .replace("${remark}", StringUtils.isBlank(detail.getRemark()) ? "" : detail.getRemark());
            if (StringUtils.isBlank(detail.getRemark())) {
                machineReport = machineReport.replace(">备注：<font color=\"info\"></font>\n", "");
            }
            result.append(machineReport + "\n");
        }

        StringBuilder h5UrlSb = new StringBuilder("http://datereport-h5.jihong.cn/#/index?produceDate="
                + DateUtil.format(report.getProduceDate(),DatePattern.NORM_DATE_PATTERN) + "&shit=" + report.getShift() + "&groupIds=" + report.getGroupId() + "&token=" + SecurityUtil.getCompanySite());
        result.append(">用料明细见：[详报链接](" + h5UrlSb + ")\n");
        log.info("发送生产日报：{}", header + result);
        return header + result;
    }

    @Override
    public List<GetMessageTypesByCompanyCodeOutVO> getMessageTypesByCompanyCode() {
        List<GetMessageTypesByCompanyCodeOutVO> list = new ArrayList<>();

        List<MessageTypePO> messageTypes = iMessageTypeService.getListByCompanyCode(SecurityUtil.getCompanySite());
        if(CollectionUtil.isNotEmpty(messageTypes)) {
            for (MessageTypePO messageTypePO : messageTypes) {
                GetMessageTypesByCompanyCodeOutVO out = new GetMessageTypesByCompanyCodeOutVO();
                BeanUtil.copyProperties(messageTypePO, out);
                list.add(out);
            }
        }
        return list;
    }

    @Override
    public Pagination<GetProductReportByFactoryOutVO> getProductPlanReport(GetProductReportByFactoryInVO getProductReportByFactoryInVO) {
        if (StringUtils.isBlank(getProductReportByFactoryInVO.getCompanyCode())) {
            getProductReportByFactoryInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }
        // 基础数据
        Page<GetProductReportByFactoryOutVO> productReportByFactoryOutVOS =
                productTicketService.getProductPlanReport(getProductReportByFactoryInVO);

        if (CollectionUtil.isEmpty(productReportByFactoryOutVOS.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<GetProductReportByFactoryOutVO> list =
                productReportByFactoryOutVOS.getRecords().stream().map(productReportByFactoryOutVO -> {

                    productReportByFactoryOutVO
                            .setShiftName(ProductShitEnum.getProductShitEnum(productReportByFactoryOutVO.getShift()).getName());
                    return productReportByFactoryOutVO;
                }).collect(Collectors.toList());

        return Pagination.newInstance(list, productReportByFactoryOutVOS.getTotal(),
                productReportByFactoryOutVOS.getPages());
    }


    private <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> partitionedLists = new ArrayList<>();
        int listSize = list.size();

        for (int i = 0; i < listSize; i += size) {
            int end = Math.min(i + size, listSize);
            List<T> sublist = list.subList(i, end);
            partitionedLists.add(new ArrayList<>(sublist));
        }

        return partitionedLists;
    }


    private List<String> splitStringByBytes(String input, int byteSize) {
        int addressIndex = input.indexOf(">用料明细见");
        // 链接在规定长度以外，可能被截断

        List<String> segments = new ArrayList<>();
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        int start = 0;

        while (start < bytes.length) {
            int end = start + byteSize;
            if (end >= bytes.length) {
                end = bytes.length;
            } else {
                // 确保不分割多字节字符
                while (end > start && (bytes[end] & 0xC0) == 0x80) {
                    end--;
                }
            }

            // 包含链接 且 链接在该段字符串内 链接的总长度超过了结尾长度 则直接 先截断到链接前个字符串
            if(addressIndex>=start && addressIndex < end && (addressIndex + 200 > end)){
                end = addressIndex;
            }

            String temSegment = new String(bytes, start, end - start, StandardCharsets.UTF_8);
            // 直接截取到前一句</font> 防止当前句中的<font>被截断
            if(temSegment.contains("</font>") && end < bytes.length){
                int lastIndexOf = temSegment.lastIndexOf("</font>");
                byte[] temBytes = temSegment.substring(0, lastIndexOf + 7).getBytes(StandardCharsets.UTF_8);
                end = start + temBytes.length;
            }

            String segment = new String(bytes, start, end - start, StandardCharsets.UTF_8);
            segments.add(segment);
            start = end;
        }

        return segments;
    }
}
