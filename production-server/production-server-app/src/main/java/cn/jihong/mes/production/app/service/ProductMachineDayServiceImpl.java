package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.util.AssertUtil2;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ProductMachineDayDTO;
import cn.jihong.mes.production.api.model.enums.ErpDocTypeEnum;
import cn.jihong.mes.production.api.model.enums.MaterialUseTypeEnum;
import cn.jihong.mes.production.api.model.enums.ProductShitEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineDayPO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementPageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetPlaceTypeInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDailySettlementOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineDayOutVO;
import cn.jihong.mes.production.api.service.IProductMachineDayService;
import cn.jihong.mes.production.api.service.IProductMachineMaterialApportionmentService;
import cn.jihong.mes.production.app.mapper.ProductMachineDayMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.BmbaTMaterialDTO;
import cn.jihong.oa.erp.api.model.dto.webservice.CancelApplicationToErpInDTO;
import cn.jihong.oa.erp.api.model.vo.BmbaTInVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import cn.jihong.oa.erp.api.service.ISfbaTService;
import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机台一日一结信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Slf4j
@DubboService
public class ProductMachineDayServiceImpl extends JiHongServiceImpl<ProductMachineDayMapper, ProductMachineDayPO> implements IProductMachineDayService {

    @DubboReference
    private IOocqlTService iOocqlTService;
    @DubboReference
    private ISfbaTService sfbaTService;
    @DubboReference
    private IA01Service a01Service;
    @Resource
    private IProductMachineMaterialApportionmentService productMachineMaterialApportionmentService;
    @DubboReference(timeout = 300000,retries = 0)
    private IInventoryToErpService iInventoryToErpService;
    @Resource
    private ProductMaterialServiceImpl productMaterialService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveProductMachineDay(ProductMachineDayDTO productMachineDayDTO) {
        ProductMachineDayPO po = getProductMachineDayPO(productMachineDayDTO);
        if (ObjectUtil.isNull(po)) {
            ProductMachineDayPO productMachineDayPO = BeanUtil.copyProperties(productMachineDayDTO, ProductMachineDayPO.class);
            productMachineDayPO.setCompanyCode(SecurityUtil.getCompanySite());
            productMachineDayPO.setCreateBy(SecurityUtil.getUserId());
            productMachineDayPO.setUpdateBy(SecurityUtil.getUserId());
            productMachineDayPO.setIsFinish(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            save(productMachineDayPO);
        } else {
            log.info("该机台一日一结信息已存在，不需要再创建");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void finishProductMachineDay(ProductMachineDayDTO productMachineDayDTO) {
        ProductMachineDayPO po = getProductMachineDayPO(productMachineDayDTO);
        if (ObjectUtil.isNotNull(po)) {
            if (po.getIsFinish().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                throw new CommonException("该机台一日一结信息已完成，不需要再次完成");
            }
            po.setIsFinish(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            updateById(po);
        } else {
            // 如果没创建日结信息的话，先创建日结信息，再去完成日日结
            saveProductMachineDay(productMachineDayDTO);
            finishProductMachineDay(productMachineDayDTO);
            log.info("该机台一日一结信息不存在，先创建日结信息，再去完成日日结");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void apportionmentProductMachineDay(ProductMachineDayDTO productMachineDayDTO) {
        LambdaUpdateWrapper<ProductMachineDayPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductMachineDayPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductMachineDayPO::getMachineName, productMachineDayDTO.getMachineName())
                .eq(ProductMachineDayPO::getProduceDate, productMachineDayDTO.getProduceDate())
                .eq(ProductMachineDayPO::getShift, productMachineDayDTO.getShift())
                .set(ProductMachineDayPO::getIsApportionment, Integer.valueOf(BooleanEnum.TRUE.getCode()));
        update(lambdaUpdateWrapper);
    }



    @Override
    public Pagination<ProductMachineDayOutVO>
        getProductMachineDays(GetMaterialDailySettlementPageInVO getMaterialDailySettlementInVO) {
        LambdaQueryWrapper<ProductMachineDayPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineDayPO::getCompanyCode, SecurityUtil.getCompanySite())
            .eq(StringUtils.isNotBlank(getMaterialDailySettlementInVO.getMachineName()),
                ProductMachineDayPO::getMachineName, getMaterialDailySettlementInVO.getMachineName())
            .eq(getMaterialDailySettlementInVO.getProduceDate() != null, ProductMachineDayPO::getProduceDate,
                getMaterialDailySettlementInVO.getProduceDate())
            .eq(getMaterialDailySettlementInVO.getShift() != null, ProductMachineDayPO::getShift,
                getMaterialDailySettlementInVO.getShift())
            .eq(getMaterialDailySettlementInVO.getIsFinish() != null, ProductMachineDayPO::getIsFinish,
                getMaterialDailySettlementInVO.getIsFinish())
            .eq(getMaterialDailySettlementInVO.getIsApportionment() != null, ProductMachineDayPO::getIsApportionment,
                getMaterialDailySettlementInVO.getIsApportionment())
                .orderByDesc(ProductMachineDayPO::getUpdateTime);

        IPage<ProductMachineDayPO> page = page(getMaterialDailySettlementInVO.getPage(), lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        List<UserDTO> userDTOS =
                a01Service.getUserInfoByIds(page.getRecords().stream().map(ProductMachineDayPO::getUpdateBy).distinct().collect(Collectors.toList()));
        Map<Long, String> userMap = userDTOS.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getName(), (k, v) -> v));

        return Pagination.newInstance(page.getRecords().stream().map(p -> {
            ProductMachineDayOutVO productMachineDayOutVO = BeanUtil.copyProperties(p, ProductMachineDayOutVO.class);
            productMachineDayOutVO.setUpdateByName(userMap.get(productMachineDayOutVO.getUpdateBy()));
            return productMachineDayOutVO;
        }).collect(Collectors.toList()),page);

    }


    @Override
    public ProductMachineDayOutVO
    getProductMachineDay(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        AssertUtil2.isBlank(getMaterialDailySettlementInVO.getMachineName(), "机台名称不能为空");
        AssertUtil2.isNull(getMaterialDailySettlementInVO.getProduceDate(), "生产日期不能为空");
        AssertUtil2.isNull(getMaterialDailySettlementInVO.getShift(), "班次不能为空");

        LambdaQueryWrapper<ProductMachineDayPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineDayPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtils.isNotBlank(getMaterialDailySettlementInVO.getMachineName()),
                        ProductMachineDayPO::getMachineName, getMaterialDailySettlementInVO.getMachineName())
                .eq(getMaterialDailySettlementInVO.getProduceDate() != null, ProductMachineDayPO::getProduceDate,
                        getMaterialDailySettlementInVO.getProduceDate())
                .eq(getMaterialDailySettlementInVO.getShift() != null, ProductMachineDayPO::getShift,
                        getMaterialDailySettlementInVO.getShift());

        ProductMachineDayPO productMachineDayPO = getOne(lambdaQueryWrapper);
        if (productMachineDayPO == null) {
            return null;
        }
        ProductMachineDayOutVO productMachineDayOutVO = BeanUtil.copyProperties(productMachineDayPO,ProductMachineDayOutVO.class);
        UserDTO userInfoById = a01Service.getUserInfoById(productMachineDayOutVO.getUpdateBy());
        if (ObjectUtil.isNotNull(userInfoById)) {
            productMachineDayOutVO.setUpdateByName(userInfoById.getName());
        }
        return productMachineDayOutVO;
    }


    @Override
    public List<EnumDTO> getOocqlTByType(GetPlaceTypeInVO getPlaceTypeInVO) {
        BmbaTInVO bmbaTInVO = new BmbaTInVO();
        bmbaTInVO.setCompanyCode(SecurityUtil.getCompanySite());
        bmbaTInVO.setProducts(getPlaceTypeInVO.getPlanTicketNo());
        bmbaTInVO.setProcess(getPlaceTypeInVO.getProcessCode());
        List<BmbaTMaterialDTO> bmbaTMaterialDTOS = sfbaTService.getByMaterial(bmbaTInVO);

        if (CollectionUtils.isNotEmpty(bmbaTMaterialDTOS)) {
            return bmbaTMaterialDTOS.stream().map(item -> {
                EnumDTO enumDTO = new EnumDTO();
                enumDTO.setCode(item.getPlace());
                enumDTO.setName(item.getPlaceName());
                return enumDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public ProductMachineDayOutVO getProductMachineDayApp(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        ProductMachineDayOutVO productMachineDay = getProductMachineDay(getMaterialDailySettlementInVO);
        if (ObjectUtil.isNull(productMachineDay)) {
            throw new CommonException("机台：" + getMaterialDailySettlementInVO.getMachineName() + "\n" + "生产日期："
                    + DateUtil.format(getMaterialDailySettlementInVO.getProduceDate(), "yyyy-MM-dd") + "\n" + "班次："
                    + ProductShitEnum.getProductShitEnum(getMaterialDailySettlementInVO.getShift()).getName() + "\n"
                    + " 还未开始生产，无法创建日结信息，请先开始当天生产任务");
        }
        return productMachineDay;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateIsFinish(Long id) {
        // 更新日结状态
        ProductMachineDayPO productMachineDayPO = getById(id);
        // 设置为相反
        Integer isFinish = productMachineDayPO.getIsFinish();
        if (isFinish.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            productMachineDayPO.setIsFinish(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            // 判断是否已经分摊
            if (productMachineDayPO.getIsApportionment().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                productMachineDayPO.setIsApportionment(Integer.valueOf(BooleanEnum.FALSE.getCode()));
                // 判断分摊后的数据是否已经扣料
                GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
                getMaterialDailySettlementInVO.setMachineName(productMachineDayPO.getMachineName());
                getMaterialDailySettlementInVO.setProduceDate(productMachineDayPO.getProduceDate());
                getMaterialDailySettlementInVO.setShift(productMachineDayPO.getShift());
                List<GetMaterialDailySettlementOutVO> materialDailyApportionment =
                    productMachineMaterialApportionmentService
                        .getMaterialDailyApportionment(getMaterialDailySettlementInVO).stream()
                            // 只需要分摊的数据
                        .filter(
                            item -> item.getUseType().equals(MaterialUseTypeEnum.APPORTIONMENT_MATERIAL.getIntCode()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(materialDailyApportionment)) {
                    List<String> deductionNos = materialDailyApportionment.stream()
                        .map(GetMaterialDailySettlementOutVO::getDeductionNo).distinct().collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(deductionNos)) {
                        // 删除erp中的数据
                        CancelApplicationToErpInDTO inDTO = new CancelApplicationToErpInDTO();
                        inDTO.setProg(ErpDocTypeEnum.STRIPPING.getCode());
                        inDTO.setDocType("1");
                        inDTO.setDocNos(deductionNos);
                        log.info("---CancelApplicationToErpInDTO:{}", JSON.toJSONString(inDTO));
                        ResponseCodeOutVO responseCodeOutVO = null;
                        try {
                            responseCodeOutVO = iInventoryToErpService.cancelApplicationToErp(inDTO);
                        } catch (Exception e) {
                            log.error("---取消扣料失败:{}", e.getMessage());
                            throw new CommonException("取消扣料失败：" + e.getMessage());
                        }
                        if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
                            throw new CommonException(responseCodeOutVO.getMessage());
                        }

                        // 删除智物流中的数据  -- 退料
                        String nextIdStr = responseCodeOutVO.getMessage();
//                        materialDailyApportionment.stream().forEach(item -> {
//                            productMaterialService.updateBarcodeStore(item.getConsumptionQuantity(),
//                                    item.getMaterialBarcodeNo(), nextIdStr, 1);
//                        });

                        productMaterialService.setUpdateBarcodeStoreDTOS(BeanUtil.copyToList(materialDailyApportionment,
                            ProductMachineMaterialApportionmentPO.class), nextIdStr, 1);                        
                    }
                    // 删除分摊物料信息
                    productMachineMaterialApportionmentService.removeBatchByIds(materialDailyApportionment.stream()
                        .map(GetMaterialDailySettlementOutVO::getId).distinct().collect(Collectors.toList()));
                }
            }
        } else {
            productMachineDayPO.setIsFinish(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        updateById(productMachineDayPO);
    }


    private ProductMachineDayPO getProductMachineDayPO(ProductMachineDayDTO productMachineDayDTO) {
        LambdaQueryWrapper<ProductMachineDayPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineDayPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductMachineDayPO::getMachineName, productMachineDayDTO.getMachineName())
                .eq(ProductMachineDayPO::getProduceDate, productMachineDayDTO.getProduceDate())
                .eq(ProductMachineDayPO::getShift, productMachineDayDTO.getShift());
        return getOne(lambdaQueryWrapper);
    }


}
