package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductUseMaterialsInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDailySettlementOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductUseMaterialsOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机台物料消耗使用信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface ProductMachineMaterialApportionmentMapper extends JiHongMapper<ProductMachineMaterialApportionmentPO> {

    List<GetMaterialDailySettlementOutVO> getMaterialDailyApportionment(@Param("getMaterialDailySettlementInVO")GetMaterialDailySettlementInVO getMaterialDailySettlementInVO);

    IPage<GetProductUseMaterialsOutVO> useMaterials(IPage page,@Param("getProductUseMaterialsInVO") GetProductUseMaterialsInVO getProductUseMaterialsInVO, @Param("companyCode") String companyCode);
}
