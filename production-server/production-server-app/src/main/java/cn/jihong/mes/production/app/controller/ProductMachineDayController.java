package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ProductMachineDayDTO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementPageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetPlaceTypeInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineDayOutVO;
import cn.jihong.mes.production.api.service.IProductMachineDayService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 机台一日一结信息 前端控制器
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@RestController
@RequestMapping("/productMachineDay")
@ShenyuSpringMvcClient(path = "/productMachineDay/**")
public class ProductMachineDayController {

    @Resource
    private IProductMachineDayService productMachineDayService;

    /**
     * 获得机台日结列表信息
     */
    @PostMapping("/getProductMachineDays")
    public StandardResult<Pagination<ProductMachineDayOutVO>> getProductMachineDays(@RequestBody @Valid GetMaterialDailySettlementPageInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineDayService.getProductMachineDays(getMaterialDailySettlementInVO));
    }

    /**
     * 获得机台日结信息 APP
     */
    @PostMapping("/getProductMachineDayApp")
    public StandardResult<ProductMachineDayOutVO>
    getProductMachineDayApp(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachineDayService.getProductMachineDayApp(getMaterialDailySettlementInVO));
    }

    /**
     * 获得机台日结信息 PC
     */
    @PostMapping("/getProductMachineDayPC")
    public StandardResult<ProductMachineDayOutVO>
    getProductMachineDayPC(@RequestBody @Valid GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO));
    }

    /**
     * 更新机台日结信息
     */
    @PostMapping("/finishProductMachineDay")
    public StandardResult finishProductMachineDay(@RequestBody @Valid ProductMachineDayDTO productMachineDayDTO) {
        productMachineDayService.finishProductMachineDay(productMachineDayDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


    /**
     * 获得部位信息
     * @return
     */
    @PostMapping("/getPlaceType")
    public StandardResult<List<EnumDTO>> getPlaceType(@RequestBody @Valid GetPlaceTypeInVO getPlaceTypeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productMachineDayService.getOocqlTByType(getPlaceTypeInVO));
    }


    /**
     * 修改机台日结信息
     */
    @GetMapping("/updateIsFinish/{id}")
    public StandardResult updateIsFinish(@PathVariable("id") Long id) {
        productMachineDayService.updateIsFinish(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

}

