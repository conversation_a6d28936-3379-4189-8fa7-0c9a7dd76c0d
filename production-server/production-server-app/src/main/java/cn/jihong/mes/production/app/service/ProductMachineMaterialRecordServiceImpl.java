package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductMachineMaterialRecordDTO;
import cn.jihong.mes.production.api.model.enums.MaterialUseTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO;
import cn.jihong.mes.production.api.service.IProductMachineMaterialRecordService;
import cn.jihong.mes.production.app.mapper.ProductMachineMaterialRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 一日一结机台物料消耗记录信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@DubboService
public class ProductMachineMaterialRecordServiceImpl
    extends JiHongServiceImpl<ProductMachineMaterialRecordMapper, ProductMachineMaterialRecordPO>
    implements IProductMachineMaterialRecordService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO) {
        ProductMachineMaterialRecordPO productMachineMaterialRecordPO =
            BeanUtil.copyProperties(productMachineMaterialRecordDTO, ProductMachineMaterialRecordPO.class);
        productMachineMaterialRecordPO.setCreateBy(SecurityUtil.getUserId());
        productMachineMaterialRecordPO.setUpdateBy(SecurityUtil.getUserId());
        productMachineMaterialRecordPO.setCompanyCode(SecurityUtil.getCompanySite());
        save(productMachineMaterialRecordPO);
    }

    @Override
    public List<ProductMachineMaterialRecordPO>
        getConsumptionQuantity(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO) {
        return baseMapper.getConsumptionQuantity(productMachineMaterialRecordDTO);
    }

    @Override
    public List<ProductMachineMaterialRecordPO> getConsumptionQuantityNeedDistribution(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO) {
        return baseMapper.getConsumptionQuantityNeedDistribution(productMachineMaterialRecordDTO);
    }

    @Override
    public List<ProductMachineMaterialRecordPO> getUndercutByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductMachineMaterialRecordPO> lambdaQueryWrapper =
            Wrappers.lambdaQuery(ProductMachineMaterialRecordPO.class)
                .eq(ProductMachineMaterialRecordPO::getProductTicketId, productTicketId)
                .eq(ProductMachineMaterialRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductMachineMaterialRecordPO::getUseType, MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode());

        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ProductMachineMaterialRecordPO> getConsumptionQuantityApp(ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO) {
        return baseMapper.getConsumptionQuantityApp(productMachineMaterialRecordDTO);
    }
}
