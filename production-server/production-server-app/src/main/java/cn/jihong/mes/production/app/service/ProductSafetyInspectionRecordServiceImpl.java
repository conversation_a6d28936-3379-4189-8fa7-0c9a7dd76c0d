package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.po.ProductionShiftDetailPO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.api.service.IProductionShiftDetailService;
import cn.jihong.mes.production.api.model.po.ProductSafetyInspectionRecordPO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordMonthlyInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordMonthlyOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionRecordOutVO;
import cn.jihong.mes.production.api.service.IProductSafetyInspectionRecordService;
import cn.jihong.mes.production.app.mapper.ProductSafetyInspectionRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 安全点检记录 服务实现
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@DubboService
public class ProductSafetyInspectionRecordServiceImpl 
    extends JiHongServiceImpl<ProductSafetyInspectionRecordMapper, ProductSafetyInspectionRecordPO>
    implements IProductSafetyInspectionRecordService {

    @DubboReference
    private IProductionMachineService productionMachineService;

    @DubboReference
    private IProductionShiftDetailService productionShiftDetailService;

    @Override
    public Pagination<ProductSafetyInspectionRecordOutVO> page(ProductSafetyInspectionRecordQueryInVO inVO) {
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .like(StrUtil.isNotBlank(inVO.getMachineName()), 
                    ProductSafetyInspectionRecordPO::getMachineName, inVO.getMachineName())
                .eq(inVO.getProduceDate() != null, 
                    ProductSafetyInspectionRecordPO::getProduceDate, inVO.getProduceDate())
                .eq(inVO.getShift() != null, 
                    ProductSafetyInspectionRecordPO::getShift, inVO.getShift())
                .orderByDesc(ProductSafetyInspectionRecordPO::getCreateTime);

        IPage<ProductSafetyInspectionRecordPO> page = 
            page(new Page<>(inVO.getPageNum(), inVO.getPageSize()), queryWrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        List<ProductSafetyInspectionRecordOutVO> records = 
            BeanUtil.copyToList(page.getRecords(), ProductSafetyInspectionRecordOutVO.class);
        return Pagination.newInstance(records, page.getTotal(), page.getPages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(ProductSafetyInspectionRecordSaveInVO inVO) {
        // 校验机台名称
        List<ProductionMachineOutVO> machine = productionMachineService.getProductionMachineByErpMachineName(inVO.getMachineName());
        if (CollectionUtil.isEmpty(machine)) {
            throw new CommonException(inVO.getMachineName() + "机台不存在，请在erp中添加设备信息");
        }

        ProductSafetyInspectionRecordOutVO currentShiftInfo = getCurrentShiftInfo();
        inVO.setProduceDate(currentShiftInfo.getProduceDate());
        inVO.setShift(currentShiftInfo.getShift());
        // 检查是否已存在记录
        ProductSafetyInspectionRecordPO existRecord = 
            this.getByMachineDateShiftPO(inVO.getMachineName(), currentShiftInfo.getProduceDate(), currentShiftInfo.getShift());
        if (existRecord != null) {
            throw new CommonException("该机台在指定日期班次已存在点检记录");
        }

        ProductSafetyInspectionRecordPO po = new ProductSafetyInspectionRecordPO();
        BeanUtil.copyProperties(inVO, po);
        po.setCompanyCode(SecurityUtil.getCompanySite());
        po.setInspectionTime(new Date());
        po.setInspector(SecurityUtil.getUserId());
        po.setInspectorName(SecurityUtil.getUserName());
        return this.save(po);
    }

    @Override
    public ProductSafetyInspectionRecordOutVO getDetail(Long id) {
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getId, id)
                .eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite());
        
        ProductSafetyInspectionRecordPO po = this.getOne(queryWrapper);
        if (po == null) {
            throw new CommonException("记录不存在");
        }
        
        ProductSafetyInspectionRecordOutVO outVO = new ProductSafetyInspectionRecordOutVO();
        BeanUtil.copyProperties(po, outVO);
        return outVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id) {
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getId, id)
                .eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite());
        return this.remove(queryWrapper);
    }

    @Override
    public ProductSafetyInspectionRecordOutVO getByMachineDateShift(
            String machineName, Date produceDate, Integer shift) {
        ProductSafetyInspectionRecordPO po = 
            this.getByMachineDateShiftPO(machineName, produceDate, shift);
        if (po == null) {
            return null;
        }
        ProductSafetyInspectionRecordOutVO outVO = new ProductSafetyInspectionRecordOutVO();
        BeanUtil.copyProperties(po, outVO);
        return outVO;
    }

    @Override
    public List<ProductSafetyInspectionRecordOutVO> getListByDate(Date produceDate) {
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductSafetyInspectionRecordPO::getProduceDate, produceDate)
                .orderByAsc(ProductSafetyInspectionRecordPO::getMachineName)
                .orderByAsc(ProductSafetyInspectionRecordPO::getShift);

        List<ProductSafetyInspectionRecordPO> poList = this.list(queryWrapper);
        return BeanUtil.copyToList(poList, ProductSafetyInspectionRecordOutVO.class);
    }

    @Override
    public List<ProductSafetyInspectionRecordMonthlyOutVO> getMonthlyRecords(
            ProductSafetyInspectionRecordMonthlyInVO inVO) {
        // 获取当月所有记录
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .like(StrUtil.isNotBlank(inVO.getMachineName()),
                    ProductSafetyInspectionRecordPO::getMachineName, inVO.getMachineName())
                .apply("DATE_FORMAT(produce_date, '%Y-%m') = {0}", 
                    DateUtil.format(inVO.getYearMonth(), DatePattern.NORM_MONTH_PATTERN))
                .orderByAsc(ProductSafetyInspectionRecordPO::getMachineName)
                .orderByAsc(ProductSafetyInspectionRecordPO::getProduceDate)
                .orderByAsc(ProductSafetyInspectionRecordPO::getShift);

        List<ProductSafetyInspectionRecordPO> records = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(records)) {
            return null;
        }

        // 按机台分组
        return records.stream()
                .collect(Collectors.groupingBy(ProductSafetyInspectionRecordPO::getMachineName))
                .entrySet()
                .stream()
                .map(entry -> {
                    ProductSafetyInspectionRecordMonthlyOutVO outVO = 
                        new ProductSafetyInspectionRecordMonthlyOutVO();
                    outVO.setMachineName(entry.getKey());
                    outVO.setDayRecordList(this.buildDayRecords(entry.getValue()));
                    return outVO;
                })
                .collect(Collectors.toList());
    }
    
    private ProductSafetyInspectionRecordPO getByMachineDateShiftPO(
            String machineName, Date produceDate, Integer shift) {
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductSafetyInspectionRecordPO::getMachineName, machineName)
                .eq(ProductSafetyInspectionRecordPO::getProduceDate, produceDate)
                .eq(ProductSafetyInspectionRecordPO::getShift, shift);
        return this.getOne(queryWrapper);
    }

    private List<ProductSafetyInspectionRecordMonthlyOutVO.DayRecord> buildDayRecords(
            List<ProductSafetyInspectionRecordPO> records) {
        return records.stream()
                .collect(Collectors.groupingBy(po -> 
                    DateUtil.format(po.getProduceDate(), DatePattern.NORM_DATE_PATTERN)))
                .entrySet()
                .stream()
                .map(entry -> {
                    ProductSafetyInspectionRecordMonthlyOutVO.DayRecord dayRecord = 
                        new ProductSafetyInspectionRecordMonthlyOutVO.DayRecord();
                    dayRecord.setProduceDate(entry.getKey());
                    
                    Map<Integer, ProductSafetyInspectionRecordPO> shiftMap = entry.getValue().stream()
                            .collect(Collectors.toMap(
                                ProductSafetyInspectionRecordPO::getShift, 
                                po -> po,
                                (p1, p2) -> p1));
                    
                    dayRecord.setDayShiftCompleted(
                        isShiftCompleted(shiftMap.get(1))); // 白班 shift=1
                    dayRecord.setNightShiftCompleted(
                        isShiftCompleted(shiftMap.get(2))); // 夜班 shift=2
                    
                    return dayRecord;
                })
                .collect(Collectors.toList());
    }

    private boolean isShiftCompleted(ProductSafetyInspectionRecordPO record) {
        return record != null && record.getInspectionStatus() == 1;
    }

    @Override
    public Boolean canSubmitTodayShift(ProductTicketVO productTicketVO) {
        // 获取当前班次信息
        ProductSafetyInspectionRecordOutVO currentShiftInfo = getCurrentShiftInfo();
        
        // 查询该机台该班次的记录
        LambdaQueryWrapper<ProductSafetyInspectionRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductSafetyInspectionRecordPO::getMachineName, productTicketVO.getMachineName())
                .eq(ProductSafetyInspectionRecordPO::getProduceDate, currentShiftInfo.getProduceDate())
                .eq(ProductSafetyInspectionRecordPO::getShift, currentShiftInfo.getShift());

        ProductSafetyInspectionRecordPO one = getOne(queryWrapper);
        if (one != null) {
            // 如果已存在记录，则不能再次提交
            return false;
        }
        return true;
    }

    @Override
    public ProductSafetyInspectionRecordOutVO getCurrentShiftInfo() {
        // 获取当前时间
        DateTime now = DateUtil.date();

        // 获取班次配置
        List<ProductionShiftDetailPO> shiftSets =
                productionShiftDetailService.getShiftsByCompanyCode(SecurityUtil.getCompanySite());
        if (CollectionUtil.isEmpty(shiftSets)) {
            throw new CommonException("未配置班次信息");
        }

        // 获取当前时间所属的班次
        int hour = now.hour(true);
        int minute = now.minute();
        int currentMinutes = hour * 60 + minute;

        // 默认使用当天日期
        Date produceDate = DateUtil.beginOfDay(now).toJdkDate();
        Integer shift = null;

        // 遍历班次配置
        for (ProductionShiftDetailPO shiftSet : shiftSets) {
            String[] timeRanges = shiftSet.getTimeSlot().split(",");
            for (String timeRange : timeRanges) {
                String[] times = timeRange.split("-");
                String[] startTimeParts = times[0].split(":");
                String[] endTimeParts = times[1].split(":");

                int startMinutes = Integer.parseInt(startTimeParts[0]) * 60 + Integer.parseInt(startTimeParts[1]);
                int endMinutes = Integer.parseInt(endTimeParts[0]) * 60 + Integer.parseInt(endTimeParts[1]);

                // 处理跨天的情况
                if (endMinutes < startMinutes) {
                    // 如果当前时间在 00:00 到结束时间之间，说明是前一天的班次
                    if (currentMinutes < endMinutes) {
                        produceDate = DateUtil.beginOfDay(DateUtil.offsetDay(now, -1)).toJdkDate();
                        shift = shiftSet.getSerialNo();
                        break;
                    }
                    // 如果当前时间在开始时间到 23:59 之间
                    if (currentMinutes >= startMinutes) {
                        shift = shiftSet.getSerialNo();
                        break;
                    }
                } else {
                    // 不跨天的情况
                    if (currentMinutes >= startMinutes && currentMinutes < endMinutes) {
                        shift = shiftSet.getSerialNo();
                        break;
                    }
                }
            }
            if (shift != null) {
                break;
            }
        }

        if (shift == null) {
            // 大于6点 且 小于20 点，则返回白班
            if (hour >= 6 && hour < 20) {
                shift = 1;
            }
            // 大于20 点，则返回夜班  或者 小于6点，则返回夜班
            if (hour >= 20 || hour < 6) {
                shift = 2;
            }
        }

        ProductSafetyInspectionRecordOutVO outVO = new ProductSafetyInspectionRecordOutVO();
        outVO.setProduceDate(produceDate);
        outVO.setShift(shift);
        return outVO;
    }
} 