package cn.jihong.mes.production.app.service.boxCode;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.jihong.channel.api.model.vo.out.SyncEventActiveOutVO;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.constant.RabbitConsts;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeRecordDTO;
import cn.jihong.mes.production.api.model.dto.ProductSettingDelayedActiveDTO;
import cn.jihong.mes.production.api.model.enums.BarcodeOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.BoxActiveEnum;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.enums.MQBizCodeEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeDetailInVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.util.RabbitMqUtil;
import cn.jihong.oa.erp.api.model.dto.ImafTDTO;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.model.po.LsafTPO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 麦当劳处理箱码
 */
@Slf4j
@DubboService
public class CompanyMDLBoxCodeService extends BoxCodeHelper {

    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;
    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;
    @DubboReference
    private ILsafTService iLsafTService;
    @DubboReference
    private IImafTService imafTService;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductSettingDelayedActiveService productSettingDelayedActiveService;
    @Resource
    private IProductBoxBarcodeRecordService productBoxBarcodeRecordService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IImaalTService iImaalTService;
    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;

    @DubboReference
    private ISfacTService sfacTService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;



    @Override
    public String getCompanyCode() {
        return CompanyBoxEnum.COMPANY_MDL.getCode();
    }

//    @Override
//    public List<BarcodeDetailDTO.BoxInfo> handleBoxCode(HandleBoxCodeDTO handleBoxCodeDTO) {
//        List<BarcodeDetailDTO.BoxInfo> boxInfos = Lists.newArrayList();
//        List<GetProductBoxBarcodeDetailOutVO> productBoxBarcodeOutVOS =
//                productBoxBarcodeService.getListByBarcodes(handleBoxCodeDTO.getBoxCodes());
//        if (CollectionUtil.isNotEmpty(productBoxBarcodeOutVOS) && productBoxBarcodeOutVOS.size() > 1) {
//            List<String> barcodeNames = productBoxBarcodeOutVOS.stream().map(GetProductBoxBarcodeDetailOutVO::getBarcodeName)
//                    .distinct().collect(Collectors.toList());
//            throw new CommonException("存在多个码段的箱码" + JSON.toJSONString(barcodeNames));
//        }
//        if (CollectionUtil.isEmpty(productBoxBarcodeOutVOS)) {
//            throw new CommonException("找不到箱码信息");
//        }
//        GetProductBoxBarcodeDetailOutVO getProductBoxBarcodeOutVO = productBoxBarcodeOutVOS.get(0);
//        for (String boxCode : handleBoxCodeDTO.getBoxCodes()) {
//            BarcodeDetailDTO.BoxInfo boxInfo = new BarcodeDetailDTO.BoxInfo();
//            boxInfo.setBoxCode(boxCode);
//            boxInfo.setBoxQuantity(BigDecimal.valueOf(getProductBoxBarcodeOutVO.getBoxNum()));
//            // 料号
//            boxInfo.setA(getProductBoxBarcodeOutVO.getMaterialCode());
//            // 生产日期
//            boxInfo.setB(cn.hutool.core.date.DateUtil.format(getProductBoxBarcodeOutVO.getProductionDate(),
//                    DatePattern.PURE_DATE_PATTERN));
//            // 箱数量
//            boxInfo.setC(String.valueOf(getProductBoxBarcodeOutVO.getBoxNum()));
//            // 批次识别码
//            boxInfo.setJ(getProductBoxBarcodeOutVO.getBatchCode());
//            // 机台
//            boxInfo.setK(getProductBoxBarcodeOutVO.getMachine());
//            // 班次
//            boxInfo.setL(getProductBoxBarcodeOutVO.getShift());
//            // 保质期 + 2 年
//            boxInfo.setM(getProductBoxBarcodeOutVO.getExpirationDate());
//            // 流水号 截取最后10位
//            if (boxCode.length() >= 10) {
//                boxInfo.setX(boxCode.substring(boxCode.length() - 10));
//            }
//            boxInfos.add(boxInfo);
//        }
//        return boxInfos;
//    }

    @Override
    public void verifyCaseCodeByErp(String caseCode, String palletCode) {
        // 校验箱码是否存在于ERP中
        String[] split = caseCode.split(",");
        List<String> list = Arrays.asList(split);
        if(CollectionUtil.isNotEmpty(list)){
            if(list.contains(palletCode)){
                throw new CommonException("箱码不允许与栈板码一致");
            }

            // 校验箱码的结构是否是麦当劳的格式
            for (String caseCodeTem : list) {
                if (caseCodeTem.indexOf("4DCD") == -1) {
                    throw new CommonException("箱码格式不是麦当劳箱码格式：" + caseCodeTem);
                }
            }

            // 校验箱码中不能存在不同的批次
            List<String> lotNos = productBoxBarcodeDetailService.getLotNosByBarcodes(list);
            if (lotNos.size() > 1) {
                throw new CommonException("存在不相同批次" + JSON.toJSONString(lotNos));
            }

            Set<String> set = new HashSet<>();
            List<String> duplicates = new ArrayList<>();
            for(String caseCodeTem : list){
                if(!set.add(caseCodeTem)){
                    duplicates.add(caseCodeTem);
                }
            }
            if(CollectionUtil.isNotEmpty(duplicates)){
                throw new CommonException("箱码中存在相同的数值：" + JSON.toJSONString(duplicates));
            }

            List<LsafTPO> sameCaseCodeList = iLsafTService.getByBoxCodes(list);
            if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
                throw new CommonException("箱码已在erp使用：" + JSON.toJSONString(sameCaseCodeList.stream().map(lsafTPO -> lsafTPO.getLsaf001() + "入库申请单号:"+ lsafTPO.getLsaf009()).collect(Collectors.toList())));
            }
        }
        
        // 校验箱码是否存在于数据库中
        Set<String> barcodeSet = productBoxBarcodeDetailDetailService.getByBarcodeNos(list).stream()
            .map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toSet());
        List<String> doesNotExist = Lists.newArrayList();
        for(String caseCodeTem : list){
            if(barcodeSet.add(caseCodeTem)){
                doesNotExist.add(caseCodeTem);
            }
        }
        if(CollectionUtil.isNotEmpty(doesNotExist)){
            throw new CommonException("箱码中存在未打印的条码：" + JSON.toJSONString(doesNotExist));
        }

    }



    @Override
    public void sendMessage(Integer code, String direct, String caseCode){
        RabbitMqUtil.sendMessage(MQBizCodeEnum.BOX_ACTIVA.getCode(), RabbitConsts.DIRECT,caseCode);
    }


    @Override
    public String activeBoxCode(String barcode){
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(barcode);
        if (productBoxBarcodeDetailDetailPO == null) {
            throw new CommonException("未找到箱码信息：" + barcode);
        }
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productBoxBarcodeDetailDetailPO.getActiva())) {
            log.error("箱码已激活，不需要再次激活：" + barcode);
            return "箱码已激活，不需要再次激活：" + barcode;
        }
        if (BoxActiveEnum.DESTROY.getCode().equals(productBoxBarcodeDetailDetailPO.getActiva())) {
            throw new CommonException("箱码已消箱，不可再次激活：" + barcode);
        }
        if (!CompanyBoxEnum.COMPANY_MDL.getCode().equals(productBoxBarcodeDetailDetailPO.getCustomerNo())) {
            throw new CommonException("不是麦当劳公司的箱码：" + barcode);
        }

        ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO = new ProductSettingDelayedActiveDTO();
        productSettingDelayedActiveDTO.setMaterialCode(productBoxBarcodeDetailDetailPO.getMaterialCode());
        List<ProductSettingDelayedActiveDTO> list = productSettingDelayedActiveService.getList(productSettingDelayedActiveDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            // 延迟激活
            log.info("延迟激活：" + barcode);
            return "延迟激活：" + barcode;
        }

        // 更新skucode
        productBoxBarcodeDetailDetailService.updateSkuCode(barcode);

        SyncEventActiveOutVO syncEventUploadOutVO = (SyncEventActiveOutVO)productOutboundService.activeBoxCode(barcode);

        log.info("---激活箱码返回结果:syncEventUploadOutVO={}", com.alibaba.fastjson.JSON.toJSONString(syncEventUploadOutVO));
        SyncEventActiveOutVO.DataVO data = syncEventUploadOutVO.getData();
        // 提取成功和失败的个数
        int successCount = data.getSuccessEpcSet().size();
        if (successCount > 0) {
            log.info("激活成功: " + barcode);
            log.info("更新本地箱码激活状态: " + barcode);
            productBoxBarcodeDetailDetailService.activa(barcode);
        } else {
            throw new CommonException("激活失败: " + barcode);
        }

        return syncEventUploadOutVO.getData().getMessage();
    }

    @Override
    public Long applyBoxBarcodeDetail(AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO) {
        ImafTDTO imafTDTO = imafTService.getByProductNo(addProductBoxBarcodeDetailInVO.getProductNo());
        ProductBoxBarcodePO productBoxBarcodePO =
                productBoxBarcodeService.getById(addProductBoxBarcodeDetailInVO.getProductBarcodeId());

        productBoxBarcodeDetailService.verify(addProductBoxBarcodeDetailInVO.getPlanTicketNo(), productBoxBarcodePO.getCustomerNo());

        if (productBoxBarcodePO.getBarcodeRemaining().compareTo(addProductBoxBarcodeDetailInVO.getApplyBarcodeCount()) < 0) {
            throw new CommonException("申请数量超出剩余数量，请检查");
        }
        String barcodeNoStart = productBoxBarcodePO.getBarcodeNoStart();
        String barcodeNoEnd = productBoxBarcodePO.getBarcodeNoEnd();
        // 将16进制的字符串转换为十进制数值进行比较
        BigInteger startHex = new BigInteger(barcodeNoStart.substring(barcodeNoStart.length() - 13), 16);
        BigInteger endHex = new BigInteger(barcodeNoEnd.substring(barcodeNoEnd.length() - 13), 16);

        String barcodeCurrent = null;
        if (productBoxBarcodePO.getBarcodeCurrent() == null) {
            barcodeCurrent = productBoxBarcodePO.getBarcodeNoStart();
        } else {
            barcodeCurrent = productBoxBarcodePO.getBarcodeCurrent();
        }
        BigInteger currentHex = new BigInteger(barcodeCurrent.substring(barcodeCurrent.length() - 13), 16);
        // 计算下一个条码
        // 获得后续第N个条码
        String nextNBarcode = productBoxBarcodeDetailService.getNextBarcode(barcodeNoStart, currentHex,new BigInteger(String.valueOf(addProductBoxBarcodeDetailInVO.getApplyBarcodeCount())));
        String lastNBarcode = productBoxBarcodeDetailService.getNextBarcode(barcodeNoStart, currentHex, new BigInteger(
                String.valueOf(addProductBoxBarcodeDetailInVO.getApplyBarcodeCount()-1)));

        productBoxBarcodePO.setBarcodeCurrent(nextNBarcode);
        productBoxBarcodePO.setBarcodeRemaining(
                productBoxBarcodePO.getBarcodeRemaining() - addProductBoxBarcodeDetailInVO.getApplyBarcodeCount());
        productBoxBarcodeService.updateById(productBoxBarcodePO);

        // 将16进制的字符串转换为十进制数值进行比较
        BigInteger startDetailHex = new BigInteger(barcodeCurrent.substring(barcodeCurrent.length() - 13), 16);
        BigInteger endDetailHex = new BigInteger(lastNBarcode.substring(lastNBarcode.length() - 13), 16);

        // 明细开始要大于等于号段开始   明细结束要小于等于号段结束
        boolean flag = startDetailHex.compareTo(startHex) >= 0 && endDetailHex.compareTo(endHex) <= 0;
        if (!flag) {
            throw new CommonException("条码范围不在码段范围内，请检查");
        }

        // 查询是否存在重叠的号码段
        LambdaQueryWrapper<ProductBoxBarcodeDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        String finalBarcodeCurrent = barcodeCurrent;
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductBoxBarcodeDetailPO::getProductBarcodeId, addProductBoxBarcodeDetailInVO.getProductBarcodeId())
                .and(wrapper -> wrapper
                        .le(ProductBoxBarcodeDetailPO::getBarcodeNoStart, lastNBarcode)
                        .ge(ProductBoxBarcodeDetailPO::getBarcodeNoEnd, finalBarcodeCurrent)
                );

        List<ProductBoxBarcodeDetailPO> list = productBoxBarcodeDetailService.list(lambdaQueryWrapper);
        if (cn.jihong.common.util.CollectionUtil.isNotEmpty(list)) {
            throw new CommonException("该客户已存在该箱码范围，请勿重复添加");
        }

        // 计算条数
        BigInteger totalCount = endDetailHex.subtract(startDetailHex).add(BigInteger.ONE);
        if (totalCount.compareTo(BigInteger.ZERO) <= 0) {
            throw new CommonException("条码范围不合法，请检查");
        }

        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = new ProductBoxBarcodeDetailPO();
        productBoxBarcodeDetailPO.setCompanyCode(SecurityUtil.getCompanySite());
        productBoxBarcodeDetailPO.setProductBarcodeId(addProductBoxBarcodeDetailInVO.getProductBarcodeId());
        productBoxBarcodeDetailPO.setBarcodeNoStart(barcodeCurrent);
        productBoxBarcodeDetailPO.setBarcodeNoEnd(lastNBarcode);
        productBoxBarcodeDetailPO.setBarcodeTotal(addProductBoxBarcodeDetailInVO.getApplyBarcodeCount());
        productBoxBarcodeDetailPO.setBarcodeRemaining(productBoxBarcodePO.getBarcodeRemaining());

        productBoxBarcodeDetailPO.setPlanTicketNo(addProductBoxBarcodeDetailInVO.getPlanTicketNo());
        // 根据工单号查询工单信息
        productBoxBarcodeDetailPO.setProductName(addProductBoxBarcodeDetailInVO.getProductName());
        productBoxBarcodeDetailPO.setMaterialCode(addProductBoxBarcodeDetailInVO.getProductNo());
        productBoxBarcodeDetailPO.setBoxNum(addProductBoxBarcodeDetailInVO.getBoxSpece());

        // 班次信息
        productBoxBarcodeDetailPO.setProductionDate(addProductBoxBarcodeDetailInVO.getProductionDate());
        productBoxBarcodeDetailPO.setLotNo(DateUtil.format(addProductBoxBarcodeDetailInVO.getProductionDate(),
                DatePattern.PURE_DATE_PATTERN));
        productBoxBarcodeDetailPO.setBatchCode(addProductBoxBarcodeDetailInVO.getBatchCode());
        productBoxBarcodeDetailPO.setMachine(addProductBoxBarcodeDetailInVO.getMachine());
        productBoxBarcodeDetailPO.setShift(addProductBoxBarcodeDetailInVO.getShift());
        DateTime last2Year = null;
        if (imafTDTO != null && (imafTDTO.getImaf031().compareTo(BigDecimal.ZERO) > 0
            || imafTDTO.getImaf032().compareTo(BigDecimal.ZERO) > 0)) {
            // 过期时间
            last2Year =
                DateUtil
                    .offset(addProductBoxBarcodeDetailInVO.getProductionDate(), DateField.MONTH,
                        imafTDTO.getImaf031().intValue())
                    .offset(DateField.DAY_OF_MONTH, imafTDTO.getImaf032().intValue());

            // 当是24个月 0 天的时候
            if (imafTDTO.getImaf031().intValue() == 24
                    && imafTDTO.getImaf032().intValue() == 0) {
                // 孝感需要减去 1天
                if (SecurityUtil.getCompanySite().equals("SITE-20")) {
                    last2Year.offset(DateField.DAY_OF_MONTH, -1);
                }
            }
        } else {
            throw new CommonException("根据工单查询不到的料件过期时间信息，请联系相关人员维护后再次申请");
//            // 过期时间默认加2年
//            last2Year = DateUtil.offset(addProductBoxBarcodeDetailInVO.getProductionDate(), DateField.YEAR, 2);
        }
        String last2YearStr = DateUtil.format(last2Year, DatePattern.NORM_DATE_PATTERN);
        productBoxBarcodeDetailPO.setExpirationDate(last2YearStr);
        productBoxBarcodeDetailPO.setCustomerNo(addProductBoxBarcodeDetailInVO.getCustomerNo());
        productBoxBarcodeDetailPO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeDetailPO.setUpdateBy(SecurityUtil.getUserId());


        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productBoxBarcodeDetailPO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(sfaaTVO.getSfaa010());
        if (materialsInfoDTO != null) {
            if (StringUtil.isBlank(materialsInfoDTO.getImaaua017()) || StringUtil.isBlank(materialsInfoDTO.getImaaua018())) {
                throw new CommonException("找不到工单的麦中8位码信息请联系相关人员维护后再次申请");
            }
            productBoxBarcodeDetailPO.setSkuCode(materialsInfoDTO.getImaaua017());
            productBoxBarcodeDetailPO.setSkuName(materialsInfoDTO.getImaaua018());
        }

        productBoxBarcodeDetailService.save(productBoxBarcodeDetailPO);

        // 保存条码详情
        saveBoxBarcodeDetailDetail(productBoxBarcodeDetailPO);

        return productBoxBarcodeDetailPO.getId();
    }

    private void saveBoxBarcodeDetailDetail(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO) {
        // 获取条码列表
        List<String> barcodeList = Lists.newArrayList();
        String barcodeNoStart = productBoxBarcodeDetailPO.getBarcodeNoStart();
        BigInteger startHex = new BigInteger(barcodeNoStart.substring(barcodeNoStart.length() - 13), 16);
        for (int i = 1; i <= productBoxBarcodeDetailPO.getBarcodeTotal(); i++) {
            String barcode = String.format("%013X", startHex);
            barcode = barcodeNoStart.substring(0, barcodeNoStart.length() - 13) + barcode;
            barcodeList.add(barcode);
            startHex = startHex.add(BigInteger.ONE);
        }
        log.info("打印条码列表：{}", barcodeList);

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByBarcodeNos(barcodeList);
        Set<String> barcodeSet = productBoxBarcodeDetailDetailPOS.stream().map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toSet());

        // 保存条码详情
        List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOS = barcodeList.stream().map(barcode -> {
            if (barcodeSet.contains(barcode)) {
                return null;
            }
            ProductBoxBarcodeDetailDetailDTO productBoxBarcodeDetailDetailDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeDetailDetailDTO.class);
            productBoxBarcodeDetailDetailDTO.setId(null);
            productBoxBarcodeDetailDetailDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
            productBoxBarcodeDetailDetailDTO.setBarcodeNo(barcode);
            productBoxBarcodeDetailDetailDTO.setCustomerNo(productBoxBarcodeDetailPO.getCustomerNo());
            productBoxBarcodeDetailDetailDTO.setSkuCode(productBoxBarcodeDetailPO.getSkuCode());
            return productBoxBarcodeDetailDetailDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        productBoxBarcodeDetailDetailService.saveProductBoxBarcodeDetailDetailPOs(productBoxBarcodeDetailDetailDTOS);

        // 更新到wms
        createbarcodeToWms(productBoxBarcodeDetailPO, productBoxBarcodeDetailDetailDTOS);
    }

    @Override
    public List<String> getBarcodeList(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO, BarcodeOperateTypeEnum barcodeOperateTypeEnum) {
        if (barcodeOperateTypeEnum.equals(BarcodeOperateTypeEnum.PRINT)) {
            productBoxBarcodeDetailPO.setPrintStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        if (barcodeOperateTypeEnum.equals(BarcodeOperateTypeEnum.RE_PRINT)) {
            productBoxBarcodeDetailPO.setPrintStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        if (barcodeOperateTypeEnum.equals(BarcodeOperateTypeEnum.SPURT)) {
            productBoxBarcodeDetailPO.setSpurtStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        if (barcodeOperateTypeEnum.equals(BarcodeOperateTypeEnum.RE_SPURT)) {
            productBoxBarcodeDetailPO.setSpurtStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        // 更新状态
        productBoxBarcodeDetailService.updateById(productBoxBarcodeDetailPO);

        // 保存条码记录
        ProductBoxBarcodeRecordDTO productBoxBarcodeRecordDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeRecordDTO.class);
        productBoxBarcodeRecordDTO.setOperationType(barcodeOperateTypeEnum.getCode());
        productBoxBarcodeRecordDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
        productBoxBarcodeRecordDTO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordDTO.setUpdateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordService.saveProductBoxBarcodeRecord(productBoxBarcodeRecordDTO);

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByDetailId(productBoxBarcodeDetailPO.getId());
        if (CollectionUtil.isEmpty(productBoxBarcodeDetailDetailPOS)) {
            throw new CommonException("找不到对应箱码信息" + productBoxBarcodeDetailPO.getId());
        }
        return productBoxBarcodeDetailDetailPOS.stream().map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        DateTime parse = DateUtil.parse("2028-02-29", DatePattern.NORM_DATE_FORMATTER);
        DateTime offset = DateUtil
                .offset(parse, DateField.MONTH,
                        24)
                .offset(DateField.DAY_OF_MONTH, 0);
        System.out.println("====================="+offset);

        System.out.printf("++++++++++++++++++++++++" + offset.offset(DateField.DAY_OF_MONTH, -1));
    }


}