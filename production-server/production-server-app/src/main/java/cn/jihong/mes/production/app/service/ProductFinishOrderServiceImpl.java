package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.dto.ProductMachineTicketRelationDTO;
import cn.jihong.mes.production.api.model.po.ProductFinishOrderPO;
import cn.jihong.mes.production.api.model.po.ProductOutboundPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetByMachineNameInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductPlanTicketVO;
import cn.jihong.mes.production.api.model.vo.in.SaveFinishOrderInfoInVO;
import cn.jihong.mes.production.api.model.vo.in.UnfinishedOrderListInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.mapper.ProductFinishOrderMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.service.IEcaaucTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import cn.jihong.oa.erp.api.service.IWorkReportInformationPushToErpService;
import cn.jihong.tms.api.model.enums.TicketStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 结单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@DubboService
public class ProductFinishOrderServiceImpl extends JiHongServiceImpl<ProductFinishOrderMapper, ProductFinishOrderPO>
    implements IProductFinishOrderService {

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductInfoService productInfoService;
    @DubboReference(timeout = 300000,retries = 0)
    private IWorkReportInformationPushToErpService workReportInformationPushToErpService;
    @DubboReference
    private IProductionMachineService productionMachineService;
    @DubboReference
    private IA01Service ia01Service;
    @Resource
    private IProductSettlementMaterialService iProductSettlementMaterialService;
    @Resource
    private IProductSettlementEndProductionService iProductSettlementEndProductionService;
    @Resource
    private IProductSettlementWorkReportService iProductSettlementWorkReportService;
    @Resource
    private IProductSettlementInfoService iProductSettlementInfoService;
    @Resource
    private IProductLastPalletService productLastPalletService;
    @Resource
    private IProductMaterialService productMaterialService;
    @DubboReference
    private IEcaaucTService ecaaucTService;
    @DubboReference
    private ISfcbTService iSfcbTService;
    @Resource
    private IProductMachineTicketService productMachineTicketService;
    @Resource
    private IProductMachineTicketRelationService productMachineTicketRelationService;
    @Resource
    private IProductMachineMaterialApportionmentService productMachineMaterialApportionmentService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveFinishOrderInfo(SaveFinishOrderInfoInVO saveFinishOrderInfoInVO) {

        ProductTicketPO productTicketPO = productTicketService.getById(saveFinishOrderInfoInVO.getProductTicketId());
        if (productTicketPO == null) {
            throw new CommonException("工单id不存在" + saveFinishOrderInfoInVO.getProductTicketId());
        }
        if (!Integer.valueOf(TicketStatusEnum.IN_PROGRESS.getCode()).equals(productTicketPO.getStatus())) {
            throw new CommonException("工单不是处理中状态，工单" + productTicketPO.getTicketRequestId());
        }
        // 校验
        LambdaQueryWrapper<ProductFinishOrderPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFinishOrderPO::getProductTicketId, productTicketPO.getId());
        ProductFinishOrderPO productFinishOrderPO = getOne(lambdaQueryWrapper);
//        if (ObjectUtil.isNotEmpty(productFinishOrderPO)) {
//            throw new CommonException("该工单已下机，请勿重复操作");
//        }

        // 更新设备止码
       // productTicketService.updateMachineStopNo(saveFinishOrderInfoInVO.getProductTicketId(), saveFinishOrderInfoInVO.getMachineStopNo());

        // 更新工单状态
        productTicketService.updateStatus(productTicketPO,
                TicketStatusEnum.COMPLETED.getCode(), saveFinishOrderInfoInVO.getProducedQuantity());


        // 获得工序的单位
        EcaaucTPO ecaaucTPO =
            ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());

        // 保存
        Long id = null;
        if (productFinishOrderPO == null) {
            productFinishOrderPO = new ProductFinishOrderPO();
        } else {
            id = productFinishOrderPO.getId();
        }
        BeanUtil.copyProperties(saveFinishOrderInfoInVO, productFinishOrderPO);
        productFinishOrderPO.setId(id);
        productFinishOrderPO.setCreateBy(SecurityUtil.getUserId());
        productFinishOrderPO.setMachineName(productTicketPO.getMachineName());
        productFinishOrderPO.setCompanyCode(productTicketPO.getCompanyCode());
        if (ecaaucTPO != null) {
            productFinishOrderPO.setUnit(ecaaucTPO.getEcaauc009());
        }
        saveOrUpdate(productFinishOrderPO);

        // 当还剩有未出站的栈板，才创建一条
        if (BigDecimal.ZERO.compareTo(saveFinishOrderInfoInVO.getProducedQuantity()) == -1) {
            // 创建一条栈板信息，但是这条栈板是未出站的
            ProductOutboundPO productOutboundPO =
                    productOutboundService.creatrPallet(saveFinishOrderInfoInVO.getProductTicketId(),
                            productTicketPO.getMachineName(), saveFinishOrderInfoInVO.getProducedQuantity());
        }


        // 创建生产汇总数据---根据生产工程单汇总
        productInfoService.createOrUpdateProductInfo(saveFinishOrderInfoInVO.getProductTicketId(), null);

        // 汇总更新工程结算 物料信息 成品信息 报工信息
        settlement(productTicketPO);

        // 创建机台和工单的关系
        createRelation(productTicketPO);

        // 如果不需要倒扣料，则自动更新倒扣料信息为已扣料
        List<GetMaterialDailySettlementOutVO> materialDailySettlementOutVOS = productMachineMaterialApportionmentService.pourUseMaterialInfo(productTicketPO.getId());
        if (CollectionUtil.isEmpty(materialDailySettlementOutVOS)) {
            // 更新为已倒扣料
            productTicketPO.setIsPour(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            productTicketPO.setIsPourMaterial(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            productTicketService.updateById(productTicketPO);
        }

        return "结单成功";
    }


    private void createRelation(ProductTicketPO productTicketPO) {
        ProductMachineTicketRelationDTO productMachineTicketRelationDTO = new ProductMachineTicketRelationDTO();
        productMachineTicketRelationDTO.setMachineName(productTicketPO.getMachineName());
        productMachineTicketRelationDTO.setCurrentTicketId(productTicketPO.getId());
        productMachineTicketRelationDTO.setCompanyCode(productTicketPO.getCompanyCode());
        productMachineTicketRelationService.stopRelation(productMachineTicketRelationDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveFinishOrderInfo(ProductPlanTicketVO productPlanTicketVO) {
//        ProductTicketPO productTicketPO = productTicketService.verify(productTicketVO.getProductTicketId());
        // 校验必须全部都下机了
        UnfinishedOrderListInVO unfinishedOrderListInVO = new UnfinishedOrderListInVO();
        unfinishedOrderListInVO.setMachineName(productPlanTicketVO.getMachineName());
        unfinishedOrderListInVO.setPlanTicketNo(productPlanTicketVO.getPlanTicketNo());
        List<UnfinishedOrderListOutVO> unfinishedOrderListOutVOS = productTicketService.unfinishedOrderList(unfinishedOrderListInVO);
        if (CollectionUtil.isNotEmpty(unfinishedOrderListOutVOS)) {
            throw new CommonException("存在未下机的工单，请先下机操作");
        }
        // 校验
        verifyInUse(productPlanTicketVO.getMachineName());
        // 解绑工程单号
        productMachineTicketService.updateByMachineName(productPlanTicketVO.getMachineName(),productPlanTicketVO.getPlanTicketNo());

        return productPlanTicketVO.getPlanTicketNo();
    }

    @Override
    public ProductFinishOrderPO getByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductFinishOrderPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductFinishOrderPO::getProductTicketId,productTicketId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public void verifyInUse(String machineName) {
        // 需要下料后才可结束
        GetByMachineNameInVO getByMachineNameInVO = new GetByMachineNameInVO();
        getByMachineNameInVO.setMachineName(machineName);
        // 剩余料信息和上栈板信息，不允许完成
        List<MaterialInfoOutVO> materialList = productMaterialService.getMaterialListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(materialList)) {
            String materialName =
                    materialList.stream().map(MaterialInfoOutVO::getMaterialName).collect(Collectors.joining(","));
            throw new CommonException("存在未下料的物料" + materialName + ",不许完成工单");
        }
        List<LastPalletOutVO> lastPalletList = productLastPalletService.getLastPalletListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(lastPalletList)) {
            String palletCode =
                    lastPalletList.stream().map(LastPalletOutVO::getPalletCode).collect(Collectors.joining(","));
            throw new CommonException("存在未下料的栈板" + palletCode + ",不许完成工单");
        }

        // 栈板也必须出站
        OutboundInfoOutVO outboundInfoOutVO = productOutboundService.getPalletByMachineName(getByMachineNameInVO);
        if (outboundInfoOutVO != null) {
            throw new CommonException("存在未出站的栈板信息,不许完成工单");
        }
    }

    /**
     * 校验暂存
     * @param machineName
     */
    @Override
    public void verifyStaging(String machineName) {
        // 需要将暂存物料处理后才可结束
        GetByMachineNameInVO getByMachineNameInVO = new GetByMachineNameInVO();
        getByMachineNameInVO.setMachineName(machineName);
        // 物料信息和上栈板信息，不允许完成
        List<MaterialInfoOutVO> materialList = productMaterialService.getStagingMaterialListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(materialList)) {
            String materialName =
                materialList.stream().map(MaterialInfoOutVO::getMaterialName).collect(Collectors.joining(","));
            throw new CommonException("存在暂存的物料" + materialName + ",不许完成工单");
        }
        List<LastPalletOutVO> lastPalletList =
            productLastPalletService.getStagingLastPalletListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(lastPalletList)) {
            String palletCode =
                lastPalletList.stream().map(LastPalletOutVO::getPalletCode).collect(Collectors.joining(","));
            throw new CommonException("存在暂存的栈板" + palletCode + ",不许完成工单");
        }

    }

    /**
     * 汇总更新工程结算 物料信息 成品信息 报工信息
     * @param productTicketPO
     * @return: void
     * <AUTHOR>
     * @date: 2023/11/14 13:29
     */
    private void settlement(ProductTicketPO productTicketPO){
        Long productTicketId = productTicketPO.getId();
        String productTicketNo = productTicketPO.getPlanTicketNo();
        iProductSettlementInfoService.collect(productTicketId);

        /*List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(productTicketNo);
        log.info("processSeqList:{}",JSON.toJSONString(processSeqList));
        Map<String, Integer> sfcbMap = processSeqList.stream().collect(Collectors.toMap(GetProcessSeqByTickNoOutVO::getProcessName,GetProcessSeqByTickNoOutVO::getProcessSeq));

        iProductSettlementMaterialService.collect(productTicketNo,productTicketId);
        iProductSettlementEndProductionService.collect(productTicketNo,productTicketId,sfcbMap);
        iProductSettlementWorkReportService.collect(productTicketNo,productTicketId,productTicketPO.getProcess(),sfcbMap);*/
    }




}
