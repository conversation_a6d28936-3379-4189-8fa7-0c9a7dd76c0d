package cn.jihong.mes.production.app.service.mq.consumer;

import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.model.enums.MQBizCodeEnum;
import cn.jihong.mes.production.api.service.IProductFirstDetectionService;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Slf4j
@Component
public class DeadReceiver {

    @Resource
    private IProductFirstDetectionService productFirstDetectionService;

    @RabbitHandler
    @RabbitListener(queues = "mes.deadQueue", containerFactory = "rabbitListenerContainerFactory", ackMode = "MANUAL")
    public void process(MessageStructDTO messageStructDTO, Channel channel,
        @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        try {
            log.info("死信队列消费者收到消息messageStructDTO  : " + JSON.toJSONString(messageStructDTO.toString()));
            // 后续有需求的话，可以根据这个code做个策略模式，现在就一个场景，就不去实现了
            Integer bizCode = messageStructDTO.getBizCode();
            if (MQBizCodeEnum.FIRST_DETECTION.getCode().equals(bizCode)) {
                productFirstDetectionService.createMessage(bizCode, messageStructDTO.getData().toString());
            }
            // 手动确认消息
            channel.basicAck(tag, false);
        } catch (Exception e) {
            log.error("死信队列消费者处理消息异常: " + e.getMessage(), e);
            try {
                // 消息处理失败，拒绝消息，并且不重新将消息放回队列
                channel.basicNack(tag, false, false);
            } catch (IOException ioException) {
                log.error("死信队列消费者拒绝消息异常: " + ioException.getMessage(), ioException);
            }
        }
    }

}
