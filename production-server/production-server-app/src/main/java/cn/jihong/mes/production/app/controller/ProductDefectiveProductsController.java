package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.vo.in.GetDefectiveReasonsInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveDefectiveProductsInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefectiveReasonsOutVO;
import cn.jihong.mes.production.api.service.IProductDefectiveProductsService;
import cn.jihong.oa.erp.api.model.dto.BmbaTMaterialDTO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 不良品表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productDefectiveProducts")
@ShenyuSpringMvcClient(path = "/productDefectiveProducts/**")
public class ProductDefectiveProductsController {

    @Resource
    private IProductDefectiveProductsService productDefectiveProductsService;

    /**
     * 保存不良品信息
     */
    @PostMapping("/saveDefectiveProductsInfo")
    public StandardResult
        saveDefectiveProductsInfo(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        String LOCK_KEY = RedisCacheConstant.STORAGE  + saveDefectiveProductsInfoInVO.getProductTicketId();
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productDefectiveProductsService.saveDefectiveProductsInfo(saveDefectiveProductsInfoInVO));
    }

    /**
     * 更新不良品信息
     */
    @PostMapping("/updateDefectiveProductsInfo")
    public StandardResult
    updateDefectiveProductsInfo(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        productDefectiveProductsService.updateDefectiveProductsInfo(saveDefectiveProductsInfoInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 保存不良品信息 PC 端
     */
    @PostMapping("/saveDefectiveProductsInfoByPC")
    public StandardResult
    saveDefectiveProductsInfoByPC(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        productDefectiveProductsService.saveDefectiveProductsInfoByPC(saveDefectiveProductsInfoInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 查询不良品记录
     */
    @PostMapping("/getDefectiveProductsRecords")
    public StandardResult<Pagination<DefectiveProductsInfoOutVO>>
        getDefectiveProductsRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productDefectiveProductsService.getDefectiveProductsRecords(productTicketPageInVO));
    }

    /**
     * 查询不良原因
     */
    @GetMapping("/getEcffucTList/{productTicketId}")
    public StandardResult<List<GetDefectiveReasonsOutVO>> getEcffucTList(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productDefectiveProductsService.getEcffucTList(productTicketId));
    }

    /**
     * 查询损失类型
     */
    @PostMapping("/getLossType")
    public StandardResult<List<EnumDTO>> getLossType(@RequestBody @Valid GetDefectiveReasonsInVO getDefectiveReasonsInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productDefectiveProductsService.getLossType(getDefectiveReasonsInVO));
    }

    /**
     * 查询不良类型
     */
    @PostMapping("/getDefectiveType")
    public StandardResult<List<String>> getDefectiveType(@RequestBody @Valid GetDefectiveReasonsInVO getDefectiveReasonsInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productDefectiveProductsService.getDefectiveType(getDefectiveReasonsInVO));
    }

    /**
     * 查询不良原因  -- 报损类型
     */
    @PostMapping("/getDefectiveReasons")
    public StandardResult<List<GetDefectiveReasonsOutVO>> getDefectiveReasons(@RequestBody @Valid GetDefectiveReasonsInVO getDefectiveReasonsInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productDefectiveProductsService.getDefectiveReasons(getDefectiveReasonsInVO));
    }

    /**
     * 查询工序列表
     */
    @GetMapping("/getProcessList/{productTicketId}")
    public StandardResult<List<BmbaTMaterialDTO>> getProcessList(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                (List<BmbaTMaterialDTO>)productDefectiveProductsService.getProcessList(productTicketId));
    }


}
