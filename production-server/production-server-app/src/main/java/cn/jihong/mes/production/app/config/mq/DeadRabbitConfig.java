package cn.jihong.mes.production.app.config.mq;

import cn.jihong.mes.production.api.model.constant.RabbitConsts;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Configuration
public class DeadRabbitConfig {

    // 普通队列
    @Bean
    public Queue QueueA() {
        HashMap<String, Object> map = new HashMap<>(4);
        /** 设置死信队列的交换机 */
        map.put("x-dead-letter-exchange", RabbitConsts.DEAD_MODE_EXCHANGE);
        /** 设置死信队列的routingKey */
        map.put("x-dead-letter-routing-key", RabbitConsts.DEAD_ROUTING_KEY);
//        /** 设置过期时间 */
//        map.put("x-message-ttl", 10 * 1000 * 60 );
        return QueueBuilder.durable(RabbitConsts.DEAD_MODE_QUEUE_A).withArguments(map).build();
    }

    // 普通交换机
    @Bean
    public DirectExchange DeadExchangeA() {
        return new DirectExchange(RabbitConsts.DEAD_MODE_EXCHANGE_A, true, false);
    }

    // 绑定 将队列和交换机绑定, 并设置用于匹配键
    @Bean
    public Binding bindingDeadA() {
        return BindingBuilder.bind(QueueA()).to(DeadExchangeA()).with(RabbitConsts.DEAD_ROUTING_KEY_A);
    }

    /**
     * 声明死信队列
     */
    @Bean
    public Queue DeadQueue() {
        return QueueBuilder.durable(RabbitConsts.DEAD_MODE_QUEUE).build();
    }

    // 交换机
    @Bean
    public DirectExchange DeadExchange() {
        return new DirectExchange(RabbitConsts.DEAD_MODE_EXCHANGE, true, false);
    }

    // 绑定 将队列和交换机绑定, 并设置用于匹配键
    @Bean
    public Binding bindingDead() {
        return BindingBuilder.bind(DeadQueue()).to(DeadExchange()).with(RabbitConsts.DEAD_ROUTING_KEY);
    }

}
