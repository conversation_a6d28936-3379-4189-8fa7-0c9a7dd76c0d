package cn.jihong.mes.production.app.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.calibre.review.server.api.model.po.NoFirstTaskProcessConfigPO;
import cn.jihong.calibre.review.server.api.model.vo.GetFirstTaskInspectionInVO;
import cn.jihong.calibre.review.server.api.model.vo.GetFirstTaskInspectionOutVO;
import cn.jihong.calibre.review.server.api.service.INoFirstTaskProcessConfigService;
import cn.jihong.calibre.review.server.api.service.IQcFirstTaskService;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductInfoPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.ProductInfoPageInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketShiftInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.mapper.ProductInfoMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 生产工程单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Slf4j
@Service
public class ProductInfoServiceImpl extends JiHongServiceImpl<ProductInfoMapper, ProductInfoPO> implements IProductInfoService {

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMaterialService productMaterialService;
    @Resource
    private IProductLastPalletService productLastPalletService;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductDefectiveProductsService productDefectiveProductsService;

    @DubboReference
    private IQcFirstTaskService iQcFirstTaskService;

    @DubboReference
    private INoFirstTaskProcessConfigService iNoFirstTaskProcessConfigService;

    @Override
    public ProductInfoPO createOrUpdateProductInfo(Long productTicketId, String firstCheckResult) {
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        ProductTicketShiftOutVO productTicketShiftOutVO = productTicketService.getPlanTicketNoList(productTicketPO);
        ProductInfoPO productInfoPO = BeanUtil.copyProperties(productTicketShiftOutVO,ProductInfoPO.class);
        productInfoPO.setProductName(productTicketPO.getProductName());

        LambdaQueryWrapper<ProductInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper
                .eq(ProductInfoPO::getCompanyCode,productTicketPO.getCompanyCode())
                .eq(ProductInfoPO::getPlanTicketNo,productTicketShiftOutVO.getPlanTicketNo())
                .eq(ProductInfoPO::getMachineName,productTicketShiftOutVO.getMachineName())
                .eq(ProductInfoPO::getShift,productTicketShiftOutVO.getShift())
                .eq(ProductInfoPO::getProduceDate,productTicketShiftOutVO.getProduceDate())
                .eq(ProductInfoPO::getProcessType,productTicketPO.getProcessType());

        ProductInfoPO po = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(po)) {
            productInfoPO.setId(po.getId());
        }
        if (StringUtils.isNotBlank(firstCheckResult)) {
            productInfoPO.setFirstCheckResult(firstCheckResult);
        }
        saveOrUpdate(productInfoPO);
        return productInfoPO;
    }

    @Override
    public Pagination<ProductTicketShiftOutVO> getProductInfoList(ProductTicketShiftInVO productTicketShiftInVO) {
        if (StringUtils.isBlank(productTicketShiftInVO.getCompanyCode())) {
            productTicketShiftInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }
        LambdaQueryWrapper<ProductInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(StringUtils.isNotBlank(productTicketShiftInVO.getCompanyCode()), ProductInfoPO::getCompanyCode,
                        productTicketShiftInVO.getCompanyCode())
                .like(StringUtils.isNotBlank(productTicketShiftInVO.getMachineName()), ProductInfoPO::getMachineName,
                        productTicketShiftInVO.getMachineName())
                .eq(StringUtils.isNotBlank(productTicketShiftInVO.getPlanTicketNo()), ProductInfoPO::getPlanTicketNo,
                        productTicketShiftInVO.getPlanTicketNo())
                .eq(ObjectUtil.isNotNull(productTicketShiftInVO.getProduceDate()), ProductInfoPO::getProduceDate,
                        productTicketShiftInVO.getProduceDate())
                .eq(ObjectUtil.isNotNull(productTicketShiftInVO.getShift()), ProductInfoPO::getShift,
                        productTicketShiftInVO.getShift())
                .orderByDesc(ProductInfoPO::getCreateTime);

        IPage<ProductInfoPO> page = page(productTicketShiftInVO.getPage(), lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        return Pagination.newInstance(page.getRecords().stream().map(productInfoPO -> {
            ProductTicketShiftOutVO productTicketShiftOutVO =
                    BeanUtil.copyProperties(productInfoPO, ProductTicketShiftOutVO.class);
            String planHours = getHoursDifference(productTicketShiftOutVO.getProductionPlanStartTime(),productTicketShiftOutVO.getProductionPlanEndTime());
            String realHours = getHoursDifference(productTicketShiftOutVO.getStartDate(),productTicketShiftOutVO.getEndDate());
            productTicketShiftOutVO.setPlanHours(new BigDecimal(planHours));
            productTicketShiftOutVO.setRealHours(new BigDecimal(realHours));
            return productTicketShiftOutVO;
        }).collect(Collectors.toList()), page);
    }

    /**
     * 获得生产工程单
     */
    @Override
    public Pagination<ProductionTicketInfoOutVO> getProductTicket(ProductInfoPageInVO productInfoPageInVO) {
        // 获得对应的生产工单信息
        ProductTicketBaseDTO productTicketBaseDTO = getProductTicketBaseDTO(productInfoPageInVO);
        return productTicketService.getTicketByTicketBase(productInfoPageInVO.getPage(), productTicketBaseDTO);
    }



    /**
     * 获得上料记录
     */
    @Override
    public Pagination<MaterialRecordsOutVO> getMaterialRecords(ProductInfoPageInVO productInfoPageInVO) {
        ProductTicketBaseDTO productTicketBaseDTO = getProductTicketBaseDTO(productInfoPageInVO);
        return productMaterialService.getMaterialRecordsByTicketBase(productInfoPageInVO.getPage(),productTicketBaseDTO);
    }

    /**
     * 获得出站记录
     */
    @Override
    public Pagination<OutboundInfoOutVO> getOutboundRecords(ProductInfoPageInVO productInfoPageInVO) {
        ProductTicketBaseDTO productTicketBaseDTO = getProductTicketBaseDTO(productInfoPageInVO);
        return productOutboundService.getOutboundRecordsByTicketBase(productInfoPageInVO.getPage(),productTicketBaseDTO);
    }

    /**
     * 获得不良品记录
     */
    @Override
    public Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsRecords(ProductInfoPageInVO productInfoPageInVO) {
        ProductTicketBaseDTO productTicketBaseDTO = getProductTicketBaseDTO(productInfoPageInVO);
        return productDefectiveProductsService.getDefectiveProductsRecordsByTicketBase(productInfoPageInVO.getPage(),productTicketBaseDTO);
    }

    /**
     * 获得上栈板记录
     */
    @Override
    public Pagination<LastPalletRecordsOutVO> getLastPalletRecords(ProductInfoPageInVO productInfoPageInVO) {
        ProductTicketBaseDTO productTicketBaseDTO = getProductTicketBaseDTO(productInfoPageInVO);
        return productLastPalletService.getLastPalletRecords(productInfoPageInVO.getPage(),productTicketBaseDTO);
    }

    @Override
    public ProductTicketShiftOutVO getProductInfo(Long id) {
        return BeanUtil.copyProperties(getById(id),ProductTicketShiftOutVO.class);
    }

    @Override
    public Boolean verifyFirstCheckCompleted(Long productTicketId) {
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        NoFirstTaskProcessConfigPO oneByCompanyCodeAndProcessCode = iNoFirstTaskProcessConfigService.getOneByCompanyCodeAndProcessCode(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if(oneByCompanyCodeAndProcessCode!=null){
            // 该工序已配置无需首检
            return true;
        }

        GetFirstTaskInspectionInVO inVO = new GetFirstTaskInspectionInVO();
        inVO.setMachineName(productTicketPO.getMachineName());
        inVO.setProductionPlanWorkerOrderNo(productTicketPO.getPlanTicketNo());
        inVO.setProductionDate(productTicketPO.getProduceDate());
        inVO.setProductionSerialNo(productTicketPO.getShift());
        inVO.setCompanyCode(productTicketPO.getCompanyCode());
        GetFirstTaskInspectionOutVO firstTaskInspection = iQcFirstTaskService.getFirstTaskInspection(inVO);
        return Objects.nonNull(firstTaskInspection) && StringUtils.isNotBlank(firstTaskInspection.getFinallyCheckResult()) && Objects.equals(firstTaskInspection.getFinallyCheckResult(), BooleanEnum.TRUE.getCode());
    }

    @Override
    public void updateFirstCheckResult(Long id) {
        LambdaUpdateWrapper<ProductInfoPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductInfoPO::getId,id)
        .set(ProductInfoPO::getFirstCheckResult,null);
        update(lambdaUpdateWrapper);
    }

    @Override
    public ProductInfoPO getProductInfo(ProductTicketPO productTicketPO) {
        LambdaQueryWrapper<ProductInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper
                .eq(ProductInfoPO::getCompanyCode,productTicketPO.getCompanyCode())
                .eq(ProductInfoPO::getPlanTicketNo,productTicketPO.getPlanTicketNo())
                .eq(ProductInfoPO::getMachineName,productTicketPO.getMachineName())
                .eq(ProductInfoPO::getShift,productTicketPO.getShift())
                .eq(ProductInfoPO::getProduceDate,productTicketPO.getProduceDate());
        return getOne(lambdaQueryWrapper);
    }


    private ProductTicketBaseDTO getProductTicketBaseDTO(ProductInfoPageInVO productInfoPageInVO) {
        ProductInfoPO productInfoPO = getById(productInfoPageInVO.getId());
        return BeanUtil.copyProperties(productInfoPO,ProductTicketBaseDTO.class);
    }



    /**
     * 两个日期之间的小时数
     * @param startDate
     * @param endDate
     * @return
     */
    private String getHoursDifference(Date startDate, Date endDate) {
        // 将Date对象转换为Calendar对象
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(startDate);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(ObjectUtil.isNull(endDate)? new Date() : endDate );

        // 获取两个Calendar对象之间的小时数差异
        Long hoursDifference = calendar2.getTimeInMillis() - calendar1.getTimeInMillis();
        String hour = String.format("%.2f", ((hoursDifference.doubleValue() / (60 * 60 * 1000))));
        log.info("两个日期的小时数差异: " + hour + " 小时");
        return hour;
    }
}
