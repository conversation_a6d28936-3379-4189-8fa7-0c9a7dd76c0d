package cn.jihong.mes.production.app.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO;
import cn.jihong.mes.production.api.model.po.ProductBarcodeSplitBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.ProductBarcodeSplitBarcodeInVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

/**
 * 条码拆分 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
public interface ProductBarcodeSplitBarcodeMapper extends JiHongMapper<ProductBarcodeSplitBarcodePO> {

    IPage<ProductBarcodeSplitBarcodeDTO> getPageList(IPage page,
                                                     @Param("companyCode") String companyCode,
                                                     @Param("productBarcodeSplitBarcodeInVO") ProductBarcodeSplitBarcodeInVO productBarcodeSplitBarcodeInVO);

}
