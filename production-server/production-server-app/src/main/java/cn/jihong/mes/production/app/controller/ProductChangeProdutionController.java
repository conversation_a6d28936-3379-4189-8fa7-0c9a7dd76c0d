package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductPlanTicketVO;
import cn.jihong.mes.production.api.service.IProductTicketService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 转产
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@RestController
@RequestMapping("/productChangeProdution")
@ShenyuSpringMvcClient(path = "/productChangeProdution/**")
public class ProductChangeProdutionController {

    @Resource
    private IProductTicketService productTicketService;


//    /**
//     * 获取生产计划工程单列表
//     */
//    @PostMapping("/getProductPlanList")
//    public StandardResult<List<ProductionPlanDTO>> getProductPlanList(@RequestBody @Valid GetProductPlanListVO vo){
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getProductPlanList(vo));
//    }

//    /**
//     * 获得当前机台的工单
//     *
//     * @param getProductionTicketNo
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/getProductionTicketNo")
//    public StandardResult getProductionTicketNo(@RequestBody @Valid GetProductionTicketNoInVO getProductionTicketNo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService
//            .getProductionMachineTicket(getProductionTicketNo.getMachineName(), TicketTypeEnum.CHANGE_PRODUCTION.getCode()));
//    }


//    /**
//     * 创建转产生产工单
//     *
//     * @param vo
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/create")
//    public StandardResult createProductTicket(@RequestBody @Valid CreateWorkOrderProductionTransferVO vo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.createChangeProduction(vo));
//    }


//    /**
//     * 获取转产记录
//     *
//     *
//     *
//     * @param dto
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/getChangeList")
//    public StandardResult<List<ProductChangeProdutionOutVO>> getChangeProductTicketList(@RequestBody @Valid ProductChangeProductionDTO dto) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getChangeProductTicketList(dto));
//    }

    /**
     * 转产确定---校验
     */
    @PostMapping("/saveChangeInfo")
    public StandardResult saveChangeInfo(@RequestBody @Valid ProductPlanTicketVO productPlanTicketVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productTicketService.saveChangeInfo(productPlanTicketVO));
    }


}
