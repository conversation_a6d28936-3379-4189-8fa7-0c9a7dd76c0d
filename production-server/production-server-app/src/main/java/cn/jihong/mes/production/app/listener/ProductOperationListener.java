package cn.jihong.mes.production.app.listener;

import cn.jihong.mes.production.api.service.IProductMaterialOperationRecordsService;
import cn.jihong.mes.production.api.service.IProductPalletOperationRecordsService;
import cn.jihong.mes.production.app.event.ProductMaterialEvent;
import cn.jihong.mes.production.app.event.ProductPalletEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ProductOperationListener {


    @Resource
    private IProductPalletOperationRecordsService productPalletOperationRecordsService;
    @Resource
    private IProductMaterialOperationRecordsService productMaterialOperationRecordsService;

    /**
     * 保存操作物料记录
     * @param productMaterialEvent
     */
    @EventListener
    public void handleProductEvent(ProductMaterialEvent productMaterialEvent) {
        log.info("操作类型为：" + productMaterialEvent.getOperation());
        if (productMaterialEvent.getProductMaterialOperationRecordsDTO() != null) {
            productMaterialOperationRecordsService
                    .saveOrUpdateMaterialUse(productMaterialEvent.getProductMaterialOperationRecordsDTO());
        }
    }


    /**
     * 保存操作栈板记录
     * @param productPalletEvent
     */
    @EventListener
    public void handleProductEvent(ProductPalletEvent productPalletEvent) {
        log.info("操作类型为：" + productPalletEvent.getOperation());
        if (productPalletEvent.getProductPalletOperationRecordsDTO() != null) {
            productPalletOperationRecordsService
                .saveOrUpdatePalletOperationRecords(productPalletEvent.getProductPalletOperationRecordsDTO());
        }
    }
}
