package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductSettlementEndProductionPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementEndProductionCollectDetailInVO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementEndProductionDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionCollectDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionListOutVO;
import cn.jihong.mes.production.api.service.IProductSettlementEndProductionService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工程结算产成品信息
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@RestController
@RequestMapping("/productSettlementEndProduction")
@ShenyuSpringMvcClient(path = "/productSettlementEndProduction/**")
public class ProductSettlementEndProductionController {


    @Autowired
    private IProductSettlementEndProductionService iProductSettlementEndProductionService;

    /**
     * 查询产成品信息
     * @param productTicketNo
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/8 16:01
     */
    @GetMapping("/getSettlementEndProductionList")
    public StandardResult<List<GetSettlementEndProductionListOutVO>> getSettlementEndProductionList(@RequestParam String productTicketNo){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementEndProductionService.getSettlementEndProductionList(productTicketNo));
    }


    /**
     * 查询产成品详情汇总信息列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/14 18:04
     */
    @PostMapping("/getSettlementEndProductionCollectDetail")
    public StandardResult<Pagination<GetSettlementEndProductionCollectDetailOutVO>> getSettlementEndProductionCollectDetail(@RequestBody @Valid GetSettlementEndProductionCollectDetailInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementEndProductionService.getSettlementEndProductionCollectDetail(inVO));
    }


/*    *//**
     * 查询产成品详情信息列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/14 18:04
     *//*
    @PostMapping("/getSettlementEndProductionDetail")
    public StandardResult<Pagination<ProductPalletPO>> getSettlementEndProductionDetail(@RequestBody @Valid GetSettlementEndProductionDetailInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementEndProductionService.getSettlementEndProductionDetail(inVO));
    }*/
}

