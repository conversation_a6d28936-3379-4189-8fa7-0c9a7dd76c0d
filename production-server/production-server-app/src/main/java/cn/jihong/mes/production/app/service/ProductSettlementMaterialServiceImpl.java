package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductSettlementMaterialPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetListByMaterialCodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementMaterialDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetListByMaterialCodeOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO;
import cn.jihong.mes.production.api.service.IProductMaterialService;
import cn.jihong.mes.production.api.service.IProductSettlementMaterialService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductSettlementMaterialMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.service.ISfbaTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 工程结算物料信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@DubboService
public class ProductSettlementMaterialServiceImpl extends JiHongServiceImpl<ProductSettlementMaterialMapper, ProductSettlementMaterialPO> implements IProductSettlementMaterialService {

    @Resource
    private IProductMaterialService iProductMaterialService;

    @Resource
    private IProductSettlementMaterialService iProductSettlementMaterialService;

    @Resource
    private IProductTicketService iProductTicketService;

    @Resource
    private ProductSettlementMaterialMapper productSettlementMaterialMapper;

    @DubboReference
    private ISfbaTService iSfbaTService;

    @Override
    public void collect(String productTicketNo,Long productTicketId) {
        // 旧的结算记录
        List<ProductSettlementMaterialPO> settlementMaterialPOList = iProductSettlementMaterialService.getList(productTicketNo, null);
        Map<String, ProductSettlementMaterialPO> settlementMaterialMap = settlementMaterialPOList.stream().collect(Collectors.toMap(ProductSettlementMaterialPO::getMaterialCode,t->t));

        ProductTicketPO productTicketPO = iProductTicketService.getById(productTicketId);
        GetListByMaterialCodeInVO inVO = new GetListByMaterialCodeInVO();
        inVO.setProductTicketId(productTicketId);
        List<GetListByMaterialCodeOutVO> materialList = iProductMaterialService.getListByMaterialCode(inVO);
        if(CollectionUtil.isNotEmpty(materialList)) {
            Map<String, List<GetListByMaterialCodeOutVO>> materialMap = materialList.stream().collect(Collectors.groupingBy(GetListByMaterialCodeOutVO::getMaterialCode));

            materialMap.keySet().forEach(materialCode->{
                List<GetListByMaterialCodeOutVO> temList = materialMap.get(materialCode);
                ProductSettlementMaterialPO settlementMaterialPO = settlementMaterialMap.get(materialCode);
                if(Objects.isNull(settlementMaterialPO)){
                    GetListByMaterialCodeOutVO materialInfoOutVO = temList.get(0);
                    settlementMaterialPO = new ProductSettlementMaterialPO();
                    BeanUtil.copyProperties(materialInfoOutVO,settlementMaterialPO);
                    settlementMaterialPO.setProcessName(productTicketPO.getProcess());
                    settlementMaterialPO.setProductTicketNo(productTicketNo);
                    settlementMaterialPO.setCreateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
                }else{
                    settlementMaterialPO.setUpdateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
                    settlementMaterialPO.setUpdateTime(new Date());
                }
                settlementMaterialPO.setPickingQuantity((temList.stream().map(GetListByMaterialCodeOutVO::getLoadingQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)));
                settlementMaterialPO.setConsumptionQuantity((temList.stream().map(GetListByMaterialCodeOutVO::getConsumptionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)));
                settlementMaterialPO.setPurchaseBatch(temList.size());
                saveOrUpdate(settlementMaterialPO);
            });
        }
    }


    @Override
    public List<ProductSettlementMaterialPO> getList(String productTicketNo, String materialCode) {
        LambdaQueryWrapper<ProductSettlementMaterialPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(productTicketNo),ProductSettlementMaterialPO::getProductTicketNo,productTicketNo)
                .eq(StrUtil.isNotBlank(materialCode),ProductSettlementMaterialPO::getMaterialCode,materialCode);
        return list(wrapper);
    }

    @Override
    public List<GetSettlementMaterialListOutVO> getSettlementMaterialList(String productTicketNo) {
       /* LambdaQueryWrapper<ProductSettlementMaterialPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductSettlementMaterialPO::getProductTicketNo,productTicketNo);
        List<GetSettlementMaterialListOutVO> list = BeanUtil.copyToList(list(wrapper), GetSettlementMaterialListOutVO.class);*/
        List<GetSettlementMaterialListOutVO> list = baseMapper.getSettlementMaterialList(productTicketNo);

        // 查询该工程单各个工序的开单数量
        Map<String, BigDecimal> map = iSfbaTService.getBillingQuantity(productTicketNo);
        return list.stream().peek(t->{
                if(Objects.nonNull(map.get(t.getProcessName()))){
                    t.setBillingQuantity(map.get(t.getProcessName()));
                }
        }).collect(Collectors.toList());
    }

    @Override
    public Pagination<GetSettlementMaterialDetailOutVO> getSettlementMaterialDetail(GetSettlementMaterialDetailInVO inVO) {
        Page<GetSettlementMaterialDetailOutVO> page = productSettlementMaterialMapper.getSettlementMaterialDetail(inVO.getPage(), inVO);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }
}
