package cn.jihong.mes.production.app.service.oa;

import cn.jihong.mes.production.api.service.oa.IOaOverClaimFlowService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * OA-超领单
 * <AUTHOR>
 * @date 2024-09-10 18:01
 */
@DubboService
public class OaOverClaimFlowServiceImpl implements IOaOverClaimFlowService  {

    @Override
    public Object createFlow() {
        return null;
    }

    @Override
    public Object updateFlowData() {
        return null;
    }

    @Override
    public Object receiveFlowResult() {
        return null;
    }
}
