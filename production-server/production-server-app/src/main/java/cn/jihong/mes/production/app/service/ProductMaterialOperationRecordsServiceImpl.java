package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Snowflake;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.enums.WorkflowRequestStatusEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.WorkflowAttachmentDTO;
import cn.jihong.common.model.dto.WorkflowUserDTO;
import cn.jihong.common.model.dto.request.GetWorkflowDetailRequest;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.model.dto.request.oa.BaseWorkflowRequest;
import cn.jihong.common.model.dto.request.oa.SubmitWorkflowRequest;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.model.vo.WorkflowDetailVO;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.service.IProductionPlanService;
import cn.jihong.mes.production.api.model.dto.DownMaterialFlowDTO;
import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.dto.UpdateMaterialDTO;
import cn.jihong.mes.production.api.model.enums.MaterialOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.ProductionBusinessType;
import cn.jihong.mes.production.api.model.enums.UpdateMaterialTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.po.ProductMaterialOperationRecordsPO;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyMaterialOverClaimRequestInVO;
import cn.jihong.mes.production.api.model.vo.out.GetInboundQuantityOutVO;
import cn.jihong.mes.production.api.model.vo.out.MaterialRecordsOutVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyMaterialOverClaimRequestOutVO;
import cn.jihong.mes.production.api.service.IProductMachineMaterialApportionmentService;
import cn.jihong.mes.production.api.service.IProductMaterialOperationRecordsService;
import cn.jihong.mes.production.api.service.IProductMaterialService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.controller.oa.DownMaterialFlowController;
import cn.jihong.mes.production.app.event.ProductMaterialEvent;
import cn.jihong.mes.production.app.mapper.ProductMaterialOperationRecordsMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.approve.api.enums.BusinessType;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OaMaterialOverClaimDTO;
import cn.jihong.oa.approve.api.service.IHrmResourceService;
import cn.jihong.oa.erp.api.model.vo.SfbaTVO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.ISfbaTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import cn.jihong.workflow.common.service.IWorkflowBizService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 物料操作记录信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Slf4j
@Service
@DubboService
public class ProductMaterialOperationRecordsServiceImpl
    extends JiHongServiceImpl<ProductMaterialOperationRecordsMapper, ProductMaterialOperationRecordsPO>
    implements IProductMaterialOperationRecordsService, IWorkflowBizService<ProductMaterialOperationRecordsPO, DownMaterialFlowDTO, OaMaterialOverClaimDTO> {

    private ApplicationEventPublisher eventPublisher;

    @Resource
    private IProductMaterialService productMaterialService;

    @Resource
    private IProductTicketService productTicketService;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @DubboReference
    private ISfbaTService iSfbaTService;

    @Resource
    private IProductionPlanService productionPlanService;

    @DubboReference
    private IHrmResourceService hrmResourceService;

    @Resource
    private DownMaterialFlowController downMaterialFlowController;

    @Resource
    private IProductMachineMaterialApportionmentService iProductMachineMaterialApportionmentService;

    @Resource
    public void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }


    @Override
    public void work(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO, String code) {
        ProductMaterialEvent productEvent =
                new ProductMaterialEvent(this, code, productMaterialOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    /**
     * 上料
     *
     * @param productMaterialOperationRecordsDTO
     */
    @Override
    public void upMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO) {
        ProductMaterialEvent productEvent =
                new ProductMaterialEvent(this, MaterialOperateTypeEnum.UP_MATERIAL.getCode(), productMaterialOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    /**
     * 下料
     *
     * @param productMaterialOperationRecordsDTO
     */
    @Override
    public void downMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO) {
        ProductMaterialEvent productEvent =
                new ProductMaterialEvent(this, MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(), productMaterialOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }

    /**
     * 换料
     *
     * @param productMaterialOperationRecordsDTO
     */
    @Override
    public void changeMaterial(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO) {
        ProductMaterialEvent productEvent =
                new ProductMaterialEvent(this, MaterialOperateTypeEnum.CHANGE_MATERIAL.getCode(), productMaterialOperationRecordsDTO);
        eventPublisher.publishEvent(productEvent);
    }



    @Override
    public ProductMaterialOperationRecordsPO saveOrUpdateMaterialUse(ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO) {
        log.info("保存操作材料信息");
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = BeanUtil.copyProperties(productMaterialOperationRecordsDTO, ProductMaterialOperationRecordsPO.class);
        save(productMaterialOperationRecordsPO);
        return productMaterialOperationRecordsPO;
    }

    @Override
    public Page<MaterialRecordsOutVO> getMaterialRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<MaterialRecordsOutVO> page = baseMapper.getMaterialRecords(productTicketPageInVO.getPage(),
            productTicketPageInVO, MaterialOperateTypeEnum.UP_MATERIAL.getCode(), SecurityUtil.getCompanySite());
        return page;
    }

    @Override
    public Page<MaterialRecordsOutVO> getMaterialRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<MaterialRecordsOutVO> ipage = baseMapper.getMaterialRecordsByTicketBase(page,productTicketBaseDTO);
        return ipage;
    }


    @Override
    public List<ProductMaterialOperationRecordsDTO> getByProductTicketIds(List<Long> productTicketIds,List<Integer> operationTypes) {
        if (CollectionUtil.isEmpty(productTicketIds)) {
            return Lists.newArrayList();
        }
        return baseMapper.getByProductTicketIds(productTicketIds,operationTypes);

    }

    @Override
    public List<ProductMaterialOperationRecordsPO> getByMateriaId(Long materiaId) {
        LambdaQueryWrapper<ProductMaterialOperationRecordsPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialOperationRecordsPO::getProductMaterialId,materiaId)
                .eq(ProductMaterialOperationRecordsPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()))
                .orderByDesc(ProductMaterialOperationRecordsPO::getCreateTime);
        return list(lambdaQueryWrapper);
    }

    @Override
    public Page<MaterialRecordsOutVO> getMaterialDownRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<MaterialRecordsOutVO> page = baseMapper.getMaterialRecords(productTicketPageInVO.getPage(),productTicketPageInVO,MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(), SecurityUtil.getCompanySite());
        return page;
    }

    @Override
    public ProductMaterialOperationRecordsPO getRecordByWorkflowRequestId(Long workflowRequestId) {
        LambdaQueryWrapper<ProductMaterialOperationRecordsPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialOperationRecordsPO::getWorkflowRequestId,workflowRequestId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public VerifyMaterialOverClaimRequestOutVO verifyMaterialOverClaimRequest(VerifyMaterialOverClaimRequestInVO inVO) {
        VerifyMaterialOverClaimRequestOutVO outVO = new VerifyMaterialOverClaimRequestOutVO();
        outVO.setResult(true);
        ProductMaterialPO materialPO = productMaterialService.getById(inVO.getId());
        ProductTicketPO productTicketPO = productTicketService.getById(inVO.getProductTicketId());

        List<SfbaTVO> sfbaTVOList = iSfbaTService.getPlaceSfbaTVO(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode(), materialPO.getMaterialCode(), materialPO.getMaterialPlace());
        AssertUtil.isNotEmpty(sfbaTVOList,"erp应发数量获取异常");
        SfbaTVO sfbaTVO = sfbaTVOList.get(0);

        // 超领数量   应发 = 已发+此次发 + 能领
        BigDecimal canUseQuantity = sfbaTVO.getSfba013().subtract(sfbaTVO.getSfba016());
        if(inVO.getConsumptionQuantity().compareTo(canUseQuantity) > 0){
            // 申请领用数量 大于 可领用数量
            outVO.setResult(false);
        }
        outVO.setConsumptionQuantity(inVO.getConsumptionQuantity());
        outVO.setCanUseQuantity(canUseQuantity);
        outVO.setShouldUseQuantity(sfbaTVO.getSfba013());
        return outVO;
    }

    @Override
    public StandardResult sendSubmitWorkflowRequest(Long productMaterialOperationRecordId,String reason) {
        log.info("sendSubmitWorkflowRequest start:{}",productMaterialOperationRecordId);
        SubmitWorkflowRequest submitWorkflowRequest = new SubmitWorkflowRequest();
        submitWorkflowRequest.setWorkflowId(ProductionBusinessType.MATERIAL_OVER_CLAIM.getWorkflowid());
        DownMaterialFlowDTO downMaterialFlowDTO = new DownMaterialFlowDTO();

        ProductMaterialOperationRecordsPO materialOperationRecordsPO = getById(productMaterialOperationRecordId);
        ProductMaterialPO materialPO = productMaterialService.getById(materialOperationRecordsPO.getProductMaterialId());
        ProductTicketPO productTicketPO = productTicketService.getById(materialOperationRecordsPO.getProductTicketId());

        SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());

        List<SfbaTVO> sfbaTVOList = iSfbaTService.getPlaceSfbaTVO(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode(), materialPO.getMaterialCode(), materialPO.getMaterialPlace());
        AssertUtil.isNotEmpty(sfbaTVOList,"erp应发数量获取异常");
        SfbaTVO sfbaTVO = sfbaTVOList.get(0);

        // 超领数量   超领 = 已发+此次发-应发
        BigDecimal clsl = sfbaTVO.getSfba016().add(materialOperationRecordsPO.getConsumptionQuantity()).subtract(sfbaTVO.getSfba013());

        downMaterialFlowDTO.setKdsl(sfcbTVO != null?sfcbTVO.getSfcb027():null);
        downMaterialFlowDTO.setSlsl(clsl);
        downMaterialFlowDTO.setDw(materialPO.getMaterialUnit());
        downMaterialFlowDTO.setSlyy(reason);
        downMaterialFlowDTO.setCpmc(productTicketPO.getProductName());
        downMaterialFlowDTO.setWlmc(materialPO.getMaterialName());
        downMaterialFlowDTO.setMesno(productTicketPO.getPlanTicketNo());
        downMaterialFlowDTO.setYwID(materialOperationRecordsPO.getId());
        downMaterialFlowDTO.setSqr(SecurityUtil.getUserId().toString());
        if("SITE-08".equals(productTicketPO.getCompanyCode())){
            downMaterialFlowDTO.setLfyq("1");
        }else if("SITE-082".equals(productTicketPO.getCompanyCode())){
            downMaterialFlowDTO.setLfeq("1");
        }else if("SITE-01".equals(productTicketPO.getCompanyCode())){
            downMaterialFlowDTO.setXmsceb("1");
        }else if("SITE-22".equals(productTicketPO.getCompanyCode()) && (StringUtils.isBlank(productTicketPO.getDeptName()) || "生产一部".equals(productTicketPO.getDeptName()))){
            downMaterialFlowDTO.setXmscyb("1");
        }else if("SITE-22".equals(productTicketPO.getCompanyCode()) && StringUtils.isNotBlank(productTicketPO.getDeptName()) && "生产二部".equals(productTicketPO.getDeptName())){
            downMaterialFlowDTO.setXmsceb("1");
        }
        submitWorkflowRequest.setFormDataInfo(downMaterialFlowDTO);
        submitWorkflowRequest.setRequestTitle("超领单");

        WorkflowUserDTO userInfo = new WorkflowUserDTO();
        userInfo.setUserId(SecurityUtil.getUserId());
        submitWorkflowRequest.setUserInfo(userInfo);
        log.info("materialOverClaim submit request:{}", JSON.toJSONString(submitWorkflowRequest));
        StandardResult submit = downMaterialFlowController.submit(submitWorkflowRequest);
        log.info("materialOverClaim submit response:{}", JSON.toJSONString(submit));

        if (submit.getCode() != OperateCode.SUCCESS.code()) {
            throw new CommonException("超领单流程提交OA异常");
        }

        JSONObject dataObj = JSON.parseObject(JSONObject.toJSONString(submit.getData()));
        materialOperationRecordsPO.setWorkflowRequestId(Convert.toLong(dataObj.get("requestId")));
        materialOperationRecordsPO.setWorkflowRequestStatus(WorkflowRequestStatusEnum.APPROVING.getCode());
        updateById(materialOperationRecordsPO);
        return StandardResult.resultCode(OperateCode.SUCCESS,submit.getData());
    }

    @Override
    public Boolean finalApprove(DownMaterialFlowDTO inVO) {
        log.info("finalApprove inVO:{}",JSON.toJSONString(inVO));
        Long workRequestId = inVO.getWorkRequestId();

        ProductMaterialOperationRecordsPO materialOperationRecordsPO = getRecordByWorkflowRequestId(workRequestId);
        materialOperationRecordsPO.setWorkflowRequestId(workRequestId);
        materialOperationRecordsPO.setWorkflowRequestStatus(WorkflowRequestStatusEnum.COMPLETED.getCode());


        ProductMaterialPO materialPO = productMaterialService.getById(materialOperationRecordsPO.getProductMaterialId());
        ProductTicketPO productTicketPO = productTicketService.getById(materialOperationRecordsPO.getProductTicketId());
        if(inVO.getSlsl().compareTo(BigDecimal.ZERO) != 0 && materialOperationRecordsPO.getConsumptionQuantity().compareTo(BigDecimal.ZERO) != 0) {
            Snowflake snowflake = new Snowflake(1, 1);
            String nextIdStr = snowflake.nextIdStr();

            // 调用erp接口，推送消耗数量
            UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
            updateMaterialDTO.setFlag(UpdateMaterialTypeEnum.SUBTRACT.getIntCode());
            updateMaterialDTO.setOperateType(MaterialOperateTypeEnum.DOWN_MATERIAL.getCode());
            updateMaterialDTO.setNextIdStr(nextIdStr);
            updateMaterialDTO.setKey(workRequestId);
            updateMaterialDTO.setMaterialOperationRecordsId(materialOperationRecordsPO.getId());
            updateMaterialDTO.setQty(materialOperationRecordsPO.getConsumptionQuantity());

            ResponseCodeOutVO responseCodeOutVO = productMaterialService.updateMaterialToErpAndWms(materialPO, productTicketPO, updateMaterialDTO);
            materialPO.setDeductionNo(responseCodeOutVO.getMessage());
            materialOperationRecordsPO.setDeductionNo(responseCodeOutVO.getMessage());
            updateById(materialOperationRecordsPO);

            ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO = iProductMachineMaterialApportionmentService.getByMaterialOperationRecordId(materialOperationRecordsPO.getId());
            if(productMachineMaterialApportionmentPO!=null) {
                productMachineMaterialApportionmentPO.setDeductionNo(responseCodeOutVO.getMessage());
                iProductMachineMaterialApportionmentService.updateById(productMachineMaterialApportionmentPO);
            }
        }
        return productMaterialService.updateById(materialPO);
    }

    @Override
    public Long getLatestRecordId(Long id) {
        LambdaQueryWrapper<ProductMaterialOperationRecordsPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialOperationRecordsPO::getProductMaterialId,id)
                .eq(ProductMaterialOperationRecordsPO::getOperationType,MaterialOperateTypeEnum.UP_MATERIAL.getCode())
                .eq(ProductMaterialOperationRecordsPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()))
                .orderByDesc(ProductMaterialOperationRecordsPO::getCreateTime)
                .last("limit 1");
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = getOne(lambdaQueryWrapper);
        if(productMaterialOperationRecordsPO == null) {
            return null;
        }
        return productMaterialOperationRecordsPO.getId();
    }

    @Override
    public void insertOrUpdate(ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO) {
        System.out.println("-----insertOrUpdate");
    }

    @Override
    public WorkflowDetailVO<DownMaterialFlowDTO> getDetail(GetWorkflowDetailRequest getWorkflowDetailRequest) {
        return null;
    }

    @Override
    public ProductMaterialOperationRecordsPO convert2PO(BaseWorkflowRequest<DownMaterialFlowDTO> baseWorkflowRequest) {
        return null;
    }

    @Override
    public OaMaterialOverClaimDTO convert2OaFormData(DownMaterialFlowDTO downMaterialFlowDTO, WorkflowUserDTO workflowUserDTO) {
        if(downMaterialFlowDTO == null) {

            log.warn("UserResignationServiceImpl.convert2OaFormData downMaterialFlowDTO is null");
            return null;
        }

        String workCode = SecurityUtil.getWorkcode();
        String applicantWorkCode = downMaterialFlowDTO.getApplicantWorkCode();
        Map<String, UserInfo> userInfoMap = hrmResourceService.getUserInfoByWorkcode(com.google.common.collect.Lists.newArrayList(workCode, applicantWorkCode));
        if(userInfoMap == null || userInfoMap.get(workCode) == null) {

            log.warn("UserResignationServiceImpl.getUserInfo userInfo is null");
            throw new CommonException(OperateCode.PARAM_INVALID);
        }

        UserInfo creatorUserInfo = userInfoMap.get(workCode);
        Integer userId = creatorUserInfo.getId().intValue();
        LocalDateTime now = LocalDateTime.now();
        String nowDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        OaMaterialOverClaimDTO oaMaterialOverClaimDTO = new OaMaterialOverClaimDTO();
        BeanUtil.copyProperties(downMaterialFlowDTO,oaMaterialOverClaimDTO);
        oaMaterialOverClaimDTO.setSqr(Long.parseLong(downMaterialFlowDTO.getSqr()));
        oaMaterialOverClaimDTO.setRequestnamenew(downMaterialFlowDTO.getRequestTitle());
        Integer applicantCompanyId = getSecurityInt(creatorUserInfo.getCompanyId());
        oaMaterialOverClaimDTO.setApplicantCompanyId(applicantCompanyId);


        Integer applicantDepartmentId = getSecurityInt(creatorUserInfo.getDeptId());
        oaMaterialOverClaimDTO.setApplicantDepartmentId(applicantDepartmentId);
        oaMaterialOverClaimDTO.setApplicantJobPosition(getSecurityInt(creatorUserInfo.getJobTitleId()));

        oaMaterialOverClaimDTO.setApplicantUserId(userId);
        oaMaterialOverClaimDTO.setApplicantWorkCode(workCode);
        oaMaterialOverClaimDTO.setApplyDate(nowDate);

        oaMaterialOverClaimDTO.setCreateBy(userId);
        oaMaterialOverClaimDTO.setCreateByWorkCode(workCode);
        oaMaterialOverClaimDTO.setOrderNo("");

        UserInfo applicantUserInfo = userInfoMap.get(workCode);
        oaMaterialOverClaimDTO.setUserId(applicantUserInfo.getId());
        oaMaterialOverClaimDTO.setWorkCode(workCode);
        oaMaterialOverClaimDTO.setDepartmentId(getSecurityInt(applicantUserInfo.getDeptId()));
        oaMaterialOverClaimDTO.setJobPosition(getSecurityInt(applicantUserInfo.getJobTitleId()));
        return oaMaterialOverClaimDTO;

    }

    private Integer getSecurityInt(Number number) {

        return number == null ? null : number.intValue();
    }

    private Long getSecurityLong(Number number) {

        return number == null ? null : number.longValue();
    }


    @Override
    public DownMaterialFlowDTO convert2FormData(OaMaterialOverClaimDTO formData, List<WorkflowAttachmentDTO> otherAttachmentList) {
        return BeanUtil.copyProperties(formData,DownMaterialFlowDTO.class);
    }



    @Override
    public Long getWorkflowId() {

        return BusinessType.MATERIAL_OVER_CLAIM.getWorkflowid();
    }

    @Override
    public Class<DownMaterialFlowDTO> getFormDataClz() {
        return DownMaterialFlowDTO.class;
    }

    @Override
    public Class<OaMaterialOverClaimDTO> getOaFormDataClz() {
        return OaMaterialOverClaimDTO.class;
    }


}
