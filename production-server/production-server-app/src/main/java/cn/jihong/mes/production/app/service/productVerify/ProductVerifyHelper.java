package cn.jihong.mes.production.app.service.productVerify;

import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO;
import cn.jihong.mes.production.api.model.enums.MaterialOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.PalletOperateTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.po.ProductOutboundPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.service.*;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产校验
 *
 * @date 2024/01/26
 */
@Slf4j
@Service
public abstract class ProductVerifyHelper implements IProductVerifyService{

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMaterialService productMaterialService;
    @Resource
    private IProductMaterialOperationRecordsService productMaterialOperationRecordsService;
    @Resource
    private IProductPalletOperationRecordsService productPalletOperationRecordsService;

    @Resource
    private IProductMachineTaskService productMachineTaskService;
    @Resource
    private IProductOutboundService productOutboundService;

    public List<Long> getProductTicketIds(String planTicketNo, String machineName) {
        List<ProductTicketPO> productTicketPOS = productTicketService.getListByPlanTicketNo(planTicketNo, machineName);
        return productTicketPOS.stream().map(ProductTicketPO::getId).collect(Collectors.toList());
    }

    /**
     * 根据工程单，获得物料上料数量
     *
     * @param planTicketNo
     */
    public Map<String, BigDecimal> getMaterialByPlanTicketNo(String planTicketNo, String machineName, String unit,
        String processCode) {
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<ProductMaterialOperationRecordsDTO> productMaterialPOS = productMaterialOperationRecordsService
            .getByProductTicketIds(productTicketIds, Arrays.asList(Integer.valueOf(MaterialOperateTypeEnum.UP_MATERIAL.getCode())));
        Map<String, List<ProductMaterialOperationRecordsDTO>> materialCodeMap = productMaterialPOS.stream()
            .collect(Collectors.groupingBy(ProductMaterialOperationRecordsDTO::getMaterialCode));
        Map<String, BigDecimal> map = Maps.newHashMap();
        materialCodeMap.entrySet().stream().forEach(m -> {
            String materialType = m.getValue().get(0).getMaterialType();
            if (!materialType.startsWith(BasePaperConst.PAPER_TYPE)) {
                log.info("物料{}不是原纸，不处理", materialType);
                return;
            }
            // 物料上料数量
            BigDecimal loadingQuantity =
                m.getValue().stream().map(ProductMaterialOperationRecordsDTO::getOriginalQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("物料{}上料总量：{}", m.getKey(), loadingQuantity);
            // 获得当前单位 和 需要转成成的单位
            String materialUnit = m.getValue().get(0).getMaterialUnit();
            loadingQuantity = unitConversion(planTicketNo, loadingQuantity, materialUnit, unit, machineName,
                materialType, m.getKey(), processCode);
            log.info("单位转换后的物料{}上料总量：{}", m.getKey(), loadingQuantity);
            map.put(m.getKey(), loadingQuantity);
        });
        return map;
    }

    /**
     * 根据工程单，获得物料消耗数量
     *
     * @param planTicketNo
     */
    public Map<String, BigDecimal> getMaterialByPlanTicketNoConsumption(String planTicketNo, String machineName,
        String unit,String processCode) {
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<Integer> operationTypes = Lists.newArrayList();
        operationTypes.add(Integer.valueOf(MaterialOperateTypeEnum.DOWN_MATERIAL.getCode()));
        operationTypes.add(Integer.valueOf(MaterialOperateTypeEnum.CHANGE_MATERIAL.getCode()));
        operationTypes.add(Integer.valueOf(MaterialOperateTypeEnum.TEMPORARY_STORAGE.getCode()));
        List<ProductMaterialOperationRecordsDTO> productMaterialPOS = productMaterialOperationRecordsService
            .getByProductTicketIds(productTicketIds, operationTypes);
        Map<String, List<ProductMaterialOperationRecordsDTO>> materialCodeMap =
            productMaterialPOS.stream().collect(Collectors.groupingBy(ProductMaterialOperationRecordsDTO::getMaterialCode));
        Map<String, BigDecimal> map = Maps.newHashMap();
        materialCodeMap.entrySet().stream().forEach(m -> {
            String materialType = m.getValue().get(0).getMaterialType();
            if (!materialType.startsWith(BasePaperConst.PAPER_TYPE)) {
                log.info("物料{}不是原纸，不处理", materialType);
                return;
            }
            // 物料上料数量
            BigDecimal consumptionQuantity =
                m.getValue().stream().map(ProductMaterialOperationRecordsDTO::getConsumptionQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("物料{}上料总量：{}", m.getKey(), consumptionQuantity);
            // 获得当前单位 和 需要转成成的单位
            String materialUnit = m.getValue().get(0).getMaterialUnit();
            consumptionQuantity = unitConversion(planTicketNo, consumptionQuantity, materialUnit, unit, machineName,
                materialType, m.getKey(),processCode);
            log.info("单位转换后的物料{}上料总量：{}", m.getKey(), consumptionQuantity);
            map.put(m.getKey(), consumptionQuantity);
        });
        return map;
    }

    /**
     * 获得上栈板上料数量
     *
     * @param planTicketNo
     * @param machineName
     * @param unit
     * @return
     */
    public Map<String, BigDecimal> getLastPallByPlanTicketNo(String planTicketNo, String machineName, String unit,String processCode) {
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<ProductPalletOperationRecordsDTO> productRequisitionPalletPOS =
                productPalletOperationRecordsService.getByProductTicketIds(productTicketIds,Arrays.asList(Integer.valueOf(PalletOperateTypeEnum.UP_PALLET.getCode())));
        Map<String, List<ProductPalletOperationRecordsDTO>> palletSourceMap = productRequisitionPalletPOS.stream()
            .collect(Collectors.groupingBy(ProductPalletOperationRecordsDTO::getPalletSource));
        Map<String, BigDecimal> map = Maps.newHashMap();
        palletSourceMap.entrySet().stream().forEach(m -> {
            // 上栈板上料数量
            BigDecimal loadingQuantity =
                m.getValue().stream().map(ProductPalletOperationRecordsDTO::getLoadingQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 获得当前单位 和 需要转成成的单位
            String palletUnit = m.getValue().get(0).getUnit();
            loadingQuantity =
                unitConversion(planTicketNo, loadingQuantity, palletUnit, unit, machineName, BasePaperConst.PALLET_TYPE,
                    BasePaperConst.PALLET_TYPE,processCode);
            map.put(m.getKey(), loadingQuantity);
        });
        return map;
    }

    /**
     * 获得报工数量
     *
     * @param planTicketNo
     * @param machineName
     * @return
     */
    public BigDecimal getReportByPlanTicketNo(String planTicketNo, String machineName) {
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<ProductMachineTaskPO> productMachineTaskPOS =
            productMachineTaskService.getByProductTicketIds(productTicketIds);
        // 物料上料数量
        BigDecimal loadingQuantity =
            productMachineTaskPOS.stream().map(ProductMachineTaskPO::getReportedQuantity)
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        return loadingQuantity;
    }

    /**
     * 获得出站数量
     *
     * @param planTicketNo
     * @param machineName
     * @return
     */
    public BigDecimal getOutboundByPlanTicketNo(String planTicketNo, String machineName) {
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<ProductOutboundPO> productOutboundPOS = productOutboundService.getByProductTicketIds(productTicketIds);
        // 出站数量
        BigDecimal loadingQuantity = productOutboundPOS.stream().map(ProductOutboundPO::getProducedQuantity)
            .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("出站数量：{}", loadingQuantity);
        return loadingQuantity;
    }

    /**
     * 校验：报工数量 在 上料数量*BasePaperConst.MIN_PROPORTION_PERCENTAGE%，上料板数量*BasePaperConst.MAX_PROPORTION_PERCENTAGE%
     *
     * @param planTicketNo
     * @param machineName
     * @return
     */
    public boolean verifyRecortBetweenMaterial(String planTicketNo, String machineName, String unit,String processCode) {
        Map<String, BigDecimal> material = getMaterialByPlanTicketNo(planTicketNo, machineName, unit,processCode);
        Map<String, BigDecimal> materialConsumption =
            getMaterialByPlanTicketNoConsumption(planTicketNo, machineName, unit,processCode);
        BigDecimal report = getReportByPlanTicketNo(planTicketNo, machineName);
        if (report.compareTo(BigDecimal.ZERO) == 1) {
            material.entrySet().stream().forEach(m -> {
                BigDecimal value = m.getValue();
                // if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))) == -1) {
                // throw new CommonException(String.format("报工数量%s小于物料上料数量%s的95%%s"), report, value,
                // value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION)));
                // }
                if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))) == 1) {
                    throw new CommonException(String.format("报工数量%s大于物料上料数量%s的105%%s"), report, value,
                        value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION)));
                }
            });
            materialConsumption.entrySet().stream().forEach(m -> {
                BigDecimal value = m.getValue();
                if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))) == -1) {
                    throw new CommonException(String.format("报工数量%s小于物料上料数量%s的95%%s"), report, value,
                        value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION)));
                }
                // if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))) == 1) {
                // throw new CommonException(String.format("报工数量%s大于物料上料数量%s的105%%s"), report, value,
                // value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION)));
                // }
            });
        }
        return true;
    }

    /**
     * 校验：报工数量 在 上栈板数量*BasePaperConst.MIN_PROPORTION_PERCENTAGE%，上栈板数量*BasePaperConst.MAX_PROPORTION_PERCENTAGE%
     *
     * @param unit
     * @param planTicketNo
     * @param machineName
     * @return
     */
    public boolean verifyRecortBetweenPallet(String planTicketNo, String machineName, String unit,String processCode) {
        Map<String, BigDecimal> lastPall = getLastPallByPlanTicketNo(planTicketNo, machineName, unit,processCode);
        BigDecimal report = getReportByPlanTicketNo(planTicketNo, machineName);
        if (report.compareTo(BigDecimal.ZERO) == 1) {
            lastPall.entrySet().stream().forEach(m -> {
                BigDecimal value = m.getValue();
                if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))) == -1) {
                    throw new CommonException(String.format("报工数量%s小于上栈板上料数量%s的95%%s"), report, value,
                        value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION)));
                }
                if (report.compareTo(value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))) == 1) {
                    throw new CommonException(String.format("报工数量%s大于上栈板上料数量%s的105%%s"), report, value,
                        value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION)));
                }
            });

        }
        return true;
    }

    /**
     * 校验：出站数量 在 上料数量*BasePaperConst.MIN_PROPORTION_PERCENTAGE%，上料数量*BasePaperConst.MAX_PROPORTION_PERCENTAGE%
     * 
     * @param planTicketNo
     * @param machineName
     * @return
     */
    public boolean verifyOutBountBetweenMaterial(String planTicketNo, String machineName, String unit,String processCode, BigDecimal producedQuantity) {
        Map<String, BigDecimal> material = getMaterialByPlanTicketNo(planTicketNo, machineName, unit,processCode);
//        Map<String, BigDecimal> materialConsumption =
//            getMaterialByPlanTicketNoConsumption(planTicketNo, machineName, unit);
        BigDecimal outbound = getOutboundByPlanTicketNo(planTicketNo, machineName).add(producedQuantity);
        if (outbound.compareTo(BigDecimal.ZERO) == 1) {
            material.entrySet().stream().forEach(m -> {
                BigDecimal value = m.getValue();
                if (outbound.compareTo(value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))) == 1) {
                    throw new CommonException(String.format("出站数量%s大于上料数量%s的105%%s", outbound, value,
                        value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))));
                }
                log.info("出站数量{}不大于上料数量{}的105%{}", outbound, value,
                    value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION)));
            });
//            materialConsumption.entrySet().stream().forEach(m -> {
//                BigDecimal value = m.getValue();
//                if (outbound.compareTo(value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))) == -1) {
//                    throw new CommonException(String.format("出站数量%s小于消耗数量%s的95%%s", outbound, value,
//                        value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))));
//                }
//                log.info("出站数量{}不小于消耗数量{}的95%{}", outbound, value,
//                    value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION)));
//            });
        }

        return true;
    }

    /**
     * 校验：出站数量 在 上栈板数量*BasePaperConst.MIN_PROPORTION_PERCENTAGE%，上栈板数量*BasePaperConst.MAX_PROPORTION_PERCENTAGE%
     * 
     * @param planTicketNo
     * @param machineName
     * @param unit
     * @return
     */
    public boolean verifyOutBountBetweenPallet(String planTicketNo, String machineName, String unit,String processCode, BigDecimal producedQuantity) {
        Map<String, BigDecimal> lastPall = getLastPallByPlanTicketNo(planTicketNo, machineName, unit,processCode);
        BigDecimal outbound = getOutboundByPlanTicketNo(planTicketNo, machineName).add(producedQuantity);
        if (outbound.compareTo(BigDecimal.ZERO) == 1) {
            lastPall.entrySet().stream().forEach(m -> {
                BigDecimal value = m.getValue();
//                if (outbound.compareTo(value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))) == -1) {
//                    throw new CommonException(String.format("出站数量%s小于上栈板上料数量%s的95%%s", outbound, value,
//                        value.multiply(new BigDecimal(BasePaperConst.MIN_PROPORTION))));
//                }
                if (outbound.compareTo(value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))) == 1) {
                    throw new CommonException(String.format("出站数量%s大于上栈板上料数量%s的105%%s", outbound, value,
                        value.multiply(new BigDecimal(BasePaperConst.MAX_PROPORTION))));
                }
            });
        }
        return true;
    }


    /**
     * 单位转换
     * 
     * @param planTicketNo
     * @param loadingQuantity
     * @param materialUnit
     * @param unit
     * @param materialType
     * @param materialCode
     */
    public BigDecimal unitConversion(String planTicketNo, BigDecimal loadingQuantity, String materialUnit, String unit,
        String machineName, String materialType, String materialCode,String processCode) {
        log.info("当前的工程单是：{}", planTicketNo);
        log.info("当前的数量是：{}", loadingQuantity);
        log.info("当前的单位是：{}", materialUnit);
        log.info("需要转换的单位是：{}", unit);
        log.info("当前的机台名称是：{}", machineName);
        log.info("当前的物料类型是：{}", materialType);
        log.info("当前的物料CODE是：{}", materialCode);
        log.info("当前的工序是：{}", processCode);
        if (materialUnit.equals("KG")) {
            if (unit.equals("PCS") || unit.equals("N") || unit.equals("TAO")) {
                log.info("kg转pcs");
                return kgToPcs(planTicketNo, loadingQuantity, materialType,processCode);
            }
            if (unit.equals("M")) {
                log.info("kg转m");
                return kgToM(loadingQuantity, planTicketNo, machineName, materialCode,processCode);
            }
            if (unit.equals("Z") || unit.equals("Y")) {
                log.info("kg转z");
                return kgToZ(loadingQuantity, planTicketNo, machineName, materialType,processCode);
            }
            if (unit.equals("A")) {
                log.info("kg转a");
                // 按照米的转换公式
                return kgToM(loadingQuantity, planTicketNo, machineName, materialCode,processCode);
            }
        }
        if (materialUnit.equals("M")) {
            if (unit.equals("PCS") || unit.equals("N") || unit.equals("TAO")) {
                log.info("m转pcs");
                return mToPcs(planTicketNo, materialType, machineName, materialCode, loadingQuantity,processCode);
            }
            if (unit.equals("KG")) {
                log.info("m转kg");
                return mToKg(planTicketNo, machineName, materialCode, loadingQuantity,processCode);
            }
            if (unit.equals("Z") || unit.equals("Y")) {
                log.info("m转Z");
                // 米转个  ：  米数  * 1000 /  截断长
                return mToZ(loadingQuantity, planTicketNo, machineName, materialCode,processCode);
            }
        }

        if (materialUnit.equals("PCS") || materialUnit.equals("N") || unit.equals("TAO")) {
            if (unit.equals("M")) {
                log.info("pcs转m");
                return pcsToM(planTicketNo, machineName, loadingQuantity, materialCode,processCode);
            }
            if (unit.equals("KG")) {
                log.info("pcs转kg");
                return pcsToKg(materialUnit, loadingQuantity,processCode);
            }
            if (unit.equals("Z") || unit.equals("Y")) {
                log.info("pcs转Z");
                return pcsToZ(loadingQuantity, planTicketNo, machineName, materialCode,processCode);
            }
            if (unit.equals("PCS") || unit.equals("N") || unit.equals("TAO")) {
                log.info("N转PCS");
                return loadingQuantity;
            }
        }
        if (materialUnit.equals("Y")) {
            if (unit.equals("PCS")  || unit.equals("N") || unit.equals("TAO") ) {
                log.info("Y转PCS");
                return yToPcs(loadingQuantity, planTicketNo,processCode);
            }
        }
        throw new CommonException("不存在的单位转换："+ materialUnit + "转" + unit);
    }




}
