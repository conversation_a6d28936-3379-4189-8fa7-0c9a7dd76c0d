package cn.jihong.mes.production.app.mapper;


import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.vo.in.GetListByMaterialCodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialListPageInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 上料信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface ProductMaterialMapper extends JiHongMapper<ProductMaterialPO> {

    Page<MaterialInfoOutVO> getMaterialRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);


    Page<MaterialUseOutVO> getMaterialUseList(IPage page, @Param("getMaterialListPageInVO") GetMaterialListPageInVO getMaterialListPageInVO);

    MaterialUseOutVO getMaterialUseDetial(@Param("id") Long id);

    Page<MaterialUseRecordOutVO> getMaterialUseDetialList(IPage page, @Param("id") Long id);

    List<GetListByMaterialCodeOutVO> getListByMaterialCode(@Param("inVO") GetListByMaterialCodeInVO inVO);

    List<MaterialTotalUseOutVO> getMaterialTotalUse(@Param("planTicketNo") String planTicketNo,
        @Param("machineName") String machineName, @Param("operationTypes") List<Integer> operationTypes);
}
