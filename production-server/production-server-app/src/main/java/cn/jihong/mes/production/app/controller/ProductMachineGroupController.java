package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineGroupOperationHistoryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductMachineOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO;
import cn.jihong.mes.production.api.service.IProductMachineGroupService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 机组信息
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@RestController
@RequestMapping("/productMachineGroup")
@ShenyuSpringMvcClient(path = "/productMachineGroup/**")
public class ProductMachineGroupController {

    @Resource
    private IProductMachineGroupService productMachineGroupService;

    /**
     * 获取机组列表
     * 
     * @return {@link StandardResult}<{@link List}<{@link ProductMachineGroupOutVO}>>
     */
    @GetMapping("/list")
    public StandardResult<List<ProductMachineGroupOutVO>> getList() {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachineGroupService.getList());
    }

    /**
     * 获取机组详情
     * 
     * @param id
     * @return
     */
    @GetMapping("/getDetialById/{id}")
    public StandardResult<ProductMachineGroupOutVO> getDetialById(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachineGroupService.getDetialById(id));
    }

    /**
     * 新增机组
     * 
     * @param productMachineGroupInVO
     * @return
     */
    @PostMapping("/add")
    public StandardResult<Long> add(@RequestBody @Valid ProductMachineGroupInVO productMachineGroupInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMachineGroupService.addMachineGroup(productMachineGroupInVO));
    }

    /**
     * 修改机组
     * 
     * @param productMachineGroupInVO
     * @return
     */
    @PostMapping("/update")
    public StandardResult<Long> update(@RequestBody @Valid ProductMachineGroupInVO productMachineGroupInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMachineGroupService.updateMachineGroup(productMachineGroupInVO));
    }

    /**
     * 删除机组
     * 
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public StandardResult delete(@PathVariable("id") Long id) {
        productMachineGroupService.deleteById(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 查询机台列表
     * 
     * @param productMachineInVO
     * @return
     */
    @PostMapping("/getMachineList")
    public StandardResult<Pagination<ProductMachineOutVO>>
        getMachineList(@RequestBody ProductMachineInVO productMachineInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productMachineGroupService.getMachineList(productMachineInVO));
    }

    /**
     * 保存人员选择机组历史信息
     * 
     * @param productMachineGroupOperationHistoryInVO
     * @return
     */
    @PostMapping("/saveOperationHistory")
    public StandardResult saveOperationHistory(
        @RequestBody ProductMachineGroupOperationHistoryInVO productMachineGroupOperationHistoryInVO) {
        productMachineGroupService.saveOperationHistory(productMachineGroupOperationHistoryInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 查询人员选择机组历史信息
     *
     * @return
     */
    @GetMapping("/getOperationHistory")
    public StandardResult<ProductMachineGroupOperationHistoryInVO> getOperationHistory() {
        return StandardResult.resultCode(OperateCode.SUCCESS,productMachineGroupService.getOperationHistory());
    }

}
