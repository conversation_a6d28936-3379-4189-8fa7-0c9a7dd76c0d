package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordPageInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordGroupOutVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordOutVO;
import cn.jihong.mes.production.api.service.ILogisticsProductManualRecordService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 物流产品手动出站记录
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@RestController
@RequestMapping("/logisticsProductManualRecord")
@ShenyuSpringMvcClient(path = "/logisticsProductManualRecord/**")
public class LogisticsProductManualRecordController {

    @Resource
    private ILogisticsProductManualRecordService logisticsProductManualRecordService;

    /**
     * 根据工单号和栈板短码查询记录
     *
     * @param queryInVO 查询参数
     * @return 记录详情
     */
    @PostMapping("/getByPlanTicketNoAndShortCode")
    public StandardResult<LogisticsProductManualRecordOutVO> getByPlanTicketNoAndShortCode(@RequestBody @Valid LogisticsProductManualRecordQueryInVO queryInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                logisticsProductManualRecordService.getByPlanTicketNoAndShortCode(queryInVO));
    }

    /**
     * 保存并处理记录
     *
     * @param saveInVO 保存参数
     * @return 处理结果
     */
    @PostMapping("/saveAndProcess")
    public StandardResult<String> saveAndProcess(@RequestBody @Valid LogisticsProductManualRecordSaveInVO saveInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                logisticsProductManualRecordService.saveAndProcess(saveInVO));
    }

    /**
     * 分页查询分组记录
     *
     * @param pageInVO 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/pageGroup")
    public StandardResult<Pagination<LogisticsProductManualRecordGroupOutVO>> pageGroup(@RequestBody @Valid LogisticsProductManualRecordPageInVO pageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                logisticsProductManualRecordService.pageGroup(pageInVO));
    }
} 