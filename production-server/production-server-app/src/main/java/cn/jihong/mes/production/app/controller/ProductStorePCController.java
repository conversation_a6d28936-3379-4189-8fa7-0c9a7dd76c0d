package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ConfirmInboundInVO;
import cn.jihong.mes.production.api.model.vo.in.DeleteApplyNoInVo;
import cn.jihong.mes.production.api.model.vo.in.QueryPalletInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.QueryPalletInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO;
import cn.jihong.mes.production.api.service.IProductStoreService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 入库表PC端
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@RequestMapping("/pc/productStore")
@ShenyuSpringMvcClient(path = "/pc/productStore/**")
public class ProductStorePCController {

    @Resource
    private IProductStoreService productStoreService;


    /**
     * 查询栈板信息
     */
    @PostMapping("/queryPalletInfo")
    public StandardResult<Pagination<QueryPalletInfoOutVO>> queryPalletInfo(@RequestBody @Valid QueryPalletInfoInVO queryPalletInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productStoreService.queryPalletInfo(queryPalletInfoInVO));
    }

    /**
     * 查询入库信息
     */
    @PostMapping("/queryStorageInfo")
    public StandardResult<Pagination<QueryStorageInfoOutVO>> queryStorageInfo(@RequestBody @Valid QueryPalletInfoInVO queryPalletInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productStoreService.queryStorageInfo(queryPalletInfoInVO));
    }

    /**
     * 确认入库 -- 按单入库
     * @param confirmInboundInVO
     * @return
     */
    @PostMapping("/confirmInboundByPallet")
    public StandardResult<String> confirmInboundByPallet(@RequestBody @Valid ConfirmInboundInVO confirmInboundInVO) {
        String storeApplyNos = confirmInboundInVO.getStoreApplyNos().stream().map(id -> String.valueOf(id)).collect(Collectors.joining(","));
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.confirmInboundByPallet(storeApplyNos,confirmInboundInVO));
    }


    /**
     * 删除入库申请单
     * @param deleteApplyNoInVo
     * @return
     */
    @PostMapping("/deleteApplyNo")
    public StandardResult<String> deleteApplyNo(@RequestBody @Valid DeleteApplyNoInVo deleteApplyNoInVo) {
        productStoreService.deleteApplyNo(deleteApplyNoInVo.getStoreApplyNo());
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }



    // 临时代码 ==========================================================
    /**
     * 查询批次号
     * @param barcode
     * @return
     */
    @PostMapping("/getBarcodeLotNo")
    public StandardResult<String> getBarcodeLotNo(@RequestBody String barcode) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productStoreService.getBarcodeLotNo(barcode));
    }

    /**
     * 更新批次号
     * @param barcode
     * @return
     */
    @PostMapping("/updateBarcodeLotNo")
    public StandardResult<String> updateBarcodeLotNo(@RequestBody String barcode) {
        barcode = barcode.replace("\"", "");
        String[] split = barcode.split(",");
        return StandardResult.resultCode(OperateCode.SUCCESS,productStoreService.updateBarcodeLotNo(split[0],split[1]));
    }


}

