package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.vo.in.ExportBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeDetailDetailInVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailDetailService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 箱码明细 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@RestController
@RequestMapping("/productBoxBarcodeDetailDetail")
@ShenyuSpringMvcClient(path = "/productBoxBarcodeDetailDetail/**")
public class ProductBoxBarcodeDetailDetailController {

    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;

    /**
     * 获得箱码明细列表
     *
     * @param getProductBoxBarcodeDetailDetailInVO
     * @return
     */
    @PostMapping("/getList")
    public StandardResult<Pagination<ProductBoxBarcodeDetailDetailDTO>> getList(@RequestBody GetProductBoxBarcodeDetailDetailInVO getProductBoxBarcodeDetailDetailInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productBoxBarcodeDetailDetailService.getList(getProductBoxBarcodeDetailDetailInVO));
    }


    /**
     * 导出
     * @param exportBoxBarcodeInVO
     * @return
     */
    @PostMapping("/exportBoxBarcode")
    public StandardResult exportBoxBarcode(@RequestBody @Valid ExportBoxBarcodeInVO exportBoxBarcodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeDetailDetailService.exportBoxBarcode(exportBoxBarcodeInVO));
    }

}

