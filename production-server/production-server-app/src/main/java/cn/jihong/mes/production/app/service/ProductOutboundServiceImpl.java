package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.channel.api.model.vo.in.SyncEventActiveInVO;
import cn.jihong.channel.api.model.vo.in.SyncEventDestroyInVO;
import cn.jihong.channel.api.model.vo.in.SyncEventSwapTagInVO;
import cn.jihong.channel.api.model.vo.out.SyncEventActiveOutVO;
import cn.jihong.channel.api.model.vo.out.SyncEventDestroyOutVO;
import cn.jihong.channel.api.model.vo.out.SyncEventSwapTagOutVO;
import cn.jihong.channel.api.service.ICaiNiaoKejiService;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.util.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.production.api.model.constant.CaiNiaoKeJiConst;
import cn.jihong.mes.production.api.model.constant.CompanyProcessConst;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.LogisticsProductConfigDTO;
import cn.jihong.mes.production.api.model.dto.ProductInterfaceRecordDTO;
import cn.jihong.mes.production.api.model.dto.ProductStoreDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.enums.*;
import cn.jihong.mes.production.api.model.po.*;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.config.JinshanLogisticsConfig;
import cn.jihong.mes.production.app.config.PushSwitchConfig;
import cn.jihong.mes.production.app.mapper.ProductOutboundMapper;
import cn.jihong.mes.production.app.service.boxCode.BoxCodeHandlerFactory;
import cn.jihong.mes.production.app.service.productVerify.ProductOutBoundServer;
import cn.jihong.mes.production.app.service.productVerify.ProductPalletServer;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.GetInboundQuantityDTO;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.model.dto.SaveLsafTDTO;
import cn.jihong.oa.erp.api.model.dto.webservice.StorageApplicationToErpInDTO;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.po.LsafTPO;
import cn.jihong.oa.erp.api.model.po.OoabTPO;
import cn.jihong.oa.erp.api.model.po.SfacTPO;
import cn.jihong.oa.erp.api.model.vo.*;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;
import cn.jihong.wms.api.model.dto.UpdateBarcodeStoreDTO;
import cn.jihong.wms.api.model.vo.SrmBarcodeDetailVO;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import cn.jihong.wms.api.service.ISrmBarcodeStoreService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 出站信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@DubboService
@Slf4j
public class ProductOutboundServiceImpl extends JiHongServiceImpl<ProductOutboundMapper, ProductOutboundPO>
    implements IProductOutboundService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private PushSwitchConfig pushSwitchConfig;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductLastPalletService productLastPalletService;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private IProductDefectiveProductsService productDefectiveProductsService;

    @Resource
    private ProductOutBoundServer productOutBoundServer;
    @Resource
    private ProductPalletServer productPalletServer;
    @Resource
    private IProductPalletOperationRecordsService productPalletOperationRecordsService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;
    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;



    @DubboReference(timeout = 300000,retries = 0)
    private ISrmBarcodeStoreService srmBarcodeStoreService;
    @DubboReference
    private ISfaaTService iSfaaTService;
    @DubboReference
    private IEcbaTService iEcbaTService;

    @DubboReference
    private IXmamTService iXmamTService;

    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @DubboReference
    private IImaalTService iImaalTService;
    @DubboReference
    private IA01Service ia01Service;
    @DubboReference
    private IEcaaucTService ecaaucTService;
    @DubboReference(timeout = 300000,retries = 0)
    private IInventoryToErpService iInventoryToErpService;
    @DubboReference
    private ILsafTService iLsafTService;
    @DubboReference
    private IEnterpriseWeChatService enterpriseWeChatService;
    @DubboReference
    private ICaiNiaoKejiService caiNiaoKejiService;
    @DubboReference
    private IOoabTService ooabTService;
    @DubboReference
    private ISfacTService sfacTService;
    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;
    @DubboReference
    private ISfcbTService sfcbTService;
    @Resource
    private IProductMachineTaskService productMachineTaskService;

    @Resource
    private IProductInterfaceRecordsService productInterfaceRecordsService;
    @Autowired
    private IProductOutboundService productOutboundService;
    @Autowired
    private IProductStorageApplyService productStorageApplyService;
    @Autowired
    private IProductStoreService productStoreService;
    @Resource
    private Executor taskExecutor;
    @Resource
    private ILogisticsService logisticsService;
    @Resource
    private JinshanLogisticsConfig jinshanLogisticsConfig;
    @DubboReference
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;
    @Resource
    private ILogisticsProductManualRecordService logisticsProductManualRecordService;
    @Resource
    private ILogisticsProductConfigService logisticsProductConfigService;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;









    @Override
    public Boolean temporaryStorageSaveOutboundAndDefective(TemporaryStorageSaveOutboundAndDefectiveInVO inVO) {
        if (inVO.getOutboundInfo() == null || StringUtils.isBlank(inVO.getOutboundInfo().getPalletCode())) {
            throw new CommonException("栈板码不能为空");
        }
        redisTemplate.opsForValue().set(RedisCacheConstant.OUTBOUND + inVO.getOutboundInfo().getPalletCode(),JSON.toJSONString(inVO), 72,TimeUnit.HOURS);
        return true;
    }

    @Override
    public GetTemporaryStorageSaveOutboundAndDefectiveOutVO getTemporaryStorageSaveOutboundAndDefective(GetTemporaryStorageSaveOutboundAndDefectiveInVO inVO) {
        Object outboundObj = redisTemplate.opsForValue().get(RedisCacheConstant.OUTBOUND + inVO.getPalletCode());
        if(outboundObj != null){
            TemporaryStorageSaveOutboundAndDefectiveInVO temporaryStorageSaveOutboundAndDefectiveInVO = JSON.parseObject(outboundObj.toString(), TemporaryStorageSaveOutboundAndDefectiveInVO.class);
            return BeanUtil.copyProperties(temporaryStorageSaveOutboundAndDefectiveInVO, GetTemporaryStorageSaveOutboundAndDefectiveOutVO.class);
        }
        return null;
    }

    @Override
    public Boolean temporaryStoragePushWmsAndErp(TemporaryStoragePushWmsAndErpInVO inVO) {
        if (StringUtils.isBlank(inVO.getPalletCode())) {
            throw new CommonException("栈板码不能为空");
        }
        redisTemplate.opsForValue().set(RedisCacheConstant.STORAGE_APPLICATION + inVO.getPalletCode(),JSON.toJSONString(inVO), 72,TimeUnit.HOURS);
        return true;
    }

    @Override
    public GetTemporaryStoragePushWmsAndErpOutVO getTemporaryStoragePushWmsAndErp(GetTemporaryStoragePushWmsAndErpInVO inVO) {
        Object pushWmsAndErpObj = redisTemplate.opsForValue().get(RedisCacheConstant.STORAGE_APPLICATION + inVO.getPalletCode());
        if(pushWmsAndErpObj != null){
            TemporaryStoragePushWmsAndErpInVO temporaryStoragePushWmsAndErpInVO = JSON.parseObject(pushWmsAndErpObj.toString(),TemporaryStoragePushWmsAndErpInVO.class);
            return BeanUtil.copyProperties(temporaryStoragePushWmsAndErpInVO,GetTemporaryStoragePushWmsAndErpOutVO.class);
        }
        return null;
    }

//    /**
//     * 保存出站信息
//     */
//    @Transactional
//    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
//    @Override
//    public String saveOutboundInfo(SaveOutboundInfoInVO saveOutboundInfoInVO){
//        log.info("Seata全局事务id=================>{}", RootContext.getXID());
//        String LOCK_KEY = RedisCacheConstant.STORAGE  + saveOutboundInfoInVO.getPalletCode();
//        return productOutboundService.saveOutboundInfoNoSeata(LOCK_KEY,saveOutboundInfoInVO);
//    }

    /**
     * 保存出站信息
     */
    @Transactional
    @RedisLock
    @Override
    public String saveOutboundInfo(String redisLockKey,SaveOutboundInfoInVO saveOutboundInfoInVO) {

        if (StringUtils.isNotBlank(saveOutboundInfoInVO.getCaseCode())
                && StringUtils.isNotBlank(saveOutboundInfoInVO.getPalletCode())
        ) {
            String palletCode = saveOutboundInfoInVO.getPalletCode();
            String caseCode = saveOutboundInfoInVO.getCaseCode();
            String[] split = caseCode.split(",");
            String boxCode = split[0];
            if (palletCode.length() == boxCode.length()) {
                throw new CommonException("箱码长度和栈板码长度不一致，请检查是否扫码错误了");
            }
        }
        
        ProductTicketPO productTicketPO = productTicketService.verify(saveOutboundInfoInVO.getProductTicketId());
        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(productTicketPO.getPlanTicketNo());

        // 校验
        saveOutboundInfoVerify(sfaaTVO,saveOutboundInfoInVO, productTicketPO);

        // 校验短码没被使用过
        if (StringUtils.isNotBlank(saveOutboundInfoInVO.getPalletShortCode())) {
            if (productPalletServer.verifyPalletShortCode(productTicketPO.getPlanTicketNo(),
                saveOutboundInfoInVO.getPalletShortCode())) {
                throw new CommonException("短码[" + saveOutboundInfoInVO.getPalletShortCode() + "]已被使用过");
            }
        }

        // 获得工序的单位
        EcaaucTPO ecaaucTPO = ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if (ecaaucTPO != null) {
            saveOutboundInfoInVO.setUnit(ecaaucTPO.getEcaauc009());
        }

        // 保存出站数据
        ProductOutboundPO productOutboundPO =
            saveProductOutboundPO(saveOutboundInfoInVO, productTicketPO);

        // 保存栈板信息
        ProductPalletPO productPalletPO = saveProductPalletPO(saveOutboundInfoInVO,productOutboundPO);

        // 更新生产的数据 及 当前机台的设备止码
        productTicketPO.setMachineStopNo(saveOutboundInfoInVO.getMachineStopNo());
        productTicketPO.setRealProduct(
                (productTicketPO.getRealProduct() == null ? BigDecimal.ZERO.add(saveOutboundInfoInVO.getProducedQuantity()) : productTicketPO.getRealProduct())
                        .add(saveOutboundInfoInVO.getProducedQuantity()));
        productTicketPO.setUpdateBy(SecurityUtil.getUserId());
        productTicketService.updateById(productTicketPO);

        // 保存栈板操作记录
        productLastPalletService.savePalletOperationRecords(productOutboundPO.getProducedQuantity(), BigDecimal.ZERO,
            productOutboundPO.getProducedQuantity(), PalletOperateTypeEnum.OUT_BOUND.getCode(), productTicketPO,
                productPalletPO.getId(), productOutboundPO.getPalletCode(), saveOutboundInfoInVO.getMachineStopNo(),
            null);

        // 入库
        storeProduct(redisLockKey, saveOutboundInfoInVO, productTicketPO, sfaaTVO, productOutboundPO);

        // 报工
        SaveMachineTaskInVO saveMachineTaskInVO = new SaveMachineTaskInVO();
        saveMachineTaskInVO.setMachineName(saveOutboundInfoInVO.getMachineName());
        saveMachineTaskInVO.setReportedProductId(saveOutboundInfoInVO.getProductTicketId());
        if (
            (StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.AHGC.getCode())
                && CompanyProcessConst.moqieList_anhui.contains(productTicketPO.getProcessCode()))
            ||
            (StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.LFEQ.getCode())
                && CompanyProcessConst.moqieList_langfang.contains(productTicketPO.getProcessCode())
            )
        ) {
            log.info("当前是模切工序，需要将小片转成大张");
            // 个转张 ： 个数 / 模数
            //  模数
            SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(
                    productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
            BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();
            log.info("个转米  ：  个数 / 模数   : {}/{}", saveOutboundInfoInVO.getProducedQuantity(), sfcbud016);
            BigDecimal divide = saveOutboundInfoInVO.getProducedQuantity().divide(sfcbud016, 0, RoundingMode.HALF_UP);
            saveMachineTaskInVO.setReportedQuantity(divide);
        } else {
            saveMachineTaskInVO.setReportedQuantity(saveOutboundInfoInVO.getProducedQuantity());
        }
        saveMachineTaskInVO.setOriginalType(1);
        saveMachineTaskInVO.setType(1);
        saveMachineTaskInVO.setProductOutboundId(productOutboundPO.getId());
        if ( productTicketPO.getReportShift() != null) {
            // 设置报工班次
            saveMachineTaskInVO.setReportShift(productTicketPO.getReportShift());
        }
        saveMachineTaskInVO.setReason(saveOutboundInfoInVO.getReason());
        productMachineTaskService.saveMachineTask(RedisCacheConstant.SAVE_MACHINE_TASK + saveOutboundInfoInVO.getMachineName(),saveMachineTaskInVO);

        // 创建生产汇总数据---根据生产工程单汇总
        productInfoService.createOrUpdateProductInfo(saveOutboundInfoInVO.getProductTicketId(), null);

        if (jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())) {
            // 金山物流 手动出站 则需要记录到数据库
            ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService
                    .getMachineConfig(productTicketPO.getMachineName(), productTicketPO.getProcessCode());

            if (machineConfig != null && OutboundTypeEnum.SUBMIT.getCode().equals(String.valueOf(machineConfig.getOutboundType()))) {
                LogisticsProductManualRecordInVO logisticsProductManualRecordInVO = new LogisticsProductManualRecordInVO();
                logisticsProductManualRecordInVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
                logisticsProductManualRecordInVO.setMachineName(productTicketPO.getMachineName());
                logisticsProductManualRecordInVO.setProcessCode(productTicketPO.getProcessCode());

                // 设置默认值，实际情况应该从数据库查询
                LogisticsProductConfigDTO logisticsProductConfigDTO = new LogisticsProductConfigDTO();
                logisticsProductConfigDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
                logisticsProductConfigDTO.setMachineName(productTicketPO.getMachineName());
                logisticsProductConfigDTO.setProcessCode(productTicketPO.getProcessCode());
                LogisticsProductConfigDTO logisticsProductConfig = logisticsProductConfigService.getLogisticsProductConfig(logisticsProductConfigDTO);

                logisticsProductManualRecordInVO.setPalletCount(Integer.valueOf(saveOutboundInfoInVO.getProducedQuantity().toString()));
                logisticsProductManualRecordInVO.setStackLength(logisticsProductConfig.getStackLength());
                logisticsProductManualRecordInVO.setStackWidth(logisticsProductConfig.getStackWidth());
                logisticsProductManualRecordInVO.setPalletLength(logisticsProductConfig.getPalletLength());
                logisticsProductManualRecordInVO.setPalletWidth(logisticsProductConfig.getPalletWidth());
                logisticsProductManualRecordInVO.setPalletColumns(logisticsProductConfig.getPalletColumns());
                logisticsProductManualRecordInVO.setCustomerShortName(logisticsProductConfig.getCustomerShortName());
                logisticsProductManualRecordInVO.setProductShortName(logisticsProductConfig.getProductShortName());
                logisticsProductManualRecordInVO.setPlanCount(logisticsProductConfig.getPlanCount());

                logisticsProductManualRecordInVO.setPalletCode(productPalletPO.getPalletCode());
                logisticsProductManualRecordInVO.setPalletShortCode(productPalletPO.getPalletShortCode());
                logisticsProductManualRecordInVO.setMaterialCategory(logisticsProductConfig.getMaterialCategory());

                logisticsProductManualRecordInVO.setProductName(productTicketPO.getProductName());

                // 保存手动出站记录
                boolean result = logisticsProductManualRecordService.saveManualRecord(logisticsProductManualRecordInVO);
                log.info("保存物流产品手动出站记录结果: {}", result);
            }

        }
        return String.valueOf(productOutboundPO.getId());
    }

    private void storeProduct(String redisLockKey, SaveOutboundInfoInVO saveOutboundInfoInVO, ProductTicketPO productTicketPO,
        SfaaTVO sfaaTVO, ProductOutboundPO productOutboundPO) {
        if (Objects.equals(BooleanEnum.TRUE.getCode(), pushSwitchConfig.getWmsUpdateCaseCode())) {
            if (StringUtils.isNotBlank(saveOutboundInfoInVO.getCaseCode())) {
                // 成品入库
                inboundProducts(saveOutboundInfoInVO, productTicketPO, sfaaTVO, productOutboundPO);
                return;
            }

            if (StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-01")
                || StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-22")
                || StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-082")
                || StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.AHGC.getCode())
            ) {
                // 厦门工厂 廊坊工厂 需要半成品入库
                halfInboundProducts(redisLockKey, saveOutboundInfoInVO, productTicketPO, sfaaTVO);
                return;
            }
        }
    }

    private void inboundProducts(SaveOutboundInfoInVO saveOutboundInfoInVO, ProductTicketPO productTicketPO, SfaaTVO sfaaTVO, ProductOutboundPO productOutboundPO) {
        verifyBoxCode(saveOutboundInfoInVO.getCaseCode(),productTicketPO.getPlanTicketNo());
        log.info("成品入库");
        ProductStoreDTO productStoreDTO = new ProductStoreDTO();
        productStoreDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        productStoreDTO.setPalletCode(saveOutboundInfoInVO.getPalletCode());
        productStoreDTO.setCaseCode(saveOutboundInfoInVO.getCaseCode());
        productStoreDTO.setProducedQuantity(saveOutboundInfoInVO.getProducedQuantity());
        productStoreDTO.setStoreType(StoreTypeEnum.BY_BOX.getIntCode());
        productStoreDTO.setUnit(sfaaTVO.getSfaa013());
        productStoreDTO.setReason(saveOutboundInfoInVO.getReason());
        String storeApplyNo = productStoreService.request(productStoreDTO);
        productOutboundPO.setResult(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productOutboundPO.setResponse(storeApplyNo);
        updateById(productOutboundPO);
    }

    @Override
    public VerifyOutboundRequestOutVO verifyOutboundRequest(VerifyOutboundRequestInVO inVO) {
        VerifyOutboundRequestOutVO outVO = new VerifyOutboundRequestOutVO();
        outVO.setResult(true);
        // 处理出站数量为0情况
        if(inVO.getOutboundQuantity().compareTo(BigDecimal.ZERO) == 0){
            return outVO;
        }

        ProductTicketPO productTicketPO = productTicketService.getById(inVO.getProductTicketId());
        SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        // 首道工序报工
        if("INIT".equals(sfcbTVO.getSfcb007())) {
            VerifyReportRequestInVO reportRequestInVO = new VerifyReportRequestInVO();
            reportRequestInVO.setReportedProductId(inVO.getProductTicketId());
            reportRequestInVO.setReportedQuantity(inVO.getOutboundQuantity());
            VerifyReportRequestOutVO reportRequestOutVO = iProductMachineTaskService.verifyReportRequest(reportRequestInVO);
            outVO.setCanOutboundQuantity(reportRequestOutVO.getCanReportQuantity());
            outVO.setTotalOutboundQuantity(reportRequestOutVO.getTotalReportQuantity());
            outVO.setBillingQuantity(reportRequestOutVO.getBillingQuantity());
            outVO.setResult(reportRequestOutVO.getResult());
        }
        // 有箱码 需校验出站数量
        if(StringUtils.isNotBlank(inVO.getBoxCode())){
            VerifyInboundRequestInVO inboundRequestInVO = new VerifyInboundRequestInVO();
            inboundRequestInVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
            inboundRequestInVO.setProducedQuantity(inVO.getOutboundQuantity());
            VerifyInboundRequestOutVO inboundRequestOutVO = productStoreService.verifyInboundRequest(inboundRequestInVO);
            outVO.setCanOutboundQuantity(inboundRequestOutVO.getCanStorageQuantity());
            outVO.setTotalOutboundQuantity(inboundRequestOutVO.getTotalStorageQuantity());
            outVO.setBillingQuantity(inboundRequestOutVO.getBillingQuantity());
            outVO.setResult(inboundRequestOutVO.getResult());
        }
        return outVO;
    }

    private void halfInboundProducts(String redisLockKey, SaveOutboundInfoInVO saveOutboundInfoInVO, ProductTicketPO productTicketPO, SfaaTVO sfaaTVO) {
        log.info("入库申请没有箱码");
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        String sfaa010 = sfaaTVO.getSfaa010();
//        ImaalTVO imaalTVO = iImaalTService.getImaalTByProductNo(sfaa010);
//        String itemSpec = imaalTVO.getImaal004();
        // 判断是否是 半成品  是半成品的话需要进行入库申请
        // 当前工序是末道工序，且产品是1 或者 3 开头的  则判断为半成品
        log.info("下一道工序是{}，产品编号是{}", sfcbTVO.getSfcb009(), sfaa010);
        if (sfcbTVO.getSfcb009().equals("END") && (sfaa010.startsWith("1") || sfaa010.startsWith("3"))) {
            log.info("进入半成品入库流程===========================================");
            InboundRequestByPalletInVO inboundRequestByPalletInVO = new InboundRequestByPalletInVO();
            inboundRequestByPalletInVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
            inboundRequestByPalletInVO.setProducedQuantity(saveOutboundInfoInVO.getProducedQuantity());
            inboundRequestByPalletInVO.setUnit(productTicketPO.getUnit());

            InboundRequestByPalletInVO.PalletCodeInfo palletCodeInfo = new InboundRequestByPalletInVO.PalletCodeInfo();
            palletCodeInfo.setPalletCode(saveOutboundInfoInVO.getPalletCode());
            palletCodeInfo.setPalletCodeQuantity(saveOutboundInfoInVO.getProducedQuantity());
            inboundRequestByPalletInVO.setPalletCodeInfos(Arrays.asList(palletCodeInfo));

            // 设置为半成品
            inboundRequestByPalletInVO.setIsFinalProduct(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            log.info("入库申请入参：{}", JSON.toJSONString(inboundRequestByPalletInVO));
            // 入库申请
            inboundRequestByPalletInVO.setReason(saveOutboundInfoInVO.getReason());
            productOutboundService.inboundRequestByPallet(redisLockKey, inboundRequestByPalletInVO);
            if (StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-01")
                    || StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-22")
                    || StringUtils.equals(SecurityUtil.getCompanySite(), "SITE-082")
                    || (StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.AHGC.getCode())
                        && CompanyProcessConst.chengxingList.contains(productTicketPO.getProcessCode()))
            ) {
                log.info("半成品入库申请成功，开始进行半成品入库确认,[{}],[{}]",SecurityUtil.getCompanySite(),productTicketPO.getProcessCode());
                // 入库确认
                ConfirmInboundInVO confirmInboundInVO = new ConfirmInboundInVO();
                ProductStorePO productStorePO = productStoreService.getByPalletCode(saveOutboundInfoInVO.getPalletCode());
                confirmInboundInVO.setIds(Arrays.asList(productStorePO.getId()));
                // 半成品默认 X001
                if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())) {
                    confirmInboundInVO.setWarehouse(StringUtil.isBlank(productStorePO.getStorage()) ? "X002" : productStorePO.getStorage());
                } else {
                    confirmInboundInVO.setWarehouse(StringUtil.isBlank(productStorePO.getStorage()) ? "X001" : productStorePO.getStorage());
                }

                String docNo = productStoreService.confirmInbound(redisLockKey,confirmInboundInVO);
            }


//            // 保存物料信息
//            log.info("封装并推送到智物流");
//            String lotNo = null;
//            SfacTPO sfacTPO = null;
//            String palletCode = saveOutboundInfoInVO.getPalletCode();
//            String[] split = palletCode.split("#");
//            if (split != null && split.length > 0 && split.length > 1) {
//                lotNo = split[1];
//                sfacTPO = sfacTService.getByProductNo(productTicketPO.getPlanTicketNo(), sfaaTVO.getSfaa010());
//            }
//
//            UpdateBarcodeStoreDTO updateBarcodeStoreDTO = saveWmsMaterialInfo(saveOutboundInfoInVO,
//                    productTicketPO, docNo, sfaa010, itemSpec,null,null,null,null,lotNo,sfacTPO.getSfac006());
//            srmBarcodeStoreService.saveBarcodeStore(updateBarcodeStoreDTO);
        }
    }

    private SfaaTVO saveOutboundInfoVerify(SfaaTVO sfaaTVO, SaveOutboundInfoInVO saveOutboundInfoInVO, ProductTicketPO productTicketPO) {
        // 校验每个栈板可是放多少箱码
        if (StringUtils.isNotBlank(saveOutboundInfoInVO.getCaseCode())) {
            EcbaTVO ecbaTVO = iEcbaTService.getVOByProductionNo(sfaaTVO.getSfaa010(), productTicketPO.getCompanyCode());
            if (ecbaTVO != null && ecbaTVO.getEcbaua049() != null && ecbaTVO.getEcbaua049() > 0) {
                if (ecbaTVO.getEcbaua049() < saveOutboundInfoInVO.getCaseCode().split(",").length) {
                    throw new CommonException("当前工序可放" + ecbaTVO.getEcbaua049() + "箱，不足以放入" + saveOutboundInfoInVO.getCaseCode().split(",").length + "箱");
                }
            }
        }


        // 一个栈板码，只能使用一次（保存一次）
        verify(saveOutboundInfoInVO);

        // 一个箱码，只能使用一次（保存一次）
        if(StringUtils.isNotBlank(saveOutboundInfoInVO.getCaseCode())) {
            // 校验箱码和栈板码
            IBoxCodeService boxCodeService =
                    boxCodeHandlerFactory.getBoxCodeService(productTicketPO.getPlanTicketNo());
            boxCodeService.verifyCaseCodeByErp(saveOutboundInfoInVO.getCaseCode(), saveOutboundInfoInVO.getPalletCode());
        }
        return sfaaTVO;
    }


//    /**
//     * 往 mes 和 erp 推动
//     * @param saveOutboundInfoInVO
//     * @param planTicketNo
//     */
//    @RedisLock
//    @Transactional
//    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
//    @Override
//    public ResponseCodeOutVO pushWmsAndErp(SaveOutboundInfoInVO saveOutboundInfoInVO, String planTicketNo,SfacTPO sfacTPO) {
//        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(planTicketNo);
//        if (sfacTPO == null) {
//            sfacTPO = sfacTService.getByProductNo(planTicketNo, sfaaTVO.getSfaa010());
//        }
//        // 箱码信息推送至WMS
//        BarcodeDetailDTO barcodeDetailDTO = pushCaseInfoToWms(saveOutboundInfoInVO, planTicketNo,sfacTPO);
//        // 入库申请推送给 ERP
//        ResponseCodeOutVO responseCodeOutVO = storageApplicationToErp(barcodeDetailDTO, saveOutboundInfoInVO, planTicketNo,sfaaTVO,sfacTPO);
//        return responseCodeOutVO;
//    }

    /**
     * 入库申请推送给 ERP
     * @param barcodeDetailDTO
     * @param saveOutboundInfoInVO
     * @param planTicketNo
     * @param sfaaTVO
     * @param sfacTPO
     * @return: void
     * <AUTHOR>
     * @date: 2024/1/17 15:48
     */
    @Override
    public ResponseCodeOutVO storageApplicationToErp(BarcodeDetailDTO barcodeDetailDTO,
                                                     SaveOutboundInfoInVO saveOutboundInfoInVO,
                                                     String planTicketNo, SfaaTVO sfaaTVO, SfacTPO sfacTPO){
//        Date curentDate = new Date();
        Date curentDate = updateReportDate();

        String timeStamp = DateUtil.parseDateToStringCustom(curentDate,"yyyyMMddHHmmssSSS");
        List<SaveLsafTDTO> saveLsafTDTOList = new ArrayList<>();

        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(barcodeDetailDTO.getPlanTicketNo());

        // 保存仓库位置
        if (saveOutboundInfoInVO.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))) {
            barcodeDetailDTO.setWarehouseNo("X001");
        } else {
            barcodeDetailDTO.setWarehouseNo("C001");
        }
        for(BarcodeDetailDTO.BoxInfo boxInfo : barcodeDetailDTO.getBoxInfos()) {
            SaveLsafTDTO saveLsafTDTO = setLsafTInfo(barcodeDetailDTO, boxInfo,curentDate,boxCodeService,sfacTPO.getSfac006(),sfaaTVO.getSfaa013(),sfacTPO.getSfac001());
            saveLsafTDTOList.add(saveLsafTDTO);
        }

        // 推送至ERP 入库保存
        log.info("---saveLsafTDTOList:{}",JSON.toJSONString(saveLsafTDTOList));
        iLsafTService.saveLsafT(saveLsafTDTOList);

        StorageApplicationToErpInDTO inDTO = new StorageApplicationToErpInDTO();
        inDTO.setKey(UUID.randomUUID().toString().replaceAll("-", ""));
        inDTO.setTimeStamp(timeStamp);
        inDTO.setPlanTicketNo(barcodeDetailDTO.getPlanTicketNo());
        log.info("---storageApplicationToErp:{}",JSON.toJSONString(inDTO));
        try {
            ResponseCodeOutVO responseCodeOutVO = iInventoryToErpService.storageApplicationToErp(inDTO);

            // 保存记录
            Long userId = SecurityUtil.getUserId();
            taskExecutor.execute(() -> {
                ProductInterfaceRecordDTO productInterfaceRecordDTO = new ProductInterfaceRecordDTO();
                productInterfaceRecordDTO.setProductTicketId(saveOutboundInfoInVO.getProductTicketId());
                productInterfaceRecordDTO.setPalletCode(saveOutboundInfoInVO.getPalletCode());
                productInterfaceRecordDTO.setUserId(userId);
                productInterfaceRecordDTO.setPlanTicketNo(planTicketNo);
                productInterfaceRecordDTO.setResponse(responseCodeOutVO.getMessage());
                productInterfaceRecordDTO.setResponseCode(responseCodeOutVO.getCode());
                saveProductInterfaceRecords(productInterfaceRecordDTO);
            });

            if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
                throw new CommonException(responseCodeOutVO.getMessage());
            }
            return responseCodeOutVO;
        } catch (Exception e) {
            log.error("推送erp报错，lsaf表回滚，参数：" + timeStamp,e);
            iLsafTService.delectByTimestamp(barcodeDetailDTO.getPlanTicketNo(),timeStamp);
            throw new CommonException("推送erp报错"+ e.getMessage());
        }
    }

    @Override
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveProductInterfaceRecords(ProductInterfaceRecordDTO productInterfaceRecordDTO) {
        // 保存记录
        ProductInterfaceRecordsPO productInterfaceRecordsPO = new ProductInterfaceRecordsPO();
        if (productInterfaceRecordDTO.getProductTicketId() != null) {
            Long productTicketId = productInterfaceRecordDTO.getProductTicketId();
            ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
            productInterfaceRecordsPO.setProductTicketId(productTicketPO.getId());
            productInterfaceRecordsPO.setCompanyCode(productTicketPO.getCompanyCode());
        }
        productInterfaceRecordsPO.setMainInfo(productInterfaceRecordDTO.getPalletCode());
        productInterfaceRecordsPO.setBusinessType(InterfaceBusinessEnum.INBOUND_REQUESTS.getCode());
        productInterfaceRecordsPO.setResponse(productInterfaceRecordDTO.getResponse());
        productInterfaceRecordsPO.setResult(Integer.valueOf(productInterfaceRecordDTO.getResponseCode()));
        productInterfaceRecordsPO.setCreateBy(productInterfaceRecordDTO.getUserId());
        productInterfaceRecordsPO.setUpdateBy(productInterfaceRecordDTO.getUserId());
        productInterfaceRecordsPO.setPlanTicketNo(productInterfaceRecordDTO.getPlanTicketNo());
        productInterfaceRecordsService.save(productInterfaceRecordsPO);
    }

    @Override
    public GetInboundQuantityOutVO getInboundQuantity(String planTicketNo) {
        GetInboundQuantityDTO inboundQuantity = iSfaaTService.getInboundQuantity(planTicketNo);
        return BeanUtil.copyProperties(inboundQuantity,GetInboundQuantityOutVO.class);
    }

    @Override
    public String verifyPalletCode(VerifyPalletCodeInVO verifyPalletCodeInVO) {
        ProductStorePO productStorePO = productStoreService.getByPalletCode(verifyPalletCodeInVO.getPalletCode());
        if (productStorePO != null) {
            throw new CommonException("栈板码已在入库申请表中存在[" + productStorePO.getPalletCode() + "]");
        }

        // 去wms校验 是否存在
        productStoreService.verifyPolletCodeFromWms(verifyPalletCodeInVO.getPalletCode());

        return "SUCCESS";
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public SyncEventActiveOutVO activeBoxCode(String barcode) {
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(barcode);
        if (productBoxBarcodeDetailDetailPO == null) {
            throw new CommonException("箱码不存在");
        }
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = productBoxBarcodeDetailService.getById(productBoxBarcodeDetailDetailPO.getProductBarcodeDetailId());
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("箱码对应的箱码详情为空：" + barcode);
        }

        // 兼容换箱操作，没有对应的sku名称
        if (StringUtils.isBlank(productBoxBarcodeDetailPO.getSkuName())) {
            MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
            if (materialsInfoDTO == null) {
                throw new CommonException("物料信息为空：" + barcode);
            }
            productBoxBarcodeDetailPO.setSkuName(materialsInfoDTO.getImaaua018());
        }

        // 物料信息
        ImaalTVO imaalTVO = iImaalTService.getImaalTByProductNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
        if (imaalTVO == null) {
            throw new CommonException("物料信息为空：" + barcode);
        }

        SyncEventActiveInVO request = new SyncEventActiveInVO();
        request.setDeviceCode(SecurityUtil.getCompanySite());
        request.setEventCode(CaiNiaoKeJiConst.EVENT_CODE_ACTIVE);

        SyncEventActiveInVO.SkuInfo skuInfo = new SyncEventActiveInVO.SkuInfo();
        skuInfo.setBatch(productBoxBarcodeDetailDetailPO.getBatchCode());
        skuInfo.setProduceDate(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getProductionDate(),DatePattern.NORM_DATE_PATTERN));
        skuInfo.setDueDate(productBoxBarcodeDetailDetailPO.getExpirationDate());
        skuInfo.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
        skuInfo.setSkuName(productBoxBarcodeDetailPO.getSkuName());
        skuInfo.setTinyBatch("");
        skuInfo.setVendorSkuCode(imaalTVO.getImaal001());
        skuInfo.setVendorSkuName(imaalTVO.getImaal003());

        SyncEventActiveInVO.EventDetail eventDetail = new SyncEventActiveInVO.EventDetail();
        eventDetail.setSkuInfo(skuInfo);

        SyncEventActiveInVO.Tag tag = new SyncEventActiveInVO.Tag();
        tag.setEpc(barcode);
        tag.setScanTime(cn.hutool.core.date.DateUtil.format(new Date(),DatePattern.NORM_DATETIME_PATTERN));

        List tagList = Lists.newArrayList();
        tagList.add(tag);
        eventDetail.setTagList(tagList);

        request.setEventDetail(eventDetail);
        request.setTenantCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getTenantCode());
        SyncEventActiveOutVO syncEventActiveOutVO = caiNiaoKejiService.syncEventActive(request);
        if (!"true".equals(syncEventActiveOutVO.getSuccess())) {
            throw new CommonException("激活失败，原因：" + syncEventActiveOutVO.getErrorMsg());
        }
        return syncEventActiveOutVO;
    }

    @Override
    public String destroyBoxCode(String boxCode) {
        if (StringUtils.isBlank(boxCode)) {
            throw new CommonException("箱码不能为空");
        }
        List<LsafTPO> sameCaseCodeList = iLsafTService.getByBoxCodes(Arrays.asList(boxCode));
        if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
            throw new CommonException("箱码已在erp使用：" + JSON.toJSONString(sameCaseCodeList.stream().map(lsafTPO -> lsafTPO.getLsaf001() + "入库申请单号:"+ lsafTPO.getLsaf009()).collect(Collectors.toList())));
        }

        // 调用菜鸟边缘系统，去消箱
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(boxCode);
        if (productBoxBarcodeDetailDetailPO == null) {
            throw new CommonException("箱码不存在");
        }
        if (productBoxBarcodeDetailDetailPO.getActiva().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))) {
            throw new CommonException("箱码未激活，不能去消箱");
        }
        if (productBoxBarcodeDetailDetailPO.getActiva().equals(BoxActiveEnum.DESTROY.getCode())) {
            throw new CommonException("箱码已消箱,不用重复消箱");
        }
        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = productBoxBarcodeDetailService.getById(productBoxBarcodeDetailDetailPO.getProductBarcodeDetailId());
        if (productBoxBarcodeDetailPO == null) {
            throw new CommonException("箱码对应的箱码详情为空：" + boxCode);
        }

        // 兼容换箱操作，没有对应的sku名称
        if (StringUtils.isBlank(productBoxBarcodeDetailPO.getSkuName())) {
            MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
            if (materialsInfoDTO == null) {
                throw new CommonException("物料信息为空：" + boxCode);
            }
            productBoxBarcodeDetailPO.setSkuName(materialsInfoDTO.getImaaua018());
        }

        // 物料信息
        ImaalTVO imaalTVO = iImaalTService.getImaalTByProductNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
        if (imaalTVO == null) {
            throw new CommonException("物料信息为空：" + productBoxBarcodeDetailDetailPO.getMaterialCode());
        }

        SyncEventDestroyInVO request = new SyncEventDestroyInVO();
        request.setDeviceCode(SecurityUtil.getCompanySite());
        request.setEventCode(CaiNiaoKeJiConst.EVENT_CODE_DESTROY);

        SyncEventDestroyInVO.EventDetail eventDetail = new SyncEventDestroyInVO.EventDetail();
        SyncEventDestroyInVO.SkuInfo skuInfo = new SyncEventDestroyInVO.SkuInfo();
        skuInfo.setBatch(productBoxBarcodeDetailDetailPO.getBatchCode());
        skuInfo.setProduceDate(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getProductionDate(),DatePattern.NORM_DATE_PATTERN));
        skuInfo.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
        skuInfo.setSkuName(productBoxBarcodeDetailPO.getSkuName());
        skuInfo.setTinyBatch("");
        skuInfo.setVendorSkuCode(imaalTVO.getImaal001());
        skuInfo.setVendorSkuName(imaalTVO.getImaal003());
        eventDetail.setSkuInfo(skuInfo);
        SyncEventDestroyInVO.Tag tag = new SyncEventDestroyInVO.Tag();
        tag.setEpc(boxCode);
        tag.setScanTime(cn.hutool.core.date.DateUtil.format(new Date(),DatePattern.NORM_DATETIME_PATTERN));
        tag.setProduceDate(new Date());
        tag.setSkuCode(productBoxBarcodeDetailDetailPO.getSkuCode());
        tag.setSkuName(productBoxBarcodeDetailPO.getSkuName());


        List tagList = Lists.newArrayList();
        tagList.add(tag);
        eventDetail.setTagList(tagList);

        request.setEventDetail(eventDetail);
        request.setTenantCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getTenantCode());

        SyncEventDestroyOutVO syncEventDestroyOutVO = caiNiaoKejiService.syncEventDestroy(request);
        if (!"true".equals(syncEventDestroyOutVO.getSuccess())) {
            throw new CommonException("消箱失败，原因：" + syncEventDestroyOutVO.getErrorMsg());
        }

        // 提取msg字段的内容
        String msg = ProductBoxBarcodeDetailDetailServiceImpl.extractField(com.alibaba.fastjson.JSON.toJSONString(syncEventDestroyOutVO), "data");
        // 提取成功和失败的个数
        int successCount = ProductBoxBarcodeDetailDetailServiceImpl.extractCount(msg, "消箱成功(\\d+)个");
        if (successCount > 0) {
            log.info("消箱成功: " + boxCode);
            log.info("更新本地箱码消箱状态: " + boxCode);
            productBoxBarcodeDetailDetailService.destroyBox(boxCode);
        } else {
            throw new CommonException("消箱失败: " + boxCode);
        }
        return syncEventDestroyOutVO.getData();
    }

    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeBoxCode(ChangeBoxInVO changeBoxInVO) {
        if (changeBoxInVO.getOldBoxCode().equals(changeBoxInVO.getNewBoxCode())) {
            throw new CommonException("新箱码与旧箱码不能相同");
        }

        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPOOld =
            productBoxBarcodeDetailDetailService.getByBarcodeNo(changeBoxInVO.getOldBoxCode());
        if (productBoxBarcodeDetailDetailPOOld == null) {
            throw new CommonException("旧箱码不存在：" + changeBoxInVO.getOldBoxCode());
        }
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPONew =
                productBoxBarcodeDetailDetailService.getByBarcodeNo(changeBoxInVO.getNewBoxCode());
        if (productBoxBarcodeDetailDetailPONew == null) {
            throw new CommonException("新箱码不存在：" + changeBoxInVO.getNewBoxCode());
        }
        if (!productBoxBarcodeDetailDetailPOOld.getPlanTicketNo().equals(productBoxBarcodeDetailDetailPONew.getPlanTicketNo())) {
            throw new CommonException("新箱码与旧箱码的生产工程单号不同");
        }
        if (!productBoxBarcodeDetailDetailPOOld.getLotNo().equals(productBoxBarcodeDetailDetailPONew.getLotNo())) {
            throw new CommonException("新箱码与旧箱码的批次号不同");
        }
        if (!productBoxBarcodeDetailDetailPOOld.getSkuCode().equals(productBoxBarcodeDetailDetailPONew.getSkuCode())) {
            throw new CommonException("新箱码与旧箱码的麦当劳8位码编码不同");
        }
        if (!productBoxBarcodeDetailDetailPOOld.getProductionDate().equals(productBoxBarcodeDetailDetailPONew.getProductionDate())) {
            throw new CommonException("新箱码与旧箱码的生产日期不同");
        }
        if (!productBoxBarcodeDetailDetailPOOld.getExpirationDate().equals(productBoxBarcodeDetailDetailPONew.getExpirationDate())) {
            throw new CommonException("新箱码与旧箱码的麦保质期不同");
        }
        if (productBoxBarcodeDetailDetailPOOld.getActiva().equals(BoxActiveEnum.DESTROY.getCode())) {
            throw new CommonException("旧箱码已消箱,不可去换箱");
        }
        if (productBoxBarcodeDetailDetailPONew.getActiva().equals(BoxActiveEnum.DESTROY.getCode())) {
            throw new CommonException("新箱码已消箱,不可去换箱");
        }

        LsafTPO lsafTPO = iLsafTService.getByPalletCode(changeBoxInVO.getNewBoxCode());
        if (lsafTPO != null) {
            throw new CommonException("新栈板码已使用,不可替换,入库申请单号为" + lsafTPO.getLsaf009());
        }
        LsafTPO lsafTPOOld = iLsafTService.getByPalletCode(changeBoxInVO.getOldBoxCode());
        if (lsafTPOOld != null) {
            throw new CommonException("旧栈板码已入库,不可替换,入库申请单号为" + lsafTPO.getLsaf009());
        }

        // 调用菜鸟边缘系统，去消箱
        SyncEventSwapTagInVO request = new SyncEventSwapTagInVO();
        request.setDeviceCode(SecurityUtil.getCompanySite());
        request.setEventCode(CaiNiaoKeJiConst.EVENT_CODE_SWAP_TAG);

        SyncEventSwapTagInVO.EventDetail eventDetail = new SyncEventSwapTagInVO.EventDetail();
        eventDetail.setNewTag(changeBoxInVO.getNewBoxCode());
        eventDetail.setOldTag(changeBoxInVO.getOldBoxCode());
        request.setEventDetail(eventDetail);
        request.setTenantCode(CaiNiaoParamEnum.getCaiNiaoParamEnum(SecurityUtil.getCompanySite()).getTenantCode());

        // 更新skucode
        productBoxBarcodeDetailDetailService.updateSkuCode(changeBoxInVO.getNewBoxCode());

        SyncEventSwapTagOutVO syncEventSwapTagOutVO = caiNiaoKejiService.syncEventSwapTag(request);
        if (!"true".equals(syncEventSwapTagOutVO.getSuccess())) {
            throw new CommonException("消箱失败，原因：" + syncEventSwapTagOutVO.getErrorMsg());
        }

        // 提取msg字段的内容
        String msg = ProductBoxBarcodeDetailDetailServiceImpl
            .extractField(JSON.toJSONString(syncEventSwapTagOutVO), "data");

        if (!msg.contains("交换成功")) {
            throw new CommonException("换箱失败：" + msg);
        }

        productBoxBarcodeDetailDetailService.activa(changeBoxInVO.getNewBoxCode());
        productBoxBarcodeDetailDetailService.destroyBox(changeBoxInVO.getOldBoxCode());

        return msg;
    }

    @Override
    public String inboundRequestByPallet(String redisLockKey, InboundRequestByPalletInVO inboundRequestByPalletInVO) {
        // 数量校验
        if (inboundRequestByPalletInVO.getProducedQuantity() == null
                || inboundRequestByPalletInVO.getProducedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new CommonException("入库数量必须大于0");
        }
        BigDecimal allPalletCodeQuantity = inboundRequestByPalletInVO.getPalletCodeInfos().stream()
                .map(InboundRequestByPalletInVO.PalletCodeInfo::getPalletCodeQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (inboundRequestByPalletInVO.getProducedQuantity().compareTo(allPalletCodeQuantity) != 0) {
            throw new CommonException("入库数量:"+inboundRequestByPalletInVO.getProducedQuantity()
                    +"与所有栈板数量:"+allPalletCodeQuantity+"不一致");
        }


        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(inboundRequestByPalletInVO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单号错误：" + inboundRequestByPalletInVO.getPlanTicketNo());
        }
        // 箱码
        List<String> caseCodes = inboundRequestByPalletInVO.getPalletCodeInfos().stream()
            .map(palletCodeInfo -> palletCodeInfo.getPalletCode()).collect(Collectors.toList());

        // 校验
        inboundRequestByPalletVerify(inboundRequestByPalletInVO, sfaaTVO, caseCodes);

        // 入库申请记录
        ProductStoreDTO productStoreDTO = new ProductStoreDTO();
        productStoreDTO.setPlanTicketNo(inboundRequestByPalletInVO.getPlanTicketNo());
        productStoreDTO.setPalletCode(null);
        productStoreDTO.setCaseCode(caseCodes.stream().collect(Collectors.joining(",")));
        productStoreDTO.setProducedQuantity(inboundRequestByPalletInVO.getProducedQuantity());
        productStoreDTO.setUnit(sfaaTVO.getSfaa013());
        productStoreDTO.setStoreType(StoreTypeEnum.BY_PALLET.getIntCode());
        productStoreDTO.setPalletCodeInfos(inboundRequestByPalletInVO.getPalletCodeInfos());
        productStoreDTO.setIsFinalProduct(inboundRequestByPalletInVO.getIsFinalProduct() != null ?
                inboundRequestByPalletInVO.getIsFinalProduct() : Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productStoreDTO.setReason(inboundRequestByPalletInVO.getReason());
        return productStoreService.requestByPallet(productStoreDTO);
    }

    private void inboundRequestByPalletVerify(InboundRequestByPalletInVO inboundRequestByPalletInVO, SfaaTVO sfaaTVO, List<String> caseCodes) {
        if (CollectionUtil.isNotEmpty(inboundRequestByPalletInVO.getPalletCodeInfos())) {
            inboundRequestByPalletInVO.getPalletCodeInfos().forEach(palletCodeInfo -> {
                if (StringUtils.isBlank(palletCodeInfo.getPalletCode())) {
                    throw new CommonException("栈板码不能为空");
                }
                if (palletCodeInfo.getPalletCodeQuantity() == null || palletCodeInfo.getPalletCodeQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new CommonException(palletCodeInfo.getPalletCode() + "栈板数量必须大于0");
                }
            });
        }

        List<String> materialCodes = Lists.newArrayList();
        try {
            materialCodes = inboundRequestByPalletInVO.getPalletCodeInfos().stream().map(palletCodeInfo -> {
                return palletCodeInfo.getPalletCode().replace("MES#","")
                        .substring(0, palletCodeInfo.getPalletCode().replace("MES#","").indexOf("#"));
            }).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            throw new CommonException("栈板码格式错误");
        }
        if (materialCodes.size() > 1) {
            throw new CommonException("栈板码中存在不同的产品编号");
        }
        SfacTPO sfacTPO = sfacTService.getByProductNo(inboundRequestByPalletInVO.getPlanTicketNo(), materialCodes.get(0));
        if (!materialCodes.get(0).equals(sfaaTVO.getSfaa010())) {
            if (sfacTPO == null) {
                throw new CommonException("栈板码中产品编号与工程单产品编号不一致:" + materialCodes.get(0) + " : " + sfaaTVO.getSfaa010());
            }
            log.info("可以在sfac表中找到工单号{}和产品编号{}的对应关系", sfacTPO.getSfacdocno(), sfacTPO.getSfac001());
        }


        if(CollectionUtil.isNotEmpty(caseCodes)){
            Set<String> set = new HashSet<>();
            List<String> duplicates = new ArrayList<>();
            for(String caseCodeTem : caseCodes){
                if(!set.add(caseCodeTem)){
                    duplicates.add(caseCodeTem);
                }
            }
            if(CollectionUtil.isNotEmpty(duplicates)){
                throw new CommonException("栈板码中存在相同的数值：" + JSON.toJSONString(duplicates));
            }

            // 这个箱码当做栈板码使用，所以按照箱码规则校验
            List<LsafTPO> sameCaseCodeList = iLsafTService.getByBoxCodes(caseCodes);
            if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
                throw new CommonException("箱码已在erp使用：" + JSON.toJSONString(sameCaseCodeList.stream().map(lsafTPO -> lsafTPO.getLsaf001() + "入库申请单号:"+ lsafTPO.getLsaf009()).collect(Collectors.toList())));
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String destroyBoxCodes(DestoryBoxInVO destoryBoxInVO) {
        if (CollectionUtil.isEmpty(destoryBoxInVO.getBoxCodes())) {
            throw new CommonException("请选择需要消箱的箱码");
        }
        Map<String, String> map = Maps.newHashMap();
        destoryBoxInVO.getBoxCodes().forEach(boxCode -> {
            try {
                String msg = destroyBoxCode(boxCode);
                map.put(boxCode, msg);
            } catch (Exception e) {
                log.error("消箱失败：" + boxCode, e);
                map.put(boxCode, e.getMessage());
            }
        });
        return JSON.toJSONString(map);
    }


    public SaveLsafTDTO setLsafTInfo(BarcodeDetailDTO barcodeDetailDTO, BarcodeDetailDTO.BoxInfo boxInfo,
                                     Date curentDate, IBoxCodeService boxCodeService, String sfac006, String sfaa013, String sfac001) {
        return boxCodeService.setLsafTInfo(barcodeDetailDTO, boxInfo, curentDate,sfac006,sfaa013,sfac001);
    }

//    public BarcodeDetailDTO pushCaseInfoToWms(SaveOutboundInfoInVO info, String planTicketNo, SfacTPO sfacTPO){
//        // 获得工单对应的产品编号
//        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(planTicketNo);
//        log.info("---pushCaseInfoToWms:sfaaTVO={}",JSON.toJSONString(sfaaTVO));
//        BarcodeDetailDTO barcodeDetailDTO = new BarcodeDetailDTO();
//        barcodeDetailDTO.setPlanTicketNo(planTicketNo);
//        barcodeDetailDTO.setBarcode(info.getPalletCode());
//        barcodeDetailDTO.setPalletsQuantity(info.getProducedQuantity());
//        barcodeDetailDTO.setItemNo(sfaaTVO.getSfaa010());
//        barcodeDetailDTO.setUnitNo(sfaaTVO.getSfaa013());
//        barcodeDetailDTO.setWorkCode(SecurityUtil.getWorkcode());
//        barcodeDetailDTO.setUserName(SecurityUtil.getUserName());
//        barcodeDetailDTO.setCompanyCode(SecurityUtil.getCompanySite());
//
//        ImaalTDTO imaalTDTO = new ImaalTDTO();
//        imaalTDTO.setImaal001(sfaaTVO.getSfaa010());
//        List<ImaalTVO> imaalTList = iImaalTService.getImaalTList(imaalTDTO);
//        log.info("---pushCaseInfoToWms:imaalTList={}",JSON.toJSONString(imaalTList));
//        if(CollectionUtil.isNotEmpty(imaalTList)){
//            barcodeDetailDTO.setItemName(imaalTList.get(0).getImaal003());
//            barcodeDetailDTO.setItemSpec(imaalTList.get(0).getImaal004());
//        }
//
//        List<String> boxCodes = Arrays.stream(info.getCaseCode().split(",")).collect(Collectors.toList());
//
//        IBoxCodeService boxCodeService =
//                boxCodeHandlerFactory.getBoxCodeService(planTicketNo);
//        HandleBoxCodeDTO handleBoxCodeDTO = new HandleBoxCodeDTO();
//        handleBoxCodeDTO.setBoxCodes(boxCodes);
//        handleBoxCodeDTO.setInfo(info);
//        handleBoxCodeDTO.setProductionNo(sfaaTVO.getSfaa010());
//
//        List<BarcodeDetailDTO.BoxInfo> boxInfoList = Lists.newArrayList();
//        // 为了兼容旧数据，智物流的码不包含MES的认为是旧的数据，直接通过截取字符串的方式处理
//        String barcodeNo = handleBoxCodeDTO.getBoxCodes().get(0);
//        if (!barcodeNo.contains("MES") && boxCodeService.getCompanyCode().equals(CompanyBoxEnum.COMPANY_ZWL.getCode())) {
//            boxInfoList.addAll(boxCodeService.oldBarcodeNO(handleBoxCodeDTO));
//        } else {
//            boxInfoList.addAll(boxCodeService.handleBoxCode(handleBoxCodeDTO));
//        }
//
//        barcodeDetailDTO.setLotDate(boxInfoList.get(0).getB());
//        barcodeDetailDTO.setLotNo(boxInfoList.get(0).getB());
//        barcodeDetailDTO.setProductCode(sfacTPO.getSfac006());
//        barcodeDetailDTO.setBoxInfos(boxInfoList);
//
//        // 保存仓库位置
//        if (info.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))) {
//            barcodeDetailDTO.setWarehouseNo("X001");
//        } else {
//            barcodeDetailDTO.setWarehouseNo("C001");
//        }
//
//        log.info("---箱码信息推送至WMS:barcodeDetailDTO={}", JSON.toJSONString(barcodeDetailDTO));
//        srmBarcodeDetailService.saveBarcodeDetail(barcodeDetailDTO);
//        return barcodeDetailDTO;
//    }


    private ProductOutboundPO saveProductOutboundPO(SaveOutboundInfoInVO saveOutboundInfoInVO,
        ProductTicketPO productTicketPO) {
        LambdaQueryWrapper<ProductOutboundPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductOutboundPO::getStatus, OutboundEnum.NOT_OUTBOUND.getCode())
            .eq(ProductOutboundPO::getProductTicketId, saveOutboundInfoInVO.getProductTicketId());
        List<ProductOutboundPO> productOutboundPOS = list(lambdaQueryWrapper);
        ProductOutboundPO productOutboundPO = new ProductOutboundPO();
        // 有多条数据，则不正常，应该是结单操作导致的数据异常
        if (productOutboundPOS.size() > 1) {
            throw new CommonException("查询到多条栈板信息");
        }
        // 新增
        productOutboundPO = BeanUtil.copyProperties(saveOutboundInfoInVO, ProductOutboundPO.class);

        productOutboundPO.setCreateBy(SecurityUtil.getUserId());
        productOutboundPO.setStatus(OutboundEnum.OUTBOUND.getCode());
        productOutboundPO.setMachineStopNo(saveOutboundInfoInVO.getMachineStopNo());
        productOutboundPO.setCompanyCode(productTicketPO.getCompanyCode());


        // 设置出站时间
        productOutboundPO.setOutboundTime(new Date());
        saveOrUpdate(productOutboundPO);
        return productOutboundPO;
    }

    private void verify(SaveOutboundInfoInVO saveOutboundInfoInVO) {
        // 栈板码只能使用一次
        ProductPalletPO productPalletPO =
                productLastPalletService.getByPalletCode(saveOutboundInfoInVO.getPalletCode());
        if (productPalletPO != null) {
            throw new CommonException("该栈板码"+ saveOutboundInfoInVO.getPalletCode() +"已使用，请勿重复操作");
        }
    }

//    private void verifyCaseCode(String caseCode,String palletCode){
//        String[] split = caseCode.split(",");
//        List<String> list = Arrays.asList(split);
//        if(CollectionUtil.isNotEmpty(list)){
//            if(list.contains(palletCode)){
//                throw new CommonException("箱码不允许与栈板码一致");
//            }
//
//            Set<String> set = new HashSet<>();
//            List<String> duplicates = new ArrayList<>();
//            for(String caseCodeTem : list){
//                if(!set.add(caseCodeTem)){
//                    duplicates.add(caseCodeTem);
//                }
//            }
//            if(CollectionUtil.isNotEmpty(duplicates)){
//                throw new CommonException("箱码中存在相同的数值：" + JSON.toJSONString(duplicates));
//            }
//
//            // 校验箱码中不能存在不同的批次
//            List<String> errorCaseCodeList = new ArrayList<>();
//            List<String> lotNos = list.stream().map(b -> {
//                String[] split1 = b.split("#");
//                if(split1.length<3){
//                    errorCaseCodeList.add(b);
//                    return "";
//                }
//                return split1[1] + split1[2];
//            }).distinct().collect(Collectors.toList());
//            if(CollectionUtil.isNotEmpty(errorCaseCodeList)){
//                throw new CommonException("箱码格式错误：" + JSON.toJSONString(errorCaseCodeList));
//            }
//
//            if (lotNos.size() > 1) {
//                throw new CommonException("存在不相同批次" + JSON.toJSONString(lotNos));
//            }
//
//
////            List<String> sameCaseCodeList = iProductOutboundBoxService.getSameCaseCode(list);
////            if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
////                throw new CommonException("箱码已使用：" + JSON.toJSONString(sameCaseCodeList));
////            }
//        }
//
//    }

    private ProductPalletPO saveProductPalletPO(SaveOutboundInfoInVO saveOutboundInfoInVO, ProductOutboundPO productOutboundPO) {
        ProductPalletPO productPalletPO = new ProductPalletPO();
        productPalletPO.setMachineName(saveOutboundInfoInVO.getMachineName());
        productPalletPO.setOutboundId(productOutboundPO.getId());
        productPalletPO.setProductTicketId(saveOutboundInfoInVO.getProductTicketId());
        productPalletPO.setPalletCode(saveOutboundInfoInVO.getPalletCode());
        productPalletPO.setProductionTime(new java.util.Date());
        productPalletPO.setProductionNum(saveOutboundInfoInVO.getProducedQuantity());
        productPalletPO.setUnit(saveOutboundInfoInVO.getUnit());
        productPalletPO.setRemainingQuantity(saveOutboundInfoInVO.getProducedQuantity());

        ProductTicketPO productTicketPO = productTicketService.getById(saveOutboundInfoInVO.getProductTicketId());
        productPalletPO.setPalletSource(productTicketPO.getProcess());
        productPalletPO.setProductionOrder(productTicketPO.getPlanTicketNo());
        productPalletPO.setShiftSource(productTicketPO.getShift());
        productPalletPO.setCreateBy(SecurityUtil.getUserId());
        productPalletPO.setStatus(Integer.valueOf(PalletOperateStatusEnum.IN_LIBRARY.getCode()));
        productPalletPO.setMachineStopNo(saveOutboundInfoInVO.getMachineStopNo());
        productPalletPO.setCompanyCode(productTicketPO.getCompanyCode());
        if (StringUtils.isNotBlank(saveOutboundInfoInVO.getPalletShortCode())) {
            productPalletPO.setPalletShortCode(saveOutboundInfoInVO.getPalletShortCode());
        } else {
            String palletShortCode = productLastPalletService.getPalletShortCode(saveOutboundInfoInVO.getProductTicketId());
            productPalletPO.setPalletShortCode(palletShortCode);
        }
        productPalletPO.setPlanTicketNo(productTicketPO.getPlanTicketNo());

        if (StringUtils.isNotBlank(saveOutboundInfoInVO.getIdsPalletId())) {
            productPalletPO.setIdsPalletId(saveOutboundInfoInVO.getPalletCode());
        }
        // 呼市 瓦线 需要把 ids 工单号当做是 工单号
        if (SecurityUtil.getCompanySite().equals(SiteEnum.HSXC.getCode())
            && productTicketPO.getProcessCode().equals("023")
                && StringUtils.isNotBlank(saveOutboundInfoInVO.getIdsWorkOrderNo())) {
            productPalletPO.setProductionOrder(saveOutboundInfoInVO.getIdsWorkOrderNo());
            productPalletPO.setPlanTicketNo(saveOutboundInfoInVO.getIdsWorkOrderNo());
        }

        productLastPalletService.save(productPalletPO);

        return productPalletPO;
    }

    /**
     * 根据栈板码查询出站信息
     */
    @Override
    public ProductOutboundPO getOutboundByPalletCode(String palletCode) {
        LambdaQueryWrapper<ProductOutboundPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductOutboundPO::getPalletCode, palletCode);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public Pagination<OutboundInfoOutVO> getOutboundRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<OutboundInfoOutVO> iPage = baseMapper.getOutboundRecords(productTicketPageInVO.getPage(),
                productTicketPageInVO.getProductTicketId(),SecurityUtil.getCompanySite());
        return getOutboundRecordsPagination(iPage, iPage);
    }

    /**
     * 创建一条栈板信息，但是这条栈板是未出站的
     * @param productTicketId
     * @param machineName
     * @param producedQuantity
     */
    @Override
    public ProductOutboundPO creatrPallet(Long productTicketId, String machineName, BigDecimal producedQuantity) {
        LambdaQueryWrapper<ProductOutboundPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductOutboundPO::getStatus,OutboundEnum.NOT_OUTBOUND.getCode())
                .eq(ProductOutboundPO::getMachineName,machineName);
        ProductOutboundPO productOutboundPO = getOne(lambdaQueryWrapper);
        if (productOutboundPO == null) {
            productOutboundPO = new ProductOutboundPO();
            productOutboundPO.setMachineName(machineName);
            productOutboundPO.setStatus(OutboundEnum.NOT_OUTBOUND.getCode());
            productOutboundPO.setProducedQuantity(producedQuantity);
            productOutboundPO.setCreateBy(SecurityUtil.getUserId());
        } else {
            productOutboundPO.setProducedQuantity(productOutboundPO.getProducedQuantity().add(producedQuantity));
            productOutboundPO.setUpdateBy(SecurityUtil.getUserId());
        }
        // 新增或者更新
        productOutboundPO.setProductTicketId(productTicketId);
        saveOrUpdate(productOutboundPO);
        return productOutboundPO;
    }


    /**
     * 查询栈板信息(未出站的栈板信息)
     * @param productTicketId
     * @return {@link Object}
     */
    @Override
    public OutboundInfoOutVO getPallet(Long productTicketId) {
        LambdaQueryWrapper<ProductOutboundPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductOutboundPO::getProductTicketId,productTicketId)
                .eq(ProductOutboundPO::getStatus,OutboundEnum.NOT_OUTBOUND.getCode());
        List<ProductOutboundPO> productOutboundPOS = list(queryWrapper);
        if (CollectionUtil.isEmpty(productOutboundPOS)) {
            return null;
        } if (productOutboundPOS.size()>1) {
            throw new CommonException("查询到多条栈板信息");
        }
        return BeanUtil.copyProperties(productOutboundPOS.get(0),OutboundInfoOutVO.class);
    }

    @Override
    public Pagination<OutboundInfoOutVO> getOutboundRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<OutboundInfoOutVO> iPage = baseMapper.getOutboundRecordsByTicketBase(page,
                productTicketBaseDTO);
        return getOutboundRecordsPagination(iPage, iPage);
    }

    @Override
    public OutboundInfoOutVO getPalletByMachineName(GetByMachineNameInVO getByMachineNameInVO) {
        LambdaQueryWrapper<ProductOutboundPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductOutboundPO::getMachineName,getByMachineNameInVO.getMachineName())
                .eq(ProductOutboundPO::getStatus,OutboundEnum.NOT_OUTBOUND.getCode());
        List<ProductOutboundPO> productOutboundPOS = list(queryWrapper);
        if (CollectionUtil.isEmpty(productOutboundPOS)) {
            return null;
        } if (productOutboundPOS.size()>1) {
            throw new CommonException("查询到多条栈板信息");
        }
        return BeanUtil.copyProperties(productOutboundPOS.get(0),OutboundInfoOutVO.class);
    }

    /**
     * 查询未出站的数据
     * @param productTicketId
     * @return
     */
    @Override
    public ProductOutboundPO getNoOutByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductOutboundPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductOutboundPO::getProductTicketId,productTicketId)
                .eq(ProductOutboundPO::getPalletCode,null);
        return getOne(queryWrapper);
    }


    private Pagination<OutboundInfoOutVO> getOutboundRecordsPagination(IPage page, Page<OutboundInfoOutVO> iPage) {
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds = Lists.newArrayList();
        Map<Long, String> userMap = Maps.newConcurrentMap();
        userIds.addAll(
                iPage.getRecords().stream().map(OutboundInfoOutVO::getCreateBy).distinct().collect(Collectors.toList()));
        String userIdsStr =
            iPage.getRecords().stream().map(p -> p.getTeamUsers()).filter(StringUtil::isNotBlank).filter(userId -> {
                return !chinesePattern.matcher(userId).find();
            }).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(userIdsStr)) {
            userIds.addAll(Arrays.stream(userIdsStr.split(",")).map(u -> Long.valueOf(u)).collect(Collectors.toList()));
        }
        userMap.putAll(ia01Service.getUserInfoByIds(userIds).stream()
                .collect(Collectors.toMap(UserDTO::getId, UserDTO::getName)));

        return Pagination.newInstance(iPage.getRecords().stream().map(outboundInfoOutVO -> {
            outboundInfoOutVO.setCreaterName(userMap.get(outboundInfoOutVO.getCreateBy()));
            if (StringUtils.isNotBlank(outboundInfoOutVO.getTeamUsers())) {
                if (!chinesePattern.matcher(outboundInfoOutVO.getTeamUsers()).find()) {
                    String userName = Arrays.stream(outboundInfoOutVO.getTeamUsers().split(",")).filter(StringUtil::isNotBlank).map(id -> {
                        return userMap.get(Long.valueOf(id));
                    }).collect(Collectors.joining(","));
                    outboundInfoOutVO.setTeamUsersName(userName);
                }
            }
            return outboundInfoOutVO;
        }).collect(Collectors.toList()), page);
    }

    /**
     * 根据工单获取对应的工序的单位
     * @param productTicketId
     * @return
     */
    @Override
    public String getUnit(Long productTicketId) {
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        // 获得工序的单位
        EcaaucTPO ecaaucTPO =
            ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if (ecaaucTPO != null) {
            return UnitEnum.getUnitEnum(ecaaucTPO.getEcaauc009()).getName();
        }
        throw new CommonException("为找到对应工序的单位");
    }

    /**
     * 根据工单获取对应的工序的单位
     * @param productTicketPO
     * @return
     */
    @Override
    public String getUnitCode(ProductTicketPO productTicketPO) {
        // 获得工序的单位
        EcaaucTPO ecaaucTPO =
                ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if (ecaaucTPO != null) {
            return ecaaucTPO.getEcaauc009();
        }
        throw new CommonException("为找到对应工序的单位");
    }

    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Override
    public SaveOutboundAndDefectiveInfoOutVO saveOutboundAndDefectiveInfo(SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo) {
        log.info("Seata全局事务id=================>{}",RootContext.getXID());
        SaveOutboundAndDefectiveInfoOutVO outVO = new SaveOutboundAndDefectiveInfoOutVO();
        ProductTicketPO productTicketPO = productTicketService.getById(saveOutboundAndDefectiveInfo.getOutboundInfo().getProductTicketId());

        // 栈板码为空的话 需要 生成对应的 栈板码
        if (StringUtils.isBlank(saveOutboundAndDefectiveInfo.getOutboundInfo().getPalletCode())) {
            String palletId = logisticsService.generatePalletId(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
            log.info("erpOrderId:{}",palletId);
            saveOutboundAndDefectiveInfo.getOutboundInfo().setPalletCode(palletId);
        }



        // 不良品
        SaveDefectiveProductsInfoInVO defectiveProductsInfo = saveOutboundAndDefectiveInfo.getDefectiveProductsInfo();
        if (ObjectUtil.isNotNull(defectiveProductsInfo)
                && ObjectUtil.isNotNull(defectiveProductsInfo.getProductTicketId())
                && ObjectUtil.isNotNull(defectiveProductsInfo.getDefectiveProductsQuantity())
                && defectiveProductsInfo.getDefectiveProductsQuantity().compareTo(BigDecimal.ZERO) > 0
        ) {
            if (StringUtils.isBlank(defectiveProductsInfo.getDefectiveProductsReason())
                    || StringUtils.isBlank(defectiveProductsInfo.getDefectiveProductsSource())) {
                throw new CommonException("不良原因和不良现象不能为空");
            }
            outVO.setDefectiveOutVO(productDefectiveProductsService.saveDefectiveProductsInfoNoSeata(defectiveProductsInfo));
        }

        // 出站信息
        SaveOutboundInfoInVO outboundInfo = saveOutboundAndDefectiveInfo.getOutboundInfo();
        if (ObjectUtil.isNotNull(outboundInfo)) {
            String LOCK_KEY = RedisCacheConstant.STORAGE  + outboundInfo.getPalletCode();
            productOutboundService.saveOutboundInfo(LOCK_KEY,outboundInfo);
        }

        return outVO;
    }

    @Override
    public UpdateBarcodeStoreDTO saveWmsMaterialInfo(SaveOutboundInfoInVO outboundInfo,
                                                     ProductTicketPO productTicketPO, String docNo, String itemNo,
                                                     String itemSpec, String storage, String storageName,
                                                     String warehouse, String warehouseName, String lotNo, String productCode, String stockCode) {
        UpdateBarcodeStoreDTO updateBarcodeStoreDTO = new UpdateBarcodeStoreDTO();
        // 基本信息
        updateBarcodeStoreDTO.setEnt("100");
        updateBarcodeStoreDTO.setSite(productTicketPO.getCompanyCode());
        updateBarcodeStoreDTO.setCreateBy(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setCreateDate(new java.util.Date());
        updateBarcodeStoreDTO.setUpdateBy(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setUpdateDate(new java.util.Date());
        updateBarcodeStoreDTO.setDelFlag(BooleanEnum.FALSE.getCode());
        // srm_bar_store_change_body条码库存调整单身档
        // 业务信息
        updateBarcodeStoreDTO.setDocNo(docNo);
        updateBarcodeStoreDTO.setConfirmDate(new java.util.Date());
        updateBarcodeStoreDTO.setConfirmCode("Y");
        updateBarcodeStoreDTO.setCreateOffice("新MES提交，自动审核通过");
        updateBarcodeStoreDTO.setDeptName("新MES");
        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getWorkcode()));
        updateBarcodeStoreDTO.setStatus("Y");
        updateBarcodeStoreDTO.setRemarks("新MES提交");
        // updateBarcodeStoreDTO.setExtension();
        updateBarcodeStoreDTO.setConfirmBy(String.valueOf(SecurityUtil.getUserId()));
        // srm_bar_store_change_head条码库存调整单头档
        updateBarcodeStoreDTO.setSeq(BigDecimal.ONE);
        updateBarcodeStoreDTO.setBarcodeNo(outboundInfo.getCaseCode());

        // 保存仓库位置
        if (outboundInfo.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            updateBarcodeStoreDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "C001");
            updateBarcodeStoreDTO.setWarehouseName(StringUtil.isNotEmpty(warehouseName) ? warehouseName : "成品仓");
        } else {
            if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())) {
                updateBarcodeStoreDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "X002");
            } else {
                updateBarcodeStoreDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "X001");
            }
            updateBarcodeStoreDTO.setWarehouseName(StringUtil.isNotEmpty(warehouseName) ? warehouseName : "线边仓");
        }

        if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())
                && outboundInfo.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                && updateBarcodeStoreDTO.getWarehouseNo().equals("X002")) {
            updateBarcodeStoreDTO.setStorageSpacesNo(StringUtil.isBlank(storage) ? "03": storage);
            updateBarcodeStoreDTO.setWarehouseNoId(StringUtil.isBlank(storage) ? "03": storage);
        } else {
            updateBarcodeStoreDTO.setStorageSpacesNo(StringUtil.isBlank(storage) ? "SC01": storage);
            updateBarcodeStoreDTO.setWarehouseNoId(StringUtil.isBlank(storage) ? "SC01": storage);
        }
        updateBarcodeStoreDTO.setStorageSpacesName(StringUtil.isBlank(storageName) ? "默认储位" : storageName);

        updateBarcodeStoreDTO.setLotNo(StringUtil.isNotBlank(lotNo) ? lotNo :LocalDate.now().format(DatePattern.PURE_DATE_FORMATTER));
        updateBarcodeStoreDTO.setItemNo(itemNo);
        updateBarcodeStoreDTO.setQty(outboundInfo.getProducedQuantity());

        updateBarcodeStoreDTO.setDeptName("新MES");
        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setErpLotNo(StringUtil.isNotBlank(lotNo) ? lotNo :LocalDate.now().format(DatePattern.PURE_DATE_FORMATTER));
        // 日期格式转换
        try {
            DateTime lotNoDate = cn.hutool.core.date.DateUtil.parse(lotNo, "yyyyMMdd");
            String format = cn.hutool.core.date.DateUtil.format(lotNoDate, DatePattern.NORM_DATE_PATTERN);
            updateBarcodeStoreDTO.setLotDate(cn.hutool.core.date.DateUtil.parse(format, DatePattern.NORM_DATE_PATTERN));
        } catch (Exception e) {
            log.error("日期转换异常", e);
        }


        // srm_barcode_change条码异动档
        updateBarcodeStoreDTO.setBadStorage("N");
        updateBarcodeStoreDTO.setPlatform("N");
        try {
            updateBarcodeStoreDTO
                    .setVldDate(cn.jihong.common.util.DateUtil.parseDate("9998-12-31", DatePattern.NORM_DATE_PATTERN));
        } catch (ParseException e) {
            log.error("异常信息", e);
        }

        updateBarcodeStoreDTO.setChagType(1);
        updateBarcodeStoreDTO.setSourceNo(productTicketPO.getPlanTicketNo());
        updateBarcodeStoreDTO.setSourceItemNo(BigDecimal.ONE);
        updateBarcodeStoreDTO.setTargetNo(docNo);
        updateBarcodeStoreDTO.setTargetItemNo(BigDecimal.ONE);
        updateBarcodeStoreDTO.setPdaOpCode(SecurityUtil.getWorkcode());
        updateBarcodeStoreDTO.setUdf01(productTicketPO.getPlanTicketNo());
        try {
            updateBarcodeStoreDTO.setScanDate(cn.jihong.common.util.DateUtil
                    .parseDate(LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER), DatePattern.NORM_DATE_PATTERN));
            updateBarcodeStoreDTO.setGenTime(LocalDateTime.now().format(DatePattern.NORM_TIME_FORMATTER));
        } catch (ParseException e) {
            log.error("异常信息", e);
        }
        updateBarcodeStoreDTO.setIsCharged("Y");
        updateBarcodeStoreDTO.setChagNumUni(outboundInfo.getUnit());
        updateBarcodeStoreDTO.setExchagRate(BigDecimal.ONE);

        updateBarcodeStoreDTO.setBarChgType("1");
        updateBarcodeStoreDTO.setCustomerNo(SecurityUtil.getCompanySite());
        updateBarcodeStoreDTO.setCustomerName(SecurityUtil.getCompanyName());
        updateBarcodeStoreDTO.setStuffCode(itemNo);
        updateBarcodeStoreDTO.setItemName(productTicketPO.getProductName());
        updateBarcodeStoreDTO.setItemSpec(itemSpec);
        updateBarcodeStoreDTO.setIsVldDate("N");
        updateBarcodeStoreDTO.setEffectiveDay(0);
        updateBarcodeStoreDTO.setInNearDay(0);
        updateBarcodeStoreDTO.setInNearControl("1");
        updateBarcodeStoreDTO.setOutNearDay(0);
        updateBarcodeStoreDTO.setOutNearControl("1");
        updateBarcodeStoreDTO.setWarningDay(0);
        updateBarcodeStoreDTO.setEffectiveType("2");
        updateBarcodeStoreDTO.setContainCode(outboundInfo.getPalletCode());
        updateBarcodeStoreDTO.setStatus("Y");
        updateBarcodeStoreDTO.setPostDate(new java.util.Date());
        updateBarcodeStoreDTO.setStockKeep(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setStockCode(stockCode);
        updateBarcodeStoreDTO.setProductCode(productCode);
        // srm_barcode_store条码库存档
        updateBarcodeStoreDTO.setCurStorNum(outboundInfo.getProducedQuantity());

        updateBarcodeStoreDTO.setErpChgNo(docNo);
        updateBarcodeStoreDTO.setErpChgCode("Y");
        updateBarcodeStoreDTO.setAppmodule("B007");
        updateBarcodeStoreDTO.setTimesTamp(String.valueOf(System.currentTimeMillis()));

        return updateBarcodeStoreDTO;
    }

    @Override
    public List<ProductOutboundPO> getByProductTicketIds(List<Long> productTicketIds) {
        if (CollectionUtil.isEmpty(productTicketIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ProductOutboundPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductOutboundPO::getProductTicketId,productTicketIds);
        return list(lambdaQueryWrapper);
    }

    @RedisLock
    @Transactional
    @Override
    public void inboundRequestByBox(String redisLockKey,PushWmsAndErpInVO pushWmsAndErpInVO) {
        
        if (StringUtils.isNotBlank(pushWmsAndErpInVO.getCaseCode())
            && StringUtils.isNotBlank(pushWmsAndErpInVO.getPalletCode())) {
            String palletCode = pushWmsAndErpInVO.getPalletCode();
            String caseCode = pushWmsAndErpInVO.getCaseCode();
            String[] split = caseCode.split(",");
            String boxCode = split[0];
            if (palletCode.length() == boxCode.length()) {
                throw new CommonException("箱码长度和栈板码长度不一致，请检查是否扫码错误了");
            }
        }

        // 校验每个栈板可是放多少箱码
        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(pushWmsAndErpInVO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单号错误："+ pushWmsAndErpInVO.getPlanTicketNo());
        }
        pushWmsAndErpVerify(pushWmsAndErpInVO, sfaaTVO);

        verifyBoxCode(pushWmsAndErpInVO.getCaseCode(),pushWmsAndErpInVO.getPlanTicketNo());


        // 入库申请记录
        ProductStoreDTO productStoreDTO = new ProductStoreDTO();
        productStoreDTO.setPlanTicketNo(pushWmsAndErpInVO.getPlanTicketNo());
        productStoreDTO.setPalletCode(pushWmsAndErpInVO.getPalletCode());
        productStoreDTO.setCaseCode(pushWmsAndErpInVO.getCaseCode());
        productStoreDTO.setProducedQuantity(pushWmsAndErpInVO.getProducedQuantity());
        productStoreDTO.setStoreType(StoreTypeEnum.BY_BOX.getIntCode());
        productStoreDTO.setIsFinalProduct(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productStoreDTO.setUnit(sfaaTVO.getSfaa013());
        productStoreDTO.setReason(pushWmsAndErpInVO.getReason());
        productStoreService.request(productStoreDTO);
    }

    private void pushWmsAndErpVerify(PushWmsAndErpInVO pushWmsAndErpInVO, SfaaTVO sfaaTVO) {
        EcbaTVO ecbaTVO = iEcbaTService.getVOByProductionNo(sfaaTVO.getSfaa010(),SecurityUtil.getCompanySite());
        if (ecbaTVO != null && ecbaTVO.getEcbaua049() != null && ecbaTVO.getEcbaua049() > 0) {
            if (ecbaTVO.getEcbaua049() < pushWmsAndErpInVO.getCaseCode().split(",").length) {
                throw new CommonException("当前工序可放" + ecbaTVO.getEcbaua049() + "箱，不足以放入" + pushWmsAndErpInVO.getCaseCode().split(",").length + "箱");
            }
        }
        // 校验箱码和栈板码
        if(StringUtils.isNotBlank(pushWmsAndErpInVO.getCaseCode())) {
            // 校验箱码和栈板码
            IBoxCodeService boxCodeService =
                    boxCodeHandlerFactory.getBoxCodeService(pushWmsAndErpInVO.getPlanTicketNo());
            boxCodeService.verifyCaseCodeByErp(pushWmsAndErpInVO.getCaseCode(), pushWmsAndErpInVO.getPalletCode());
        }
    }


    @Override
    public String getUnitByPlanTicketNo(String planTicketNo) {
        // 获得工程单对应的出库的单位
        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(planTicketNo);

        if (sfaaTVO != null) {
            return sfaaTVO.getSfaa013();
        }
        return null;
    }

    @Override
    public BigDecimal getBoxSpecs(String planTicketNo) {
        // 获得工程单
        SfaaTVO sfaaTVO = iSfaaTService.getInfoByTicket(planTicketNo);
        if(sfaaTVO != null) {
            XmamTVO xmamTVO = iXmamTService.getXmamTByProductionNo(sfaaTVO.getSfaa010(), SecurityUtil.getCompanySite());

            if (xmamTVO != null) {
                return xmamTVO.getXmam008();
            }
        }
        return null;
    }


    /**
     * 推送wms和erp  --- 测试
     */
    @Override
    public void testA(String timeStamp, String planTicketNo) {
        StorageApplicationToErpInDTO inDTO = new StorageApplicationToErpInDTO();
        inDTO.setKey(UUID.randomUUID().toString().replaceAll("-", ""));
        inDTO.setTimeStamp(timeStamp);
        inDTO.setPlanTicketNo(planTicketNo);
        System.out.println("---storageApplicationToErp:{} ---"+ JSON.toJSONString(inDTO));
        iInventoryToErpService.storageApplicationToErp(inDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @RedisLock
    public String verifyBoxCode(String key,VerifyCaseCodeInVO verifyCaseCodeInVO) {
        IBoxCodeService boxCodeService =
                boxCodeHandlerFactory.getBoxCodeService(verifyCaseCodeInVO.getPlanTicketNo());
        boxCodeService.verifyBoxCode(verifyCaseCodeInVO);
        boxCodeService.activeBoxCode(verifyCaseCodeInVO.getBoxCode());
        return "成功";
    }

    private void verifyBoxCodeAndTicketNo(VerifyCaseCodeInVO verifyCaseCodeInVO, String[] caseCodes) {
        List<SrmBarcodeDetailVO> srmBarcodeDetailVOS =
            srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(Arrays.asList(caseCodes));
        if (CollectionUtil.isNotEmpty(srmBarcodeDetailVOS)) {
            List<String> sourceNos = srmBarcodeDetailVOS.stream().map(SrmBarcodeDetailVO::getSourceNo).distinct()
                .collect(Collectors.toList());
            if (sourceNos.size() > 1 || !sourceNos.contains(verifyCaseCodeInVO.getPlanTicketNo())) {
                throw new CommonException("箱码的工单号和当前工单号不匹配：" + JSON.toJSONString(sourceNos) + "   "
                    + JSON.toJSONString(verifyCaseCodeInVO.getPlanTicketNo()));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOutbound(UpdateOutboundInVO updateOutboundInVO) {
        // 修改出站表
        ProductOutboundPO productOutboundPO = getById(updateOutboundInVO.getOutboundId());
        // 原来的数量
        BigDecimal orgProducedQuantity = productOutboundPO.getProducedQuantity();

        if (productOutboundPO != null) {
            if (StringUtils.isNotBlank(productOutboundPO.getCaseCode())) {
                throw new CommonException("栈板包含箱码，已推送到erp，不能修改");
            }
            productOutboundPO.setProducedQuantity(updateOutboundInVO.getProducedQuantity());
            productOutboundPO.setUpdateBy(SecurityUtil.getUserId());
            productOutboundPO.setUpdateTime(new Date());
            updateById(productOutboundPO);
        }

        // 修改栈板表
        ProductPalletPO productPalletPO = productLastPalletService.getByOutboundId(productOutboundPO.getId());
        if (productPalletPO != null) {
            if (productPalletPO.getRequisitionId() != null) {
                throw new CommonException("栈板已被使用，工单：" + productPalletPO.getRequisitionOrder() + "，不能修改");
            }
            productPalletPO.setProductionNum(updateOutboundInVO.getProducedQuantity());
            productPalletPO.setLoadingQuantity(updateOutboundInVO.getProducedQuantity());
            productPalletPO.setRemainingQuantity(updateOutboundInVO.getProducedQuantity());
            productPalletPO.setUpdateBy(SecurityUtil.getUserId());
            productPalletPO.setUpdateTime(new Date());
            productLastPalletService.updateById(productPalletPO);
        }

        // 修改出站记录表
        ProductPalletOperationRecordsPO productPalletOperationRecordsPOOld =
            productPalletOperationRecordsService.getOutBoundByPalletId(productPalletPO.getId());

        ProductPalletOperationRecordsPO productPalletOperationRecordsPONew1 =
                BeanUtil.copyProperties(productPalletOperationRecordsPOOld, ProductPalletOperationRecordsPO.class);
        productPalletOperationRecordsPONew1.setOriginalQuantity(productPalletOperationRecordsPOOld.getOriginalQuantity().multiply(new BigDecimal(-1)));
        productPalletOperationRecordsPONew1.setRemainingQuantity(productPalletOperationRecordsPOOld.getRemainingQuantity().multiply(new BigDecimal(-1)));
        productPalletOperationRecordsPONew1.setCreateBy(SecurityUtil.getUserId());
        productPalletOperationRecordsPONew1.setUpdateBy(SecurityUtil.getUserId());
        productPalletOperationRecordsPONew1.setUpdateTime(new Date());
        productPalletOperationRecordsPONew1.setId(null);
        productPalletOperationRecordsService.save(productPalletOperationRecordsPONew1);

        ProductPalletOperationRecordsPO productPalletOperationRecordsPONew2 =
            BeanUtil.copyProperties(productPalletOperationRecordsPOOld, ProductPalletOperationRecordsPO.class);
        if (productPalletOperationRecordsPONew2 != null) {
            productPalletOperationRecordsPONew2.setOriginalQuantity(updateOutboundInVO.getProducedQuantity());
            productPalletOperationRecordsPONew2.setRemainingQuantity(updateOutboundInVO.getProducedQuantity());
            productPalletOperationRecordsPONew2.setCreateBy(SecurityUtil.getUserId());
            productPalletOperationRecordsPONew2.setUpdateBy(SecurityUtil.getUserId());
            productPalletOperationRecordsPONew2.setUpdateTime(new Date());
            productPalletOperationRecordsPONew2.setId(null);
            productPalletOperationRecordsService.save(productPalletOperationRecordsPONew2);
        }

        // 修改工单信息表数据
        ProductTicketPO productTicketPO = productTicketService.getById(productOutboundPO.getProductTicketId());
        if (productTicketPO != null) {
            productTicketPO.setRealProduct((productTicketPO.getRealProduct() == null
                    ? BigDecimal.ZERO.add(updateOutboundInVO.getProducedQuantity().subtract(orgProducedQuantity))
                    : productTicketPO.getRealProduct()
                    .add(updateOutboundInVO.getProducedQuantity().subtract(orgProducedQuantity))));
            productTicketPO.setUpdateBy(SecurityUtil.getUserId());
            productTicketPO.setUpdateTime(new Date());
            productTicketService.updateById(productTicketPO);
        }

        // 修改班次数据
        LambdaQueryWrapper<ProductInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper
                .eq(ProductInfoPO::getCompanyCode,productTicketPO.getCompanyCode())
                .eq(ProductInfoPO::getPlanTicketNo,productTicketPO.getPlanTicketNo())
                .eq(ProductInfoPO::getMachineName,productTicketPO.getMachineName())
                .eq(ProductInfoPO::getShift,productTicketPO.getShift())
                .eq(ProductInfoPO::getProduceDate,productTicketPO.getProduceDate());

        ProductInfoPO productInfoPO = productInfoService.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(productInfoPO)) {
            productInfoPO.setRealProductionCapacity(productInfoPO.getRealProductionCapacity().add(updateOutboundInVO.getProducedQuantity().subtract(orgProducedQuantity)));
            productInfoPO.setUpdateBy(SecurityUtil.getUserId());
            productInfoPO.setUpdateTime(new Date());
            productInfoService.updateById(productInfoPO);
        }

        // 修改报工记录
        productMachineTaskService.updateMachineTaskHistory(orgProducedQuantity,updateOutboundInVO,productTicketPO);

    }



    @Override
    public Date updateReportDate() {
        // 有多个地方使用到类似的。注意看其他地方要不要改，按照方法名取搜索
        Date currentDate = new Date();
        OoabTPO stopDate = ooabTService.getStopDate();
        if (stopDate != null) {
            String stopDateStr = stopDate.getOoab002().replace("/", "-");
            DateTime stopDateDate = cn.hutool.core.date.DateUtil.parse(stopDateStr, DatePattern.NORM_DATE_PATTERN);

            // 格式化当前日期为同一格式
            DateTime formattedCurrentDate = cn.hutool.core.date.DateUtil.parse(cn.hutool.core.date.DateUtil.format(currentDate, DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);

            log.info("截止日期为：" + stopDateStr);
            log.info("当前日期为：" + formattedCurrentDate);

            int result = cn.hutool.core.date.DateUtil.compare(stopDateDate, formattedCurrentDate);
            if (result >= 0) {
                log.info("截止日期在今天之后，日期加一天");
                // 截止日期在今天之后，日期加一天，并保持当前时间
                stopDateDate.setField(DateField.HOUR_OF_DAY, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.HOUR_OF_DAY));
                stopDateDate.setField(DateField.MINUTE, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.MINUTE));
                stopDateDate.setField(DateField.SECOND, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.SECOND));
                stopDateDate.setField(DateField.MILLISECOND, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.MILLISECOND));
                return cn.hutool.core.date.DateUtil.offsetDay(stopDateDate, 1);
            }
        }
        return currentDate;
    }


    private void verifyBoxCode(String boxCode,String planTicketNo) {
        IBoxCodeService boxCodeService =
                boxCodeHandlerFactory.getBoxCodeService(planTicketNo);
        VerifyCaseCodeInVO verifyCaseCodeInVO = new VerifyCaseCodeInVO();
        if (boxCode.contains(",")) {
            String[] split = boxCode.split(",");
            boxCode = split[0];
            if (StringUtils.isBlank(boxCode)) {
                throw new CommonException("箱码错误");
            }
        }
        verifyCaseCodeInVO.setBoxCode(boxCode);
        verifyCaseCodeInVO.setPlanTicketNo(planTicketNo);
        boxCodeService.verifyBoxCode(verifyCaseCodeInVO);
    }

//    public static void main(String[] args) {
//        Date currentDate = new Date();
//        OoabTPO stopDate = new OoabTPO();
//        stopDate.setOoab002("2024/07-01");
//        if (stopDate != null) {
//            String stopDateStr = stopDate.getOoab002().replace("/", "-");
//            DateTime stopDateDate = cn.hutool.core.date.DateUtil.parse(stopDateStr, DatePattern.NORM_DATE_PATTERN);
//
//            // 格式化当前日期为同一格式
//            DateTime formattedCurrentDate = cn.hutool.core.date.DateUtil.parse(cn.hutool.core.date.DateUtil.format(currentDate, DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
//
//            log.info("截止日期为：" + stopDateStr);
//            log.info("当前日期为：" + formattedCurrentDate);
//
//            int result = cn.hutool.core.date.DateUtil.compare(stopDateDate, formattedCurrentDate);
//            if (result >= 0) {
//                log.info("截止日期在今天之后，日期加一天");
//                // 截止日期在今天之后，日期加一天，并保持当前时间
//                stopDateDate.setField(DateField.HOUR_OF_DAY, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.HOUR_OF_DAY));
//                stopDateDate.setField(DateField.MINUTE, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.MINUTE));
//                stopDateDate.setField(DateField.SECOND, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.SECOND));
//                stopDateDate.setField(DateField.MILLISECOND, cn.hutool.core.date.DateUtil.date(currentDate).getField(DateField.MILLISECOND));
//                System.out.println(DateUtil.parseDateToStringCustom(cn.hutool.core.date.DateUtil.offsetDay(stopDateDate, 1),"yyyyMMddHHmmssSSS"));
//            }
//        }
//        System.out.println(DateUtil.parseDateToStringCustom(currentDate,"yyyyMMddHHmmssSSS"));
//    }



}
