package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.LogisticsProductConfigDTO;
import cn.jihong.mes.production.api.model.enums.WorkShopEnum;
import cn.jihong.mes.production.api.model.po.LogisticsProductConfigPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.SysDictQueryInVO;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mes.production.api.service.ILogisticsProductConfigService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.api.service.ISysDictService;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.config.JinshanLogisticsConfig;
import cn.jihong.mes.production.app.mapper.LogisticsProductConfigMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.portrait.CorporationGroupOutVO;
import cn.jihong.oa.erp.api.service.IImaalTService;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import cn.jihong.oa.erp.api.service.IPmaalTService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流产品配置信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@DubboService
public class LogisticsProductConfigServiceImpl extends JiHongServiceImpl<LogisticsProductConfigMapper, LogisticsProductConfigPO> implements ILogisticsProductConfigService {

    @DubboReference
    private ISfaaTService sfaaTService;
    @Resource
    private ISysDictService sysDictService;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private ILogisticsService logisticsService;
    @DubboReference
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;
    @DubboReference
    private IOocqlTService iOocqlTService;
    @Resource
    private JinshanLogisticsConfig jinshanLogisticsConfig;
    @DubboReference
    private IPmaalTService pmaalTService;
    @DubboReference
    private IImaalTService imaalTService;

    @Override
    public Pagination<LogisticsProductConfigDTO> getList(LogisticsProductConfigDTO logisticsProductConfigDTO) {
        return null;
    }

    @Override
    public LogisticsProductConfigDTO getById(Long id) {
        LambdaQueryWrapper<LogisticsProductConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsProductConfigPO::getId, id);
        LogisticsProductConfigPO po = getOne(queryWrapper);
        if (po != null) {
            LogisticsProductConfigDTO dto = new LogisticsProductConfigDTO();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }
        return null;
    }

    @Override
    public LogisticsProductConfigDTO getLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO) {
        LambdaQueryWrapper<LogisticsProductConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsProductConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
            .eq(LogisticsProductConfigPO::getPlanTicketNo, logisticsProductConfigDTO.getPlanTicketNo())
            .eq(LogisticsProductConfigPO::getMachineName, logisticsProductConfigDTO.getMachineName())
            .eq(LogisticsProductConfigPO::getProcessCode, logisticsProductConfigDTO.getProcessCode())
            .eq(logisticsProductConfigDTO.getId() != null, LogisticsProductConfigPO::getId,
                logisticsProductConfigDTO.getId())
            .like(StringUtil.isNotBlank(logisticsProductConfigDTO.getProductName()),
                LogisticsProductConfigPO::getProductName, logisticsProductConfigDTO.getProductName());
        LogisticsProductConfigPO po = getOne(queryWrapper);
        if (po != null) {
            LogisticsProductConfigDTO dto = new LogisticsProductConfigDTO();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }
        // 没有则需要去erp封装数据
        LogisticsProductConfigDTO dto = new LogisticsProductConfigDTO();
        dto.setCompanyCode(SecurityUtil.getCompanySite());
        dto.setMachineName(logisticsProductConfigDTO.getMachineName());
        dto.setPlanTicketNo(logisticsProductConfigDTO.getPlanTicketNo());
        // 查询纸板长度
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(logisticsProductConfigDTO.getPlanTicketNo());
        dto.setStackLength(sfaaTVO.getSfaaud012());
        dto.setStackWidth(sfaaTVO.getSfaaud013());

        ImaalTVO imaalTVO = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTVO != null) {
            dto.setCustomerShortName(imaalTVO.getImaal003());
        }

        dto.setProductShortName(sfaaTVO.getSfaaud012() + "*(" + sfaaTVO.getSfaaud013() + ")");
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO) {
        ProductTicketPO productTicketPO = productTicketService.getById(logisticsProductConfigDTO.getProductTicketId());
        LambdaQueryWrapper<LogisticsProductConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsProductConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(LogisticsProductConfigPO::getPlanTicketNo, logisticsProductConfigDTO.getPlanTicketNo())
                .eq(LogisticsProductConfigPO::getMachineName, logisticsProductConfigDTO.getMachineName())
                .eq(LogisticsProductConfigPO::getProcessCode, logisticsProductConfigDTO.getProcessCode())
                .eq(logisticsProductConfigDTO.getId() != null, LogisticsProductConfigPO::getId,
                        logisticsProductConfigDTO.getId())
                .like(StringUtil.isNotBlank(logisticsProductConfigDTO.getProductName()),
                        LogisticsProductConfigPO::getProductName, logisticsProductConfigDTO.getProductName());
        LogisticsProductConfigPO po = getOne(queryWrapper);
        if (po != null) {
            Long id = po.getId();
            BeanUtils.copyProperties(logisticsProductConfigDTO, po);
            po.setCompanyCode(SecurityUtil.getCompanySite());
            po.setId(id);
            updateById(po);
            return;
        }
        po = new LogisticsProductConfigPO();
        BeanUtils.copyProperties(logisticsProductConfigDTO, po);
        Integer planCount = productTicketPO.getPlannedProduct().compareTo(BigDecimal.ZERO) == 0 ? 1 : productTicketPO.getPlannedProduct().intValue();
        po.setPlanCount(planCount);
        po.setCompanyCode(SecurityUtil.getCompanySite());
        save(po);



        // 调用 修改物流信息接口
        log.info("修改物流信息");
        logisticsService.updatePalletAutoCreator(po,productTicketPO.getPlannedProduct());
        // 开启自动出站
        log.info("开启自动出站");
        logisticsService.startPalletAutoCreator(logisticsProductConfigDTO.getMachineName(),
                logisticsProductConfigDTO.getProcessCode());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateLogisticsProductConfig(LogisticsProductConfigDTO logisticsProductConfigDTO) {
        log.info("修改物流信息");
        ProductTicketPO productTicketPO = productTicketService.getById(logisticsProductConfigDTO.getProductTicketId());
        LogisticsProductConfigPO po = new LogisticsProductConfigPO();
        BeanUtils.copyProperties(logisticsProductConfigDTO, po);
        Integer planCount = productTicketPO.getPlannedProduct().compareTo(BigDecimal.ZERO) == 0 ? 1 : productTicketPO.getPlannedProduct().intValue();
        po.setPlanCount(planCount);
        po.setCompanyCode(SecurityUtil.getCompanySite());
        updateById(po);

        logisticsService.updatePalletAutoCreator(po,productTicketPO.getPlannedProduct());
        log.info("开启自动出站");
        // 开启自动出站
        logisticsService.startPalletAutoCreator(logisticsProductConfigDTO.getMachineName(),
                logisticsProductConfigDTO.getProcessCode());
    }

    @Override
    public void delete(Long id) {
        removeById(id);
    }

    @Override
    public List<EnumDTO> getMaterialCategory(Long id) {
        // 其他据点不处理
        if (!jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())) {
            return Lists.newArrayList();
        }
        ProductTicketPO productTicketPO = productTicketService.getById(id);
        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService
            .getMachineConfig(productTicketPO.getMachineName(), productTicketPO.getProcessCode());

        if (machineConfig == null) {
            return Lists.newArrayList();
        }
        SysDictQueryInVO sysDictQueryInVO = new SysDictQueryInVO();
        if (StringUtil.equals(machineConfig.getWorkshopCode(), WorkShopEnum.HUSHI_WATERMARK.getCode())) {
            sysDictQueryInVO.setDictType("S_PAPERBOARD_TYPE");
        } else if (StringUtil.equals(machineConfig.getWorkshopCode(), WorkShopEnum.HUSHI_OFFSET_PRINT.getCode())) {
            sysDictQueryInVO.setDictType("J_PAPERBOARD_TYPE");
        } else {
           throw new RuntimeException("车间没有对应的物料类别");
        }
        sysDictQueryInVO.setPageNum(1);
        sysDictQueryInVO.setPageSize(9999);
        Pagination<SysDictOutVO> page = sysDictService.page(sysDictQueryInVO);
        Collection<SysDictOutVO> data = page.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            List<EnumDTO> list = data.stream().map(item -> {
                EnumDTO dto = new EnumDTO();
                dto.setCode(item.getDictCode());
                dto.setName(item.getDictLabel());
                return dto;
            }).collect(Collectors.toList());
            return list;
        }
        return null;
    }

    @Override
    public List<EnumDTO> getProcessType() {
        List<CorporationGroupOutVO> corporationGroupOutVOS = iOocqlTService.getOocqlTByType("221", null);
        if (CollectionUtils.isNotEmpty(corporationGroupOutVOS)) {
            return corporationGroupOutVOS.stream().map(item -> {
                EnumDTO enumDTO = new EnumDTO();
                enumDTO.setCode(item.getOocql002());
                enumDTO.setName(item.getOocql004());
                return enumDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }
}