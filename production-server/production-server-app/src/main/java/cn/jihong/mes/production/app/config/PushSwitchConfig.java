package cn.jihong.mes.production.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 推送开关
 * <AUTHOR>
 * @date 2023/12/29 13:42
 */
@Data
@Component
@ConfigurationProperties(prefix = "push.switch")
@RefreshScope
public class PushSwitchConfig {

    /**
     * 是否给erp推送报工
     */
    private String erpReport;

    /**
     * 是否给erp推送更新物料
     */
    private String erpUpdateMaterial;

    /**
     * 是否给wms推送箱码信息
     */
    private String wmsUpdateCaseCode;

    /**
     * 是否出站校验
     */
    private String outBoundverify;

    /**
     * 是否报工校验
     */
    private String reportVerify;



    /**
     * 测试据点
     */
    private List<String> companyCodes;

}
