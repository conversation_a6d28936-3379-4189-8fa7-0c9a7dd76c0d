package cn.jihong.mes.production.app.mapper;

import cn.jihong.common.util.CollectionUtil;
import cn.jihong.mes.production.api.model.po.WorkflowRequestAttachmentFilePO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface WorkflowRequestAttachmentFileMapper extends JiHongMapper<WorkflowRequestAttachmentFilePO> {

    default WorkflowRequestAttachmentFilePO getWorkflowRequestAttachmentPO(Long fileId) {
        LambdaQueryWrapper<WorkflowRequestAttachmentFilePO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WorkflowRequestAttachmentFilePO :: getFileId, fileId);
        List<WorkflowRequestAttachmentFilePO> poList = selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(poList) ? poList.get(0) : null;
    }
}
