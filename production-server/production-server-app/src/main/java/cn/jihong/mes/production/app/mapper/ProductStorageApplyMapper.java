package cn.jihong.mes.production.app.mapper;


import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.po.ProductStorageApplyPO;
import cn.jihong.mes.production.api.model.vo.in.GetStorageApplyInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

/**
 * 入库申请表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
public interface ProductStorageApplyMapper extends JiHongMapper<ProductStorageApplyPO> {

    Page<GetStorageApplyInfoOutVO> getStorageApplyInfo(@Param("page") IPage page,
                                                       @Param("getStorageApplyInfoInVO") GetStorageApplyInfoInVO getStorageApplyInfoInVO);

    String getPlanTicketNoByPalletCode(String palletCode);
}
