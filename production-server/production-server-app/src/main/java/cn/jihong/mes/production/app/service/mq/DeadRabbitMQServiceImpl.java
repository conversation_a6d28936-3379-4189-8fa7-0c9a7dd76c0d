package cn.jihong.mes.production.app.service.mq;

import cn.jihong.common.exception.CommonException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.jihong.mes.production.api.model.constant.RabbitConsts;
import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.service.IRabbitMQService;

@Service
public class DeadRabbitMQServiceImpl implements IRabbitMQService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public String getRabbitMQType() {
        return RabbitConsts.DEAD;
    }

    @Override
    public void sendMessage(MessageStructDTO msg) {
        throw new CommonException("需要设置ttl时间");
    }

    @Override
    public void sendMessage(MessageStructDTO msg,String ttlTime) {
        // 发送到普通交换机,并设置过期时间
        rabbitTemplate.convertAndSend(RabbitConsts.DEAD_MODE_EXCHANGE_A, RabbitConsts.DEAD_ROUTING_KEY_A, msg,message -> {
            /**生产者设置过期时间*/
            message.getMessageProperties().setExpiration(ttlTime);
            return message;
        });
    }


}
