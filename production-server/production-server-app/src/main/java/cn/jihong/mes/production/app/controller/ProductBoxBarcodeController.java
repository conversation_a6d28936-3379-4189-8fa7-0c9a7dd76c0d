package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeOutVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 *  前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@RestController
@RequestMapping("/productBoxBarcode")
@ShenyuSpringMvcClient(path = "/productBoxBarcode/**")
public class ProductBoxBarcodeController {

    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;

    /**
     * 查询条码段列表
     * @param getProductBoxBarcodeInVO
     * @return
     */
    @PostMapping("/getList")
    public StandardResult<Pagination<GetProductBoxBarcodeOutVO>> getProductBoxBarcodeList(@RequestBody @Valid GetProductBoxBarcodeInVO getProductBoxBarcodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeService.getProductBoxBarcodeList(getProductBoxBarcodeInVO));
    }


    /**
     * 启用/禁用条码
     * @param
     * @return
     */
    @GetMapping("/enableBoxBarcode/{id}")
    public StandardResult enableBoxBarcode(@PathVariable("id") Integer id) {
        productBoxBarcodeService.enableBoxBarcode(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


    /**
     * 增加条码
     * @param
     * @return
     */
    @PostMapping("/addBoxBarcode")
    public StandardResult addBoxBarcode(@RequestBody @Valid AddProductBoxBarcodeInVO addProductBoxBarcodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeService.addBoxBarcode(addProductBoxBarcodeInVO));
    }

    /**
     * 查询条码段明细
     * @param id
     * @return
     */
    @GetMapping("/getBoxBarcode/{id}")
    public StandardResult<GetProductBoxBarcodeOutVO> getBoxBarcode(@PathVariable("id") Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeService.getBoxBarcode(id));
    }


    /**
     * 查询条码段明细  默认启用的那一条
     * @return
     */
    @GetMapping("/getEnableBoxBarcode/{customerNo}")
    public StandardResult<GetProductBoxBarcodeOutVO> getEnableBoxBarcode(@PathVariable String customerNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeService.getEnableBoxBarcode(customerNo));
    }

}

