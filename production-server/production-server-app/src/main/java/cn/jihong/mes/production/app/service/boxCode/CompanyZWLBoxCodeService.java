package cn.jihong.mes.production.app.service.boxCode;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeRecordDTO;
import cn.jihong.mes.production.api.model.enums.BarcodeOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeDetailInVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailDetailService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeRecordService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
import cn.jihong.oa.erp.api.model.dto.ImafTDTO;
import cn.jihong.oa.erp.api.service.IImafTService;
import cn.jihong.oa.erp.api.service.IXmamTService;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智物流处理箱码
 */
@Slf4j
@Service
public class CompanyZWLBoxCodeService extends BoxCodeHelper {

    private static final String BOX_CODE_KEY = "mes:box:code:";
    private static final String BOX_CODE_LOCK_KEY = "mes:box:code:locked";

    @DubboReference(timeout = 180000,retries = 0)
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;
    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeRecordService productBoxBarcodeRecordService;
    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;
    @Resource
    private RedisTemplate redisTemplate;

    @DubboReference
    private IXmamTService iXmamTService;
    @DubboReference
    private IImafTService imafTService;

    @Override
    public String getCompanyCode() {
        return CompanyBoxEnum.COMPANY_ZWL.getCode();
    }


//    @Override
//    public List<BarcodeDetailDTO.BoxInfo> handleBoxCode(HandleBoxCodeDTO handleBoxCodeDTO) {
//        List<BarcodeDetailDTO.BoxInfo> boxInfos = Lists.newArrayList();
//        XmamTVO xmamTVO = iXmamTService.getXmamTByProductionNo(handleBoxCodeDTO.getProductionNo(), SecurityUtil.getCompanySite());
//        log.info("---pushCaseInfoToWms:xmamTVO={}", JSON.toJSONString(xmamTVO));
//
//        for(String boxCode : handleBoxCodeDTO.getBoxCodes()){
//            BarcodeDetailDTO.BoxInfo boxInfo = new BarcodeDetailDTO.BoxInfo();
//            boxInfo.setBoxCode(boxCode);
//            if (xmamTVO == null) {
//                BigDecimal divide = handleBoxCodeDTO.getInfo().getProducedQuantity()
//                        .divide(BigDecimal.valueOf(handleBoxCodeDTO.getInfo().getCaseCode().split(",").length), 2, RoundingMode.HALF_UP);
//                boxInfo.setBoxQuantity(divide);
//            } else {
//                boxInfo.setBoxQuantity(xmamTVO.getXmam008());
//            }
//
//            String[] split = boxInfo.getBoxCode().split("#");
//
//            // 料号
//            boxInfo.setA(split[0]);
//            // 生产日期
//            boxInfo.setB(split[1]);
//            // 箱数量
//            boxInfo.setC(String.valueOf(boxInfo.getBoxQuantity()));
//            // 批次识别码
//            boxInfo.setJ(split[2]);
//            try {
//                // 机台
//                boxInfo.setK(split[3]);
//                // 班次
//                boxInfo.setL(split[4]);
//                // 保质期 + 2 年
//                boxInfo.setM(split[5]);
//                // 流水号
//                boxInfo.setX(split[6]);
//            } catch (Exception e) {
//                log.error(boxInfo.getBoxCode() + "箱码格式太短");
//            }
//
//            boxInfos.add(boxInfo);
//        }
//        return boxInfos;
//    }




    @Override
    public Long applyBoxBarcodeDetail(AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO) {
        ImafTDTO imafTDTO = imafTService.getByProductNo(addProductBoxBarcodeDetailInVO.getProductNo());
//        料号#生产日期#批次识别码#机台#班次#保质期(生产日期加两年)#流水号
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("MES");
        stringBuffer.append("#");
        stringBuffer.append(addProductBoxBarcodeDetailInVO.getProductNo());
        stringBuffer.append("#");
        stringBuffer.append(DateUtil.format(addProductBoxBarcodeDetailInVO.getProductionDate(),
                DatePattern.PURE_DATE_PATTERN));
        stringBuffer.append("#");
        stringBuffer.append(addProductBoxBarcodeDetailInVO.getBatchCode());
        stringBuffer.append("#");
        stringBuffer.append(addProductBoxBarcodeDetailInVO.getMachine());
        stringBuffer.append("#");
        stringBuffer.append(addProductBoxBarcodeDetailInVO.getShift());
        stringBuffer.append("#");
        DateTime last2Year = null;
        if (imafTDTO != null && (imafTDTO.getImaf031().compareTo(BigDecimal.ZERO) > 0
                || imafTDTO.getImaf032().compareTo(BigDecimal.ZERO) > 0)) {
            // 过期时间
            last2Year =
                    DateUtil
                            .offset(addProductBoxBarcodeDetailInVO.getProductionDate(), DateField.MONTH,
                                    imafTDTO.getImaf031().intValue())
                            .offset(DateField.DAY_OF_MONTH, imafTDTO.getImaf032().intValue());
        } else {
            throw new CommonException("根据工单查询不到的料件过期时间信息，请联系相关人员维护后再次申请");
            // 过期时间默认加2年
//            last2Year = DateUtil.offset(addProductBoxBarcodeDetailInVO.getProductionDate(), DateField.YEAR, 2);
        }
        String last2YearStr = DateUtil.format(last2Year, DatePattern.PURE_DATE_PATTERN);
        stringBuffer.append(last2YearStr);
        stringBuffer.append("#");
        // 标准号
        String standardCode = stringBuffer.toString();

        Long orderSequence = getAndIncrementOrderSequence(standardCode,null);
        String start = String.format("%06d", orderSequence);
        String startCode =  standardCode + start;

        Long orderSequenceEnd = getAndIncrementOrderSequence(standardCode,orderSequence + addProductBoxBarcodeDetailInVO.getApplyBarcodeCount() -1);
        String end = String.format("%06d", orderSequenceEnd );
        String endCode = standardCode + end;


        ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO = new ProductBoxBarcodeDetailPO();
        productBoxBarcodeDetailPO.setCompanyCode(SecurityUtil.getCompanySite());
        productBoxBarcodeDetailPO.setProductBarcodeId(addProductBoxBarcodeDetailInVO.getProductBarcodeId());
        productBoxBarcodeDetailPO.setBarcodeNoStart(startCode);
        productBoxBarcodeDetailPO.setBarcodeNoEnd(endCode);
        productBoxBarcodeDetailPO.setBarcodeTotal(addProductBoxBarcodeDetailInVO.getApplyBarcodeCount());
        productBoxBarcodeDetailPO.setBarcodeRemaining(null);
        productBoxBarcodeDetailPO.setCustomerNo(addProductBoxBarcodeDetailInVO.getCustomerNo());

        productBoxBarcodeDetailPO.setPlanTicketNo(addProductBoxBarcodeDetailInVO.getPlanTicketNo());
        // 根据工单号查询工单信息
        productBoxBarcodeDetailPO.setProductName(addProductBoxBarcodeDetailInVO.getProductName());
        productBoxBarcodeDetailPO.setMaterialCode(addProductBoxBarcodeDetailInVO.getProductNo());
        productBoxBarcodeDetailPO.setBoxNum(addProductBoxBarcodeDetailInVO.getBoxSpece());

        // 班次信息
        productBoxBarcodeDetailPO.setProductionDate(addProductBoxBarcodeDetailInVO.getProductionDate());
        productBoxBarcodeDetailPO.setLotNo(DateUtil.format(addProductBoxBarcodeDetailInVO.getProductionDate(),
                DatePattern.PURE_DATE_PATTERN));
        productBoxBarcodeDetailPO.setBatchCode(addProductBoxBarcodeDetailInVO.getBatchCode());
        productBoxBarcodeDetailPO.setMachine(addProductBoxBarcodeDetailInVO.getMachine());
        productBoxBarcodeDetailPO.setShift(addProductBoxBarcodeDetailInVO.getShift());
        // 过期时间
        productBoxBarcodeDetailPO.setExpirationDate(last2YearStr);
        productBoxBarcodeDetailPO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeDetailPO.setUpdateBy(SecurityUtil.getUserId());

        productBoxBarcodeDetailService.save(productBoxBarcodeDetailPO);

        // 保存条码详情
        saveBoxBarcodeDetailDetail(productBoxBarcodeDetailPO);

        return productBoxBarcodeDetailPO.getId();
    }

    private void saveBoxBarcodeDetailDetail(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO) {
        // 获取条码列表
        List<String> barcodeList = Lists.newArrayList();
        String barcodeNoStart = productBoxBarcodeDetailPO.getBarcodeNoStart();
        BigInteger startHex = BigInteger.valueOf(Long.valueOf(barcodeNoStart.substring(barcodeNoStart.length() - 6)));
        for (int i = 1; i <= productBoxBarcodeDetailPO.getBarcodeTotal(); i++) {
            String barcode = String.format("%06d", startHex);
            barcode = barcodeNoStart.substring(0, barcodeNoStart.length() - 6) + barcode;
            barcodeList.add(barcode);
            startHex = startHex.add(BigInteger.ONE);
        }
        log.info("打印条码列表：{}", barcodeList);

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByBarcodeNos(barcodeList);
        Set<String> barcodeSet = productBoxBarcodeDetailDetailPOS.stream().map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toSet());

        // 保存条码详情
        List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOS = barcodeList.stream().map(barcode -> {
            if (barcodeSet.contains(barcode)) {
                return null;
            }
            ProductBoxBarcodeDetailDetailDTO productBoxBarcodeDetailDetailDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeDetailDetailDTO.class);
            productBoxBarcodeDetailDetailDTO.setId(null);
            productBoxBarcodeDetailDetailDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
            productBoxBarcodeDetailDetailDTO.setBarcodeNo(barcode);
            return productBoxBarcodeDetailDetailDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        productBoxBarcodeDetailDetailService.saveProductBoxBarcodeDetailDetailPOs(productBoxBarcodeDetailDetailDTOS);

        // 更新到wms
        createbarcodeToWms(productBoxBarcodeDetailPO, productBoxBarcodeDetailDetailDTOS);
    }

    @Override
    public List<String> getBarcodeList(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO, BarcodeOperateTypeEnum barcodeOperateTypeEnum) {
        // 更新状态
        productBoxBarcodeDetailPO.setPrintStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productBoxBarcodeDetailService.updateById(productBoxBarcodeDetailPO);

        // 保存条码记录
        ProductBoxBarcodeRecordDTO productBoxBarcodeRecordDTO = BeanUtil.copyProperties(productBoxBarcodeDetailPO, ProductBoxBarcodeRecordDTO.class);
        productBoxBarcodeRecordDTO.setOperationType(barcodeOperateTypeEnum.getCode());
        productBoxBarcodeRecordDTO.setProductBarcodeDetailId(productBoxBarcodeDetailPO.getId());
        productBoxBarcodeRecordDTO.setCreateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordDTO.setUpdateBy(SecurityUtil.getUserId());
        productBoxBarcodeRecordService.saveProductBoxBarcodeRecord(productBoxBarcodeRecordDTO);

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = productBoxBarcodeDetailDetailService.getByDetailId(productBoxBarcodeDetailPO.getId());
        if (CollectionUtil.isEmpty(productBoxBarcodeDetailDetailPOS)) {
            throw new CommonException("找不到对应条码信息" + productBoxBarcodeDetailPO.getId());
        }
        return productBoxBarcodeDetailDetailPOS.stream().map(ProductBoxBarcodeDetailDetailPO::getBarcodeNo).collect(Collectors.toList());
    }


    private Long getAndIncrementOrderSequence(String standardCode,Long endNum) {
        standardCode = BOX_CODE_KEY + standardCode;
        // 使用Redis加锁
        boolean lockAcquired = acquireLock();
        if (!lockAcquired) {
            throw new CommonException("系统繁忙，请稍后再试");
        }

        try {
            // 获取当前号递增数
            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();

            // 更新订单号递增数到Redis
            if (endNum != null) {
                valueOps.set(standardCode, endNum.toString());
                return endNum;
            } else {
                String orderSequenceStr = valueOps.get(standardCode);
                Long orderSequence = (orderSequenceStr != null) ? Long.parseLong(orderSequenceStr) : 0;

                // 递增订单号递增数
                orderSequence++;
                if (orderSequence>=1000000) {
                    orderSequence = 1L;
                }
                valueOps.set(standardCode, orderSequence.toString());
                return orderSequence;
            }
        } finally {
            // 释放锁
            releaseLock();
        }
    }

    private boolean acquireLock() {
        // 使用Redis的SET命令实现分布式锁，并设置过期时间
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(BOX_CODE_LOCK_KEY, "locked", 10, TimeUnit.SECONDS);
        return lockAcquired != null && lockAcquired;
    }

    private void releaseLock() {
        // 释放锁
        redisTemplate.delete(BOX_CODE_LOCK_KEY);
    }

}