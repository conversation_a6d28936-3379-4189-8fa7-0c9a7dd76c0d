package cn.jihong.mes.production.app.mapper;


import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductOutboundPO;
import cn.jihong.mes.production.api.model.vo.out.OutboundInfoOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 出站信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface ProductOutboundMapper extends JiHongMapper<ProductOutboundPO> {

    Page<OutboundInfoOutVO> getOutboundRecords(IPage page, @Param("productTicketId") Long productTicketId, @Param("companyCode")String companyCode);

    Page<OutboundInfoOutVO> getOutboundRecordsByTicketBase(IPage page,@Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);
}
