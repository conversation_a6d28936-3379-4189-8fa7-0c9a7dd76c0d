package cn.jihong.mes.production.app.controller.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.logistics.api.model.vo.in.OutboundStackerPageInVO;

import cn.jihong.logistics.api.model.vo.out.OutboundStackerOutVO;
import cn.jihong.logistics.api.service.IOutboundStackerService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 出站堆码机
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@RestController
@RequestMapping("/outboundStacker")
@ShenyuSpringMvcClient(path = "/outboundStacker/**")
public class OutboundStackerController {

    @DubboReference
    private IOutboundStackerService service;

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.OutboundStackerOutVO>
     */
    @GetMapping("/getSingleOutboundStackerById/{id}")
    public StandardResult<OutboundStackerOutVO> getSingleOutboundStackerById(@PathVariable("id") Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getSingleOutboundStackerById(id));
    }

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.OutboundStackerOutVO>>
     */
    @PostMapping("/getOutboundStackerPage")
    public StandardResult<Pagination<OutboundStackerOutVO>> getOutboundStackerPage(@RequestBody @Valid OutboundStackerPageInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getOutboundStackerPage(inVO));
    }


}

