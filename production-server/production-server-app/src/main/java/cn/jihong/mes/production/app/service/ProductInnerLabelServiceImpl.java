package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.dto.TemplateInfoDTO;
import cn.jihong.mes.production.api.model.po.ProductInnerLabelPO;
import cn.jihong.mes.production.api.model.vo.in.ApplyProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetTemplateInfosInVO;
import cn.jihong.mes.production.api.model.vo.out.ApplyProductInnerLabelOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetErpTicketInfoOutVO;
import cn.jihong.mes.production.api.service.IProductInnerLabelService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductInnerLabelMapper;
import cn.jihong.mes.production.app.service.boxCode.BoxCodeHandlerFactory;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.dto.ImafTDTO;
import cn.jihong.oa.erp.api.model.dto.LstaucTDTO;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.model.po.PmaalTPO;
import cn.jihong.oa.erp.api.model.po.PmaoTPO;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.in.TemplateInfoPageInVO;
import cn.jihong.oa.erp.api.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 生产内标签表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@DubboService
public class ProductInnerLabelServiceImpl extends JiHongServiceImpl<ProductInnerLabelMapper, ProductInnerLabelPO> implements IProductInnerLabelService {

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;

    @DubboReference
    private ILstaucTService lstaucTService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;
    @DubboReference
    private IImafTService imafTService;
    @DubboReference
    private IPmaalTService pmaalTService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IImaalTService imaalTService;
    @DubboReference
    private IPmaoTService pmaoTService;


    @RedisLock
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyProductInnerLabel(String lock_key, ApplyProductInnerLabelInVO applyProductInnerLabelInVO) {
        ProductInnerLabelPO productInnerLabelPO = BeanUtil.copyProperties(applyProductInnerLabelInVO, ProductInnerLabelPO.class);
        productInnerLabelPO.setCompanyCode(SecurityUtil.getCompanySite());
        productInnerLabelPO.setCreateBy(SecurityUtil.getUserId());
        productInnerLabelPO.setUpdateBy(SecurityUtil.getUserId());
        productInnerLabelPO.setCreateTime(new Date());
        productInnerLabelPO.setUpdateTime(new Date());

//        // 系统时间减去8小时 转成YYYYMMDD 结束时间默认加两年
//        DateTime start = DateUtil.offset(new Date(), DateField.HOUR, -8);
//        DateTime endTime = DateUtil.offset(start, DateField.YEAR, 2);
//        productInnerLabelPO.setProductionDate(start.toString("yyyyMMdd"));
//        productInnerLabelPO.setExpirationDate(endTime.toString("yyyyMMdd"));


        save(productInnerLabelPO);
    }

    @Override
    public Pagination<ApplyProductInnerLabelOutVO> getList(GetProductInnerLabelInVO getProductInnerLabelInVO) {
        LambdaQueryWrapper<ProductInnerLabelPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ProductInnerLabelPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtil.isNotBlank(getProductInnerLabelInVO.getPlanTicketNo()), ProductInnerLabelPO::getPlanTicketNo, getProductInnerLabelInVO.getPlanTicketNo())
                .eq(StringUtil.isNotBlank(getProductInnerLabelInVO.getProductName()), ProductInnerLabelPO::getProductName, getProductInnerLabelInVO.getProductName())
                .orderByDesc(ProductInnerLabelPO::getId);
        IPage page = page(getProductInnerLabelInVO.getPage(), queryWrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return null;
        }
        List<ApplyProductInnerLabelOutVO> applyProductInnerLabelOutVOS = BeanUtil.copyToList(page.getRecords(), ApplyProductInnerLabelOutVO.class);

        return Pagination.newInstance(applyProductInnerLabelOutVOS,page);
    }

    @Override
    public void saveTemplateInfo(TemplateInfoDTO templateInfoDTO) {
        LstaucTDTO lstaucTDTO = new LstaucTDTO();
        lstaucTDTO.setLstaucent(100);
        lstaucTDTO.setLstauc002(templateInfoDTO.getTemplateName());

        List<LstaucTDTO> templates = lstaucTService.getTemplates(lstaucTDTO);
        if (CollectionUtil.isNotEmpty(templates)) {
            throw new CommonException("模板名称已存在");
        }
        lstaucTDTO.setLstauc003(templateInfoDTO.getCustomerNo());
        lstaucTDTO.setLstauc004(templateInfoDTO.getCustomerName());
        lstaucTDTO.setLstauc021(templateInfoDTO.getTemplateBody());
        lstaucTService.addTemplate(lstaucTDTO);
    }

    @Override
    public TemplateInfoDTO getTemplateInfo(TemplateInfoDTO templateInfoDTO) {
        LstaucTDTO lstaucTDTO = new LstaucTDTO();
        lstaucTDTO.setLstauc001(templateInfoDTO.getTemplateId());
        LstaucTDTO template = lstaucTService.getTemplate(lstaucTDTO);
        if (template == null) {
            throw new CommonException("模板不存在");
        }

        TemplateInfoDTO t = new TemplateInfoDTO();
        t.setTemplateId(template.getLstauc001());
        t.setTemplateBody(template.getLstauc021());
        return t;
    }

    @Override
    public GetErpTicketInfoOutVO getTicketDetailInfo(String planTicketNo) {
        GetErpTicketInfoOutVO getErpTicketInfoOutVO = new GetErpTicketInfoOutVO();
        // 获得工程单
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNo);
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        getErpTicketInfoOutVO.setPlanTicketNo(sfaaTVO.getSfaadocno());
        getErpTicketInfoOutVO.setProductNo(sfaaTVO.getSfaa010());
        ImaalTVO imaalTVO = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTVO != null) {
            getErpTicketInfoOutVO.setProductName(imaalTVO.getImaal003());
        }

        MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(getErpTicketInfoOutVO.getProductNo());
        if (materialsInfoDTO != null) {
            getErpTicketInfoOutVO.setSkuCode(materialsInfoDTO.getImaaua017());
            getErpTicketInfoOutVO.setSkuName(materialsInfoDTO.getImaaua018());
            getErpTicketInfoOutVO.setTemplateId(materialsInfoDTO.getImaaua019());

            if (StringUtil.isNotBlank(materialsInfoDTO.getImaaua019())) {
                LstaucTDTO lstaucTDTO = new LstaucTDTO();
                lstaucTDTO.setLstauc001(materialsInfoDTO.getImaaua019());
                LstaucTDTO template = lstaucTService.getTemplate(lstaucTDTO);
                if (template != null) {
                    getErpTicketInfoOutVO.setTemplateName(template.getLstauc002());
                }
            }

        }

        // 料件过期时间
        ImafTDTO imafTDTO = imafTService.getByProductNo(getErpTicketInfoOutVO.getProductNo());
        if (imafTDTO != null && (imafTDTO.getImaf031().compareTo(BigDecimal.ZERO) > 0
                || imafTDTO.getImaf032().compareTo(BigDecimal.ZERO) > 0)) {
            // 过期时间
            getErpTicketInfoOutVO.setOverdueMonth(imafTDTO.getImaf031().intValue());
            getErpTicketInfoOutVO.setOverdueDay(imafTDTO.getImaf032().intValue());
        } else {
            throw new CommonException("根据工单查询不到的料件过期时间信息，请联系相关人员维护后再次申请");
        }

        PmaalTPO customerInfo = pmaalTService.getCustomerInfo(sfaaTVO.getSfaa009());
        getErpTicketInfoOutVO.setCustomerNo(customerInfo.getPmaal001());
        getErpTicketInfoOutVO.setCustomerName(customerInfo.getPmaal003());
//        getErpTicketInfoOutVO.setProductNo(customerInfo.getPmaal001());
        PmaoTPO pmaoTPO = new PmaoTPO();
        pmaoTPO.setPmao001(customerInfo.getPmaal001());
        pmaoTPO.setPmao002(sfaaTVO.getSfaa010());
        List<PmaoTPO> list = pmaoTService.getList(pmaoTPO);
        if (CollectionUtil.isEmpty(list)) {
            throw new CommonException("根据工单查询不到的客户产品编号信息，请联系相关人员维护后再次申请");
        }
        getErpTicketInfoOutVO.setProductNo(list.get(0).getPmao004());

        return getErpTicketInfoOutVO;
    }

    @Override
    public String getDate(String date, String month, String day) {
        DateTime dateTime = DateUtil.parse(date, "yyyy-MM-dd");
        DateTime endDateTime = DateUtil
                .offset(dateTime, DateField.MONTH, Integer.parseInt(month))
                .offset(DateField.DAY_OF_MONTH, Integer.parseInt(day));

        return DateUtil.format(endDateTime, "yyyy-MM-dd");
    }

    @Override
    public Pagination<TemplateInfoDTO> getTemplateInfos(GetTemplateInfosInVO getTemplateInfosInVO) {
        TemplateInfoPageInVO templateInfoPageInVO = new TemplateInfoPageInVO();
        templateInfoPageInVO.setPageNum(getTemplateInfosInVO.getPageNum());
        templateInfoPageInVO.setPageSize(getTemplateInfosInVO.getPageSize());
        templateInfoPageInVO.setLstauc002(getTemplateInfosInVO.getTemplateName());
        templateInfoPageInVO.setLstauc004(getTemplateInfosInVO.getCustomerName());
        Pagination<LstaucTDTO> pagination = lstaucTService.getTemplatesByPagination(templateInfoPageInVO);
        if (pagination == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(pagination.getData())) {
            return null;
        }
        List<TemplateInfoDTO> templateInfoDTOS = pagination.getData().stream().map(t -> {
            TemplateInfoDTO templateInfoDTO = new TemplateInfoDTO();
            templateInfoDTO.setTemplateId(t.getLstauc001());
            templateInfoDTO.setTemplateName(t.getLstauc002());
            templateInfoDTO.setCustomerNo(t.getLstauc003());
            templateInfoDTO.setCustomerName(t.getLstauc004());
            templateInfoDTO.setTemplateBody(t.getLstauc021());
            return templateInfoDTO;
        }).collect(Collectors.toList());

        return Pagination.newInstance(templateInfoDTOS,pagination.getTotal(),pagination.getPages());
    }

    @Override
    public void updateTemplateInfo(TemplateInfoDTO templateInfoDTO) {
        LstaucTDTO lstaucTDTO = new LstaucTDTO();
        lstaucTDTO.setLstaucent(100);
        lstaucTDTO.setLstauc001(templateInfoDTO.getTemplateId());
        lstaucTDTO.setLstauc002(templateInfoDTO.getTemplateName());
        lstaucTDTO.setLstauc003(templateInfoDTO.getCustomerNo());
        lstaucTDTO.setLstauc004(templateInfoDTO.getCustomerName());
        lstaucTDTO.setLstauc021(templateInfoDTO.getTemplateBody());
        lstaucTService.addTemplate(lstaucTDTO);
    }

    @Override
    public void deleteTemplateInfo(TemplateInfoDTO templateInfoDTO) {
        LstaucTDTO lstaucTDTO = new LstaucTDTO();
        lstaucTDTO.setLstaucent(100);
        lstaucTDTO.setLstauc001(templateInfoDTO.getTemplateId());
        lstaucTService.deleteTemplate(lstaucTDTO);
    }


    public String compressString(String data) {
        if (data == null || data.isEmpty()) {
            return data;
        }
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             GZIPOutputStream gzip = new GZIPOutputStream(baos)) {
            gzip.write(data.getBytes());
            gzip.finish();
            return baos.toString("ISO-8859-1"); // 使用 ISO-8859-1 避免乱码
        } catch (IOException e) {
            throw new RuntimeException("Failed to compress data", e);
        }
    }

    public String decompressString(String compressedData) {
        if (compressedData == null || compressedData.isEmpty()) {
            return compressedData;
        }
        try (ByteArrayInputStream bais = new ByteArrayInputStream(compressedData.getBytes("ISO-8859-1"));
             GZIPInputStream gzip = new GZIPInputStream(bais);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzip.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to decompress data", e);
        }
    }


}
