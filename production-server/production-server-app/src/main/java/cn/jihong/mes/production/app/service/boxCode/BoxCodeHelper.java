package cn.jihong.mes.production.app.service.boxCode;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.HandleBoxCodeDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.SaveOutboundInfoInVO;
import cn.jihong.mes.production.api.model.vo.in.VerifyCaseCodeInVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.oa.erp.api.model.dto.SaveLsafTDTO;
import cn.jihong.oa.erp.api.model.po.LsafTPO;
import cn.jihong.oa.erp.api.model.po.SfacTPO;
import cn.jihong.oa.erp.api.model.vo.XmamTVO;
import cn.jihong.oa.erp.api.service.ILsafTService;
import cn.jihong.oa.erp.api.service.ISfacTService;
import cn.jihong.oa.erp.api.service.IXmamTService;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;
import cn.jihong.wms.api.model.vo.SrmBarcodeDetailVO;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产校验
 *
 * @date 2024/01/26
 */
@Slf4j
@Service
public abstract class BoxCodeHelper implements IBoxCodeService {

    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;
    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;

    @DubboReference
    private IXmamTService iXmamTService;
    @DubboReference
    private ILsafTService iLsafTService;
    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @DubboReference
    private ISfacTService sfacTService;
    @Resource
    private IProductStoreBoxService productStoreBoxService;

    @Override
    public List<BarcodeDetailDTO.BoxInfo> handleBoxCode(HandleBoxCodeDTO handleBoxCodeDTO, String sfac006) {
        List<BarcodeDetailDTO.BoxInfo> boxInfos = Lists.newArrayList();
//        List<GetProductBoxBarcodeDetailOutVO> productBoxBarcodeOutVOS =
//                productBoxBarcodeService.getListByBarcodes(handleBoxCodeDTO.getBoxCodes());
//        if (CollectionUtil.isEmpty(productBoxBarcodeOutVOS)) {
//            throw new CommonException("找不到箱码信息");
//        }
        // 去除码段校验
//        if (CollectionUtil.isNotEmpty(productBoxBarcodeOutVOS) && productBoxBarcodeOutVOS.size() > 1) {
//            List<String> barcodeNames = productBoxBarcodeOutVOS.stream().map(GetProductBoxBarcodeDetailOutVO::getBarcodeName)
//                    .distinct().collect(Collectors.toList());
//            throw new CommonException("存在多个码段的箱码" + JSON.toJSONString(barcodeNames));
//        }

        // 获得箱码信息 IProductBoxBarcodeDetailDetailService
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS =
                productBoxBarcodeDetailDetailService.getByBarcodeNos(handleBoxCodeDTO.getBoxCodes());

        Map<String, ProductBoxBarcodeDetailDetailPO> map =
                productBoxBarcodeDetailDetailPOS.stream()
                        .collect(Collectors.toMap(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, Function.identity()));
//        GetProductBoxBarcodeDetailOutVO getProductBoxBarcodeOutVO = productBoxBarcodeOutVOS.get(0);
        for (String boxCode : handleBoxCodeDTO.getBoxCodes()) {
            ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = map.get(boxCode);
            if (productBoxBarcodeDetailDetailPO == null) {
                throw new CommonException("找不到箱码信息[" + boxCode + "]");
            }
            BarcodeDetailDTO.BoxInfo boxInfo = new BarcodeDetailDTO.BoxInfo();
            boxInfo.setBoxCode(boxCode);
            boxInfo.setBoxQuantity(BigDecimal.valueOf(productBoxBarcodeDetailDetailPO.getBoxNum()));
            boxInfo.setLotNo(productBoxBarcodeDetailDetailPO.getLotNo());
            // 料号
            boxInfo.setA(productBoxBarcodeDetailDetailPO.getMaterialCode());
            // 生产日期
            boxInfo.setB(cn.hutool.core.date.DateUtil.format(productBoxBarcodeDetailDetailPO.getProductionDate(),
                    DatePattern.PURE_DATE_PATTERN));
            // 箱数量
            boxInfo.setC(String.valueOf(productBoxBarcodeDetailDetailPO.getBoxNum()));
            // 批次识别码
            boxInfo.setJ(productBoxBarcodeDetailDetailPO.getBatchCode());
            // 机台
            boxInfo.setK(productBoxBarcodeDetailDetailPO.getMachine());
            // 班次
            boxInfo.setL(productBoxBarcodeDetailDetailPO.getShift());
            // 保质期
            boxInfo.setM(productBoxBarcodeDetailDetailPO.getExpirationDate());
            // 流水号 截取最后10位
            if (boxCode.length() >= 10) {
                boxInfo.setX(boxCode.substring(boxCode.length() - 10));
            }
            boxInfo.setProductCode(sfac006);
            boxInfo.setStockCode(sfac006);
            boxInfos.add(boxInfo);
        }
        return boxInfos;
    }

    @Override
    public void verifyCaseCodeByErp(String caseCode, String palletCode) {
        String[] split = caseCode.split(",");
        List<String> list = Arrays.asList(split);
        if(CollectionUtil.isNotEmpty(list)){
            if(list.contains(palletCode)){
                throw new CommonException("箱码不允许与栈板码一致");
            }

            Set<String> set = new HashSet<>();
            List<String> duplicates = new ArrayList<>();
            for(String caseCodeTem : list){
                if(!set.add(caseCodeTem)){
                    duplicates.add(caseCodeTem);
                }
            }
            if(CollectionUtil.isNotEmpty(duplicates)){
                throw new CommonException("箱码中存在相同的数值：" + JSON.toJSONString(duplicates));
            }

            // 校验箱码中不能存在不同的批次
            List<String> errorCaseCodeList = new ArrayList<>();
            List<String> lotNos = list.stream().map(b -> {
                String[] split1 = b.split("#");
                if(split1.length<3){
                    errorCaseCodeList.add(b);
                    return "";
                }
                return split1[1];
            }).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(errorCaseCodeList)){
                throw new CommonException("箱码格式错误：" + JSON.toJSONString(errorCaseCodeList));
            }
            if (lotNos.size() > 1) {
                throw new CommonException("存在不相同批次" + JSON.toJSONString(lotNos));
            }

            List<LsafTPO> sameCaseCodeList = iLsafTService.getByBoxCodes(list);
            if(CollectionUtil.isNotEmpty(sameCaseCodeList)) {
                throw new CommonException("箱码已在erp使用：" + JSON.toJSONString(sameCaseCodeList.stream().map(lsafTPO -> lsafTPO.getLsaf001() + "入库申请单号:"+ lsafTPO.getLsaf009()).collect(Collectors.toList())));
            }
        }
    }

    @Override
    public SaveLsafTDTO setLsafTInfo(BarcodeDetailDTO barcodeDetailDTO, BarcodeDetailDTO.BoxInfo boxInfo,
                                     Date curentDate, String sfac006, String sfaa013, String sfac001) {
        SaveLsafTDTO saveLsafTDTO = new SaveLsafTDTO();
        saveLsafTDTO.setLsafent(100);
        saveLsafTDTO.setLsafsite(SecurityUtil.getCompanySite());
        saveLsafTDTO.setLsaf001(boxInfo.getBoxCode());
        saveLsafTDTO.setLsaf004(barcodeDetailDTO.getLotNo());
        if (boxInfo.getBoxQuantity() != null) {
            saveLsafTDTO.setLsaf005(boxInfo.getBoxQuantity());
            saveLsafTDTO.setLsaf047(boxInfo.getBoxQuantity());
        }
        saveLsafTDTO.setLsaf006(1);
        saveLsafTDTO.setLsaf002(barcodeDetailDTO.getWarehouseNo());
        saveLsafTDTO.setLsaf003(barcodeDetailDTO.getStorageSpacesNo());
        saveLsafTDTO.setLsaf036(barcodeDetailDTO.getStorageSpacesNo());
        saveLsafTDTO.setLsaf007(barcodeDetailDTO.getPlanTicketNo());
        saveLsafTDTO.setLsaf008(0);
        // saveLsafTDTO.setLsaf009(barcodeDetailDTO.getPlanTicketNo());
        saveLsafTDTO.setLsaf010(0);
        saveLsafTDTO.setLsaf013(Objects.nonNull(SecurityUtil.getWorkcode()) ? SecurityUtil.getWorkcode() : "");
        saveLsafTDTO.setLsaf014(curentDate);
        saveLsafTDTO.setLsaf015(DateUtil.parseDateToStringCustom(curentDate, "HH:mm:ssSSS"));
        saveLsafTDTO.setLsaf019("Y");
        saveLsafTDTO.setLsaf024(sfaa013);
        saveLsafTDTO.setLsaf025(new BigDecimal(1));

        saveLsafTDTO.setLsaf027(DateUtil.parseDateToStringCustom(curentDate, "yyyyMMddHHmmssSSS"));

        saveLsafTDTO.setLsaf029("N");
        saveLsafTDTO.setLsaf031("1");
        saveLsafTDTO.setLsaf033(UUID.randomUUID().toString().replaceAll("-", ""));
        saveLsafTDTO.setLsaf037(barcodeDetailDTO.getLotNo());
        saveLsafTDTO.setLsaf038(sfac006);
        saveLsafTDTO.setLsaf039("002");
        saveLsafTDTO.setLsaf081(new BigDecimal(0));
        saveLsafTDTO.setLsaf082(new BigDecimal(0));
//        saveLsafTDTO.setLsaf083(barcodeDetailDTO.getItemNo());
        saveLsafTDTO.setLsaf083(sfac001);
        saveLsafTDTO.setLsaf085(curentDate);
        saveLsafTDTO.setLsaf086(curentDate);
        saveLsafTDTO.setLsaf087(barcodeDetailDTO.getBarcode());
        saveLsafTDTO.setLsaf905(barcodeDetailDTO.getPlanTicketNo());
        saveLsafTDTO.setLsaf906(0L);
        saveLsafTDTO.setLsaf907(0L);
        saveLsafTDTO.setLsafownid(Objects.nonNull(SecurityUtil.getWorkcode()) ? SecurityUtil.getWorkcode() : "");
        saveLsafTDTO.setLsafcrtid(Objects.nonNull(SecurityUtil.getWorkcode()) ? SecurityUtil.getWorkcode() : "");
        saveLsafTDTO.setLsafcrtdt(LocalDateTime.ofInstant(curentDate.toInstant(), ZoneId.systemDefault()));
        saveLsafTDTO.setLsafmodid(Objects.nonNull(SecurityUtil.getWorkcode()) ? SecurityUtil.getWorkcode() : "");
        saveLsafTDTO.setLsafmoddt(LocalDateTime.ofInstant(curentDate.toInstant(), ZoneId.systemDefault()));
        saveLsafTDTO.setLsafstus("Y");

        saveLsafTDTO.setLsaf041("1");
        saveLsafTDTO.setLsaf042("NNN");
        saveLsafTDTO.setLsaf045(new BigDecimal(0));

        saveLsafTDTO.setLsaf053(new BigDecimal(0));
        saveLsafTDTO.setLsaf054(new BigDecimal(0));
        saveLsafTDTO.setLsaf055(new BigDecimal(0));
        saveLsafTDTO.setLsaf056(new BigDecimal(0));
        saveLsafTDTO.setLsaf057(new BigDecimal(0));

        try {
            saveLsafTDTO.setLsaf058(
                    cn.jihong.common.util.DateUtil.parseDate("9998-12-31 00:00:00", DatePattern.NORM_DATETIME_PATTERN));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return saveLsafTDTO;
    }


    @Override
    public void verifyBoxCode(VerifyCaseCodeInVO verifyCaseCodeInVO) {
        String caseCode = verifyCaseCodeInVO.getBoxCode();
        String[] caseCodes = caseCode.split(",");
        List<String> barcodeList = Lists.newArrayList();
        Arrays.stream(caseCodes).forEach(s -> {
            if (s.startsWith("box#")) {
                return;
            }
            barcodeList.add(s);
        });
        if (CollectionUtil.isEmpty(barcodeList)) {
            return;
        }
        // 这个有三种校验方式，分策略了
        // 20250630合并了，都是去查 wms的数据
        verifyBoxCodeAndTicketNo(verifyCaseCodeInVO, barcodeList);

//        List<String> sameCaseCodeList = iLsafTService.getCaseCode(barcodeList);
//        if (CollectionUtil.isNotEmpty(sameCaseCodeList)) {
//            throw new CommonException("箱码已经erp中被使用过：" + caseCode);
//        }
        productStoreBoxService.verifyBoxCode(barcodeList);
        productStoreBoxService.verifyBoxCodeFromWms(barcodeList);

        return;
    }



    /**
     * 发送消息
     */
    @Override
    public void sendMessage(Integer code, String direct, String caseCode){
        return;
    }

    @Override
    public String activeBoxCode(String boxCode){
        return null;
    }


    @Override
    public List<BarcodeDetailDTO.BoxInfo> oldBarcodeNO(HandleBoxCodeDTO handleBoxCodeDTO, String sfac006){
        List<BarcodeDetailDTO.BoxInfo> boxInfos = Lists.newArrayList();
        XmamTVO xmamTVO = iXmamTService.getXmamTByProductionNo(handleBoxCodeDTO.getProductionNo(), SecurityUtil.getCompanySite());
        log.info("---pushCaseInfoToWms:xmamTVO={}", JSON.toJSONString(xmamTVO));
        SaveOutboundInfoInVO info = handleBoxCodeDTO.getInfo();
        List<SaveOutboundInfoInVO.PalletCodeInfo> palletCodeInfos = info.getPalletCodeInfos();
        Map<String, SaveOutboundInfoInVO.PalletCodeInfo> palleteCodeInfoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(palletCodeInfos)){
            palleteCodeInfoMap.putAll(palletCodeInfos.stream().collect(Collectors.toMap(SaveOutboundInfoInVO.PalletCodeInfo::getPalletCode, Function.identity())));
        }

        List<SrmBarcodeDetailVO> srmBarcodeDetailVOS =
            srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(handleBoxCodeDTO.getBoxCodes());
        if (CollectionUtil.isEmpty(srmBarcodeDetailVOS)) {
            srmBarcodeDetailVOS = Lists.newArrayList();
        }
        Map<String, SrmBarcodeDetailVO> barcodeDetailVOMap = srmBarcodeDetailVOS.stream()
            .collect(Collectors.toMap(SrmBarcodeDetailVO::getBarcodeNo, Function.identity(), (v1, v2) -> v1));
        if (barcodeDetailVOMap == null) {
            barcodeDetailVOMap = Maps.newHashMap();
        }

        for(String boxCode : handleBoxCodeDTO.getBoxCodes()){
            BarcodeDetailDTO.BoxInfo boxInfo = new BarcodeDetailDTO.BoxInfo();
            boxInfo.setBoxCode(boxCode);
            if (xmamTVO == null) {
                BigDecimal divide = handleBoxCodeDTO.getInfo().getProducedQuantity()
                        .divide(BigDecimal.valueOf(handleBoxCodeDTO.getInfo().getCaseCode().split(",").length), 2, RoundingMode.HALF_UP);
                boxInfo.setBoxQuantity(divide);
            } else {
                boxInfo.setBoxQuantity(xmamTVO.getXmam008());
            }

            // 按照栈板入库，则需要覆盖这部分值
            if (CollectionUtil.isNotEmpty(palleteCodeInfoMap) && palleteCodeInfoMap.containsKey(boxCode)) {
                SaveOutboundInfoInVO.PalletCodeInfo palletCodeInfo = palleteCodeInfoMap.get(boxCode);
                boxInfo.setBoxQuantity(palletCodeInfo.getPalletCodeQuantity());
            }

            SrmBarcodeDetailVO srmBarcodeDetailVO = barcodeDetailVOMap.get(boxCode);
            if (srmBarcodeDetailVO == null) {
                String[] split = boxInfo.getBoxCode().split("#");
                boxInfo.setLotNo(split[1]);
                // 料号
                boxInfo.setA(split[0]);
                // 生产日期
                boxInfo.setB(split[1]);
                // 箱数量
                boxInfo.setC(String.valueOf(boxInfo.getBoxQuantity()));
                // 批次识别码
                boxInfo.setJ(split[2]);
                try {
                    // 机台
                    boxInfo.setK(split[3]);
                    // 班次
                    boxInfo.setL(split[4]);
                    // 保质期 + 2 年
                    boxInfo.setM(split[5]);
                    // 流水号
                    boxInfo.setX(split[6]);
                } catch (Exception e) {
                    log.error(boxInfo.getBoxCode() + "箱码格式太短");
                }
                boxInfo.setProductCode(sfac006);
                boxInfo.setStockCode(sfac006);
            } else {
                boxInfo.setLotNo(srmBarcodeDetailVO.getLotNo());
                // 料号
                boxInfo.setA(srmBarcodeDetailVO.getA());
                // 生产日期
                boxInfo.setB(srmBarcodeDetailVO.getB());
                // 箱数量
                boxInfo.setC(srmBarcodeDetailVO.getC());
                // 批次识别码
                boxInfo.setJ(srmBarcodeDetailVO.getJ());
                // 机台
                boxInfo.setK(srmBarcodeDetailVO.getK());
                // 班次
                boxInfo.setL(srmBarcodeDetailVO.getL());
                // 保质期 + 2 年
                boxInfo.setM(srmBarcodeDetailVO.getM());
                // 流水号
                boxInfo.setX(srmBarcodeDetailVO.getX());
                boxInfo.setProductCode(srmBarcodeDetailVO.getProductCode());
                boxInfo.setStockCode(srmBarcodeDetailVO.getStockCode());
            }

            boxInfos.add(boxInfo);
        }
        return boxInfos;
    }

    public void createbarcodeToWms(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO, List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOS) {
        SfacTPO sfacTPO = sfacTService.getByProductNo(productBoxBarcodeDetailPO.getPlanTicketNo(), productBoxBarcodeDetailPO.getMaterialCode());
        String sfac006 = sfacTPO.getSfac006();

        BarcodeDetailDTO barcodeDetailDTO = new BarcodeDetailDTO();
        barcodeDetailDTO.setPlanTicketNo(productBoxBarcodeDetailPO.getPlanTicketNo());
        barcodeDetailDTO.setItemNo(productBoxBarcodeDetailPO.getMaterialCode());
        barcodeDetailDTO.setWorkCode(SecurityUtil.getWorkcode());
        barcodeDetailDTO.setUserName(SecurityUtil.getUserName());
        barcodeDetailDTO.setCompanyCode(SecurityUtil.getCompanySite());
        barcodeDetailDTO.setItemName(productBoxBarcodeDetailPO.getProductName());
        barcodeDetailDTO.setLotNo(productBoxBarcodeDetailPO.getLotNo());
        barcodeDetailDTO.setLotDate(cn.hutool.core.date.DateUtil.format(
                productBoxBarcodeDetailPO.getProductionDate(), DatePattern.PURE_DATE_PATTERN));

        List<BarcodeDetailDTO.BoxInfo> barcodeInfoList = Lists.newArrayList();
        productBoxBarcodeDetailDetailDTOS.stream().forEach(pb -> {
            BarcodeDetailDTO.BoxInfo boxInfo = new BarcodeDetailDTO.BoxInfo();
            boxInfo.setBoxCode(pb.getBarcodeNo());
            boxInfo.setBoxQuantity(BigDecimal.valueOf(pb.getBoxNum()));
            boxInfo.setLotNo(pb.getLotNo());
            // 料号
            boxInfo.setA(pb.getMaterialCode());
            // 生产日期
            boxInfo.setB(cn.hutool.core.date.DateUtil.format(pb.getProductionDate(),
                    DatePattern.PURE_DATE_PATTERN));
            // 箱数量
            boxInfo.setC(String.valueOf(pb.getBoxNum()));
            // 批次识别码
            boxInfo.setJ(pb.getBatchCode());
            // 机台
            boxInfo.setK(pb.getMachine());
            // 班次
            boxInfo.setL(pb.getShift());
            // 保质期
            boxInfo.setM(pb.getExpirationDate());
            // 流水号 截取最后10位
            if (pb.getBarcodeNo().length() >= 10) {
                boxInfo.setX(pb.getBarcodeNo().substring(pb.getBarcodeNo().length() - 10));
            }
            boxInfo.setProductCode(sfac006);
            boxInfo.setStockCode(sfac006);

            barcodeInfoList.add(boxInfo);
        });
        barcodeDetailDTO.setBoxInfos(barcodeInfoList);
        srmBarcodeDetailService.saveBatch(barcodeDetailDTO);
    }

    @Override
    public void verifyBoxCodeAndTicketNo(VerifyCaseCodeInVO verifyCaseCodeInVO, List<String> barcodeList) {
        List<SrmBarcodeDetailVO> srmBarcodeDetailVOS =
                srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(barcodeList);
        if (CollectionUtil.isNotEmpty(srmBarcodeDetailVOS)) {
            List<String> sourceNos = srmBarcodeDetailVOS.stream().map(SrmBarcodeDetailVO::getSourceNo).distinct()
                    .collect(Collectors.toList());
            if (sourceNos.size() > 1 || !sourceNos.contains(verifyCaseCodeInVO.getPlanTicketNo())) {
                throw new CommonException("箱码的工单号和当前工单号不匹配：" + JSON.toJSONString(sourceNos) + "   "
                        + JSON.toJSONString(verifyCaseCodeInVO.getPlanTicketNo()));
            }
        } else {
            throw new CommonException("找不到WMS的箱码信息:" + JSON.toJSONString(barcodeList));
        }
    }

}
