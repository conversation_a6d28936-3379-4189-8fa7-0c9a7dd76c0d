package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionItemOutVO;
import cn.jihong.mes.production.api.service.IProductSafetyInspectionItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 安全点检项配置 控制器
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@RestController
@RequestMapping("/production/safety/inspection/item")
@ShenyuSpringMvcClient(path = "/production/safety/inspection/item/**")
public class ProductSafetyInspectionItemController {

    @Resource
    private IProductSafetyInspectionItemService productSafetyInspectionItemService;

    /**
     * 分页查询安全点检项配置
     */
    @PostMapping("/page")
    public StandardResult<Pagination<ProductSafetyInspectionItemOutVO>> page(@RequestBody @Valid ProductSafetyInspectionItemQueryInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productSafetyInspectionItemService.page(inVO));
    }
//
//    /**
//     * 获取安全点检项配置详情
//     */
//    @GetMapping("/{id}")
//    public StandardResult<ProductSafetyInspectionItemOutVO> getDetail(@PathVariable("id") Long id) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productSafetyInspectionItemService.getDetail(id));
//    }

    /**
     * 保存安全点检项配置
     */
    @PostMapping
    public StandardResult<Boolean> save(@RequestBody @Valid ProductSafetyInspectionItemSaveInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productSafetyInspectionItemService.save(inVO));
    }

    /**
     * 删除安全点检项配置
     */
    @PostMapping("/{id}")
    public StandardResult<Boolean> delete(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productSafetyInspectionItemService.delete(id));
    }

//    /**
//     * 获取启用的安全点检项列表
//     */
//    @GetMapping("/enable/list")
//    public StandardResult<List<ProductSafetyInspectionItemOutVO>> getEnableList() {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productSafetyInspectionItemService.getEnableList());
//    }
} 