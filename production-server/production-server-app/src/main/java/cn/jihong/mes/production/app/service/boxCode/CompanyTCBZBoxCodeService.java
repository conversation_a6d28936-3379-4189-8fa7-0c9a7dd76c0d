package cn.jihong.mes.production.app.service.boxCode;

import cn.jihong.mes.production.api.model.dto.HandleBoxCodeDTO;
import cn.jihong.mes.production.api.model.enums.BarcodeOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeDetailInVO;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 天成包装处理箱码
 */
@Slf4j
@Service
public class CompanyTCBZBoxCodeService extends BoxCodeHelper {


    @Override
    public List<BarcodeDetailDTO.BoxInfo> handleBoxCode(HandleBoxCodeDTO handleBoxCodeDTO, String sfac006) {
        throw new RuntimeException("天成包装处理箱码不支持此操作");
    }



    @Override
    public String getCompanyCode() {
        return CompanyBoxEnum.COMPANY_TCBZ.getCode();
    }

    @Override
    public Long applyBoxBarcodeDetail(AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO) {
        throw new RuntimeException("天成包装处理箱码不支持此操作");
    }

    @Override
    public List<String> getBarcodeList(ProductBoxBarcodeDetailPO productBoxBarcodeDetailPO, BarcodeOperateTypeEnum print) {
        throw new RuntimeException("天成包装处理箱码不支持此操作");
    }

}