package cn.jihong.mes.production.app.service;

import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineGroupRelationshipPO;
import cn.jihong.mes.production.api.service.IProductMachineGroupRelationshipService;
import cn.jihong.mes.production.app.mapper.ProductMachineGroupRelationshipMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * 机组和机台关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@DubboService
public class ProductMachineGroupRelationshipServiceImpl extends JiHongServiceImpl<ProductMachineGroupRelationshipMapper, ProductMachineGroupRelationshipPO> implements IProductMachineGroupRelationshipService {

    @Override
    public List<ProductMachineGroupRelationshipPO> getByGroupId(Long groupId) {
        LambdaQueryWrapper<ProductMachineGroupRelationshipPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineGroupRelationshipPO::getMachineGroupId, groupId)
                .eq(ProductMachineGroupRelationshipPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ProductMachineGroupRelationshipPO> getByMachineNames(List<String> machineNames) {
        LambdaQueryWrapper<ProductMachineGroupRelationshipPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductMachineGroupRelationshipPO::getMachineName, machineNames)
                .eq(ProductMachineGroupRelationshipPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        return list(lambdaQueryWrapper);
    }

    @Override
    public void removeByGroupId(Long id) {
        removeBatchByIds(getByGroupId(id));
    }

    @Override
    public List<ProductMachineGroupRelationshipPO> getByCompanyCode(String companySite) {
        LambdaQueryWrapper<ProductMachineGroupRelationshipPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductMachineGroupRelationshipPO::getCompanyCode, companySite)
                .eq(ProductMachineGroupRelationshipPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        return list(lambdaQueryWrapper);
    }
}
