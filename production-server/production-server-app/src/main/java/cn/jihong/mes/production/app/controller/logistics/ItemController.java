package cn.jihong.mes.production.app.controller.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.logistics.ItemQueryByConditionInVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.ItemQueryByConditionOutVO;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 在制品
 * <AUTHOR>
 * @date 2025-03-07 11:44
 */
@RestController
@RequestMapping("/item")
@ShenyuSpringMvcClient(path = "/item/**")
public class ItemController {


    @Resource
    private ILogisticsService iLogisticsService;

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.OutboundStackerOutVO>>
     */
    @PostMapping("/itemQueryByCondition")
    public StandardResult<Pagination<ItemQueryByConditionOutVO>> itemQueryByCondition(@RequestBody @Valid ItemQueryByConditionInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.itemQueryByCondition(inVO));
    }



}
