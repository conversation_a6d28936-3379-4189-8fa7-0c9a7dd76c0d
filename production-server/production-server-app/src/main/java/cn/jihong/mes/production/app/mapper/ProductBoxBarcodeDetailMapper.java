package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailPO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 箱码号段明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface ProductBoxBarcodeDetailMapper extends JiHongMapper<ProductBoxBarcodeDetailPO> {

    Page<GetProductBoxBarcodeDetailOutVO> getList(@Param("page") IPage page,
                                                       @Param("getProductBoxBarcodeDetailInVO") GetProductBoxBarcodeDetailInVO getProductBoxBarcodeDetailInVO);
}
