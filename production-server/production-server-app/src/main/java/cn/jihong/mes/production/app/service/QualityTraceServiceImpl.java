package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.enums.MaterialOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.ProductShitEnum;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.out.ProcessTeamMembersVO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductionProcessesVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class QualityTraceServiceImpl implements IQualityTraceService {

    @DubboReference
    private ISfcbTService sfcbTService;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMaterialService productMaterialService;
    @Resource
    private IProductMaterialOperationRecordsService productMaterialOperationRecordsService;
    @Resource
    private IProductShiftService productShiftService;
    @Resource
    private IProductDefectiveProductsCallbackService productDefectiveProductsCallbackService;

    @DubboReference
    private IA01Service ia01Service;

    @Override
    public List<ProductionProcessesVO> getProductionProcessesVOs(String planTicketNo) {
        List<ProductionProcessesVO> list = Lists.newArrayList();

        // 获得这个机台的所有工序
        ticketProcess(planTicketNo);

        // 通过工程单号和工序，获得用料信息
        List<ProductTicketPO> productTicketPOS = productTicketService.getListByPlanTicketNo(planTicketNo);
        if (CollectionUtil.isEmpty(productTicketPOS)) {
            return list;
        }

        // 班组信息MAP
        Map<Long, UserDTO> userMap = getUserDTOMap(productTicketPOS);

        if (CollectionUtil.isNotEmpty(productTicketPOS)) {
            Map<String, List<ProductTicketPO>> processMap =
                productTicketPOS.stream().collect(Collectors.groupingBy(ProductTicketPO::getProcessCode));
            // 按照工序分组
            processMap.entrySet().stream().forEach(process -> {
                setProcessInfo(list, userMap, process);
            });
        }
        return list;
    }

    private Map<Long, UserDTO> getUserDTOMap(List<ProductTicketPO> productTicketPOS) {
        List<ProductShiftOutVO> productShifts = productShiftService
            .getProductShift(productTicketPOS.stream().map(ProductTicketPO::getId).collect(Collectors.toList()));
        String users = productShifts.stream().map(ProductShiftOutVO::getTeamUsers).collect(Collectors.joining(","));
        String[] userStr = users.split(",");
        List<Long> userLong = Arrays.stream(userStr).map(u -> Long.valueOf(u)).collect(Collectors.toList());
        List<UserDTO> userInfoByIds = ia01Service.getUserInfoByIds(userLong);
        Map<Long, UserDTO> userMap =
            userInfoByIds.stream().collect(Collectors.toMap(UserDTO::getId, Function.identity()));
        return userMap;
    }

    private void ticketProcess(String planTicketNo) {
        List<SfcbTVO> sfcbTVOS = sfcbTService.getByTickNo(planTicketNo);
        Map<String, SfcbTVO> current =
            sfcbTVOS.stream().collect(Collectors.toMap(SfcbTVO::getSfcb003, Function.identity()));
        Map<String, SfcbTVO> last =
            sfcbTVOS.stream().collect(Collectors.toMap(SfcbTVO::getSfcb007, Function.identity()));

        List<SfcbTVO> sortSfcbTVOS = new ArrayList<>();
        SfcbTVO start = last.get("INIT");
        getNest(start, current, sortSfcbTVOS);
    }

    /**
     * 封装工序信息
     * 
     * @param list
     * @param userMap
     * @param productTicketMap
     */
    private void setProcessInfo(List<ProductionProcessesVO> list, Map<Long, UserDTO> userMap,
        Map.Entry<String, List<ProductTicketPO>> productTicketMap) {
        ProductionProcessesVO productionProcessesVO = new ProductionProcessesVO();

        String key = productTicketMap.getKey();
        List<ProductTicketPO> values = productTicketMap.getValue();

        List<ProductMaterialOperationRecordsDTO> productMaterialPOS = productMaterialOperationRecordsService
            .getByProductTicketIds(values.stream().map(ProductTicketPO::getId).collect(Collectors.toList()),
                Arrays.asList(Integer.valueOf(MaterialOperateTypeEnum.UP_MATERIAL.getCode())));
        List<String> itemNos =
            productMaterialPOS.stream().map(ProductMaterialOperationRecordsDTO::getMaterialCode).collect(Collectors.toList());
        List<String> lotNos =
            productMaterialPOS.stream().map(ProductMaterialOperationRecordsDTO::getPurchaseBatch).collect(Collectors.toList());

        // 根据机台名称，还要再次分组
        Map<String, List<ProductTicketPO>> machineNameMap =
            values.stream().collect(Collectors.groupingBy(ProductTicketPO::getMachineName));

        ProductionProcessesVO.MaterialInfo materialInfo = new ProductionProcessesVO.MaterialInfo();
        // 机台信息
        List<ProductionProcessesVO.MachineInfo> machineInfos = Lists.newArrayList();
        machineNameMap.entrySet().forEach(m -> {
            setMachineInfo(userMap, values.get(0), machineInfos, m);
        });
        materialInfo.setItemNos(itemNos.stream().distinct().collect(Collectors.toList()));
        materialInfo.setLotNos(lotNos.stream().distinct().collect(Collectors.toList()));
        productionProcessesVO.setMaterialInfo(materialInfo);
        productionProcessesVO.setMachineInfos(machineInfos);
        productionProcessesVO.setProcessNo(key);
        productionProcessesVO.setMono(values.get(0).getPlanTicketNo());
        productionProcessesVO.setProcessName(values.get(0).getProcess());
        list.add(productionProcessesVO);
    }

    /**
     * 封装机台信息
     * 
     * @param userMap
     * @param productTicketPO
     * @param machineInfos
     * @param m
     */
    private void setMachineInfo(Map<Long, UserDTO> userMap, ProductTicketPO productTicketPO,
        List<ProductionProcessesVO.MachineInfo> machineInfos, Map.Entry<String, List<ProductTicketPO>> m) {
        ProductionProcessesVO.MachineInfo machineInfo = new ProductionProcessesVO.MachineInfo();

        List<ProductTicketPO> pos = m.getValue();

        List<ProductShiftOutVO> productShift =
            productShiftService.getProductShift(pos.stream().map(ProductTicketPO::getId).collect(Collectors.toList()));

        machineInfo.setDeviceNo(productTicketPO.getMachineName());
        machineInfo.setDeviceName(productTicketPO.getMachineName());

        // 获得不良品数量 log.info("不良品数量：" + defectiveProductsQuantity);


        // 设置生产数量
        List<ProcessTeamMembersVO> processTeamMembersVOS = Lists.newArrayList();
        productShift.stream().forEach(productShiftOutVO -> {
            // 设置班组信息
            String userIds =
                productShift.stream().map(ProductShiftOutVO::getTeamUsers).collect(Collectors.joining(","));
            String[] userIdStr = userIds.split(",");
            Arrays.stream(userIdStr).distinct().forEach(u -> {
                ProcessTeamMembersVO processTeamMembersVO = new ProcessTeamMembersVO();
                processTeamMembersVO.setUserNo(u);
                processTeamMembersVO
                    .setUserName(userMap.get(Long.valueOf(u)) == null ? "" : userMap.get(Long.valueOf(u)).getName());
                processTeamMembersVO.setTitleNo(String
                    .valueOf(userMap.get(Long.valueOf(u)) == null ? "" : userMap.get(Long.valueOf(u)).getJobTitleId()));
                processTeamMembersVO.setTitleName(
                    userMap.get(Long.valueOf(u)) == null ? "" : userMap.get(Long.valueOf(u)).getJobTitleName());
                processTeamMembersVO.setShift(productShiftOutVO.getShift());
                processTeamMembersVO
                    .setShiftName(ProductShitEnum.getProductShitEnum(productShiftOutVO.getShift()).getName());
                processTeamMembersVO.setProduceDate(productShiftOutVO.getProduceDate());
                processTeamMembersVOS.add(processTeamMembersVO);
            });
        });

        machineInfo.setProcessTeamMembersVOS(processTeamMembersVOS);
        machineInfo.setProcessingDate(productTicketPO.getProduceDate());
        machineInfos.add(machineInfo);
    }

    private void getNest(SfcbTVO start, Map<String, SfcbTVO> current, List<SfcbTVO> sortSfcbTVOS) {
        if (ObjectUtil.isNotNull(start)) {
            sortSfcbTVOS.add(start);
            String sfcb009 = start.getSfcb009();
            getNest(current.get(sfcb009), current, sortSfcbTVOS);
        }
    }

}
