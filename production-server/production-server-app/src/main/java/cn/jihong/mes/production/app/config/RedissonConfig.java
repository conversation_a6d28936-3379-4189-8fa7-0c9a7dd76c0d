package cn.jihong.mes.production.app.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setDatabase(0)
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setPassword(redisPassword).setConnectionPoolSize(100) // 设置连接池大小
                .setConnectionMinimumIdleSize(10); // 设置最小空闲连接数
        return Redisson.create(config);
    }
}
