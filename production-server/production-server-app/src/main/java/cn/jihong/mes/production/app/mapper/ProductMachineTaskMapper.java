package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 生产机台任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface ProductMachineTaskMapper extends JiHongMapper<ProductMachineTaskPO> {


    Page<GetProjectDailyReportsOutVO> getProjectDailyReports(IPage page,@Param("inVO") GetProjectDailyReportsInVO inVO);

    Page<GetPageByDayOutVO> getPageByDay(IPage page, @Param("inVO") GetPageByDayInVO inVO);

    Page<GetPageByDayDetailOutVO> getPageByDayDetail(IPage page, @Param("inVO") GetPageByDayDetailInVO inVO);

    Page<GetPageByEndOutVO> getPageByEnd(IPage page, @Param("inVO") GetPageByEndInVO inVO ,@Param("workingPlanTicketNoList") List<String> workingPlanTicketNoList);

    Page<GetPageByEndDetailOutVO> getPageByEndDetail(IPage page, @Param("inVO") GetPageByEndDetailInVO inVO);

    Page<GetProductMachineTaskPageOutVO> getProductMachineTaskPage(IPage page, @Param("inVO") GetProductMachineTaskPageInVO inVO, @Param("companyCode")String companyCode);
}
