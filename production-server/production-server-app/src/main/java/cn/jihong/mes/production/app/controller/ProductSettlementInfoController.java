package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO;
import cn.jihong.mes.production.api.model.vo.in.GetProductSettlementInfoListInVO;
import cn.jihong.mes.production.api.service.IProductSettlementInfoService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 生产工程单结算信息
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@RestController
@RequestMapping("/productSettlementInfo")
@ShenyuSpringMvcClient(path = "/productSettlementInfo/**")
public class ProductSettlementInfoController {

    @Autowired
    private IProductSettlementInfoService iProductSettlementInfoService;

    /**
     * 获取工程结算列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO>>
     * <AUTHOR>
     * @date: 2023/11/14 17:24
     */
    @PostMapping("/getProductSettlementInfoList")
    public StandardResult<Pagination<ProductSettlementInfoPO>> getProductSettlementInfoList(@RequestBody GetProductSettlementInfoListInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProductSettlementInfoService.getProductSettlementInfoList(inVO));
    }

    /**
     * 获取工程结算详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO>
     * <AUTHOR>
     * @date: 2023/11/15 15:19
     */
    @GetMapping("/getSingleById/{id}")
    public StandardResult<ProductSettlementInfoPO> getSingleById(@PathVariable Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementInfoService.getSingleById(id));
    }


    /**
     * 工程结算 结单
     * @param id
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2023/11/15 10:54
     */
    @GetMapping("/finish/{id}")
    public StandardResult<Boolean> finish(@PathVariable Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementInfoService.finish(id));
    }

}

