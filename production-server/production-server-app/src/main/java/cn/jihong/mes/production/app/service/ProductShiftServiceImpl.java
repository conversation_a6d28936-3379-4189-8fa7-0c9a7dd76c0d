package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductShiftPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetDefaultRoleListsInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefaultRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mes.production.api.service.IProductShiftService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductShiftMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.po.GzcblTPO;
import cn.jihong.oa.erp.api.model.po.OoagTPO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.IGzcblTService;
import cn.jihong.oa.erp.api.service.IOoagTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 班次信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@DubboService
public class ProductShiftServiceImpl extends JiHongServiceImpl<ProductShiftMapper, ProductShiftPO>
    implements IProductShiftService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Resource
    private IProductTicketService productTicketService;
    @DubboReference
    private IA01Service ia01Service;
    @DubboReference
    private IGzcblTService gzcblTService;
    @DubboReference
    private IOoagTService ooagTService;
    @DubboReference
    private ISfcbTService iSfcbTService;


    @Override
    public String saveProductShift(ProductShiftInVO productShiftInVO) {
        ProductTicketPO productTicketPO = productTicketService.getById(productShiftInVO.getProductTicketId());
        productShiftInVO.setTeamUsers(productShiftInVO.getTeamUsers().stream().sorted(Comparator.comparing(ProductShiftInVO.TeamUser::getRoleCode))
                .collect(Collectors.toList()));
        List<ProductShiftPO> productShiftPOS = productShiftInVO.getTeamUsers().stream().distinct().map(productShift -> {
            ProductShiftPO productShiftPO = new ProductShiftPO();
            productShiftPO.setCompanyCode(productTicketPO.getCompanyCode());
            productShiftPO.setProductTicketId(productTicketPO.getId());
            productShiftPO.setProduceDate(productTicketPO.getProduceDate());
            productShiftPO.setShift(productTicketPO.getShift());
            productShiftPO.setRoleCode(productShift.getRoleCode());
            productShiftPO.setRoleName(productShift.getRoleName());
            productShiftPO.setTeamUsers(productShift.getUserId());
            productShiftPO.setCreateBy(SecurityUtil.getUserId());
            return productShiftPO;
        }).collect(Collectors.toList());
        saveBatch(productShiftPOS);
        return "保存班组信息成功";
    }

    @Override
    public Pagination<ProductShiftOutVO> getProductShift(ProductTicketPageInVO productTicketPageInVO) {
        Page<ProductShiftOutVO> iPage = baseMapper.getProductShift(productTicketPageInVO.getPage(),
                productTicketPageInVO.getProductTicketId(),SecurityUtil.getCompanySite());
        return getProductShiftOutVOPagination(iPage, iPage);
    }

    @Override
    public Pagination<ProductShiftOutVO> getProductShift(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<ProductShiftOutVO> iPage =
                baseMapper.getProductShiftByTicketBase(page, productTicketBaseDTO);
        return getProductShiftOutVOPagination(page, iPage);
    }

    @Override
    public List<ProductShiftOutVO> getProductShift(List<Long> productTicketId) {
        LambdaQueryWrapper<ProductShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductShiftPO::getProductTicketId,productTicketId)
                .eq(ProductShiftPO::getDeleted, BooleanEnum.FALSE.getCode());
        List<ProductShiftPO> productShiftPOS = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(productShiftPOS)) {
            return BeanUtil.copyToList(productShiftPOS,ProductShiftOutVO.class);
        }
        return null;
    }

    @Override
    public void updateProductShift(ProductShiftInVO productShiftInVO) {
        ProductTicketPO productTicketPO = productTicketService.getById(productShiftInVO.getProductTicketId());
        productShiftInVO.setTeamUsers(productShiftInVO.getTeamUsers().stream().sorted(Comparator.comparing(ProductShiftInVO.TeamUser::getRoleCode))
                .collect(Collectors.toList()));
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicketPO.getIsSignUp())) {
            throw new CommonException("该工单已良品报工，不能修改班组信息");
        }
        List<Long> userIds = productShiftInVO.getTeamUsers().stream().map(teamUser -> Long.valueOf(teamUser.getUserId())).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
        String teamUserName = productShiftInVO.getTeamUsers().stream().distinct().map(teamUser -> {
            return userMap.get(Long.valueOf(teamUser.getUserId())) + "(" + teamUser.getRoleName() + ")";
        }).collect(Collectors.joining(","));
        productTicketPO.setTeamUsers(teamUserName);
        productTicketService.updateById(productTicketPO);

        // 更新班组信息
        LambdaQueryWrapper<ProductShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductShiftPO::getProductTicketId,productShiftInVO.getProductTicketId())
                .eq(ProductShiftPO::getDeleted, BooleanEnum.FALSE.getCode());
        List<ProductShiftPO> productShiftPOS = list(lambdaQueryWrapper);

        Map<String, ProductShiftPO> existingMap = productShiftPOS.stream()
                .collect(Collectors.toMap(ProductShiftPO::getTeamUsers, vo -> vo));
        for (ProductShiftInVO.TeamUser teamUser : productShiftInVO.getTeamUsers()) {
            ProductShiftPO productShiftPO = existingMap.get(teamUser.getUserId());
            if (productShiftPO == null) {
                // 插入操作
                productShiftPO = new ProductShiftPO();
                productShiftPO.setCompanyCode(productTicketPO.getCompanyCode());
                productShiftPO.setProductTicketId(productTicketPO.getId());
                productShiftPO.setProduceDate(productTicketPO.getProduceDate());
                productShiftPO.setShift(productTicketPO.getShift());
                productShiftPO.setRoleCode(teamUser.getRoleCode());
                productShiftPO.setRoleName(teamUser.getRoleName());
                productShiftPO.setTeamUsers(teamUser.getUserId());
                productShiftPO.setCreateBy(SecurityUtil.getUserId());
                save(productShiftPO);
            } else {
                productShiftPO.setRoleName(teamUser.getRoleName());
                productShiftPO.setRoleCode(teamUser.getRoleCode());
                productShiftPO.setUpdateBy(SecurityUtil.getUserId());
                updateById(productShiftPO);
            }
        }

        // 查找需要删除的记录
        for (ProductShiftPO productShiftPO : productShiftPOS) {
            if (productShiftInVO.getTeamUsers().stream()
                .noneMatch(teamUser -> teamUser.getUserId().equals(productShiftPO.getTeamUsers()))) {
                removeById(productShiftPO.getId());
            }
        }
    }

    @Override
    public List<GetRoleListsOutVO> getRoleLists() {
        List<GzcblTPO> roleLists = gzcblTService.getRoleLists();
        return roleLists.stream().map(role -> {
            GetRoleListsOutVO getRoleListsOutVO = new GetRoleListsOutVO();
            getRoleListsOutVO.setRoleCode(role.getGzcbl002());
            getRoleListsOutVO.setRoleName(role.getGzcbl004());
            return getRoleListsOutVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GetDefaultRoleListsOutVO> getDefaultRoleLists(GetDefaultRoleListsInVO getDefaultRoleListsInVO) {
        if (CollectionUtil.isEmpty(getDefaultRoleListsInVO.getUserIds())) {
            return Lists.newArrayList();
        }
        Map<String, String> roleMap = getRoleLists().stream()
            .collect(Collectors.toMap(GetRoleListsOutVO::getRoleCode, GetRoleListsOutVO::getRoleName));

        // 更新班组信息
        LambdaQueryWrapper<ProductShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductShiftPO::getProductTicketId,getDefaultRoleListsInVO.getProductTicketId())
                .eq(ProductShiftPO::getDeleted, BooleanEnum.FALSE.getCode());
        List<ProductShiftPO> productShiftPOS = list(lambdaQueryWrapper);
        Map<String, ProductShiftPO> teamMap = productShiftPOS.stream().collect(Collectors.toMap(ProductShiftPO::getTeamUsers, Function.identity()));

        List<Long> userIds = getDefaultRoleListsInVO.getUserIds();
        List<UserDTO> userDTOS = ia01Service.getUserInfoByIds(userIds);
        Map<String, UserDTO> userMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getWorkcode, Function.identity()));
        List<String> workCodes = userDTOS.stream().map(UserDTO::getWorkcode).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(workCodes)) {
            List<OoagTPO> ooagTPOS = ooagTService.getListByWorkNos(workCodes);
            // 将ooagTPOS转换为按工号分组的Map
            Map<String, OoagTPO> ooagMap = ooagTPOS.stream().collect(Collectors.toMap(OoagTPO::getOoag001, Function.identity()));

            // 改为遍历用户列表，确保所有用户都有返回值
            return userDTOS.stream().map(userDTO -> {
                GetDefaultRoleListsOutVO outVO = new GetDefaultRoleListsOutVO();
                outVO.setUserId(userDTO.getId());
                outVO.setUserName(userDTO.getName());
                String userIdStr = String.valueOf(userDTO.getId());

                // 优先使用已配置的班组角色
                if (teamMap.containsKey(userIdStr)) {
                    outVO.setRoleCode(teamMap.get(userIdStr).getRoleCode());
                    outVO.setRoleName(teamMap.get(userIdStr).getRoleName());
                }
                // 其次使用ooagTPO中的角色配置
                else if (ooagMap.containsKey(userDTO.getWorkcode())) {
                    OoagTPO ooagTPO = ooagMap.get(userDTO.getWorkcode());
                    outVO.setRoleCode(ooagTPO.getOoagud003());
                    outVO.setRoleName(roleMap.getOrDefault(ooagTPO.getOoagud003(), ""));
                }
                // 最后保留空值而不是过滤掉记录
                else {
                    outVO.setRoleCode("");
                    outVO.setRoleName("");
                }
                return outVO;
            }).sorted(Comparator.comparing(GetDefaultRoleListsOutVO::getRoleCode)).collect(Collectors.toList());
        }

        return Lists.newArrayList();
    }

    @Override
    public List<ProductShiftInVO.TeamUser> getResponsibleShift(Long planTicketId) {
        // 上道工序
        ProductTicketPO productTicketPO = productTicketService.getById(planTicketId);
        SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        // 首道工序
        if("INIT".equals(sfcbTVO.getSfcb007())) {

        } else {
            // 根据工序 + 工单 获取班组信息
            List<String> processList = Lists.newArrayList();
            processList.add(sfcbTVO.getSfcb007());
            processList.add(productTicketPO.getProcessCode());
            List<Long> ids = productTicketService.getProductShiftByProcess(planTicketId, processList);

            LambdaQueryWrapper<ProductShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(ProductShiftPO::getCompanyCode, productTicketPO.getCompanyCode())
                    .in(ProductShiftPO::getProductTicketId, ids);
            List<ProductShiftPO> productShiftPOS = list(lambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(productShiftPOS)) {
                // 根据用户id去重
                List<ProductShiftInVO.TeamUser> users = productShiftPOS.stream()
                        .map(productShiftPO -> {
                            ProductShiftInVO.TeamUser teamUser = new ProductShiftInVO.TeamUser();
                            teamUser.setUserId(productShiftPO.getTeamUsers());
                            teamUser.setRoleCode(productShiftPO.getRoleCode());
                            teamUser.setRoleName(productShiftPO.getRoleName());
                            return teamUser;
                        })
                        .collect(Collectors.toMap(
                                ProductShiftInVO.TeamUser::getUserId,
                                Function.identity(),
                                (existing, replacement) -> existing))
                        .values()
                        .stream()
                        .collect(Collectors.toList());

                // 赋值名称
                List<Long> userIds = users.stream().map(teamUser -> Long.valueOf(teamUser.getUserId())).collect(Collectors.toList());
                Map<Long, String> userMap =
                        ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
                users.forEach(teamUser -> {
                    teamUser.setUserName(userMap.get(Long.valueOf(teamUser.getUserId())));
                });
                return users;
            }
        }
        return List.of();
    }


    private Pagination<ProductShiftOutVO> getProductShiftOutVOPagination(IPage page, Page<ProductShiftOutVO> iPage) {
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        return Pagination.newInstance(iPage.getRecords().stream().map(productShiftPO -> {
            ProductShiftOutVO productShiftOutVO = BeanUtil.copyProperties(productShiftPO, ProductShiftOutVO.class);
            if (StringUtils.isNotBlank(productShiftOutVO.getTeamUsers()) && !chinesePattern.matcher(productShiftOutVO.getTeamUsersName()).find()) {
                String[] ids = productShiftOutVO.getTeamUsers().split(",");
                List<UserDTO> userInfoByIds =
                        ia01Service.getUserInfoByIds(Arrays.stream(ids).map(i -> Long.valueOf(i)).collect(Collectors.toList()));
                productShiftOutVO
                        .setTeamUsersName(userInfoByIds.stream().map(UserDTO::getName).collect(Collectors.joining(",")));
            }
            return productShiftOutVO;
        }).collect(Collectors.toList()),page);

    }


}
