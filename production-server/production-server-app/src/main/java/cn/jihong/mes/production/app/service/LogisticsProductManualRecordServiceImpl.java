package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.logistics.api.model.response.PalletResponse;
import cn.jihong.mes.production.api.model.po.LogisticsProductManualRecordPO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordPageInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.LogisticsProductManualRecordSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordGroupOutVO;
import cn.jihong.mes.production.api.model.vo.out.LogisticsProductManualRecordOutVO;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mes.production.api.service.ILogisticsProductManualRecordService;
import cn.jihong.mes.production.api.service.ISysDictService;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.mapper.LogisticsProductManualRecordMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流产品手动出站记录服务实现类
 * 
 * <AUTHOR>
 * @date 2024-03-30
 */
@Slf4j
@Service
public class LogisticsProductManualRecordServiceImpl 
    extends JiHongServiceImpl<LogisticsProductManualRecordMapper, LogisticsProductManualRecordPO>
    implements ILogisticsProductManualRecordService {

    @Resource
    private ILogisticsService logisticsService;
    @Resource
    private ISysDictService sysDictService;
    
    /**
     * 保存物流产品手动出站记录
     *
     * @param inVO 物流产品手动出站记录入参
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveManualRecord(LogisticsProductManualRecordInVO inVO) {
        LogisticsProductManualRecordPO recordPO = new LogisticsProductManualRecordPO();
        BeanUtil.copyProperties(inVO, recordPO);
        
        // 设置基础字段
        recordPO.setCompanyCode(SecurityUtil.getCompanySite());
        recordPO.setCreateBy(SecurityUtil.getUserId());
        recordPO.setCreateTime(LocalDateTime.now());
        recordPO.setDeleted(0);
        recordPO.setStatus(0); // 默认待处理状态
        
        return save(recordPO);
    }
    
    /**
     * 根据工单号和栈板短码查询记录
     *
     * @param queryInVO 查询参数
     * @return 记录详情
     */
    @Override
    public LogisticsProductManualRecordOutVO getByPlanTicketNoAndShortCode(LogisticsProductManualRecordQueryInVO queryInVO) {
        // 参数校验
        AssertUtil.isNotBlank(queryInVO.getPlanTicketNo(), "工单号不能为空");
        AssertUtil.isNotBlank(queryInVO.getPalletShortCode(), "栈板短码不能为空");
        
        // 查询条件
        LambdaQueryWrapper<LogisticsProductManualRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsProductManualRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(LogisticsProductManualRecordPO::getPlanTicketNo, queryInVO.getPlanTicketNo())
                .eq(LogisticsProductManualRecordPO::getPalletShortCode, queryInVO.getPalletShortCode())
                .eq(LogisticsProductManualRecordPO::getDeleted, 0)
                .orderByDesc(LogisticsProductManualRecordPO::getCreateTime);
        
        // 查询记录
        LogisticsProductManualRecordPO recordPO = getOne(queryWrapper);
        if (recordPO == null) {
            return null;
        }

        List<SysDictOutVO> sysDictOutVOS = sysDictService.listByType("J_PAPERBOARD_TYPE");
        Map<String, String> map =
                sysDictOutVOS.stream().collect(Collectors.toMap(SysDictOutVO::getDictCode, SysDictOutVO::getDictValue));

        LogisticsProductManualRecordOutVO logisticsProductManualRecordOutVO = BeanUtil.copyProperties(recordPO, LogisticsProductManualRecordOutVO.class);
        logisticsProductManualRecordOutVO.setMaterialCategoryName(map.get(recordPO.getMaterialCategory()));
        return logisticsProductManualRecordOutVO;
    }
    
    /**
     * 保存并处理记录
     *
     * @param saveInVO 保存参数
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveAndProcess(LogisticsProductManualRecordSaveInVO saveInVO) {
        AssertUtil.isNotNull(saveInVO.getId(), "记录ID不能为空");
        AssertUtil.isNotBlank(saveInVO.getMeshCode(), "网待编号不能为空");
        AssertUtil.isNotBlank(saveInVO.getMaterialCategory(), "物料类别不能为空");

        // 查询记录
        LogisticsProductManualRecordPO recordPO = getById(saveInVO.getId());
        AssertUtil.isNotNull(recordPO, "找不到对应的记录");
        if (recordPO.getStatus().equals(2)) {
            throw new CommonException("该短码已上网带，请勿重复处理");
        }
        // 赋值
        recordPO.setPlanCount(saveInVO.getPlanCount());
        recordPO.setMaterialCategory(saveInVO.getMaterialCategory());
        recordPO.setPalletCount(saveInVO.getPalletCount());
        recordPO.setStackLength(saveInVO.getStackLength());
        recordPO.setStackWidth(saveInVO.getStackWidth());
        recordPO.setPalletLength(saveInVO.getPalletLength());
        recordPO.setPalletWidth(saveInVO.getPalletWidth());
        recordPO.setPalletColumns(saveInVO.getPalletColumns());
        recordPO.setCustomerShortName(saveInVO.getCustomerShortName());
        recordPO.setProductShortName(saveInVO.getProductShortName());


        // 更新网待编号和状态
        recordPO.setMeshCode(saveInVO.getMeshCode());
        recordPO.setUpdateBy(SecurityUtil.getUserId());
        recordPO.setUpdateTime(LocalDateTime.now());
        updateById(recordPO);
        
        // 调用外部接口处理
        StandardResult standardResult = logisticsService.internetTape(recordPO);
        PalletResponse palletResponse = (PalletResponse)standardResult.getData();
        recordPO.setIdsPalletId(palletResponse.getPalletId());
        // 根据处理结果更新状态
        recordPO.setStatus(2);
        recordPO.setResult("SUCCESS");
        recordPO.setUpdateTime(LocalDateTime.now());
        updateById(recordPO);
        return recordPO.getId().toString();
    }
    
    /**
     * 分页查询分组记录
     *
     * @param logisticsProductManualRecordPageInVO 分页查询参数
     * @return 分页结果
     */
    @Override
    public Pagination<LogisticsProductManualRecordGroupOutVO> pageGroup(LogisticsProductManualRecordPageInVO logisticsProductManualRecordPageInVO) {
        // 查询条件
        LambdaQueryWrapper<LogisticsProductManualRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsProductManualRecordPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(LogisticsProductManualRecordPO::getDeleted, 0)
                .eq(StringUtils.isNotBlank(logisticsProductManualRecordPageInVO.getMeshCode()),
                    LogisticsProductManualRecordPO::getMeshCode, logisticsProductManualRecordPageInVO.getMeshCode())
                .eq(LogisticsProductManualRecordPO::getStatus, 2)
                .orderByDesc(LogisticsProductManualRecordPO::getCreateTime);
        
        // 分页查询
        IPage<LogisticsProductManualRecordPO> page =
                page(logisticsProductManualRecordPageInVO.getPage(), queryWrapper);
        
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        
        // 转换为输出VO
        List<LogisticsProductManualRecordOutVO> records = BeanUtil.copyToList(
                page.getRecords(), LogisticsProductManualRecordOutVO.class);


        // 根据工单号和物料类别分组
        Map<String, List<LogisticsProductManualRecordOutVO>> groupMap = records.stream()
                .collect(Collectors.groupingBy(record -> 
                        record.getPlanTicketNo() + "|" + record.getMaterialCategory()));

        List<SysDictOutVO> sysDictOutVOS = sysDictService.listByType("J_PAPERBOARD_TYPE");
        Map<String, String> map =
            sysDictOutVOS.stream().collect(Collectors.toMap(SysDictOutVO::getDictCode, SysDictOutVO::getDictValue));

        // 构建分组结果
        List<LogisticsProductManualRecordGroupOutVO> groupList = new ArrayList<>();
        groupMap.forEach((key, value) -> {
            if (CollectionUtil.isNotEmpty(value)) {
                LogisticsProductManualRecordOutVO firstRecord = value.get(0);
                
                LogisticsProductManualRecordGroupOutVO groupOutVO = new LogisticsProductManualRecordGroupOutVO();
                groupOutVO.setPlanTicketNo(firstRecord.getPlanTicketNo());
                groupOutVO.setMaterialCategory(firstRecord.getMaterialCategory());
                groupOutVO.setMaterialCategoryName(map.get(firstRecord.getMaterialCategory()));

                groupOutVO.setProductName(firstRecord.getProductName());
                groupOutVO.setRecords(value);
                
                groupList.add(groupOutVO);
            }
        });

        return Pagination.newInstance(groupList.stream().sorted(Comparator.comparing(LogisticsProductManualRecordGroupOutVO::getPlanTicketNo))
                .collect(Collectors.toList()), page.getTotal(), page.getPages());
    }
    

}