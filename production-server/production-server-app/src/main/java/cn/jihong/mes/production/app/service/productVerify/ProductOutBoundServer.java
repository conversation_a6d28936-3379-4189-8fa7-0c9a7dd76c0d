package cn.jihong.mes.production.app.service.productVerify;

import cn.jihong.mes.production.api.model.enums.OutboundVerifyEnum;
import cn.jihong.mes.production.api.model.enums.UnitEnum;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.service.IProductOutboundService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.api.service.IProductVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@Service
public class ProductOutBoundServer {

    @Resource
    private ProductVerifyFactory productVerifyFactory;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductTicketService productTicketService;



    public void verify(Long productTicketId, String planTicketNo, String machineName, BigDecimal producedQuantity) {
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        String unitCode = productOutboundService.getUnitCode(productTicketPO);
        // 当前工序的报工和出站的单位
        String unit = getUnitConversion(unitCode);

        IProductVerifyService productVerifyService =
                productVerifyFactory.getProductVerifyService(OutboundVerifyEnum.BASE_PAPER.getName());
        productVerifyService.verifyOutBount(planTicketNo, machineName, unit,productTicketPO.getProcessCode(),producedQuantity);
    }


    private String getUnitConversion(String unitCode) {
        return UnitEnum.getUnitEnum(unitCode).getStandardCode().toUpperCase();
    }

}
