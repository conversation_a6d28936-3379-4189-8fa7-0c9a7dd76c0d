package cn.jihong.mes.production.app.mapper;


import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.vo.in.GetPalletListPageInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductInfoPageInVO;
import cn.jihong.mes.production.api.model.vo.out.LastPalletOutVO;
import cn.jihong.mes.production.api.model.vo.out.PalletUseOutVO;
import cn.jihong.mes.production.api.model.vo.out.PalletUseRecordOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 上栈板信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface ProductLastPalletMapper extends JiHongMapper<ProductPalletPO> {

    Page<LastPalletOutVO> getLastPalletRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);

    Page<LastPalletOutVO> getLastPalletRecords(IPage page, @Param("productTicketId") Long productTicketId);

    Page<PalletUseOutVO> getPalletUseList(IPage page,
        @Param("getPalletListPageInVO") GetPalletListPageInVO getPalletListPageInVO);

    PalletUseOutVO getPalletUseDetial(@Param("id")Long id);

    Page<PalletUseRecordOutVO> getPalletUseDetialList(IPage page,@Param("productInfoPageInVO") ProductInfoPageInVO productInfoPageInVO);
}
