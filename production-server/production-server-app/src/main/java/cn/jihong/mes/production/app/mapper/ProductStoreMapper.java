package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.vo.in.QueryPalletInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.QueryPalletInfoOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

/**
 * 入库表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface ProductStoreMapper extends JiHongMapper<ProductStorePO> {

    IPage<QueryPalletInfoOutVO> queryPalletInfo(IPage page, @Param("queryPalletInfoInVO") QueryPalletInfoInVO queryPalletInfoInVO);

    IPage<QueryStorageInfoOutVO> queryStorageInfo(IPage page, @Param("queryPalletInfoInVO") QueryPalletInfoInVO queryPalletInfoInVO);

    IPage<QueryStorageInfoOutVO> queryStorageInfoIsAllIn(IPage page, @Param("queryPalletInfoInVO") QueryPalletInfoInVO queryPalletInfoInVO);
}
