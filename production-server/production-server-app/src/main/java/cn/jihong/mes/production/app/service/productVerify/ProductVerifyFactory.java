package cn.jihong.mes.production.app.service.productVerify;

import cn.jihong.mes.production.api.service.IProductVerifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ProductVerifyFactory{

    @Autowired
    private List<IProductVerifyService> productVerifyService;

    private static Map<String,IProductVerifyService> map = new HashMap<>(100);

    @PostConstruct
    public void init(){
        if(productVerifyService == null) {
            return;
        }
        productVerifyService.stream().forEach(productVerifyService->{
            map.put(productVerifyService.getOutboundVerify().getName(),productVerifyService);
        });
    }

    public IProductVerifyService getProductVerifyService(String type) {
        return map.get(type);
    }

}
