package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.dto.ProductMachineMaterialRecordDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 一日一结机台物料消耗记录信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface ProductMachineMaterialRecordMapper extends JiHongMapper<ProductMachineMaterialRecordPO> {

    List<ProductMachineMaterialRecordPO> getConsumptionQuantity(@Param("productMachineMaterialRecordDTO") ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);

    List<ProductMachineMaterialRecordPO> getConsumptionQuantityNeedDistribution(@Param("productMachineMaterialRecordDTO") ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);

    List<ProductMachineMaterialRecordPO> getConsumptionQuantityApp(@Param("productMachineMaterialRecordDTO")ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO);
}
