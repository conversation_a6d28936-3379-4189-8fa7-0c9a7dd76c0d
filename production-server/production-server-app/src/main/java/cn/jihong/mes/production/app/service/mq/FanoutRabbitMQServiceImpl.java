package cn.jihong.mes.production.app.service.mq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.jihong.common.exception.CommonException;
import cn.jihong.mes.production.api.model.constant.RabbitConsts;
import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.service.IRabbitMQService;

@Service
public class FanoutRabbitMQServiceImpl implements IRabbitMQService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public String getRabbitMQType() {
        return RabbitConsts.FANOUT;
    }

    @Override
    public void sendMessage(MessageStructDTO msg) {
        throw new CommonException("暂未实现");
//        rabbitTemplate.convertAndSend(RabbitConsts.DIRECT_MODE_EXCHANGE, RabbitConsts.DIRECT_MODE_QUEUE, msg);
    }


}
