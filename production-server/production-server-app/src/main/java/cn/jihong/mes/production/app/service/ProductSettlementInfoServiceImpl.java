package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.convertor.PageConvertor;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductSettlementInfoPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetProductSettlementInfoListInVO;
import cn.jihong.mes.production.api.service.IProductSettlementInfoService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductSettlementInfoMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 生产工程单结算信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@DubboService
public class ProductSettlementInfoServiceImpl extends JiHongServiceImpl<ProductSettlementInfoMapper, ProductSettlementInfoPO> implements IProductSettlementInfoService {

    @Resource
    private IProductTicketService iProductTicketService;

    @Resource
    private IProductSettlementInfoService iProductSettlementInfoService;

    @Override
    public ProductSettlementInfoPO getOneByProductTickNo(String productTickNo) {
        LambdaQueryWrapper<ProductSettlementInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductSettlementInfoPO::getProductTicketNo,productTickNo);
        return getOne(wrapper);
    }

    @Override
    public void collect(Long productTicketId) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(productTicketId);
        ProductSettlementInfoPO settlementInfoPO = getOneByProductTickNo(productTicketPO.getPlanTicketNo());
        if(Objects.isNull(settlementInfoPO)){
            settlementInfoPO = new ProductSettlementInfoPO();
            BeanUtil.copyProperties(productTicketPO,settlementInfoPO);
            settlementInfoPO.setId(null);
            settlementInfoPO.setProductTicketNo(productTicketPO.getPlanTicketNo());
            settlementInfoPO.setProductDate(productTicketPO.getProduceDate());
            settlementInfoPO.setCreateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
            save(settlementInfoPO);
        }else {
            settlementInfoPO.setEndDate(productTicketPO.getEndDate());
            settlementInfoPO.setPlannedProduct(productTicketPO.getPlannedProduct().add(settlementInfoPO.getPlannedProduct()));
            settlementInfoPO.setRealProduct(productTicketPO.getRealProduct().add(settlementInfoPO.getRealProduct()));
            settlementInfoPO.setUpdateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
            settlementInfoPO.setUpdateTime(new Date());
            updateById(settlementInfoPO);
        }
    }

    @Override
    public Pagination<ProductSettlementInfoPO> getProductSettlementInfoList(GetProductSettlementInfoListInVO inVO) {
        LambdaQueryWrapper<ProductSettlementInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(inVO.getFinish()),ProductSettlementInfoPO::getFinish,inVO.getFinish())
                .eq(StringUtils.isNotBlank(inVO.getCompanyCode()),ProductSettlementInfoPO::getCompanyCode,inVO.getCompanyCode())
                .like(StringUtils.isNotBlank(inVO.getProductTicketNo()),ProductSettlementInfoPO::getProductTicketNo,inVO.getProductTicketNo())
                .like(StringUtils.isNotBlank(inVO.getProductName()),ProductSettlementInfoPO::getProductName,inVO.getProductName());
        wrapper.orderByDesc(ProductSettlementInfoPO::getCreateTime);
        IPage<ProductSettlementInfoPO> ipage = PageConvertor.toPage(inVO.getPageNum().longValue(), inVO.getPageSize().longValue());
        IPage page = page(ipage, wrapper);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null,0,0);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }


    @Override
    public ProductSettlementInfoPO getSingleById(Long id) {
        return getById(id);
    }

    @Override
    public Boolean finish(Long id) {
        ProductSettlementInfoPO productSettlementInfoPO = getById(id);
        if(Objects.equals(productSettlementInfoPO.getFinish(),Integer.parseInt(BooleanEnum.TRUE.getCode()))){
            throw new CommonException("该生产工程单已结单");
        }
        productSettlementInfoPO.setFinish(Integer.parseInt(BooleanEnum.TRUE.getCode()));
        productSettlementInfoPO.setUpdateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
        productSettlementInfoPO.setUpdateTime(new Date());
        return updateById(productSettlementInfoPO);
    }
}
