package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetFlowProcessListInVO;
import cn.jihong.mes.production.api.model.vo.out.GetFlowProcessListOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProcessQuantityByPlanTicketNo;
import cn.jihong.mes.production.api.service.IFlowProcessService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.oa.erp.api.model.vo.GetSortProcessListByTickNoOutVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-18 15:40
 */
@Service
public class FlowProcessServiceImpl implements IFlowProcessService {

    @Resource
    private IProductTicketService iProductTicketService;

    @DubboReference
    private ISfcbTService iSfcbTService;


    @Override
    public List<GetFlowProcessListOutVO> getFlowProcessList(GetFlowProcessListInVO inVO) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(inVO.getProductTicketId());

        List<GetProcessQuantityByPlanTicketNo> processQuantityList = iProductTicketService.getProcessQuantityByPlanTicketNo(productTicketPO.getPlanTicketNo());
        Map<String, GetProcessQuantityByPlanTicketNo> processQuantityMap = processQuantityList.stream().collect(Collectors.toMap(GetProcessQuantityByPlanTicketNo::getProcessCode, Function.identity()));

        List<GetSortProcessListByTickNoOutVO> processListByTickNoList = iSfcbTService.getSortProcessListByTickNo(productTicketPO.getPlanTicketNo());

        List<GetFlowProcessListOutVO> outVOList = BeanUtil.copyToList(processListByTickNoList, GetFlowProcessListOutVO.class);
        outVOList.forEach(out->{
            GetProcessQuantityByPlanTicketNo processQuantityByPlanTicketNo = processQuantityMap.get(out.getProcessCode());
            if(processQuantityByPlanTicketNo!=null) {
                out.setTotalGoodProductQuantity(processQuantityByPlanTicketNo.getTotalGoodProductQuantity());
                out.setUnit(processQuantityByPlanTicketNo.getUnit());
                out.setReportTime(processQuantityByPlanTicketNo.getReportTime());
            }
        });
        return outVOList.stream().sorted(Comparator.comparing(GetFlowProcessListOutVO::getSeq)).collect(Collectors.toList());
    }
}
