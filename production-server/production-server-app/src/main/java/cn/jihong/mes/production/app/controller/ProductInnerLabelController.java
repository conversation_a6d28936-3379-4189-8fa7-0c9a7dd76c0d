package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.TemplateInfoDTO;
import cn.jihong.mes.production.api.model.vo.in.ApplyProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductInnerLabelInVO;
import cn.jihong.mes.production.api.model.vo.in.GetTemplateInfosInVO;
import cn.jihong.mes.production.api.model.vo.out.ApplyProductInnerLabelOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetErpTicketInfoOutVO;
import cn.jihong.mes.production.api.service.IProductInnerLabelService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 生产内标签表
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@RestController
@RequestMapping("/pc/productInnerLabel")
@ShenyuSpringMvcClient(path = "/pc/productInnerLabel/**")
public class ProductInnerLabelController {

    @Resource
    private IProductInnerLabelService productInnerLabelService;


    /**
     * 申请内标签
     *
     * @param applyProductInnerLabelInVO
     * @return
     */
    @PostMapping("/applyProductInnerLabel")
    public StandardResult
    applyBoxBarcodeDetail(@RequestBody @Valid ApplyProductInnerLabelInVO applyProductInnerLabelInVO) {
        String LOCK_KEY = RedisCacheConstant.BOX_APPLY + applyProductInnerLabelInVO.getPlanTicketNo();
        productInnerLabelService.applyProductInnerLabel(LOCK_KEY, applyProductInnerLabelInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


    /**
     * 查询内标签列表
     *
     * @param getProductInnerLabelInVO
     * @return
     */
    @PostMapping("/getList")
    public StandardResult<Pagination<ApplyProductInnerLabelOutVO>>
    getList(@RequestBody @Valid GetProductInnerLabelInVO getProductInnerLabelInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInnerLabelService.getList(getProductInnerLabelInVO));
    }


    /**
     * 保存模板信息
     *
     * @param templateInfoDTO
     * @return
     */
    @PostMapping("/saveTemplateInfo")
    public StandardResult
    saveTemplateInfo(@RequestBody @Valid TemplateInfoDTO templateInfoDTO) {
        productInnerLabelService.saveTemplateInfo(templateInfoDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 更新模板信息
     * @param templateInfoDTO
     * @return
     */
    @PostMapping("/updateTemplateInfo")
    public StandardResult
    updateTemplateInfo(@RequestBody @Valid TemplateInfoDTO templateInfoDTO) {
        productInnerLabelService.updateTemplateInfo(templateInfoDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 删除模板信息
     * @param templateInfoDTO
     * @return
     */
    @PostMapping("/deleteTemplateInfo")
    public StandardResult
    deleteTemplateInfo(@RequestBody @Valid TemplateInfoDTO templateInfoDTO) {
        productInnerLabelService.deleteTemplateInfo(templateInfoDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }


    /**
     * 获取模板信息
     *
     * @param templateInfoDTO
     * @return
     */
    @PostMapping("/getTemplateInfo")
    public StandardResult<TemplateInfoDTO>
    getTemplateInfo(@RequestBody @Valid TemplateInfoDTO templateInfoDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productInnerLabelService.getTemplateInfo(templateInfoDTO));
    }

    /**
     * 获取模板信息列表
     *
     * @param getTemplateInfosInVO
     * @return
     */
    @PostMapping("/getTemplateInfos")
    public StandardResult<Pagination<TemplateInfoDTO>>
    getTemplateInfos(@RequestBody @Valid GetTemplateInfosInVO getTemplateInfosInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productInnerLabelService.getTemplateInfos(getTemplateInfosInVO));
    }


    /**
     * 获得工单信息
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getErpTicketDetailInfo/{planTicketNo}")
    public StandardResult<GetErpTicketInfoOutVO> getTicketDetailInfo(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productInnerLabelService.getTicketDetailInfo(planTicketNo));
    }

    /**
     * 计算日期
     * @return
     */
    @GetMapping("/getDate/{date}/{month}/{day}")
    public StandardResult<String> getDate(@PathVariable String date,
                                          @PathVariable String month,
                                          @PathVariable String day) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productInnerLabelService.getDate(date,month,day));
    }

}

