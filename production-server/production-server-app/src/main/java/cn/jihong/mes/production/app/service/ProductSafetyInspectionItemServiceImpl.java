package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductSafetyInspectionItemPO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemQueryInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductSafetyInspectionItemSaveInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductSafetyInspectionItemOutVO;
import cn.jihong.mes.production.api.service.IProductSafetyInspectionItemService;
import cn.jihong.mes.production.app.mapper.ProductSafetyInspectionItemMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 安全点检项配置 服务实现
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
public class ProductSafetyInspectionItemServiceImpl
    extends JiHongServiceImpl<ProductSafetyInspectionItemMapper, ProductSafetyInspectionItemPO>
    implements IProductSafetyInspectionItemService {

    @Override
    public Pagination<ProductSafetyInspectionItemOutVO> page(ProductSafetyInspectionItemQueryInVO inVO) {
        LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionItemPO::getCompanyCode, getCompanyCode())
            .like(StringUtils.isNotBlank(inVO.getInspectionCode()), ProductSafetyInspectionItemPO::getInspectionCode,
                inVO.getInspectionCode())
            .like(StringUtils.isNotBlank(inVO.getInspectionName()), ProductSafetyInspectionItemPO::getInspectionName,
                inVO.getInspectionName())
            .eq(inVO.getStatus() != null, ProductSafetyInspectionItemPO::getStatus, inVO.getStatus())
            .orderByAsc(ProductSafetyInspectionItemPO::getSortOrder);

        IPage<ProductSafetyInspectionItemPO> page =
            page(new Page<>(inVO.getPageNum(), inVO.getPageSize()), queryWrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        List<ProductSafetyInspectionItemOutVO> productSafetyInspectionItemOutVOS =
            BeanUtil.copyToList(page.getRecords(), ProductSafetyInspectionItemOutVO.class);
        return Pagination.newInstance(productSafetyInspectionItemOutVOS, page.getTotal(), page.getPages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ProductSafetyInspectionItemSaveInVO inVO) {
        ProductSafetyInspectionItemPO po = new ProductSafetyInspectionItemPO();
        BeanUtil.copyProperties(inVO, po);

        // 设置公司编码
        po.setCompanyCode(getCompanyCode());

        // 新增或修改
        if (inVO.getId() != null) {
            // 检查记录是否存在
            LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductSafetyInspectionItemPO::getId, inVO.getId())
                .eq(ProductSafetyInspectionItemPO::getCompanyCode, getCompanyCode());
            ProductSafetyInspectionItemPO existPO = getOne(queryWrapper);
            if (existPO == null) {
                return false;
            }

            return updateById(po);
        } else {
            // 检查编码是否重复
            LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductSafetyInspectionItemPO::getCompanyCode, getCompanyCode())
                .eq(ProductSafetyInspectionItemPO::getInspectionCode, inVO.getInspectionCode());
            if (count(queryWrapper) > 0) {
                throw new RuntimeException("点检项编码已存在");
            }

            return save(po);
        }
    }

    @Override
    public ProductSafetyInspectionItemOutVO getDetail(Long id) {
        LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionItemPO::getId, id).eq(ProductSafetyInspectionItemPO::getCompanyCode,
            getCompanyCode());

        ProductSafetyInspectionItemPO po = getOne(queryWrapper);
        if (po == null) {
            return null;
        }

        return BeanUtil.copyProperties(po, ProductSafetyInspectionItemOutVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionItemPO::getId, id).eq(ProductSafetyInspectionItemPO::getCompanyCode,
            getCompanyCode());

        return remove(queryWrapper);
    }

    @Override
    public List<ProductSafetyInspectionItemOutVO> getEnableList() {
        LambdaQueryWrapper<ProductSafetyInspectionItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSafetyInspectionItemPO::getCompanyCode, getCompanyCode())
            .eq(ProductSafetyInspectionItemPO::getStatus, 1).orderByAsc(ProductSafetyInspectionItemPO::getSortOrder);

        List<ProductSafetyInspectionItemPO> list = list(queryWrapper);

        return BeanUtil.copyToList(list, ProductSafetyInspectionItemOutVO.class);
    }

    /**
     * 获取当前公司编码
     */
    private String getCompanyCode() {
        return SecurityUtil.getCompanySite();
    }
}