package cn.jihong.mes.production.app.controller.logistics;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.jihong.logistics.api.model.request.GetFOrderToErpRequest;
import cn.jihong.logistics.api.model.request.PostOrderRequest;
import cn.jihong.logistics.api.model.response.ApiOrderCarryInstructionResponse;
import cn.jihong.logistics.api.model.response.GetFOrderToErpResponse;
import cn.jihong.logistics.api.model.response.PostOrderResponse;
import cn.jihong.mes.production.api.model.vo.in.logistics.*;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetDeviceOrderCarrayPauseStatusOutVO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.logistics.api.model.response.ApiMeshDeviceScadaltemResponse;
import cn.jihong.mes.production.api.model.vo.in.GetErpOrderIdListInVO;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;

/**
 * 物流接口
 * <AUTHOR>
 * @date 2025-03-10 15:58
 */
@RestController
@RequestMapping("/logistics")
@ShenyuSpringMvcClient(path = "/logistics/**")
public class LogisticsController {

    @Resource
    private ILogisticsService iLogisticsService;

    /**
     * 通过设备ID 获取供料计划执行状态  0：开启 1：暂停 2：无供料计划
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.OutboundStackerOutVO>>
     */
    @PostMapping("/getDeviceOrderCarrayPauseStatus")
    public StandardResult<List<GetDeviceOrderCarrayPauseStatusOutVO>> getDeviceOrderCarrayPauseStatus(@RequestBody @Valid GetDeviceOrderCarrayPauseStatusInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.getDeviceOrderCarrayPauseStatus(inVO));
    }

    /**
     * 通过设备 ID 开始供料计划
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-10 16:04
     */
    @PostMapping("/startCallMaterial}")
    public StandardResult<Boolean> startCallMaterial(@RequestBody @Valid StartCallMaterialInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.startCallMaterial(inVO));
    }

    /**
     * 通过设备 ID 暂停供料计划
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-12 14:08
     */
    @PostMapping("/pauseCallMaterial")
    public StandardResult<Boolean> pauseCallMaterial(@RequestBody @Valid PauseCallMaterialInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.pauseCallMaterial(inVO));
    }

    /**
     * 根据目标网带ID 获取供料计划列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.logistics.api.model.response.ApiOrderCarryInstructionResponse>>
     * <AUTHOR>
     * @date: 2025-03-13 15:11
     */
    @PostMapping("/getInstructionByTargetDeviceId")
    public StandardResult<List<ApiOrderCarryInstructionResponse>> getInstructionByTargetDeviceId(@RequestBody @Valid GetInstructionByTargetDeviceIdInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.getInstructionByTargetDeviceId(inVO));
    }


    /**
     * 取消供料计划
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-12 14:08
     */
    @PostMapping("/cancelOrderCarry")
    public StandardResult<Boolean> cancelOrderCarry(@RequestBody @Valid CancelOrderCarryInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.cancelOrderCarry(inVO));
    }

    /**
     * 获取erpOrderId
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-10 16:04
     */
    @PostMapping("/getErpOrderIdList")
    public StandardResult<List<String>> getErpOrderIdList(@RequestBody @Valid GetErpOrderIdListInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.getErpOrderIdList(inVO));
    }


    /**
     * 获取生产任务的可退料网带列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.logistics.api.model.response.ApiMeshDeviceScadaltemResponse>>
     * <AUTHOR>
     * @date: 2025-03-12 10:36
     */
    @PostMapping("/returnOfMaterialMeshList")
    public StandardResult<List<ApiMeshDeviceScadaltemResponse>> returnOfMaterialMeshList(@RequestBody @Valid ReturnOfMaterialMeshListInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.returnOfMaterialMeshList(inVO));
    }

    /**
     * 手动退料
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-12 10:48
     */
    @PostMapping("/returnOfMaterial")
    public StandardResult<Boolean> returnOfMaterial(@RequestBody @Valid ReturnOfMaterialInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.returnOfMaterial(inVO));
    }

    /**
     * 瓦线DCS订单API
     * @param request
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     * <AUTHOR>
     * @date: 2025-03-12 10:48
     */
    @PostMapping("/getFOrderToErp")
    public StandardResult<List<GetFOrderToErpResponse>> getFOrderToErp(@RequestBody @Valid GetFOrderToErpRequest request){
        return StandardResult.resultCode(OperateCode.SUCCESS,iLogisticsService.getFOrderToErp(request));
    }



}
