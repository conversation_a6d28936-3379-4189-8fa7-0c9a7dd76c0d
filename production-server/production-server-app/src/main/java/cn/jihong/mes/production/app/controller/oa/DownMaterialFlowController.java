package cn.jihong.mes.production.app.controller.oa;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.jihong.mes.production.api.model.vo.in.VerifyMaterialOverClaimRequestInVO;
import cn.jihong.mes.production.api.model.vo.out.VerifyMaterialOverClaimRequestOutVO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.DownMaterialFlowDTO;
import cn.jihong.mes.production.api.model.enums.ProductionBusinessType;
import cn.jihong.mes.production.api.model.po.ProductMaterialOperationRecordsPO;
import cn.jihong.mes.production.api.model.vo.in.GetSubmitWorkflowRequestInVO;
import cn.jihong.mes.production.api.service.IProductMaterialOperationRecordsService;
import cn.jihong.mes.production.app.service.ProductMaterialOperationRecordsServiceImpl;
import cn.jihong.oa.approve.api.model.dto.param.newflow.OaMaterialOverClaimDTO;
import cn.jihong.workflow.common.biz.IWorkflowAttachmentService;
import cn.jihong.workflow.common.controller.BaseWorkflowActionController;
import cn.jihong.workflow.common.service.IWfService;

/**
 * 下料-超领单OA流程
 * <AUTHOR>
 * @date 2024-09-11 11:35
 */
@RestController
@RequestMapping("/downMaterialFlow")
@ShenyuSpringMvcClient(path = "/downMaterialFlow/**")
public class DownMaterialFlowController extends BaseWorkflowActionController<ProductMaterialOperationRecordsPO, DownMaterialFlowDTO, OaMaterialOverClaimDTO, ProductMaterialOperationRecordsServiceImpl> {


    @Resource
    private IProductMaterialOperationRecordsService iProductMaterialOperationRecordsService;


    @Autowired
    public DownMaterialFlowController(IWfService wfService, ProductMaterialOperationRecordsServiceImpl service, IWorkflowAttachmentService workflowAttachmentService) {
        super(wfService, service, workflowAttachmentService);
    }

    @Override
    public Long getWorkflowId() {
        return ProductionBusinessType.MATERIAL_OVER_CLAIM.getWorkflowid();
    }


    /**
     * 发起OA超领料流程
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.workflow.common.model.request.SubmitWorkflowRequest>
     * <AUTHOR>
     * @date: 2024-09-12 18:15
     */
    @PostMapping("/sendSubmitWorkflowRequest")
    public StandardResult<Object>  sendSubmitWorkflowRequest(@RequestBody @Valid GetSubmitWorkflowRequestInVO inVO){
        return iProductMaterialOperationRecordsService.sendSubmitWorkflowRequest(inVO.getProductMaterialOperationRecordId(),inVO.getReason());
    }

    /**
     * 最终批准
     */
    @PostMapping("/finalApprove")
    public StandardResult finalApprove(@RequestBody @Valid DownMaterialFlowDTO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductMaterialOperationRecordsService.finalApprove(inVO));
    }

    /**
     * 验证下料申请
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.VerifyInboundRequestOutVO>
     * <AUTHOR>
     * @date: 2025-05-15 16:45
     */
    @PostMapping("/verifyMaterialOverClaimRequest")
    public StandardResult<VerifyMaterialOverClaimRequestOutVO> verifyMaterialOverClaimRequest(@RequestBody @Valid VerifyMaterialOverClaimRequestInVO inVO){
        return StandardResult.ok(iProductMaterialOperationRecordsService.verifyMaterialOverClaimRequest(inVO));
    }

}
