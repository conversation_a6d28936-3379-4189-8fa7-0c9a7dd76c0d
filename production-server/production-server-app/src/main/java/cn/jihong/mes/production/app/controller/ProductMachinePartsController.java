package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.po.ProductMachinePartsPO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.UpdateProductMachinePartsInVO;
import cn.jihong.mes.production.api.service.IProductMachinePartsService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 任务机位信息 前端控制器
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@RestController
@RequestMapping("/productMachineParts")
@ShenyuSpringMvcClient(path = "/productMachineParts/**")
public class ProductMachinePartsController {


    @Resource
    private IProductMachinePartsService productMachinePartsService;

    /**
     * 获得任务机位 与  用途的映射关系
     */
    @PostMapping("/getProductMachineParts")
    public StandardResult<Pagination<ProductMachinePartsPO>> getProductMachineParts(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachinePartsService.getProductMachineParts(productTicketPageInVO));
    }

    /**
     * 更新任务机位 与  用途的映射关系
     */
    @PostMapping("/updateProductMachineParts")
    public StandardResult updateProductMachineParts(@RequestBody UpdateProductMachinePartsInVO invo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachinePartsService.updateProductMachineParts(invo));
    }

    /**
     * 新增接口 任务机位 与  用途的映射关系
     * @param productMachinePartsPO
     * @return
     */
    @PostMapping("/addProductMachineParts")
    public StandardResult addProductMachineParts(@RequestBody ProductMachinePartsPO productMachinePartsPO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachinePartsService.addProductMachineParts(productMachinePartsPO));
    }

    /**
     * 删除接口 任务机位 与  用途的映射关系
     * @param id
     * @return
     */
    @GetMapping("/deleteProductMachineParts/{id}")
    public StandardResult deleteProductMachineParts(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productMachinePartsService.deleteById(id));
    }
}

