package cn.jihong.mes.production.app.service;

import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.enums.ProductConfigBusinessTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductConfigPO;
import cn.jihong.mes.production.api.service.IProductConfigService;
import cn.jihong.mes.production.app.mapper.ProductConfigMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * 生产配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@DubboService
public class ProductConfigServiceImpl extends JiHongServiceImpl<ProductConfigMapper, ProductConfigPO> implements IProductConfigService {

    @Override
    public ProductConfigPO getProductConfig(String businessType) {
        LambdaQueryWrapper<ProductConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductConfigPO::getBusinessType, businessType)
                .eq(ProductConfigPO::getCompanyCode, SecurityUtil.getCompanySite());
        return getOne(lambdaQueryWrapper);
    }

//    @Override
//    public Integer getBillingType() {
//        // 默认一单一结
//        Integer billingType = ProductConfigBillingTypeEnum.ONE_DAY.getIntCode();
//        ProductConfigPO productConfig = getProductConfig(ProductConfigBusinessTypeEnum.BILLING_TYPE.getCode());
//        if (productConfig != null) {
//            billingType = productConfig.getBillingType();
//        }
//        return billingType;
//    }

    @Override
    public String getMinimumVersion() {
        // 默认一单一结
        ProductConfigPO productConfig = getProductConfig(ProductConfigBusinessTypeEnum.VERSION.getCode());
        if (productConfig != null) {
            return productConfig.getVersion();
        }
        return "1.0.0";
    }

    @Override
    public List<ProductConfigPO> getAllMinimumVersion() {
        LambdaQueryWrapper<ProductConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductConfigPO::getBusinessType, ProductConfigBusinessTypeEnum.VERSION.getCode());
        return list(lambdaQueryWrapper);
    }


}
