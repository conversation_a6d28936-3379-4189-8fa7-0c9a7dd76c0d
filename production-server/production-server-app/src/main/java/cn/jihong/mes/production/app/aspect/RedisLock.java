package cn.jihong.mes.production.app.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisLock {
    String value() default "";

    long expire() default 300L; // 锁的过期时间，默认300秒

    TimeUnit timeUnit() default TimeUnit.SECONDS; // 过期时间的单位，默认秒
}
