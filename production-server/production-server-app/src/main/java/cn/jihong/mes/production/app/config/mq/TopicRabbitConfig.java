package cn.jihong.mes.production.app.config.mq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.jihong.mes.production.api.model.constant.RabbitConsts;


@Configuration
public class TopicRabbitConfig {


    @Bean
    public Queue TopicQueue() {
        return new Queue(RabbitConsts.TOPIC_MODE_QUEUE);
    }
 

    @Bean
    TopicExchange TopicExchange() {
        return new TopicExchange(RabbitConsts.TOPIC_MODE_EXCHANGE);
    }
 
 
    //将队列和交换机绑定,而且绑定的键值为 key
    @Bean
    Binding bindingTopic() {
        return BindingBuilder.bind(TopicQueue()).to(TopicExchange()).with(RabbitConsts.TOPIC_ROUTING_KEY);
    }
 

 
}
