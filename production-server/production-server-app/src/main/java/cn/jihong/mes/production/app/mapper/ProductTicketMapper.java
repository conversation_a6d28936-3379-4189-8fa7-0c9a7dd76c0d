package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO;
import cn.jihong.mes.production.api.model.dto.GetOeeDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 生产工单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface ProductTicketMapper extends JiHongMapper<ProductTicketPO> {

    ProductTicketShiftOutVO getPlanTicketNoList(@Param("machineName") String machineName,
                                                @Param("produceDate") Date produceDate, @Param("shift") Integer shift,
                                                @Param("planTicketNo") String planTicketNo, @Param("companyCode") String companyCode,
                                                @Param("processCode") String processCode,
                                                @Param("processType") String processType
                                                );

    Page<ProductionTicketInfoOutVO> getTicketByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);


    List<GetProductTicketInfoListOutVO> getProductTicketInfoList(@Param("machineName") String machineName,@Param("produceDate") String produceDate,@Param("shift") Integer shift);


    /**
     * 获得生产日报（工厂）
     *
     * @param page
     * @param getProductReportByFactoryInVO
     * @return
     */
    Page<GetProductReportByFactoryOutVO> getReportByFactory(IPage page,
        @Param("getProductReportByFactoryInVO") GetProductReportByFactoryInVO getProductReportByFactoryInVO);

    List<GetProduceDetailsOutVO> getProduceDetailsCalibreReview(@Param("planTicketNo") String planTicketNo);

    List<GetMaterialDetailsOutVO> getMaterialDetailsCalibreReview(@Param("planTicketNo") String planTicketNo);

    Page<GetMaterialUseDetailsOutVO> getMaterialUseDetails(@Param("page") IPage page,
        @Param("getMaterialUseDetailsInVO") GetMaterialUseDetailsInVO getMaterialUseDetailsInVO);

    List<GetMachineGroupReportOutVO>
        getMachineGroupReport(@Param("getMachineGroupReportInVO") GetMachineGroupReportInVO getMachineGroupReportInVO,@Param("companyCode") String companyCode);

    Page<GetProductReportByFactoryOutVO> getProductPlanReport(IPage page, @Param("getProductReportByFactoryInVO")GetProductReportByFactoryInVO getProductReportByFactoryInVO);

    List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummary(@Param("machineName") String machineName,
                                                         @Param("produceDate") Date produceDate,
                                                         @Param("shift") Integer shift,
                                                         @Param("companyCode") String companyCode
    );

    List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummaryDetail(@Param("machineName") String machineName,
                                                               @Param("produceDate") Date produceDate,
                                                               @Param("shift") Integer shift,
                                                               @Param("companyCode") String companyCode);

    List<DeviceOEEReportDTO> deviceOEEReportShow(@Param("companyCode") String companyCode,
                                                 @Param("machineName") String machineName,
                                                 @Param("processName") String processName,
                                                 @Param("produceYearMonth") String produceYearMonth);

    List<GetOeeBaseDataOutVO> getOeeBaseData(@Param("dto") GetOeeDTO dto);

    GetOeeOutVO getOee(@Param("dto") GetOeeDTO dto);

    Page<ProductionTicketInfoOutVO> selectProductTicketPage(IPage page, @Param("vo") GetProductTicketListInVO getProductTicketListInVO);

    List<GetProcessQuantityByPlanTicketNo> getProcessQuantityByPlanTicketNo(@Param("planTicketNo") String planTicketNo,
                                                                            @Param("companyCode") String companyCode);


}
