package cn.jihong.mes.production.app.mapper;


import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsPO;
import cn.jihong.mes.production.api.model.vo.in.GetListByDefectiveSourceNameInVO;
import cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefectListByTicketNoAndProcessOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 不良品记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Repository
public interface ProductDefectiveProductsMapper extends JiHongMapper<ProductDefectiveProductsPO> {

    Page<DefectiveProductsInfoOutVO> getDefectiveProductsRecords(IPage page,
                                                                 @Param("productTicketId") Long productTicketId,@Param("companyCode") String companyCode);

    Page<DefectiveProductsInfoOutVO> getDefectiveProductsRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);

    List<GetListByDefectiveSourceNameOutVO> getListByDefectiveSourceName(@Param("inVO") GetListByDefectiveSourceNameInVO inVO);

    List<GetDefectListByTicketNoAndProcessOutVO> getDefectListByTicketNoAndProcess(String productTicketNo);


}
