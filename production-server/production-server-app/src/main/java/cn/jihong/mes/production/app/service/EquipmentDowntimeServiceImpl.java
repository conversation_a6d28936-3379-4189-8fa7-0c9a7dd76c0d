package cn.jihong.mes.production.app.service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.jihong.mes.production.api.model.dto.GetOeeDTO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetOeeBaseEquipmentDowntime;
import cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByProcessNameOutVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.dto.GetOeeDTO;
import cn.jihong.mes.production.api.model.po.EquipmentDowntimePO;
import cn.jihong.mes.production.api.model.po.EquipmentDowntimeProcessPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeProcessService;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.EquipmentDowntimeMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByProcessNameOutVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 设备停机代码表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@DubboService
public class EquipmentDowntimeServiceImpl extends JiHongServiceImpl<EquipmentDowntimeMapper, EquipmentDowntimePO> implements IEquipmentDowntimeService {

    @Resource
    private IEquipmentDowntimeProcessService iEquipmentDowntimeProcessService;

    @Resource
    private IProductTicketService iProductTicketService;

    @DubboReference
    private IOocqlTService iOocqlTService;

    @Resource
    private IProductionMachineService iProductionMachineService;


    @Override
    public Boolean saveEquipmentDowntime(EquipmentDowntimeInVO inVO){
        EquipmentDowntimePO po = new EquipmentDowntimePO();
        BeanUtil.copyProperties(inVO,po);
        po.setCompanyCode(SecurityUtil.getCompanySite());
        po.setCreateBy(SecurityUtil.getUserId());
        po.setCreateTime(LocalDateTime.now());
        save(po);
        return saveDownTimeProcess(po.getId(),inVO);
    }

    private Boolean saveDownTimeProcess(Long equipmentDowntimeId,EquipmentDowntimeInVO inVO) {
        if (inVO.getCodeType() == 2) {
            AssertUtil.isNotBlank(inVO.getProcessName(), "专用工序名称不能为空");
            List<GetOocqlTByProcessNameOutVO> processNameOutVOList = iOocqlTService.getOocqlTByProcessName(SecurityUtil.getCompanySite(), Collections.singletonList(inVO.getProcessName()));
            if(CollectionUtil.isEmpty(processNameOutVOList) || StringUtils.isBlank(processNameOutVOList.get(0).getOocq002())) {
                // 专用工序
                throw new CommonException("erp查询不到专用工序编码");
            }
            EquipmentDowntimeProcessInVO downtimeProcessInVO = new EquipmentDowntimeProcessInVO();
            downtimeProcessInVO.setEquipmentDowntimeId(equipmentDowntimeId);
            downtimeProcessInVO.setProcessCode(processNameOutVOList.get(0).getOocq002());
            downtimeProcessInVO.setProcessName(inVO.getProcessName());
            return iEquipmentDowntimeProcessService.saveEquipmentDowntimeProcess(downtimeProcessInVO);
        }
        return true;
    }

    @Override
    public Boolean editEquipmentDowntime(EquipmentDowntimeInVO inVO){
        EquipmentDowntimePO po = getById(inVO.getId());
        BeanUtil.copyProperties(inVO,po);
        po.setUpdateBy(SecurityUtil.getUserId());
        po.setUpdateTime(LocalDateTime.now());
        updateById(po);

        // 设备停机和工序关联表
        List<EquipmentDowntimeProcessPO> equipmentDowntimeProcessPOList = iEquipmentDowntimeProcessService.getListByEquipmentDowntimeId(po.getId());
        if(CollectionUtil.isNotEmpty(equipmentDowntimeProcessPOList)){
            iEquipmentDowntimeProcessService.removeByIds(equipmentDowntimeProcessPOList.stream().map(EquipmentDowntimeProcessPO::getId).collect(Collectors.toList()));
        }
        return saveDownTimeProcess(po.getId(),inVO);
    }

    @Override
    public Boolean deleteByIds(String ids){
        if (StringUtils.isBlank(ids)) {
            throw new CommonException("删除条件id不能为空");
        }
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        return removeByIds(idList);
    }


    @Override
    public EquipmentDowntimeOutVO getSingleEquipmentDowntimeById(Long id){
        if (ObjectUtils.isNull(id) || id <= 0L) {
            throw new CommonException("id不能为空！");
        }
        return BeanUtil.copyProperties(getById(id),EquipmentDowntimeOutVO.class);
    }

    @Override
    public EquipmentDowntimeOutVO getSingleEquipmentDowntimeByCode(String code) {
        LambdaQueryWrapper<EquipmentDowntimePO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentDowntimePO::getDowntimeCode,code);
        List<EquipmentDowntimePO> list = list(wrapper);
        return CollectionUtil.isNotEmpty(list)?BeanUtil.copyProperties(list.get(0),EquipmentDowntimeOutVO.class):null;
    }

    @Override
    public List<EquipmentDowntimeOutVO> getListEquipmentDowntimeByCodes(List<String> codeList) {
        LambdaQueryWrapper<EquipmentDowntimePO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentDowntimePO::getDowntimeCode,codeList);
        List<EquipmentDowntimePO> list = list(wrapper);
        return CollectionUtil.isNotEmpty(list)?BeanUtil.copyToList(list,EquipmentDowntimeOutVO.class):null;
    }

    @Override
    public Pagination<EquipmentDowntimeOutVO> getEquipmentDowntimePage(EquipmentDowntimePageInVO inVO){
        Page<EquipmentDowntimeOutVO> page = baseMapper.getEquipmentDowntimePage(inVO.getPage(), inVO);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }

    @Override
    public List<EquipmentDowntimeOutVO> getEquipmentDowntimeList(EquipmentDowntimeListInVO inVO) {
        String processCode = "";
        if(inVO.getProductTicketId()!=null){
            ProductTicketPO productTicketPO = iProductTicketService.getById(inVO.getProductTicketId());
            processCode = productTicketPO.getProcessCode();
        }
        return baseMapper.getEquipmentDowntimeList(inVO,processCode);
    }

    @Override
    public List<GetOeeBaseEquipmentDowntime> getOeeBaseEquipmentDowntimeList(GetOeeDTO dto) {
        return baseMapper.getOeeBaseEquipmentDowntimeList(dto);
    }

    @Override
    public List<EquipmentDowntimeOutVO> getEquipmentDowntimeOeeList(EquipmentDowntimeOeeListInVO inVO) {
        List<ProductionMachinePO> machinePOList = iProductionMachineService.getByMachineName(inVO.getMachineName());
        if(CollectionUtil.isEmpty(machinePOList)){
            throw new CommonException("mes尚未存在该机台，请先在机台管理下的机台列表同步机台");
        }
        String processCode = machinePOList.get(0).getProcessCode();
        return baseMapper.getEquipmentDowntimeOeeList(inVO,processCode);
    }

    @Override
    public List<GetOeeEquipmentDowntime> getOeeEquipmentDowntimeList(GetOeeDTO dto) {
        return baseMapper.getOeeEquipmentDowntimeList(dto);
    }
}
