package cn.jihong.mes.production.app.service;

import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.BarCodeTreePO;
import cn.jihong.mes.production.api.model.po.PackingBoxCodeCheckPO;
import cn.jihong.mes.production.api.model.po.PackingBoxCodePO;
import cn.jihong.mes.production.api.model.vo.in.packing.*;
import cn.jihong.mes.production.api.model.vo.out.packing.*;
import cn.jihong.mes.production.api.service.IBarCodeTreeService;
import cn.jihong.mes.production.api.service.IPackingBoxCodeCheckService;
import cn.jihong.mes.production.api.service.IPackingBoxCodeService;
import cn.jihong.mes.production.app.mapper.BarCodeTreeMapper;
import cn.jihong.mes.production.app.mapper.PackingBoxCodeCheckMapper;
import cn.jihong.mes.production.app.mapper.PackingBoxCodeMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 包装线箱码信息服务实现类
 * 实现自动包装生产线相关业务逻辑
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Slf4j
@Service
public class PackingBoxCodeServiceImpl extends JiHongServiceImpl<PackingBoxCodeMapper,PackingBoxCodePO> implements IPackingBoxCodeService {

    @Resource
    private IPackingBoxCodeCheckService packingBoxCodeCheckService;
    
    @Resource
    private IBarCodeTreeService barCodeTreeService;

    @Override
    public BoxCodeValidationOutVO validateBoxCode(BoxCodeValidationInVO vo) {
        log.info("开始校验箱码，箱码：{}", vo.getBoxCode());
        
        BoxCodeValidationOutVO result = new BoxCodeValidationOutVO();
        try {
            LambdaQueryWrapper<PackingBoxCodePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PackingBoxCodePO::getBoxCode, vo.getBoxCode());
            PackingBoxCodePO boxCodePO = getOne(wrapper);
            
            if (boxCodePO == null) {
                result.setValid(Integer.valueOf(BooleanEnum.FALSE.getCode()));
                result.setMessage("箱码不存在");
                return result;
            }
            
            // 设置返回数据
            result.setValid(boxCodePO.getIsValid());
            result.setWorkOrderNo(boxCodePO.getPlanTicketNo());
            result.setProductCode(boxCodePO.getProductCode());
            result.setProductWeight(boxCodePO.getProductWeight());
            result.setMinWeight(boxCodePO.getMinWeight());
            result.setMaxWeight(boxCodePO.getMaxWeight());
            result.setProductionDate(boxCodePO.getProduceDate());
            result.setInnerPackageCount(boxCodePO.getInnerPackCount());
            result.setProductionQuantity(boxCodePO.getProduceCount());
            result.setPreviousProcessQualified(boxCodePO.getPrevProcessAllOk());
            result.setOrderCompleted(boxCodePO.getIsClosed());
            result.setCompanyCode(boxCodePO.getCompanyCode());
            result.setMessage(boxCodePO.getIsValid() == 1 ? "箱码校验成功" : "箱码无效");

        } catch (Exception e) {
            log.error("箱码校验异常，箱码：{}", vo.getBoxCode(), e);
            result.setValid(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            result.setMessage("箱码校验失败：" + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DetectionDataUploadOutVO uploadDetectionData(DetectionDataUploadInVO vo) {
        log.info("开始上传检测数据，箱码：{}，站点：{}，检测结果：{}", 
                vo.getBoxCode(), vo.getStation(), vo.getCheckResult());
        
        DetectionDataUploadOutVO result = new DetectionDataUploadOutVO();
        
        try {
            // 验证箱码是否存在
            LambdaQueryWrapper<PackingBoxCodePO> boxWrapper = new LambdaQueryWrapper<>();
            boxWrapper.eq(PackingBoxCodePO::getBoxCode, vo.getBoxCode());
            PackingBoxCodePO boxCodePO = this.getOne(boxWrapper);
            
            if (boxCodePO == null) {
                result.setSuccess(false);
                result.setMessage("箱码不存在");
                return result;
            }
            
            // 保存检测数据
            PackingBoxCodeCheckPO checkPO = new PackingBoxCodeCheckPO();
            checkPO.setCompanyCode(vo.getCompanyCode() != null ? vo.getCompanyCode() : boxCodePO.getCompanyCode());
            checkPO.setBoxCode(vo.getBoxCode());
            checkPO.setStation(vo.getStation());
            checkPO.setCheckResult(vo.getCheckResult());
            checkPO.setCheckValue(vo.getCheckValue() != null ? vo.getCheckValue().toString() : null);
            checkPO.setCreateTime(new Date());
            
            packingBoxCodeCheckService.save(checkPO);

            result.setSuccess(true);
            result.setRecordId(checkPO.getId());
            result.setCheckTime(new Date().toString());
            log.info("检测数据上传完成，箱码：{}，记录ID：{}", vo.getBoxCode(), checkPO.getId());
        } catch (Exception e) {
            log.error("检测数据上传异常，箱码：{}", vo.getBoxCode(), e);
            result.setSuccess(false);
            result.setMessage("检测数据上传失败：" + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PalletizeResultOutVO palletizeResult(PalletizeResultInVO vo) {
        log.info("开始处理组托结果，栈板码：{}，机械臂ID：{}", vo.getPalletCode(), vo.getRobotId());
        
        PalletizeResultOutVO result = new PalletizeResultOutVO();
        
        try {
            LambdaQueryWrapper<BarCodeTreePO> boxWrapper = new LambdaQueryWrapper<>();
            boxWrapper.in(BarCodeTreePO::getBarcode, vo.getBoxCodes());
            List<BarCodeTreePO> boxCodePOS = barCodeTreeService.list(boxWrapper);

            if (CollectionUtil.isNotEmpty(boxCodePOS)) {
                result.setSuccess(false);
                result.setMessage("箱码已组托过");
                return result;
            }

            // 生成托盘号
            String palletCode = generatePalletCode();

            // 保存托盘信息
            BarCodeTreePO palletTreePO = new BarCodeTreePO();
            // 托盘作为根节点
            palletTreePO.setParentId(0L);
            palletTreePO.setCompanyCode(vo.getCompanyCode());
            // 1:托盘
            palletTreePO.setType(1);
            palletTreePO.setBarcode(palletCode);
            palletTreePO.setCreateTime(new Date());
            barCodeTreeService.save(palletTreePO);

            // 组托箱码
            List<BarCodeTreePO> barCodeTreePOS = vo.getBoxCodes().stream().map(b -> {
                BarCodeTreePO boxTreePO = new BarCodeTreePO();
                // 托盘作为根节点
                boxTreePO.setParentId(palletTreePO.getId());
                boxTreePO.setCompanyCode(vo.getCompanyCode());
                // 2 箱码
                boxTreePO.setType(2);
                boxTreePO.setBarcode(b);
                boxTreePO.setCreateTime(new Date());
                return boxTreePO;
            }).collect(Collectors.toList());
            barCodeTreeService.saveBatch(barCodeTreePOS);
        } catch (Exception e) {
            log.error("组托结果处理异常，箱码：{}", vo.getBoxCodes(), e);
            result.setSuccess(false);
            result.setMessage("组托结果处理失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public StationStatusOutVO getStationStatus(StationStatusInVO vo) {
        log.info("查询工站状态，站点：{}", vo.getStation());
        
        StationStatusOutVO result = new StationStatusOutVO();
        
        try {
            // 获取查询时间范围
            Date startTime = vo.getStartTime() != null ? vo.getStartTime() : getStartOfDay(new Date());
            Date endTime = vo.getEndTime() != null ? vo.getEndTime() : getEndOfDay(new Date());
            
            // 查询今日总数量
            LambdaQueryWrapper<PackingBoxCodeCheckPO> totalWrapper = new LambdaQueryWrapper<>();
            totalWrapper.eq(PackingBoxCodeCheckPO::getStation, vo.getStation())
                       .ge(PackingBoxCodeCheckPO::getCreateTime, startTime)
                       .le(PackingBoxCodeCheckPO::getCreateTime, endTime);
            Long totalCount = packingBoxCodeCheckService.count(totalWrapper);
            
            // 查询合格数量
            LambdaQueryWrapper<PackingBoxCodeCheckPO> qualifiedWrapper = new LambdaQueryWrapper<>();
            qualifiedWrapper.eq(PackingBoxCodeCheckPO::getStation, vo.getStation())
                           .eq(PackingBoxCodeCheckPO::getCheckResult, "1")
                           .ge(PackingBoxCodeCheckPO::getCreateTime, startTime)
                           .le(PackingBoxCodeCheckPO::getCreateTime, endTime);
            Long qualifiedCount = packingBoxCodeCheckService.count(qualifiedWrapper);
            
            // 计算合格率
            Double qualificationRate = totalCount > 0 ? (qualifiedCount.doubleValue() / totalCount.doubleValue()) * 100 : 0.0;
            
            result.setStation(vo.getStation());
            result.setStationName(getStationName(vo.getStation()));
            result.setOnline(true);
            result.setTodayCount(totalCount.intValue());
            result.setQualifiedCount(qualifiedCount.intValue());
            result.setUnqualifiedCount((int)(totalCount - qualifiedCount));
            result.setQualificationRate(qualificationRate);
            result.setDeviceStatus("RUNNING");
            result.setLastUpdateTime(LocalDateTime.now());
            result.setCompanyCode(vo.getCompanyCode());
            result.setMessage("工站状态正常");
            
            log.info("工站状态查询完成，站点：{}，时间范围：{} - {}，总数：{}，合格数：{}", 
                    vo.getStation(), startTime, endTime, totalCount, qualifiedCount);
            
        } catch (Exception e) {
            log.error("工站状态查询异常，站点：{}", vo.getStation(), e);
            result.setOnline(false);
            result.setMessage("工站状态查询失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public BoxCodeInfoOutVO getBoxCodeInfo(String boxCode) {
        log.info("查询箱码详细信息，箱码：{}", boxCode);
        
        BoxCodeInfoOutVO result = new BoxCodeInfoOutVO();
        
        try {
            // 查询箱码基本信息
            LambdaQueryWrapper<PackingBoxCodePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PackingBoxCodePO::getBoxCode, boxCode);
            PackingBoxCodePO boxCodePO = this.getOne(wrapper);
            
            if (boxCodePO == null) {
                result.setExists(false);
                result.setMessage("箱码不存在");
                return result;
            }
            
            result.setExists(true);
            result.setBoxCode(boxCodePO.getBoxCode());
            result.setValid(boxCodePO.getIsValid() == 1);
            result.setWorkOrderNo(boxCodePO.getPlanTicketNo());
            result.setProductCode(boxCodePO.getProductCode());
            result.setProductWeight(boxCodePO.getProductWeight());
            result.setMinWeight(boxCodePO.getMinWeight());
            result.setMaxWeight(boxCodePO.getMaxWeight());
            result.setProductionDate(boxCodePO.getProduceDate());
            result.setInnerPackageCount(boxCodePO.getInnerPackCount());
            result.setProductionQuantity(boxCodePO.getProduceCount());
            result.setPreviousProcessQualified(boxCodePO.getPrevProcessAllOk());
            result.setOrderCompleted(boxCodePO.getIsClosed());
            result.setCreateTime(boxCodePO.getCreateTime());
            result.setUpdateTime(boxCodePO.getUpdateTime());
            result.setCompanyCode(boxCodePO.getCompanyCode());

        } catch (Exception e) {
            log.error("箱码详细信息查询异常，箱码：{}", boxCode, e);
            result.setExists(false);
            result.setMessage("查询失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public BoxCodeCheckHistoryOutVO getBoxCodeCheckHistory(BoxCodeCheckHistoryInVO vo) {
        log.info("查询箱码检测历史，箱码：{}", vo.getBoxCode());
        
        BoxCodeCheckHistoryOutVO result = new BoxCodeCheckHistoryOutVO();
        try {
            // 查询检测历史
            LambdaQueryWrapper<PackingBoxCodeCheckPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PackingBoxCodeCheckPO::getBoxCode, vo.getBoxCode())
                   .orderByDesc(PackingBoxCodeCheckPO::getCreateTime);
            
            if (vo.getStation() != null) {
                wrapper.eq(PackingBoxCodeCheckPO::getStation, vo.getStation());
            }
            
            if (vo.getCompanyCode() != null) {
                wrapper.eq(PackingBoxCodeCheckPO::getCompanyCode, vo.getCompanyCode());
            }
            
            List<PackingBoxCodeCheckPO> checkList = packingBoxCodeCheckService.list(wrapper);
            
            List<BoxCodeCheckHistoryOutVO.CheckRecord> records = checkList.stream()
                    .map(check -> {
                        BoxCodeCheckHistoryOutVO.CheckRecord record = new BoxCodeCheckHistoryOutVO.CheckRecord();
                        record.setRecordId(check.getId());
                        record.setStation(check.getStation());
                        record.setStationName(getStationName(check.getStation()));
                        record.setCheckResult(check.getCheckResult());
                        record.setCheckResultDesc("1".equals(check.getCheckResult()) ? "合格" : "不合格");
                        record.setCheckValue(check.getCheckValue());
                        record.setCheckTime(check.getCreateTime());
                        record.setCompanyCode(check.getCompanyCode());
                        return record;
                    })
                    .collect(Collectors.toList());
            
            result.setBoxCode(vo.getBoxCode());
            result.setCheckRecords(records);
            result.setTotalCount(records.size());
            result.setMessage("查询成功");
            
            log.info("箱码检测历史查询完成，箱码：{}，记录数：{}", vo.getBoxCode(), records.size());
            
        } catch (Exception e) {
            log.error("箱码检测历史查询异常，箱码：{}", vo.getBoxCode(), e);
            result.setMessage("查询失败：" + e.getMessage());
        }
        
        return result;
    }



    /**
     * 根据站点代码获取站点名称
     */
    private String getStationName(String stationCode) {
        switch (stationCode) {
            case "DIRECTION":
                return "方向判断站";
            case "WEIGHING":
                return "称重站";
            case "METAL_DETECT":
                return "金探站";
            case "CODING":
                return "打码站";
            case "PALLETIZE":
                return "码垛站";
            default:
                return "未知工站";
        }
    }

    /**
     * 生成托盘号
     */
    private String generatePalletCode() {
        return "PL" + System.currentTimeMillis();
    }


    /**
     * 获取今日结束时间（第二天8点）
     */
    private Date getEndOfDay(Date date) {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(date);
        // 加一天
        cal.add(java.util.Calendar.DAY_OF_MONTH, 1);
        // 设置为8点
        cal.set(java.util.Calendar.HOUR_OF_DAY, 8);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    
    /**
     * 获取今日开始时间
     */
    private Date getStartOfDay(Date date) {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(date);
        cal.set(java.util.Calendar.HOUR_OF_DAY, 8);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // ==================== 内标签绑定相关方法实现 ====================

    /**
     * 箱码内标签绑定
     * 基于BarCodeTree表实现三级层次绑定：栈板码 → 箱码 → 内标签码
     */
    @Override
    @Transactional
    public void bindInnerLabels(InnerLabelBindingInVO vo) {
        log.info("开始绑定内标签，箱码：{}，内标签数量：{}", vo.getBoxCode(), vo.getInnerLabelCodes().size());

        try {
            // 1. 验证箱码是否存在
            LambdaQueryWrapper<PackingBoxCodePO> boxWrapper = new LambdaQueryWrapper<>();
            boxWrapper.eq(PackingBoxCodePO::getBoxCode, vo.getBoxCode());
            boxWrapper.eq(PackingBoxCodePO::getCompanyCode, SecurityUtil.getCompanySite());
            PackingBoxCodePO boxCodePO = getOne(boxWrapper);
            
            if (boxCodePO == null) {
                throw new CommonException()
                return result;
            }

            // 2. 从ERP获取产品信息（这里您需要实现具体的ERP调用）
            // TODO: 实现ERP接口调用，类似您的示例：
            // ErpProductInfoTVO erpInfo = erpService.getProductInfoByBoxCode(vo.getBoxCode());
            // if (erpInfo != null) {
            //     boxCodePO.setProductWeight(erpInfo.getProductWeight());
            //     boxCodePO.setMinWeight(erpInfo.getMinWeight());
            //     // ... 更新其他字段
            //     this.updateById(boxCodePO);
            // }
            
            // 临时使用箱码中已有的信息
            InnerLabelBindingOutVO.ErpProductInfo productInfo = new InnerLabelBindingOutVO.ErpProductInfo();
            productInfo.setProductWeight(boxCodePO.getProductWeight());
            productInfo.setMinWeight(boxCodePO.getMinWeight());
            productInfo.setMaxWeight(boxCodePO.getMaxWeight());
            productInfo.setInnerPackCount(boxCodePO.getInnerPackCount());
            productInfo.setIsClosed(boxCodePO.getIsClosed());
            productInfo.setProductCode(boxCodePO.getProductCode());
            productInfo.setProduceDate(boxCodePO.getProduceDate());

            // 3. 校验内标签数量是否超过内包数量
            if (boxCodePO.getInnerPackCount() != null && 
                vo.getInnerLabelCodes().size() > boxCodePO.getInnerPackCount()) {
                result.setSuccess(false);
                result.setMessage("内标签数量超过规定的内包数量: " + boxCodePO.getInnerPackCount());
                return result;
            }

            // 4. 查找或创建箱码在BarCodeTree中的记录
            Long boxTreeId = getOrCreateBoxCodeTree(vo.getBoxCode(), vo.getPalletCode(), vo.getCompanyCode(), vo.getOperatorId());
            
            // 5. 批量绑定内标签
            List<BarCodeTreePO> innerLabelTrees = new ArrayList<>();
            List<String> failedLabels = new ArrayList<>();
            
            for (String innerLabelCode : vo.getInnerLabelCodes()) {
                // 检查内标签是否已绑定
                if (!isInnerLabelAlreadyBound(innerLabelCode)) {
                    BarCodeTreePO innerLabelTree = new BarCodeTreePO();
                    innerLabelTree.setParentId(boxTreeId);
                    innerLabelTree.setCompanyCode(vo.getCompanyCode() != null ? vo.getCompanyCode() : boxCodePO.getCompanyCode());
                    innerLabelTree.setType(3); // 内标签类型
                    innerLabelTree.setBarcode(innerLabelCode);
                    innerLabelTree.setCreateBy(vo.getOperatorId());
                    
                    innerLabelTrees.add(innerLabelTree);
                } else {
                    failedLabels.add(innerLabelCode);
                }
            }
            
            // 6. 批量保存内标签树记录
            if (!innerLabelTrees.isEmpty()) {
                barCodeTreeService.saveBatch(innerLabelTrees);
            }
            
            // 7. 返回结果
            result.setSuccess(true);
            result.setMessage("内标签绑定完成");
            result.setBoxCode(vo.getBoxCode());
            result.setBoundCount(innerLabelTrees.size());
            result.setFailedLabels(failedLabels);
            result.setBoxTreeId(boxTreeId);
            result.setProductInfo(productInfo);
            
            log.info("内标签绑定完成，箱码：{}，成功绑定：{}个，失败：{}个", 
                    vo.getBoxCode(), innerLabelTrees.size(), failedLabels.size());
            
        } catch (Exception e) {
            log.error("内标签绑定异常，箱码：{}", vo.getBoxCode(), e);
            result.setSuccess(false);
            result.setMessage("绑定失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 查询箱码绑定的内标签
     */
    @Override
    public List<InnerLabelQueryOutVO> getInnerLabels(String boxCode) {
        log.info("查询箱码内标签，箱码：{}", boxCode);
        
        try {
            // 1. 查找箱码在BarCodeTree中的记录
            LambdaQueryWrapper<BarCodeTreePO> boxWrapper = new LambdaQueryWrapper<>();
            boxWrapper.eq(BarCodeTreePO::getBarcode, boxCode)
                      .eq(BarCodeTreePO::getType, 2); // 箱码类型
            BarCodeTreePO boxTree = barCodeTreeService.getOne(boxWrapper);
            
            if (boxTree == null) {
                return new ArrayList<>();
            }
            
            // 2. 查询该箱码下的所有内标签
            LambdaQueryWrapper<BarCodeTreePO> innerWrapper = new LambdaQueryWrapper<>();
            innerWrapper.eq(BarCodeTreePO::getParentId, boxTree.getId())
                        .eq(BarCodeTreePO::getType, 3); // 内标签类型
            List<BarCodeTreePO> innerLabelTrees = barCodeTreeService.list(innerWrapper);
            
            // 3. 转换为输出VO
            return innerLabelTrees.stream().map(tree -> {
                InnerLabelQueryOutVO vo = new InnerLabelQueryOutVO();
                vo.setInnerLabelCode(tree.getBarcode());
                vo.setBoxCode(boxCode);
                vo.setTreeId(tree.getId());
                vo.setBindingTime(tree.getCreateTime());
                vo.setOperatorId(tree.getCreateBy());
                vo.setCompanyCode(tree.getCompanyCode());
                
                // 查找栈板码（如果有的话）
                if (boxTree.getParentId() != null) {
                    LambdaQueryWrapper<BarCodeTreePO> palletWrapper = new LambdaQueryWrapper<>();
                    palletWrapper.eq(BarCodeTreePO::getId, boxTree.getParentId())
                                 .eq(BarCodeTreePO::getType, 1); // 栈板类型
                    BarCodeTreePO palletTree = barCodeTreeService.getOne(palletWrapper);
                    if (palletTree != null) {
                        vo.setPalletCode(palletTree.getBarcode());
                    }
                }
                
                return vo;
            }).collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("查询箱码内标签异常，箱码：{}", boxCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 解绑内标签
     */
    @Override
    @Transactional
    public Boolean unbindInnerLabel(InnerLabelUnbindInVO vo) {
        log.info("解绑内标签，内标签码：{}", vo.getInnerLabelCode());
        
        try {
            LambdaQueryWrapper<BarCodeTreePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BarCodeTreePO::getBarcode, vo.getInnerLabelCode())
                   .eq(BarCodeTreePO::getType, 3); // 内标签类型
            
            if (vo.getCompanyCode() != null) {
                wrapper.eq(BarCodeTreePO::getCompanyCode, vo.getCompanyCode());
            }
            
            BarCodeTreePO innerLabelTree = barCodeTreeService.getOne(wrapper);
            if (innerLabelTree != null) {
                // 如果指定了箱码，进行二次校验
                if (vo.getBoxCode() != null) {
                    LambdaQueryWrapper<BarCodeTreePO> boxWrapper = new LambdaQueryWrapper<>();
                    boxWrapper.eq(BarCodeTreePO::getId, innerLabelTree.getParentId())
                              .eq(BarCodeTreePO::getBarcode, vo.getBoxCode())
                              .eq(BarCodeTreePO::getType, 2); // 箱码类型
                    BarCodeTreePO boxTree = barCodeTreeService.getOne(boxWrapper);
                    if (boxTree == null) {
                        log.warn("内标签与指定箱码不匹配，内标签：{}，箱码：{}", vo.getInnerLabelCode(), vo.getBoxCode());
                        return false;
                    }
                }
                
                barCodeTreeService.removeById(innerLabelTree.getId());
                log.info("内标签解绑成功，内标签码：{}", vo.getInnerLabelCode());
                return true;
            } else {
                log.warn("未找到内标签记录，内标签码：{}", vo.getInnerLabelCode());
                return false;
            }
            
        } catch (Exception e) {
            log.error("解绑内标签异常，内标签码：{}", vo.getInnerLabelCode(), e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取或创建箱码在BarCodeTree中的记录
     */
    private Long getOrCreateBoxCodeTree(String boxCode, String palletCode, String companyCode, Long operatorId) {
        // 查找现有的箱码记录
        LambdaQueryWrapper<BarCodeTreePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BarCodeTreePO::getBarcode, boxCode)
               .eq(BarCodeTreePO::getType, 2); // 箱码类型
        
        BarCodeTreePO existingBoxTree = barCodeTreeService.getOne(wrapper);
        if (existingBoxTree != null) {
            return existingBoxTree.getId();
        }
        
        // 如果有栈板码，先处理栈板码
        Long palletTreeId = null;
        if (palletCode != null) {
            palletTreeId = getOrCreatePalletCodeTree(palletCode, companyCode, operatorId);
        }
        
        // 创建箱码记录
        BarCodeTreePO boxTree = new BarCodeTreePO();
        boxTree.setParentId(palletTreeId);
        boxTree.setCompanyCode(companyCode);
        boxTree.setType(2); // 箱码类型
        boxTree.setBarcode(boxCode);
        boxTree.setCreateBy(operatorId);
        
        barCodeTreeService.save(boxTree);
        return boxTree.getId();
    }

    /**
     * 获取或创建栈板码在BarCodeTree中的记录
     */
    private Long getOrCreatePalletCodeTree(String palletCode, String companyCode, Long operatorId) {
        // 查找现有的栈板码记录
        LambdaQueryWrapper<BarCodeTreePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BarCodeTreePO::getBarcode, palletCode)
               .eq(BarCodeTreePO::getType, 1); // 栈板类型
        
        BarCodeTreePO existingPalletTree = barCodeTreeService.getOne(wrapper);
        if (existingPalletTree != null) {
            return existingPalletTree.getId();
        }
        
        // 创建栈板码记录
        BarCodeTreePO palletTree = new BarCodeTreePO();
        palletTree.setParentId(null); // 栈板是顶级
        palletTree.setCompanyCode(companyCode);
        palletTree.setType(1); // 栈板类型
        palletTree.setBarcode(palletCode);
        palletTree.setCreateBy(operatorId);
        
        barCodeTreeService.save(palletTree);
        return palletTree.getId();
    }

    /**
     * 检查内标签是否已经绑定
     */
    private boolean isInnerLabelAlreadyBound(String innerLabelCode) {
        LambdaQueryWrapper<BarCodeTreePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BarCodeTreePO::getBarcode, innerLabelCode)
               .eq(BarCodeTreePO::getType, 3); // 内标签类型
        return barCodeTreeService.count(wrapper) > 0;
    }
}