package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductMachinePartsPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.UpdateProductMachinePartsInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductMachineDayOutVO;
import cn.jihong.mes.production.api.service.IProductMachineDayService;
import cn.jihong.mes.production.api.service.IProductMachinePartsService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductMachinePartsMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务机位信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@DubboService
public class ProductMachinePartsServiceImpl extends JiHongServiceImpl<ProductMachinePartsMapper, ProductMachinePartsPO> implements IProductMachinePartsService {

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMachineDayService productMachineDayService;

    @Override
    public Pagination<ProductMachinePartsPO> getProductMachineParts(ProductTicketPageInVO productTicketPageInVO) {
        LambdaQueryWrapper<ProductMachinePartsPO> lambdaQueryWrapper = Wrappers.lambdaQuery(ProductMachinePartsPO.class)
                .eq(ProductMachinePartsPO::getProductTicketId, productTicketPageInVO.getProductTicketId());
        IPage page = page(productTicketPageInVO.getPage(), lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page);
    }

    @Override
    public List<ProductMachinePartsPO> getProductMachineParts(Long productTicketId) {
        LambdaQueryWrapper<ProductMachinePartsPO> lambdaQueryWrapper = Wrappers.lambdaQuery(ProductMachinePartsPO.class)
                .eq(ProductMachinePartsPO::getProductTicketId, productTicketId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public Long updateProductMachineParts(UpdateProductMachinePartsInVO invo) {
        // 校验分摊是否完成
        ProductMachinePartsPO productMachinePartsPO = getById(invo.getId());

        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(productMachinePartsPO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(productMachinePartsPO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(productMachinePartsPO.getShift());
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);
        if (productMachineDay != null && Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productMachineDay.getIsApportionment())) {
            throw new CommonException("该工单分摊已完成，不可修改机位信息");
        }

        productMachinePartsPO.setParts(invo.getParts());
        productMachinePartsPO.setPartsName(invo.getPartsName());
        productMachinePartsPO.setPlace(invo.getPlace());
        productMachinePartsPO.setPlaceName(invo.getPlaceName());
        productMachinePartsPO.setUpdateBy(SecurityUtil.getUserId());
        productMachinePartsPO.setUpdateTime(new java.util.Date());
        updateById(productMachinePartsPO);
        return productMachinePartsPO.getId();
    }

    @Override
    public Object addProductMachineParts(ProductMachinePartsPO productMachinePartsPO) {
        LambdaQueryWrapper<ProductMachinePartsPO> lambdaQueryWrapper = Wrappers.lambdaQuery(ProductMachinePartsPO.class)
                .eq(ProductMachinePartsPO::getProductTicketId, productMachinePartsPO.getProductTicketId());
        List<ProductMachinePartsPO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            Set<String> collect = list.stream().map(ProductMachinePartsPO::getParts).collect(Collectors.toSet());
            boolean add = collect.add(productMachinePartsPO.getParts());
            if (!add) {
                throw new CommonException("该任务已存在机位，请勿重复添加" + productMachinePartsPO.getPartsName());
            }
        }

        ProductTicketPO productTicketPO = productTicketService.getById(productMachinePartsPO.getProductTicketId());

        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(productTicketPO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(productTicketPO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(productTicketPO.getShift());
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);
        if (productMachineDay != null && Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productMachineDay.getIsApportionment())) {
            throw new CommonException("该工单分摊已完成，不可修改机位信息");
        }

        productMachinePartsPO.setId(null);
        productMachinePartsPO.setMachineName(productTicketPO.getMachineName());
        productMachinePartsPO.setProduceDate(productTicketPO.getProduceDate());
        productMachinePartsPO.setShift(productTicketPO.getShift());
        productMachinePartsPO.setProcess(productTicketPO.getProcess());
        productMachinePartsPO.setProcessCode(productTicketPO.getProcessCode());
        productMachinePartsPO.setCompanyCode(SecurityUtil.getCompanySite());
        productMachinePartsPO.setCreateBy(SecurityUtil.getUserId());
        productMachinePartsPO.setCreateTime(new java.util.Date());
        productMachinePartsPO.setUpdateBy(SecurityUtil.getUserId());
        productMachinePartsPO.setUpdateTime(new java.util.Date());
        return save(productMachinePartsPO);
    }

    @Override
    public Object deleteById(Long id) {
        ProductMachinePartsPO productMachinePartsPO = getById(id);

        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(productMachinePartsPO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(productMachinePartsPO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(productMachinePartsPO.getShift());
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);
        if (productMachineDay != null && Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productMachineDay.getIsApportionment())) {
            throw new CommonException("该工单分摊已完成，不可修改机位信息");
        }
        return removeById(id);
    }
}
