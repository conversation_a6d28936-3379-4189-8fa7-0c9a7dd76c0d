package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeInVO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeListInVO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimePageInVO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeOeeListInVO;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeService;
import cn.jihong.mes.production.app.listener.EquipmentDowntimeExcelListener;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import com.alibaba.excel.EasyExcel;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 设备停机代码表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@RestController
@RequestMapping("/equipmentDowntime")
@ShenyuSpringMvcClient(path = "/equipmentDowntime/**")
public class EquipmentDowntimeController {

    @Resource(name = "equipmentDowntimeServiceImpl")
    private IEquipmentDowntimeService service;


    @DubboReference
    private IOocqlTService iOocqlTService;

    /**
     * 保存
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     */
    @PostMapping("/saveEquipmentDowntime")
    public StandardResult<Boolean> saveEquipmentDowntime(@RequestBody @Valid EquipmentDowntimeInVO inVO){
       return StandardResult.resultCode(OperateCode.SUCCESS,service.saveEquipmentDowntime(inVO));
    }

    /**
     * 更新
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     */
    @PostMapping("/editEquipmentDowntime")
    public StandardResult<Boolean> editEquipmentDowntime(@RequestBody @Valid EquipmentDowntimeInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.editEquipmentDowntime(inVO));
    }

    /**
     * 批量删除
     * @param ids
     * @return: cn.jihong.common.model.StandardResult<java.lang.Boolean>
     */
    @GetMapping("/deleteByIds/{ids}")
    public StandardResult<Boolean> deleteByIds(@PathVariable("ids") String ids){
         return StandardResult.resultCode(OperateCode.SUCCESS,service.deleteByIds(ids));
    }

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO>
     */
    @GetMapping("/getSingleEquipmentDowntimeById/{id}")
    public StandardResult<EquipmentDowntimeOutVO> getSingleEquipmentDowntimeById(@PathVariable("id") Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getSingleEquipmentDowntimeById(id));
    }

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO>>
     */
    @PostMapping("/getEquipmentDowntimePage")
    public StandardResult<Pagination<EquipmentDowntimeOutVO>> getEquipmentDowntimePage(@RequestBody @Valid EquipmentDowntimePageInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getEquipmentDowntimePage(inVO));
    }

    /**
     * 获取设备停机列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO>>
     */
    @PostMapping("/getEquipmentDowntimeList")
    public StandardResult<List<EquipmentDowntimeOutVO>> getEquipmentDowntimeList(@RequestBody @Valid EquipmentDowntimeListInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getEquipmentDowntimeList(inVO));
    }


    /**
     * 获取OEE设备停机列表（PC）
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO>>
     */
    @PostMapping("/getEquipmentDowntimeOeeList")
    public StandardResult<List<EquipmentDowntimeOutVO>> getEquipmentDowntimeOeeList(@RequestBody @Valid EquipmentDowntimeOeeListInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getEquipmentDowntimeOeeList(inVO));
    }




    /**
     *  下载模板
     * @param response
     * @return: void
     * <AUTHOR>
     * @date: 2025-04-30 13:32
     */
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("templates/equipmentDowntimeTemplate.xlsx");

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=equipmentDowntimeTemplate.xlsx");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");

        try (InputStream inputStream = resource.getInputStream()) {
            StreamUtils.copy(inputStream, response.getOutputStream());
            response.flushBuffer();
        }
    }


    /**
     * 上传停机数据
     * @param file
     * @return: com.jh.common.core.domain.R
     * <AUTHOR>
     * @date: 2025-04-30 13:32
     */
    @PostMapping("/upload")
    public StandardResult upload(@RequestParam MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(),new EquipmentDowntimeExcelListener(iOocqlTService)).sheet(0).doRead();
        return StandardResult.ok();
    }


}

