package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO;
import cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO;
import cn.jihong.mes.production.api.model.vo.in.DeviceOEEReportShowInVO;
import cn.jihong.mes.production.api.model.vo.in.GetOeeInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductBarcodeSplitBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.DeviceOEEReportShowOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOeeOutVO;
import cn.jihong.mes.production.api.service.IDeviceOEEReportService;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByCompanyCodeOutVO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * OEE设备
 * <AUTHOR>
 * @date 2025-03-22 16:36
 */
@RestController
@RequestMapping("/deviceOEEReport")
@ShenyuSpringMvcClient(path = "/deviceOEEReport/**")
public class DeviceOEEReportController {

    @Resource
    private IDeviceOEEReportService iDeviceOEEReportService;

    @Resource
    private IProductionMachineService iProductionMachineService;

    /**
     * 设备OEE报表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.mes.production.api.model.vo.out.DeviceOEEReportShowOutVO>>
     * <AUTHOR>
     * @date: 2025-03-22 17:07
     */
    @PostMapping("/deviceOEEReportShow")
    public StandardResult<List<DeviceOEEReportShowOutVO>> deviceOEEReportShow(@RequestBody @Valid DeviceOEEReportShowInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iDeviceOEEReportService.deviceOEEReportShow(inVO));
    }



    /**
     * oee
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO>>
     */
    @PostMapping("/getOee")
    public StandardResult<GetOeeOutVO> getOee(@RequestBody @Valid GetOeeInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iDeviceOEEReportService.getOee(inVO));
    }

    /**
     * 获取工序列表
     * @param
     * @return: cn.jihong.common.model.StandardResult<java.util.List<cn.jihong.oa.erp.api.model.vo.GetOocqlTByCompanyCodeOutVO>>
     * <AUTHOR>
     * @date: 2025-06-18 18:12
     */
    @GetMapping("/getAllProcess")
    public StandardResult<List<GetOocqlTByCompanyCodeOutVO>> getAllProcess(){
        return StandardResult.resultCode(OperateCode.SUCCESS,iDeviceOEEReportService.getAllProcess());
    }

    /**
     * 获取机台列表
     * @param
     * @return: cn.jihong.common.model.StandardResult<java.util.List<java.lang.String>>
     * <AUTHOR>
     * @date: 2025-06-18 18:12
     */
    @GetMapping("/getAllMachine")
    public StandardResult<List<String>> getAllMachine(){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductionMachineService.getAllMachine());
    }

}
