package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.ProductBoxBarcodePO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface ProductBoxBarcodeMapper extends JiHongMapper<ProductBoxBarcodePO> {

    List<GetProductBoxBarcodeDetailOutVO> getListByBarcodes(@Param("companyCode") String companyCode,
                                                            @Param("barcodes") List<String> barcodes);
}
