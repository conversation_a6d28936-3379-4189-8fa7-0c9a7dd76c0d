package cn.jihong.mes.production.app.mapper;

import java.util.List;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementWorkReportDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportDetailOutVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import cn.jihong.mes.production.api.model.po.ProductSettlementWorkReportPO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportListOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

/**
 * <p>
 * 工程结算报工信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface ProductSettlementWorkReportMapper extends JiHongMapper<ProductSettlementWorkReportPO> {

    List<GetSettlementWorkReportListOutVO> getSettlementWorkReportList(@Param("productTicketNo") String productTicketNo);

    Page<GetSettlementWorkReportDetailOutVO> getSettlementWorkReportDetail(IPage page, @Param("inVo") GetSettlementWorkReportDetailInVO inVo);

}
