package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductPlanTicketVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketVO;
import cn.jihong.mes.production.api.model.vo.in.UnfinishedOrderListInVO;
import cn.jihong.mes.production.api.model.vo.out.UnfinishedOrderListOutVO;
import cn.jihong.mes.production.api.service.IProductFinishOrderService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 结单
 *
 * <AUTHOR>
 * @since 2023-11-23
 */
@RestController
@RequestMapping("/productFinishProdution")
@ShenyuSpringMvcClient(path = "/productFinishProdution/**")
public class ProductFinishProdutionController {

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductFinishOrderService productFinishOrderService;

//    /**
//     * 获取生产计划工程单列表
//     */
//    @PostMapping("/getProductPlanList")
//    public StandardResult<List<ProductionPlanDTO>> getProductPlanList(@RequestBody @Valid GetProductPlanListVO vo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//            productTicketService.getProductPlanList(vo));
//    }

//    /**
//     * 获得当前机台的工单
//     *
//     * @param getProductionTicketNo
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/getProductionTicketNo")
//    public StandardResult getProductionTicketNo(@RequestBody @Valid GetProductionTicketNoInVO getProductionTicketNo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productTicketService.getProductionMachineTicket(getProductionTicketNo.getMachineName(), TicketTypeEnum.FINISH_PRODUCTION.getCode()));
//    }

//    /**
//     * 创建结单生产工单
//     *
//     * @param vo
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/create")
//    public StandardResult createProductTicket(@RequestBody @Valid CreateFinishProductionInVO vo) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.createFinishProduction(vo));
//    }

//    /**
//     * 获取结单记录
//     * @param dto
//     * @return {@link StandardResult}
//     */
//    @PostMapping("/getFinishList")
//    public StandardResult<List<ProductChangeProdutionOutVO>> getFinishProductTicketList(@RequestBody @Valid ProductChangeProductionDTO dto) {
//        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getFinishProductTicketList(dto));
//    }

    /**
     * 结单确定---校验
     */
    @PostMapping("/saveFinishOrderInfo")
    public StandardResult saveFinishOrderInfo(@RequestBody @Valid ProductPlanTicketVO productPlanTicketVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productFinishOrderService.saveFinishOrderInfo(productPlanTicketVO));
    }


    /**
     * 未结单的生产工单
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/12/26 11:49
     */
    @PostMapping("/unfinishedOrderList")
    public StandardResult<List<UnfinishedOrderListOutVO>> unfinishedOrderList(@RequestBody @Valid UnfinishedOrderListInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.unfinishedOrderList(inVO));
    }


}
