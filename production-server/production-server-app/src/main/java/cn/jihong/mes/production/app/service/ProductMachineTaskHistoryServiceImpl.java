package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskHistoryPO;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetChangeVersionHistoryPageOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetChangeVersionInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskHistoryPageOutVO;
import cn.jihong.mes.production.api.service.IProductMachineTaskHistoryService;
import cn.jihong.mes.production.api.service.IProductMachineTaskService;
import cn.jihong.mes.production.api.service.IProductStoreService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductMachineTaskHistoryMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.po.EcbxucTPO;
import cn.jihong.oa.erp.api.service.IEcbxucTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;

import static java.util.stream.Collectors.*;

/**
 * <p>
 * 生产机台任务操作历史 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Slf4j
@DubboService
public class ProductMachineTaskHistoryServiceImpl extends JiHongServiceImpl<ProductMachineTaskHistoryMapper, ProductMachineTaskHistoryPO> implements IProductMachineTaskHistoryService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @DubboReference
    private IA01Service ia01Service;

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;

    @Resource
    private IProductTicketService iProductTicketService;

    @DubboReference
    private IEcbxucTService iEcbxucTService;

    @Resource
    private IProductStoreService iProductStoreService;

    @Override
    public StandardResult sendOverProductionFlow(SendOverProductionFlowInVO inVO) {
        return iProductStoreService.sendOverProductionFlow(inVO);
    }

    /**
     * 保存机台任务操作记录
     * @param productMachineTaskPO
     * @return: void
     * <AUTHOR>
     * @date: 2024/1/11 11:38
     */
    @Override
    public Boolean saveMachineTaskHistory(ProductMachineTaskPO productMachineTaskPO,
                                          BigDecimal reportedQuantity,String reason,Long productOutboundId){
        ProductMachineTaskHistoryPO historyPO = new ProductMachineTaskHistoryPO();
        BeanUtil.copyProperties(productMachineTaskPO,historyPO);
        historyPO.setId(null);
        historyPO.setProductMachineTaskId(productMachineTaskPO.getId());
        historyPO.setReportedQuantity(reportedQuantity);
        historyPO.setProductOutboundId(productOutboundId);
        historyPO.setCreateTime(new Date());
        historyPO.setCreateBy(SecurityUtil.getUserId());
        save(historyPO);

        if(reportedQuantity!=null && reportedQuantity.compareTo(BigDecimal.ZERO) > 0 && StringUtils.isNotBlank(reason)){
            // 触发超产单 OA流程
            log.info("报工触发超产单 OA流程 ywid:{}",historyPO.getId());
            SendOverProductionFlowInVO sendOverProductionFlowInVO = new SendOverProductionFlowInVO();
            sendOverProductionFlowInVO.setCflx("2");
            sendOverProductionFlowInVO.setYwId(historyPO.getId());
            sendOverProductionFlowInVO.setProducedQuantity(reportedQuantity);
            sendOverProductionFlowInVO.setReason(reason);
            sendOverProductionFlow(sendOverProductionFlowInVO);
        }

        return true;
    }


    @Override
    public Pagination<GetProductMachineTaskHistoryPageOutVO> getProductMachineTaskHistoryPage(GetProductMachineTaskHistoryPageInVO inVO) {
        IPage<GetProductMachineTaskHistoryPageOutVO> taskHistoryPage = baseMapper.getProductMachineTaskHistoryPage(inVO.getPage(), inVO,SecurityUtil.getCompanySite());
        if(CollectionUtil.isNotEmpty(taskHistoryPage.getRecords())) {
            String reportedUserIds = taskHistoryPage.getRecords().stream().map(t -> String.valueOf(t.getReportedUserId())).collect(joining(","));
            String teamUserIds = taskHistoryPage.getRecords().stream().map(GetProductMachineTaskHistoryPageOutVO::getTeamUsers).collect(joining(","));
            Map<Long, UserDTO> userNameMap = getUserNameMap(reportedUserIds + "," + teamUserIds);
            taskHistoryPage.getRecords().stream().peek(t -> {
                if (chinesePattern.matcher(t.getTeamUsers()).find()) {
                    t.setTeamUsersName(t.getTeamUsers());
                } else {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String teamUserId : t.getTeamUsers().split(",")) {
                        if(StringUtils.isNotBlank(teamUserId)) {
                            UserDTO userDTO = userNameMap.get(Long.parseLong(teamUserId));
                            stringJoiner.add(userDTO != null? userDTO.getName():"");
                        }
                    }
                    t.setTeamUsersName(stringJoiner.toString());
                }
                t.setReportedUserName(userNameMap.get(t.getReportedUserId()).getName());
            }).collect(toList());
            return Pagination.newInstance(taskHistoryPage);
        }else {
            return Pagination.newInstance(null, 0, 0);
        }
    }


    @Override
    public Boolean updateMachineTaskHistory(UpdateMachineTaskHistoryInVO inVO) {
        ProductMachineTaskHistoryPO machineTaskHistoryPO = getById(inVO.getMachineTaskHistoryId());

        ProductTicketPO productTicketPO = iProductTicketService.getById(machineTaskHistoryPO.getReportedProductId());
        if(productTicketPO.getIsSignUp()!=null && productTicketPO.getIsSignUp().equals(1)){
            // 已经报工到erp 无法修改
            throw new CommonException("已报工到erp，无法修改");
        }

        ProductMachineTaskPO productMachineTaskPO = iProductMachineTaskService.getById(machineTaskHistoryPO.getProductMachineTaskId());
        productMachineTaskPO.setReportedQuantity(productMachineTaskPO.getReportedQuantity().subtract(machineTaskHistoryPO.getReportedQuantity()).add(inVO.getReportedQuantity()));
        productMachineTaskPO.setUpdateBy(SecurityUtil.getUserId());
        productMachineTaskPO.setUpdateTime(new Date());
        iProductMachineTaskService.updateById(productMachineTaskPO);

        machineTaskHistoryPO.setReportedQuantity(inVO.getReportedQuantity());
        machineTaskHistoryPO.setUpdateBy(SecurityUtil.getUserId());
        machineTaskHistoryPO.setUpdateTime(new Date());
        return updateById(machineTaskHistoryPO);
    }


    @Override
    public List<ProductMachineTaskHistoryPO> getHistoryListByProductMachineTaskId(Long productMachineTaskId,Integer type) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskHistoryPO::getProductMachineTaskId,productMachineTaskId)
                .eq(type!=null,ProductMachineTaskHistoryPO::getType,type);
        return list(wrapper);
    }

    private Map<Long, UserDTO> getUserNameMap(String userIds) {
        String[] userIdAry = userIds.split(",");
        List<Long> userLong = Arrays.stream(userIdAry).filter(userId->{
           return   StringUtils.isNotBlank(userId) && !chinesePattern.matcher(userId).find();
        }).map(Long::valueOf).collect(toList());
        List<UserDTO> userInfoByIds = ia01Service.getUserInfoByIds(userLong);
        return userInfoByIds.stream().collect(toMap(UserDTO::getId, Function.identity()));
    }


    @Override
    public List<GetChangeVersionInfoOutVO> getChangeVersionInfo(GetChangeVersionInfoInVO inVO) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(inVO.getReportedProductId());
        List<EcbxucTPO> ecbxucTPOList = iEcbxucTService.getListByProcessCode(SecurityUtil.getCompanySite(), productTicketPO.getProcessCode());
        AssertUtil.isNotEmpty(ecbxucTPOList,"当前机台所在的工序匹配不到ERP换型报工数据");
        List<GetChangeVersionInfoOutVO.ChangeVersionTypeInfo> changeList = ecbxucTPOList.stream().map(t -> {
            GetChangeVersionInfoOutVO.ChangeVersionTypeInfo changeVersionTypeInfo = new GetChangeVersionInfoOutVO.ChangeVersionTypeInfo();
            changeVersionTypeInfo.setChangeVersionType(t.getEcbxuc002());
            return changeVersionTypeInfo;
        }).collect(toList());

        List<GetChangeVersionInfoOutVO> list = new ArrayList<>();
        GetChangeVersionInfoOutVO newOutVO = new GetChangeVersionInfoOutVO();
        newOutVO.setChangeVersionNewOld("1：新版");
        newOutVO.setChangeVersionTypeList(changeList);
        list.add(newOutVO);

        GetChangeVersionInfoOutVO oldOutVO = new GetChangeVersionInfoOutVO();
        oldOutVO.setChangeVersionNewOld("2：旧版");
        oldOutVO.setChangeVersionTypeList(changeList);
        list.add(oldOutVO);
        return list;
    }

    @Override
    public Pagination<GetChangeVersionHistoryPageOutVO> getChangeVersionHistoryPage(GetChangeVersionHistoryPageInVO inVO) {
        IPage<GetChangeVersionHistoryPageOutVO> changeVersionHistoryPage = baseMapper.getChangeVersionHistoryPage(inVO.getPage(), inVO, SecurityUtil.getCompanySite());
        List<GetChangeVersionHistoryPageOutVO> records = changeVersionHistoryPage.getRecords();
        if(CollectionUtil.isEmpty(records)){
            return Pagination.newInstance(null);
        }
        String reportedUserIds = records.stream().map(t -> String.valueOf(t.getReportedUserId())).collect(joining(","));
        Map<Long, UserDTO> userNameMap = getUserNameMap(reportedUserIds);
        records.stream().peek(t->{
            UserDTO userDTO = userNameMap.get(t.getReportedUserId());
            t.setReportedUserName(userDTO!=null?userDTO.getName():"");
        }).collect(toList());
        return Pagination.newInstance(changeVersionHistoryPage);
    }

    @Override
    public Boolean updateChangeVersion(UpdateChangeVersionInVO inVO) {
        ProductMachineTaskHistoryPO machineTaskHistoryPO = getById(inVO.getMachineTaskHistoryId());
        machineTaskHistoryPO.setChangeVersionNewOld(inVO.getChangeVersionNewOld());
        machineTaskHistoryPO.setChangeVersionType(inVO.getChangeVersionType());
        machineTaskHistoryPO.setChangeVersionQuantity(inVO.getChangeVersionQuantity());

        ProductMachineTaskPO productMachineTaskPO = iProductMachineTaskService.getById(machineTaskHistoryPO.getProductMachineTaskId());
        productMachineTaskPO.setChangeVersionNewOld(inVO.getChangeVersionNewOld());
        productMachineTaskPO.setChangeVersionType(inVO.getChangeVersionType());
        productMachineTaskPO.setChangeVersionQuantity(inVO.getChangeVersionQuantity());

        return commonUpdate(machineTaskHistoryPO, productMachineTaskPO);
    }

    @Override
    public List<ProductMachineTaskHistoryPO> getListByProductTicketIds(Integer type, List<Long> productTicketIdList) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskHistoryPO::getType, type)
                .in(ProductMachineTaskHistoryPO::getReportedProductId,productTicketIdList);
        return list(wrapper);
    }

    private boolean commonUpdate(ProductMachineTaskHistoryPO machineTaskHistoryPO, ProductMachineTaskPO productMachineTaskPO) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(machineTaskHistoryPO.getReportedProductId());
        if(productTicketPO.getIsSignUp()!=null && productTicketPO.getIsSignUp().equals(1)){
            // 已经报工到erp 无法修改
            throw new CommonException("已报工到erp，无法修改");
        }

        productMachineTaskPO.setUpdateBy(SecurityUtil.getUserId());
        productMachineTaskPO.setUpdateTime(new Date());
        iProductMachineTaskService.updateById(productMachineTaskPO);

        machineTaskHistoryPO.setUpdateBy(SecurityUtil.getUserId());
        machineTaskHistoryPO.setUpdateTime(new Date());
        return updateById(machineTaskHistoryPO);
    }

    @Override
    public List<ProductMachineTaskHistoryPO> getSubmitApprovalList(Long productTicketId,Integer workflowRequestStatus) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskHistoryPO::getReportedProductId,productTicketId)
                .isNotNull(ProductMachineTaskHistoryPO::getWorkflowRequestId)
                .eq(Objects.nonNull(workflowRequestStatus),ProductMachineTaskHistoryPO::getWorkflowRequestStatus,workflowRequestStatus);
        return list(wrapper);
    }

    @Override
    public ProductMachineTaskHistoryPO getSingleByWorkRequestId(Long workRequestId) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskHistoryPO::getWorkflowRequestId,workRequestId)
                .last("limit 1");
        return getOne(wrapper);
    }

    @Override
    public ProductMachineTaskHistoryPO updateByOutboundId(UpdateOutboundInVO updateOutboundInVO) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineTaskHistoryPO::getProductOutboundId,updateOutboundInVO.getOutboundId());
        ProductMachineTaskHistoryPO one = getOne(lambdaQueryWrapper);
        if (one == null) {
            return null;
        }
        one.setReportedQuantity(updateOutboundInVO.getProducedQuantity());
        one.setUpdateTime(new Date());
        one.setUpdateBy(SecurityUtil.getUserId());
        updateById(one);
        return one;
    }

    @Override
    public List<ProductMachineTaskHistoryPO> getListByProductMachineTaskId(Long productMachineTaskId) {
        LambdaQueryWrapper<ProductMachineTaskHistoryPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineTaskHistoryPO::getProductMachineTaskId,productMachineTaskId);
        return list(lambdaQueryWrapper);
    }
}
