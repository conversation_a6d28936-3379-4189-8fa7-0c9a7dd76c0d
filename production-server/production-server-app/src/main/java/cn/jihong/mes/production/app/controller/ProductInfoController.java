package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductInfoPageInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketShiftInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductInfoService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 生产工程单信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@RestController
@RequestMapping("/productInfo")
@ShenyuSpringMvcClient(path = "/productInfo/**")
public class ProductInfoController {

    @Resource
    private IProductInfoService productInfoService;

    /**
     * 获得班次生产数据信息列表
     */
    @PostMapping("/getProductInfoList")
    public StandardResult<Pagination<ProductTicketShiftOutVO>>
        getProductInfoList(@RequestBody @Valid ProductTicketShiftInVO productTicketShiftInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productInfoService.getProductInfoList(productTicketShiftInVO));
    }

    /**
     * 获得班次生产数据信息
     */
    @GetMapping("/getProductInfo/{id}")
    public StandardResult<ProductTicketShiftOutVO>
    getProductInfo(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getProductInfo(id));
    }

    /**
     * 获得生产工单
     */
    @PostMapping("/getProductTicket")
    public StandardResult<Pagination<ProductionTicketInfoOutVO>>
    getProductTicket(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getProductTicket(productInfoPageInVO));
    }

    /**
     * 获得上料记录
     */
    @PostMapping("/getMaterialRecords")
    public StandardResult<Pagination<MaterialRecordsOutVO>>
    getMaterialRecords(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getMaterialRecords(productInfoPageInVO));
    }

    /**
     * 获得上栈板记录
     */
    @PostMapping("/getLastPalletRecords")
    public StandardResult<Pagination<LastPalletRecordsOutVO>>
    getLastPalletRecords(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getLastPalletRecords(productInfoPageInVO));
    }


    /**
     * 获得出站记录
     */
    @PostMapping("/getOutboundRecords")
    public StandardResult<Pagination<OutboundInfoOutVO>>
    getOutboundRecords(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getOutboundRecords(productInfoPageInVO));
    }

    /**
     * 获得不良品记录
     */
    @PostMapping("/getDefectiveProductsRecords")
    public StandardResult<Pagination<DefectiveProductsInfoOutVO>>
    getDefectiveProductsRecords(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInfoService.getDefectiveProductsRecords(productInfoPageInVO));
    }


    /**
     * 校验首检有无完成 true:已完成 false:未完成
     */
    @GetMapping("/verifyFirstCheckCompleted/{productTicketId}")
    public StandardResult<Boolean> verifyFirstCheckCompleted(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productInfoService.verifyFirstCheckCompleted(productTicketId));
    }





}
