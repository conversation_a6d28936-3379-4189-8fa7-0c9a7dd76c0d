package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.GetOldBoxCodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeOutVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailService;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 箱码号段明细
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@RestController
@RequestMapping("/productBoxBarcodeDetail")
@ShenyuSpringMvcClient(path = "/productBoxBarcodeDetail/**")
public class ProductBoxBarcodeDetailController {

    @Resource
    private IProductBoxBarcodeDetailService productBoxBarcodeDetailService;
    @Resource
    private IProductBoxBarcodeService productBoxBarcodeService;

    /**
     * 申请箱码号段
     * 
     * @param addProductBoxBarcodeDetailInVO
     * @return
     */
    @PostMapping("/applyBoxBarcodeDetail")
    public StandardResult
        applyBoxBarcodeDetail(@RequestBody @Valid AddProductBoxBarcodeDetailInVO addProductBoxBarcodeDetailInVO) {
        String LOCK_KEY = RedisCacheConstant.BOX_APPLY + addProductBoxBarcodeDetailInVO.getProductBarcodeId();
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productBoxBarcodeDetailService.applyBoxBarcodeDetail(LOCK_KEY, addProductBoxBarcodeDetailInVO));
    }


    /**
     * 申请箱码号段  -- 空白条码
     *
     * @param applyBoxBarcodeDetailNoInfoInVO
     * @return
     */
    @PostMapping("/applyBoxBarcodeDetailNoInfo")
    public StandardResult
    applyBoxBarcodeDetailNoInfo(@RequestBody @Valid ApplyBoxBarcodeDetailNoInfoInVO applyBoxBarcodeDetailNoInfoInVO) {
        GetProductBoxBarcodeOutVO enableBoxBarcode = productBoxBarcodeService.getEnableBoxBarcode(CompanyBoxEnum.COMPANY_MDL.getCode());
        applyBoxBarcodeDetailNoInfoInVO.setProductBarcodeId(enableBoxBarcode.getId());
        String LOCK_KEY = RedisCacheConstant.BOX_APPLY + applyBoxBarcodeDetailNoInfoInVO.getProductBarcodeId();
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.applyBoxBarcodeDetailNoInfo(LOCK_KEY, applyBoxBarcodeDetailNoInfoInVO));
    }

    /**
     * 查询申请箱码号段列表
     *
     * @param getProductBoxBarcodeDetailInVO
     * @return
     */
    @PostMapping("/getList")
    public StandardResult<Pagination<GetProductBoxBarcodeDetailOutVO>>
        getList(@RequestBody @Valid GetProductBoxBarcodeDetailInVO getProductBoxBarcodeDetailInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productBoxBarcodeDetailService.getList(getProductBoxBarcodeDetailInVO));
    }

    /**
     * 查询申请箱码号段详情
     *
     * @param id
     * @return
     */
    @PostMapping("/getBoxBarcodeDetail/{id}")
    public StandardResult<GetProductBoxBarcodeDetailOutVO>
    getBoxBarcodeDetail(@PathVariable("id") Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.getBoxBarcodeDetail(id));
    }

    /**
     * 变更箱码号段明细
     * 
     * @param updateProductBoxBarcodeDetailInVO
     * @return
     */
    @PostMapping("/update")
    public StandardResult
        update(@RequestBody @Valid UpdateProductBoxBarcodeDetailInVO updateProductBoxBarcodeDetailInVO) {
        String LOCK_KEY = RedisCacheConstant.BOX_APPLY_UPDATE + updateProductBoxBarcodeDetailInVO.getId();
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productBoxBarcodeDetailService.update(LOCK_KEY, updateProductBoxBarcodeDetailInVO));
    }

    /**
     * 启用/作废条码
     * 
     * @param
     * @return
     */
    @GetMapping("/disableBoxBarcode/{id}")
    public StandardResult disableBoxBarcode(@PathVariable("id") Integer id) {
        productBoxBarcodeDetailService.disableBoxBarcode(id);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 打印
     * 
     * @param
     * @return
     */
    @GetMapping("/printBoxBarcode/{id}")
    public StandardResult<List<String>> printBoxBarcode(@PathVariable("id") Integer id) {
        String LOCK_KEY = RedisCacheConstant.BOX_PRINT + id;
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productBoxBarcodeDetailService.printBoxBarcode(LOCK_KEY, id));
    }

    /**
     * 获得服务器列表
     *
     * @param
     * @return
     */
    @GetMapping("/getClintList")
    public StandardResult getClintList() {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.getClintList());
    }

    /**
     * 首件喷码
     *
     * @param
     * @return
     */
    @PostMapping("/firstPrintPMJBoxBarcode")
    public StandardResult<String> firstPrintPMJBoxBarcode(@RequestBody PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.firstPrintPMJBoxBarcode(printPMJBoxBarcodeInVO));
    }

    /**
     * 补打印
     *
     * @param
     * @return
     */
    @GetMapping("/rePrintBoxBarcode/{id}")
    public StandardResult<List<String>> rePrintBoxBarcode(@PathVariable("id") Integer id) {
        String LOCK_KEY = RedisCacheConstant.BOX_PRINT + id;
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.rePrintBoxBarcode(LOCK_KEY, id));
    }

    /**
     * 喷码打开
     *
     * @param
     * @return
     */
    @PostMapping("/printOpen")
    public StandardResult<String> printOpen(@RequestBody PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        productBoxBarcodeDetailService.printOpen(printPMJBoxBarcodeInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 喷码关闭
     *
     * @param
     * @return
     */
    @PostMapping("/printClose")
    public StandardResult<String> printClose(@RequestBody PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        productBoxBarcodeDetailService.printClose(printPMJBoxBarcodeInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 喷码
     *
     * @param
     * @return
     */
    @PostMapping("/printPMJBoxBarcode")
    public StandardResult<String> printPMJBoxBarcode(@RequestBody PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
        String LOCK_KEY = RedisCacheConstant.BOX_PRINT + printPMJBoxBarcodeInVO.getIp();
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productBoxBarcodeDetailService.printPMJBoxBarcode(LOCK_KEY, printPMJBoxBarcodeInVO));
    }


//    /**
//     * 补喷码
//     *
//     * @param
//     * @return
//     */
//    @PostMapping("/rePrintPMJBoxBarcode")
//    public StandardResult<String> rePrintPMJBoxBarcode(@RequestBody PrintPMJBoxBarcodeInVO printPMJBoxBarcodeInVO) {
//        String LOCK_KEY = RedisCacheConstant.BOX_PRINT + printPMJBoxBarcodeInVO.getIp();
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productBoxBarcodeDetailService.rePrintPMJBoxBarcode(LOCK_KEY, printPMJBoxBarcodeInVO));
//    }



    /**
     * 获得明细中每一个箱码的信息
     *
     * @param id
     * @return
     */
    @PostMapping("/getDetail/{id}")
    public StandardResult<List<ProductBoxBarcodeDetailDetailDTO>> getDetail(@PathVariable("id") Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productBoxBarcodeDetailService.getDetail(id));
    }


    /**
     * 获得旧箱码的信息
     * @param getOldBoxCodeDetailInVO
     * @return
     */
    @PostMapping("/getOldBoxCodeDetail")
    public StandardResult<GetOldBoxCodeDetailOutVO> getOldBoxCodeDetail(@RequestBody GetOldBoxCodeDetailInVO getOldBoxCodeDetailInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productBoxBarcodeDetailService.getOldBoxCodeDetail(getOldBoxCodeDetailInVO));
    }


    /**
     * 复制旧箱码到新箱码
     * @param copyOldBoxCodeToNewBoxCodeInVO
     * @return
     */
    @PostMapping("/copyOldBoxCodeToNewBoxCode")
    public StandardResult copyOldBoxCodeToNewBoxCode(@RequestBody @Valid CopyOldBoxCodeToNewBoxCodeInVO copyOldBoxCodeToNewBoxCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productBoxBarcodeDetailService.copyOldBoxCodeToNewBoxCode(copyOldBoxCodeToNewBoxCodeInVO));
    }

}
