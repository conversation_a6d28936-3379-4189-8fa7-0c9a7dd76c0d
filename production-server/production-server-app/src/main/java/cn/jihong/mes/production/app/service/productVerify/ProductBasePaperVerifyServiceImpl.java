package cn.jihong.mes.production.app.service.productVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.mes.api.service.IProductionPlanService;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.dto.BasePaperDTO;
import cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO;
import cn.jihong.mes.production.api.model.enums.MaterialOperateTypeEnum;
import cn.jihong.mes.production.api.model.enums.OutboundVerifyEnum;
import cn.jihong.mes.production.api.service.IProductMaterialOperationRecordsService;
import cn.jihong.mes.production.api.service.IProductMaterialService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.model.vo.GetStandardConsumptionOutVO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.IMaterialsInfoService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import cn.jihong.oa.erp.api.service.ISfbaTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 原纸出站校验
 * 
 * @date 2024/01/26
 */
@Slf4j
@Service
public class ProductBasePaperVerifyServiceImpl extends ProductVerifyHelper  {

    @DubboReference
    private ISfcbTService sfcbTService;
    @DubboReference
    private IProductionPlanService productionPlanService;
    @DubboReference
    private ISfbaTService sfbaTService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Resource
    private IProductMaterialService productMaterialService;
    @Resource
    private IProductMaterialOperationRecordsService productMaterialOperationRecordsService;
    @Resource
    private IProductTicketService productTicketService;

    @Override
    public OutboundVerifyEnum getOutboundVerify() {
        return OutboundVerifyEnum.BASE_PAPER;
    }

    /**
     * 全局校验
     * 
     * @param planTicketNo
     * @param machineName
     * @return
     */
    @Override
    public boolean verifyOutBount(String planTicketNo, String machineName, String unit,String processCode, BigDecimal producedQuantity) {
        log.info("校验出站数量和物料之间的关系开始");
        verifyOutBountBetweenMaterial(planTicketNo, machineName, unit,processCode,producedQuantity);
        log.info("校验出站数量和物料之间的关系结束");
        return true;
    }


    /**
     * 全局校验
     *
     * @param planTicketNo
     * @param machineName
     * @return
     */
    @Override
    public boolean verifyPallet(String planTicketNo, String machineName, String unit,String processCode, BigDecimal producedQuantity) {
        log.info("校验出站数量和栈板之间的关系开始");
        verifyOutBountBetweenPallet(planTicketNo, machineName, unit,processCode,producedQuantity);
        log.info("校验出站数量和栈板之间的关系结束");
        return true;
    }


    /**
     * 获得工艺损耗率
     * 
     * @param planTicketNo
     * @return
     */
    @Override
    public BigDecimal getProcessAttritionRate(String planTicketNo,String process) {
        List<SfcbTVO> sfcbTVOS = sfcbTService.getByTickNo(planTicketNo);
        Map<String, SfcbTVO> current =
            sfcbTVOS.stream().collect(Collectors.toMap(SfcbTVO::getSfcb003, Function.identity()));
        // 排序 获得当前所在的工艺，并获得之前的工艺
        List<SfcbTVO> sfcbTSort = Lists.newArrayList();
        SfcbTVO sfcbTVO = current.get(process);
        setSfcbTSort(current, sfcbTSort, sfcbTVO);
        // 获得每个工艺的工艺损耗率
        return sfcbTSort.stream().map(s -> {
            // BigDecimal sfcbud013 = s.getSfcbud013();
            // BigDecimal sfcbud015 = s.getSfcbud015();
            log.info("当前工艺{}的损耗值：{}", s.getSfcb003(), s.getSfcbud011());
            return BigDecimal.ONE.subtract(s.getSfcbud011());
        }).reduce(BigDecimal.ONE, BigDecimal::multiply);
    }

    /**
     * 工序排序
     * 
     * @param last
     * @param sfcbTSort
     * @param sfcbTVO
     */
    private void setSfcbTSort(Map<String, SfcbTVO> last, List<SfcbTVO> sfcbTSort, SfcbTVO sfcbTVO) {
        if (ObjectUtil.isNotNull(sfcbTVO)) {
            sfcbTSort.add(sfcbTVO);
            sfcbTVO = last.get(sfcbTVO.getSfcb007());
            setSfcbTSort(last, sfcbTSort, sfcbTVO);
        }
    }

    /**
     * 获得开单标准消耗值
     * 
     * @param planTicketNo
     * @return
     */
    @Override
    public Map<String, BigDecimal> getStandardConsumption(String planTicketNo) {
        // // 取mes中的开单数量 / sfba 的开单数量
        // List<ProductionPlanPO> productionPlanPOS = productionPlanService
        // .getListByNameAndTicketNo(productTicketPO.getMachineName(), productTicketPO.getPlanTicketNo());
        // BigDecimal plannedProductionCapacity =
        // productionPlanPOS.stream().map(p -> new BigDecimal(p.getPlannedProductionCapacity()))
        // .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        //
        // List<SfbaTVO> byProductTicketNo = sfbaTService.getByProductTicketNo(productTicketPO.getPlanTicketNo());
        List<GetStandardConsumptionOutVO> standardConsumption = sfbaTService.getStandardConsumption(planTicketNo);
        if (CollectionUtil.isNotEmpty(standardConsumption)) {
            return standardConsumption.stream().collect(Collectors.toMap(GetStandardConsumptionOutVO::getImaaua005,
                s -> s.getSfba010().divide(s.getSfba011(), 5, RoundingMode.HALF_UP)));
        }
        return null;
    }

    /**
     * 获得重量、幅宽、克重
     * 
     * @param planTicketNo
     * @return
     */
    @Override
    public HashMap<String, BasePaperDTO> getBasePaperInfo(String planTicketNo, String machineName) {
        HashMap<String, BasePaperDTO> basePaperMap = Maps.newHashMap();
        // 原纸克重：imaa_t(imaaud011)，幅宽：imaa_t(imaaud015)
        List<Long> productTicketIds = getProductTicketIds(planTicketNo, machineName);
        List<ProductMaterialOperationRecordsDTO> productMaterialPOS = productMaterialOperationRecordsService
            .getByProductTicketIds(productTicketIds, Arrays.asList(Integer.valueOf(MaterialOperateTypeEnum.UP_MATERIAL.getCode())));
        if (CollectionUtil.isEmpty(productMaterialPOS)) {
            throw new CommonException("查询不到物料信息,机台还未上料操作");
        }
//        List<String> materialTypes =
//            productMaterialPOS.stream().map(ProductMaterialPO::getMaterialType).distinct().collect(Collectors.toList());

        Stream<Map.Entry<String, String>> map = productMaterialPOS.stream()
            .collect(Collectors.toMap(ProductMaterialOperationRecordsDTO::getMaterialCode, ProductMaterialOperationRecordsDTO::getMaterialType,(v1,v2)->v1))
            .entrySet().stream().distinct();

        map.forEach(m -> {
            String materialCode = m.getKey();
            String materialType = m.getValue();
            if (!materialType.startsWith(BasePaperConst.PAPER_TYPE)) {
                log.info("物料{}不是原纸，不处理", materialType);
                return;
            }
            BasePaperDTO basePaperDTO = new BasePaperDTO();
            BasePaperDTO.PaperInfo paperInfo = new BasePaperDTO.PaperInfo();
            MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(materialCode);
            paperInfo.setGrammage(materialsInfoDTO.getImaaud011());
            paperInfo.setWidth(materialsInfoDTO.getImaaud015());
            log.info("物料{}的克重是{}", materialCode, materialsInfoDTO.getImaaud011());
            log.info("物料{}的幅宽是{}", materialCode, materialsInfoDTO.getImaaud015());
            basePaperDTO.setPaperInfo(paperInfo);
            basePaperMap.put(materialCode, basePaperDTO);
        });

        return basePaperMap;
    }

    /**
     * kg到pcs单位转换
     * 
     * @param planTicketNo
     * @param weight
     * @param materialType
     * @return
     */
    @Override
    public BigDecimal kgToPcs(String planTicketNo, BigDecimal weight, String materialType,String process) {
        // kg到pcs的转换：按照工单的开单标准消耗值，如某工单计划产量100个，开单标准消耗为3kg，若已经上料卷纸xKg，则xKg对应的pcs量为（y=x * 100 / 3）。
        Map<String, BigDecimal> standardConsumptionMap = getStandardConsumption(planTicketNo);
        BigDecimal standardConsumption = standardConsumptionMap.get(materialType); // 13003 原纸
        BigDecimal processAttritionRate = getProcessAttritionRate(planTicketNo,process);
        // 千克 * 消耗率 = pcs 数 ， pcs 数 * 损耗值 = 实际生产数
        // eg: 10kg 在消耗率100中，能生产1000pcs, 然后有一定的损耗率，比如0.9,那么只能生产 900pcs
        log.info("重量{}", weight);
        log.info("单位消耗率{}", standardConsumption);
        log.info("工序损耗值{}", processAttritionRate);
        log.info("转换后的数据{}", weight.multiply(standardConsumption).multiply(processAttritionRate));
        return weight.divide(standardConsumption, 5, RoundingMode.HALF_UP).multiply(processAttritionRate);
    }

    // pcs到kg单位转换
    @Override
    public BigDecimal pcsToKg(String planTicketNo, BigDecimal pcs,String process) {
        // kg到pcs的转换：按照工单的开单标准消耗值，如某工单计划产量100个，开单标准消耗为3kg，若已经上料卷纸xKg，则xKg对应的pcs量为（y=x * 100 / 3）。
        Map<String, BigDecimal> standardConsumptionMap = getStandardConsumption(planTicketNo);
        BigDecimal standardConsumption = standardConsumptionMap.get(BasePaperConst.PAPER_BIG_TYPE); // 13003 原纸
        BigDecimal processAttritionRate = getProcessAttritionRate(planTicketNo,process);
        // 片数 / 消耗率 得到 用了多少kg， kg / 损耗率 = 实际需要准备的物料数量
        // eg: 生产900pcs,在消耗率100中 需要 9kg，然后有一定的损耗率，比如0.9,那么需要准备10kg物料
        return pcs.multiply(standardConsumption).divide(processAttritionRate, 2, RoundingMode.HALF_UP);
    }

    // Kg到m单位转换
    @Override
    public BigDecimal kgToM(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialCode,String process) {
        HashMap<String, BasePaperDTO> basePaperMap = getBasePaperInfo(planTicketNo, machineName);
        BasePaperDTO basePaperDTO = basePaperMap.get(materialCode);
        basePaperDTO.setWeight(loadingQuantity);
        log.info("原纸信息：{}", JSON.toJSONString(basePaperDTO));
        // 原纸kg转换米数=原纸重量kg/原纸克重kg/原纸幅宽（M） 长 * 宽 * 单位面积 = 重量
        BigDecimal m = basePaperDTO.getWeight().multiply(BigDecimal.valueOf(1000L))
            .divide(basePaperDTO.getPaperInfo().getGrammage(), 10, RoundingMode.HALF_UP)
            .divide(basePaperDTO.getPaperInfo().getWidth().divide(BigDecimal.valueOf(1000L), 10, RoundingMode.HALF_UP),
                2, RoundingMode.HALF_UP);
        log.info("物料重量{}(转成克后)除以克重{}除以宽幅{}(转成M)，得到m数{}", basePaperDTO.getWeight().multiply(BigDecimal.valueOf(1000L)),
            basePaperDTO.getPaperInfo().getGrammage(),
            basePaperDTO.getPaperInfo().getWidth().divide(BigDecimal.valueOf(1000L)), m);
        BigDecimal processAttritionRate = getProcessAttritionRate(planTicketNo,process);
        log.info("物料重量m数{}乘以损失率{}", m, processAttritionRate);
        return m.multiply(processAttritionRate);
    }

    // Kg到Z单位转换
    @Override
    public BigDecimal kgToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialType,String processCode) {
        BigDecimal pcs = kgToPcs(planTicketNo, loadingQuantity, materialType,processCode);
        //  模数
//        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNo);
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        return pcs.divide(sfcbTVO.getSfcbud016(), 2, RoundingMode.HALF_UP);
    }

    // m到Kg单位转换
    @Override
    public BigDecimal mToKg(String planTicketNo, String machineName, String materialCode, BigDecimal loadingQuantity,
        String process) {
        HashMap<String, BasePaperDTO> basePaperMap = getBasePaperInfo(planTicketNo, machineName);
        BasePaperDTO basePaperDTO = basePaperMap.get(materialCode);
        basePaperDTO.setNumberOfRice(loadingQuantity);
        // 原纸重量kg = 米数*原纸幅宽（M）*原纸克重kg
        BigDecimal kg = basePaperDTO.getNumberOfRice()
            .multiply(basePaperDTO.getPaperInfo().getGrammage().divide(new BigDecimal(1000L), 10, RoundingMode.HALF_UP))
            .multiply(basePaperDTO.getPaperInfo().getWidth()).divide(new BigDecimal(1000L), 10, RoundingMode.HALF_UP);

        BigDecimal processAttritionRate = getProcessAttritionRate(planTicketNo, process);
        return kg.divide(processAttritionRate, 2, RoundingMode.HALF_UP);
    }

    // m到pcs单位转换
    @Override
    public BigDecimal mToPcs(String planTicketNo, String materialType, String machineName, String materialCode,
        BigDecimal loadingQuantity,String processCode) {
        if (BasePaperConst.PALLET_TYPE.equals(materialCode)) {
            // 栈板的话，需要取上一个工序的code
            List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(planTicketNo);
            for (GetProcessSeqByTickNoOutVO getProcessSeqByTickNoOutVO : processSeqList) {
                if (getProcessSeqByTickNoOutVO.getSfcb003().equals(processCode)) {
                    processCode = getProcessSeqByTickNoOutVO.getSfcb007();
                    break;
                }
            }
            // 米转个 ： 米数 * 模数 * 1000 / 截断长
            // 截断长 sfcbud017
            SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
            BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();

            if (sfcbTVO == null) {
                throw new CommonException("获取截断长失败：工单号" + planTicketNo + " 工序编号" + processCode);
            }
            BigDecimal sfcbud017 = sfcbTVO.getSfcbud017();
            if (sfcbud017.compareTo(BigDecimal.ZERO) == 0) {
                throw new CommonException("截断长为0");
            }
            log.info("米转个: 米数 * 模数 * 1000 / 截断长 : {}*{}*1000/{}",loadingQuantity,sfcbud016,sfcbud017);
            return loadingQuantity.multiply(sfcbud016).multiply(new BigDecimal(1000L)).divide(sfcbud017,
                    2, RoundingMode.HALF_UP);
        } else {
            // 米转个 ： 米转千克（涉及重量、幅宽、克重）  然后 千克转个
            // m-> kg
            BigDecimal kg = mToKg(planTicketNo, machineName, materialCode, loadingQuantity,processCode);
            // kg-> pcs
            return kgToPcs(planTicketNo, kg, materialType,processCode);
        }
    }

    @Override
    public BigDecimal pcsToM(String planTicketNo, String machineName, BigDecimal loadingQuantity, String materialCode, String processCode) {
        // 个转米 ： 个数 / 模数 / 1000 * 截断长
        //  模数
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        BigDecimal sfcbud017 = sfcbTVO.getSfcbud017();
        BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();
        log.info("个转米  ：  个数 / 模数  / 1000 *  截断长 : {}/{}/1000*{}", loadingQuantity, sfcbud016, sfcbud017);
        return loadingQuantity.divide(sfcbud016, 10, RoundingMode.HALF_UP)
                .divide(new BigDecimal(1000L), 2, RoundingMode.HALF_UP).multiply(sfcbud017);
    }

    @Override
    public BigDecimal yToPcs(BigDecimal loadingQuantity, String planTicketNo,String processCode) {
        // 张转个 ： 张数 * 模数
        //  模数
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();
        log.info("张转个 ： 张数 * 模数   : {} * {}", loadingQuantity, sfcbud016);
        return loadingQuantity.multiply(sfcbud016);
    }

    // pcs到m单位转换
    @Override
    public BigDecimal pcsToM(BigDecimal loadingQuantity, String planTicketNo, String machineName, BigDecimal pcs,
        String materialCode,String processCode) {
        // pcs-> kg
        BigDecimal kg = pcsToKg(planTicketNo, pcs,processCode);
        // kg-> m
        HashMap<String, BasePaperDTO> basePaperMap = getBasePaperInfo(planTicketNo, machineName);
        BasePaperDTO basePaperDTO = basePaperMap.get(materialCode);
        basePaperDTO.setWeight(kg);
        BigDecimal m = kgToM(loadingQuantity, planTicketNo, machineName, materialCode,processCode);
        return m;
    }

    @Override
    public BigDecimal mToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialCode,
        String processCode) {
        SfcbTVO sfcbTVO = new SfcbTVO();
        if (BasePaperConst.PALLET_TYPE.equals(materialCode)) {
            // 栈板的话，需要取上一个工序的code
            List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(planTicketNo);
            for (GetProcessSeqByTickNoOutVO getProcessSeqByTickNoOutVO : processSeqList) {
                if (getProcessSeqByTickNoOutVO.getSfcb003().equals(processCode)) {
                    processCode = getProcessSeqByTickNoOutVO.getSfcb007();
                    break;
                }
            }
            sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        } else {
            sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        }
        // 米转张 ： 米数 * 1000 / 截断长
        BigDecimal sfcbud017 = sfcbTVO.getSfcbud017();
        log.info("米转张  ：  米数  * 1000 /  截断长 : {}*1000/{}", loadingQuantity, sfcbud017);
        if (sfcbud017.compareTo(BigDecimal.ZERO) == 0) {
            throw new CommonException("截断长为0");
        }
        return loadingQuantity.multiply(new BigDecimal(1000L)).divide(sfcbud017, 2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal pcsToZ(BigDecimal loadingQuantity, String planTicketNo, String machineName, String materialCode,
        String processCode) {
        // 个转张 ： 个数 / 模数
        //  模数
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(planTicketNo, processCode);
        BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();
        log.info("个转米  ：  个数 / 模数   : {}/{}", loadingQuantity, sfcbud016);
        return loadingQuantity.divide(sfcbud016, 2, RoundingMode.HALF_UP);
    }

}
