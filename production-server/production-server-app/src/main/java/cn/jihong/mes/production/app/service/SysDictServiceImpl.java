package cn.jihong.mes.production.app.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.SysDictPO;
import cn.jihong.mes.production.api.model.vo.in.SysDictInVO;
import cn.jihong.mes.production.api.model.vo.in.SysDictQueryInVO;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mes.production.api.service.ISysDictService;
import cn.jihong.mes.production.app.mapper.SysDictMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统字典表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@DubboService
public class SysDictServiceImpl extends JiHongServiceImpl<SysDictMapper, SysDictPO> implements ISysDictService {

    @Override
    public Pagination<SysDictOutVO> page(SysDictQueryInVO queryInVO) {
        // 构建查询条件
        LambdaQueryWrapper<SysDictPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(queryInVO.getDictType())) {
            queryWrapper.like(SysDictPO::getDictType, queryInVO.getDictType());
        }
        if (StringUtils.isNotBlank(queryInVO.getDictCode())) {
            queryWrapper.like(SysDictPO::getDictCode, queryInVO.getDictCode());
        }
        if (StringUtils.isNotBlank(queryInVO.getDictLabel())) {
            queryWrapper.like(SysDictPO::getDictLabel, queryInVO.getDictLabel());
        }
        
        // 按照排序字段排序
        queryWrapper.orderByAsc(SysDictPO::getSort);
        
        // 分页查询
        IPage<SysDictPO> page = page(new Page<>(queryInVO.getPageNum(), queryInVO.getPageSize()), queryWrapper);
        
        // 转换为VO对象
        List<SysDictOutVO> records = page.getRecords().stream()
                .map(this::convertToOutVO)
                .collect(Collectors.toList());

        return Pagination.newInstance(records, page.getTotal(), page.getPages());
    }

    @Override
    public SysDictOutVO getSysDictById(Long id) {
        SysDictPO sysDictPO = this.getById(id);
        return convertToOutVO(sysDictPO);
    }

    @Override
    public boolean add(SysDictInVO dictInVO) {
        SysDictPO sysDictPO = new SysDictPO();
        BeanUtils.copyProperties(dictInVO, sysDictPO);
        return save(sysDictPO);
    }

    @Override
    public boolean update(SysDictInVO dictInVO) {
        SysDictPO sysDictPO = new SysDictPO();
        BeanUtils.copyProperties(dictInVO, sysDictPO);
        return updateById(sysDictPO);
    }

    @Override
    public boolean delete(Long id) {
        return removeById(id);
    }

    @Override
    public boolean batchDelete(List<Long> ids) {
        return removeByIds(ids);
    }

    @Override
    public List<SysDictOutVO> listByType(String dictType) {
        LambdaQueryWrapper<SysDictPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictPO::getDictType, dictType);
        queryWrapper.orderByAsc(SysDictPO::getSort);
        
        List<SysDictPO> list = list(queryWrapper);
        return list.stream()
                .map(this::convertToOutVO)
                .collect(Collectors.toList());
    }

    @Override
    public SysDictOutVO listByTypeAndCode(String dictType, String code) {
        LambdaQueryWrapper<SysDictPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictPO::getDictType, dictType)
                .eq(SysDictPO::getDictCode, code);
        SysDictPO one = getOne(queryWrapper);
        if (one != null) {
            return convertToOutVO(one);
        }
        return null;
    }

    /**
     * 将PO对象转换为OutVO对象
     */
    private SysDictOutVO convertToOutVO(SysDictPO sysDictPO) {
        if (sysDictPO == null) {
            return null;
        }
        SysDictOutVO outVO = new SysDictOutVO();
        BeanUtils.copyProperties(sysDictPO, outVO);
        return outVO;
    }
}
