# Spring
spring:
  cloud:
    nacos:
      server-addr: **********:8848
      config:
        namespace: prod
      discovery:
        namespace: prod
dubbo:
  registry:
    address: nacos://**********:8848
    # 配置namespace命名空间
    parameters:
      namespace: ${spring.cloud.nacos.config.namespace}

#mybatis
mybatis-plus:
  # 日志打印
  configuration:
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
    #    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl #关闭sql日志
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl #开启sql日志