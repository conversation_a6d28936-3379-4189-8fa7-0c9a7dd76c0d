server:
  port: 9003
spring:
  application:
    name: production-server
  profiles:
    active: @spring.profiles.active@
#    active: dev
  cloud:
    nacos:
      config:
        file-extension: yml
        group: mes
        shared-configs:
          - dataId: common.yml
            group: OA
            refresh: true
          - dataId: seata-common.yml
            group: SEATA_GROUP
            refresh: true
          - dataId: tx-global-seata-service-group.yml
            group: SEATA_GROUP
            refresh: true

  redisson:
    enabled: true

shenyu:
  client:
    http:
      props:
        contextPath: /production-server



