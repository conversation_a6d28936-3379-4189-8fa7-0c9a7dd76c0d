<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductShiftMapper">

    <select id="getProductShift" resultType="cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO">
        select
            GROUP_CONCAT(ps.team_users SEPARATOR ',') as teamUsers,
            pt.machine_name as machineName,
            pt.process as process,
            ps.produce_date as produceDate,
            ps.shift as shift,
            pt.team_users as teamUsersName,
            ps.product_ticket_id as productTicketId
        FROM
            product_ticket pt
        LEFT JOIN
            product_shift ps on pt.id = ps.product_ticket_id
        WHERE
            pt.id = #{productTicketId}
            AND pt.deleted = 0
            and ps.deleted = 0
            AND pt.company_code = #{companyCode}
        group by
            pt.machine_name,
            pt.process,
            ps.produce_date,
            ps.shift,
            ps.product_ticket_id
    </select>

    <select id="getProductShiftByTicketBase" resultType="cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO">
        select
            pt.machine_name as machineName,
            pt.process as process,
            ps.id as id,
            ps.produce_date as produceDate,
            ps.shift as shift,
            pt.team_users as teamUsersName,
            ps.product_ticket_id as productTicketId
        FROM
            product_ticket pt
        LEFT JOIN
            product_shift ps on pt.id = ps.product_ticket_id
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pt.deleted = 0
            and ps.deleted = 0
        group by
            pt.machine_name,
            pt.process,
            ps.id,
            ps.produce_date,
            ps.shift,
            ps.product_ticket_id
    </select>
</mapper>
