<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductLastPalletMapper">

    <select id="getLastPalletRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.LastPalletOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            plp.id as id,
            plp.pallet_code as palletCode,
            plp.pallet_source as palletSource,
            plp.loading_quantity as loadingQuantity,
            plp.unit as unit,
            plp.machine_stop_no as machineStopNo,
            plp.create_by as createBy
        FROM
            product_ticket pt
        LEFT JOIN
            product_pallet plp on pt.id = plp.product_ticket_id
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pt.deleted = 0
            and plp.deleted = 0
    </select>

    <select id="getLastPalletRecords" resultType="cn.jihong.mes.production.api.model.vo.out.LastPalletOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            plp.id as id,
            plp.pallet_code as palletCode,
            plp.pallet_source as palletSource,
            plp.loading_quantity as loadingQuantity,
            plp.loading_time as loadingTime,
            plp.unit as unit,
            plp.machine_stop_no as machineStopNo,
            plp.create_by as createBy
        FROM
            product_ticket pt
        LEFT JOIN
            product_pallet plp on pt.id = plp.product_ticket_id
        WHERE
            pt.id = #{productTicketId}
            AND pt.deleted = 0
            and plp.deleted = 0
    </select>

    <select id="getPalletUseList" resultType="cn.jihong.mes.production.api.model.vo.out.PalletUseOutVO">
        select
            plp.*,
            plp.production_num as productQuantity
        FROM
            product_pallet plp
        LEFT JOIN
            product_ticket pt on pt.id = plp.product_ticket_id
        <where>
            pt.deleted = 0
            <if test="getPalletListPageInVO.companyCode != null and getPalletListPageInVO.companyCode != '' ">
                and pt.company_code = #{getPalletListPageInVO.companyCode}
            </if>
            <if test="getPalletListPageInVO.palletCode != null and getPalletListPageInVO.palletCode != '' ">
                and plp.pallet_code like CONCAT('%',#{getPalletListPageInVO.palletCode} ,'%')
            </if>
            <if test="getPalletListPageInVO.planTicketNo != null and getPalletListPageInVO.planTicketNo != '' ">
                and plp.production_order like CONCAT('%',#{getPalletListPageInVO.planTicketNo},'%')
            </if>
            <if test="getPalletListPageInVO.palletSource != null and getPalletListPageInVO.palletSource != '' ">
                and plp.pallet_source = #{getPalletListPageInVO.palletSource}
            </if>
            <if test="getPalletListPageInVO.produceDate != null">
                and DATE(plp.production_time) = #{getPalletListPageInVO.produceDate,jdbcType=DATE}
            </if>
            <if test="getPalletListPageInVO.shiftSource != null and getPalletListPageInVO.shiftSource != '' ">
                and plp.shift_source = #{getPalletListPageInVO.shiftSource}
            </if>
            <if test="getPalletListPageInVO.status != null and getPalletListPageInVO.status != '' ">
                and plp.status = #{getPalletListPageInVO.status}
            </if>
            <if test="getPalletListPageInVO.writeOffStatus != null and getPalletListPageInVO.writeOffStatus != '' ">
                and plp.write_off_status = #{getPalletListPageInVO.writeOffStatus}
            </if>
        </where>
        order by plp.create_time desc

    </select>

    <select id="getPalletUseDetial" resultType="cn.jihong.mes.production.api.model.vo.out.PalletUseOutVO">
        select
            plp.*,
            plp.production_num as productQuantity,
            pt.company_code
        FROM
            product_pallet plp
        LEFT JOIN
            product_ticket pt on pt.id = plp.product_ticket_id
        <where>
            plp.id = #{id}
        </where>
    </select>

    <select id="getPalletUseDetialList" resultType="cn.jihong.mes.production.api.model.vo.out.PalletUseRecordOutVO">
        select
        pr.*,
        plp.outbound_id
        FROM
            product_pallet_operation_records pr
        inner join
            product_pallet plp on pr.product_pallet_id = plp.id
        <where>
            plp.id = #{productInfoPageInVO.id}
        </where>

    </select>
</mapper>
