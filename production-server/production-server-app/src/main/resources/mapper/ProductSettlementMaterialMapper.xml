<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductSettlementMaterialMapper">


    <select id="getSettlementMaterialList" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO">
        SELECT
            d.material_name,
            d.material_unit,
            d.material_code,
            d.material_type,
            c.process_name,
            c.material_barcode_no,
            c.picking_quantity,
            c.consumption_quantity,
            c.purchase_batch
        FROM
            (
                SELECT
                    b.process AS process_name,
                    a.material_barcode_no,
                    sum( a.loading_quantity ) AS picking_quantity,
                    sum( a.consumption_quantity ) AS consumption_quantity,
                    count( 1 ) AS purchase_batch
                FROM
                    product_ticket b
                        LEFT JOIN product_material a  ON b.id = a.product_ticket_id
                WHERE
                    a.deleted = 0
                  AND b.deleted = 0
                  AND b.STATUS = 70
                  AND b.plan_ticket_no = #{productTicketNo}
                GROUP BY
                    b.process,
                    a.material_barcode_no
            ) c
                LEFT JOIN product_material d ON c.material_barcode_no = d.material_barcode_no
        WHERE
            d.deleted = 0
    </select>


    <select id="getSettlementMaterialDetail" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialDetailOutVO">
        SELECT
            a.process,
            b.material_name,
            b.material_place_name,
            b.consumption_quantity,
            b.loading_quantity,
            a.planned_product,
            a.real_product,
            ROUND(((1/(a.real_product/b.consumption_quantity)) - 1)*100) as attrition_rate
        FROM
            product_ticket a
                LEFT JOIN product_material b on a.id = b.product_ticket_id
        WHERE
            a.deleted = 0
          AND b.deleted = 0
          AND a.status = 70
          AND a.plan_ticket_no = #{inVo.productTicketNo} and a.process = #{inVo.process} and b.material_barcode_no = #{inVo.materialBarcodeNo}
    </select>
</mapper>
