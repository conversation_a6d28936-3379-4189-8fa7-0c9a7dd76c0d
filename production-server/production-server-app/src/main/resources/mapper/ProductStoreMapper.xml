<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductStoreMapper">

    <select id="queryPalletInfo" resultType="cn.jihong.mes.production.api.model.vo.out.QueryPalletInfoOutVO">
        SELECT
            t1.id,
            t1.company_code,
            t1.plan_ticket_no,
            t1.pallet_code,
            t1.case_code,
            t1.produced_quantity,
            t1.unit,
            t1.store_type,
            t1.store_apply_no,
            t1.store_no,
            t1.store_status,
            t1.product_name,
            t1.product_no,
            t1.storage,
            t1.lot_no,
            max(t1.workflow_request_id) AS workflow_request_id,
            max(t1.workflow_request_status) AS workflow_request_status,
            MAX(CASE WHEN t2.operation = '10' THEN t2.create_time END) AS apply_time,
            MAX(CASE WHEN t2.operation = '10' THEN t2.operator_name END) AS apply_user_name,
            MAX(CASE WHEN t2.operation = '20' THEN t2.create_time END) AS pull_time,
            MAX(CASE WHEN t2.operation = '20' THEN t2.operator_name END) AS pull_user_name,
            MAX(CASE WHEN t2.operation = '30' THEN t2.create_time END) AS inbound_time,
            MAX(CASE WHEN t2.operation = '30' THEN t2.operator_name END) AS inbound_user_name
        FROM
            product_store t1
        LEFT JOIN
            product_store_record t2 ON t1.id = t2.product_store_id
        WHERE
            t1.deleted = 0
            and t2.deleted = 0
            and t1.company_code = #{queryPalletInfoInVO.companyCode}
            <if test="queryPalletInfoInVO.storeNo!= null and queryPalletInfoInVO.storeNo!= ''">
                and t1.store_no = #{queryPalletInfoInVO.storeNo}
            </if>
            <if test="queryPalletInfoInVO.storeApplyNo!= null and queryPalletInfoInVO.storeApplyNo!= ''">
                and t1.store_apply_no = #{queryPalletInfoInVO.storeApplyNo}
            </if>
            <if test="queryPalletInfoInVO.planTicketNo!= null and queryPalletInfoInVO.planTicketNo!= ''">
                and t1.plan_ticket_no = #{queryPalletInfoInVO.planTicketNo}
            </if>
            <if test="queryPalletInfoInVO.productName != null and queryPalletInfoInVO.productName != ''">
                and t1.product_name like CONCAT('%', #{queryPalletInfoInVO.productName}, '%')
            </if>
            <if test="queryPalletInfoInVO.palletCode!= null and queryPalletInfoInVO.palletCode!= ''">
                and t1.pallet_code = #{queryPalletInfoInVO.palletCode}
            </if>
            <if test="queryPalletInfoInVO.storeStatus!= null and queryPalletInfoInVO.storeStatus!= ''">
                and t1.store_status = #{queryPalletInfoInVO.storeStatus}
            </if>
            <if test="queryPalletInfoInVO.applyTime != null ">
                and STR_TO_DATE(t1.create_time, '%Y-%m-%d') = #{queryPalletInfoInVO.applyTime}
            </if>
        GROUP BY
            t1.id,
            t1.company_code,
            t1.plan_ticket_no,
            t1.pallet_code,
            t1.case_code,
            t1.produced_quantity,
            t1.unit,
            t1.store_type,
            t1.store_apply_no,
            t1.store_no,
            t1.store_status,
            t1.product_name,
            t1.product_no,
            t1.storage
        ORDER BY
            t1.store_apply_no DESC
    </select>

    <select id="queryStorageInfo" resultType="cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO">
        <include refid="storageInfo">
        </include>
        ORDER BY
            t1.store_apply_no DESC
    </select>



    <sql id="storageInfo">
        select
            t1.company_code,
            t1.plan_ticket_no,
            SUM(t1.produced_quantity) as produced_quantity,
            CASE
                WHEN t1.store_type = 1 THEN 1
                WHEN t1.store_type = 2 THEN SUM(COALESCE(t3.pallet_count, 0))
            ELSE 0
            END as pallet_count,
            t1.unit,
            t1.store_apply_no,
            t1.store_no,
            t1.product_name,
            t1.product_no,
            t1.create_time as apply_time,
            t1.create_by as apply_user_name,
            SUM(case when t1.store_no is not null then 1 else 0 end) as had_store_count,
            case when SUM(case when t1.store_no is not null then 1 else 0 end) = COUNT(t1.store_apply_no) then 1 else 0 end as isAllIn,
            t1.lot_no,t1.standard,
            GROUP_CONCAT(DISTINCT t1.pallet_code SEPARATOR ',') AS palletCode
        from
            product_store t1
        left join (
            select
                product_store_id,
                COUNT(*) as pallet_count
            from
                product_store_box
            group by
                product_store_id
            ) t3 on t1.id = t3.product_store_id
        WHERE
            t1.deleted = 0
            and t1.company_code = #{queryPalletInfoInVO.companyCode}
            <if test="queryPalletInfoInVO.storeNo != null and queryPalletInfoInVO.storeNo!= ''">
                and t1.store_no = #{queryPalletInfoInVO.storeNo}
            </if>
            <if test="queryPalletInfoInVO.storeApplyNo!= null and queryPalletInfoInVO.storeApplyNo!= ''">
                and t1.store_apply_no = #{queryPalletInfoInVO.storeApplyNo}
            </if>
            <if test="queryPalletInfoInVO.planTicketNo != null and queryPalletInfoInVO.planTicketNo!= ''">
                and t1.plan_ticket_no = #{queryPalletInfoInVO.planTicketNo}
            </if>
            <if test="queryPalletInfoInVO.productName != null and queryPalletInfoInVO.productName != ''">
                and t1.product_name like CONCAT('%', #{queryPalletInfoInVO.productName}, '%')
            </if>
            <if test="queryPalletInfoInVO.palletCode!= null and queryPalletInfoInVO.palletCode!= ''">
                and t1.pallet_code = #{queryPalletInfoInVO.palletCode}
            </if>
            <if test="queryPalletInfoInVO.storeStatus!= null and queryPalletInfoInVO.storeStatus!= ''">
                and t1.store_status = #{queryPalletInfoInVO.storeStatus}
            </if>
            <if test="queryPalletInfoInVO.applyTime != null ">
                and STR_TO_DATE(t1.create_time, '%Y-%m-%d') = #{queryPalletInfoInVO.applyTime}
            </if>
        GROUP BY
            t1.company_code,
            t1.plan_ticket_no,
            t1.unit,
            t1.store_apply_no,
            t1.store_no,
            t1.product_name,
            t1.product_no,
            apply_time,
            apply_user_name,
            t1.store_type,
            t1.lot_no,
            t1.standard
    </sql>


    <select id="queryStorageInfoIsAllIn" resultType="cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO">
        <include refid="storageInfo">
        </include>
            having isAllIn != 1
        ORDER BY
            t1.store_apply_no DESC
    </select>
</mapper>
