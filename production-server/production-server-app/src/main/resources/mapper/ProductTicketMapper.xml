<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductTicketMapper">
    <select id="getPlanTicketNoList" resultType="cn.jihong.mes.production.api.model.vo.out.ProductTicketShiftOutVO">
        SELECT pt.company_code                                                                                        as companyCode,
               pt.machine_name                                                                                        as machineName,
               pt.produce_date                                                                                        as produceDate,
               pt.process_type                                                                                        as processType,
               pt.process                                                                                             as process,
               pt.shift                                                                                               as shift,
               pt.plan_ticket_no                                                                                      as planTicketNo,
               max(CASE
                       WHEN pp.standard_production_capacity IS NULL THEN 0
                       ELSE pp.standard_production_capacity END)                                                      as standardProductionCapacity,
               max(
                       CASE WHEN pt.planned_product IS NULL THEN 0 ELSE pt.planned_product END)                       AS plannedProductionCapacity,
               SUM(pt.real_product)                                                                                   as realProductionCapacity,
               min(pt.plan_start_date)                                                                                as productionPlanStartTime,
               max(pt.plan_end_date)                                                                                  as productionPlanEndTime,
               ROUND(TIMESTAMPDIFF(SECOND, min(pt.plan_start_date), max(pt.plan_end_date)) / 3600.0,
                     2)                                                                                               AS planHours,
               min(pt.start_date)                                                                                     as startDate,
               max(pt.end_date)                                                                                       as endDate,
               IFNULL(SUM(ROUND(TIMESTAMPDIFF(SECOND, pt.start_date, pt.end_date) / 3600.0, 2)),
                      0.0)                                                                                            AS realHours
        FROM product_ticket pt
                 LEFT JOIN production_plan pp ON pt.machine_name = pp.erp_machine_name
            AND pt.plan_ticket_no = pp.worker_order_no
            AND pt.produce_date = pp.production_plan_date
            AND pt.product_name = pp.production_name
            AND pt.shift = pp.serial_no
            and pt.company_code = pp.company_code
        where pt.machine_name = #{machineName}
          and pt.produce_date = #{produceDate}
          and pt.shift = #{shift}
          and pt.plan_ticket_no = #{planTicketNo}
          and pt.deleted = 0
          and pt.company_code = #{companyCode}
          and pt.process_code = #{processCode}
          and pt.process_type = #{processType}
          -- and pp.is_deleted = 0
        GROUP BY pt.company_code,
                 pt.produce_date,
                 pt.process_type,
                 pt.process,
                 pt.machine_name,
                 pt.plan_ticket_no,
                 pt.shift
    </select>

    <select id="getTicketByTicketBase" resultType="cn.jihong.mes.production.api.model.vo.out.ProductionTicketInfoOutVO">
        select pt.id                                                                             as id,
               pt.company_code                                                                   as companyCode,
               pt.machine_name                                                                   as machineName,
               pt.produce_date                                                                   as produceDate,
               pt.process_type                                                                   as processType,
               pt.process                                                                        as process,
               pt.shift                                                                          as shift,
               pt.plan_ticket_no                                                                 as planTicketNo,
               pt.product_name                                                                   as productName,
               pt.real_product,
               pt.start_date                                                                     as startDate,
               pt.end_date                                                                       as endDate,
               IFNULL(ROUND(TIMESTAMPDIFF(SECOND, pt.start_date, pt.end_date) / 3600.0, 2), 0.0) AS realHours,
               pt.team_users                                                                     as teamUsers,
               pt.status
        FROM product_ticket pt
        WHERE pt.machine_name = #{productTicketBaseDTO.machineName}
          AND pt.produce_date = #{productTicketBaseDTO.produceDate}
          AND pt.shift = #{productTicketBaseDTO.shift}
          AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
          AND pt.deleted = 0
    </select>

    <select id="getReportByFactory"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProductReportByFactoryOutVO">
        SELECT
        pt.company_code AS companyCode,
        pt.machine_name AS machineName,
        pt.produce_date AS produceDate,
        pt.shift AS shift,
        pt.plan_ticket_no AS planTicketNo,
        pt.product_name AS productName,
        pp.unit AS unit,
        max(pdrs.send_remark) as sendRemark,
        max(pdrs.send_name) as sendName,
        pt.planned_product AS plannedProductionCapacity,
        IFNULL(pp.standard_production_capacity, 0.0) AS standardProductionCapacity,
        ROUND(SUM(IFNULL(pmt.reported_quantity, 0.0)), 2) AS realProductionCapacity,
        ROUND(SUM(pt.defective_product), 2) AS defectiveProduct,
        ROUND(TIMESTAMPDIFF(SECOND, MIN(pt.plan_start_date), MAX(pt.plan_end_date)) / 3600.0, 2) AS planHours,
        ROUND(IFNULL(SUM(ROUND(TIMESTAMPDIFF(SECOND, pt.start_date, pt.end_date) / 3600.0, 2)), 0.0), 2) AS realHours,
        -- 损耗率：不良品/良品
        ROUND(
        CASE
        WHEN SUM(IFNULL(pmt.reported_quantity, 0.0)) > 0
        THEN ( SUM(pt.defective_product) / (SUM(IFNULL(pmt.reported_quantity, 0.0)) + SUM(pt.defective_product)) ) * 100
        ELSE 100
        END, 2
        ) AS lossRate,
        -- 生产计划达成率：实际产量/计划产量
        ROUND(
        CASE
        WHEN pt.planned_product > 0
        THEN (SUM(IFNULL(pmt.reported_quantity, 0.0)) / pt.planned_product) * 100
        ELSE 100
        END, 2
        ) AS planReachRate,
        -- 计划产能利用率：计划产量/生产时长/标准产能。生产时长取班次时长。
        ROUND(
        CASE
        WHEN pt.planned_product > 0 AND IFNULL(pp.standard_production_capacity, 0.0) > 0
        THEN ((pt.planned_product / ROUND(TIMESTAMPDIFF(SECOND, MIN(pt.plan_start_date), MAX(pt.plan_end_date)) /
        3600.0, 2)) / IFNULL(pp.standard_production_capacity, 0.0)) * 100
        ELSE 100
        END, 2
        ) AS planCapacityUtilRate,
        -- 产能利用率：实际产量/生产时长/标准产能。生产时长取班次时长。
        ROUND(
        CASE
        WHEN IFNULL(SUM(ROUND(TIMESTAMPDIFF(SECOND, pt.start_date, pt.end_date) / 3600.0, 2)), 0.0) > 0 AND
        IFNULL(pp.standard_production_capacity, 0.0) > 0
        THEN (SUM(IFNULL(pmt.reported_quantity, 0.0)) / ROUND(TIMESTAMPDIFF(SECOND, MIN(pt.plan_start_date),
        MAX(pt.plan_end_date)) / 3600.0, 2)) / IFNULL(pp.standard_production_capacity, 0.0) * 100
        ELSE 100
        END, 2
        ) AS realCapacityUtilRate,
        -- 设备时间稼动率：按照班次的报工记录
        ROUND(
        (IFNULL(SUM(ROUND(TIMESTAMPDIFF(SECOND, pt.start_date, pt.end_date) / 3600.0, 2)), 0.0) /
        ROUND(TIMESTAMPDIFF(SECOND, MIN(pt.plan_start_date), MAX(pt.plan_end_date)) / 3600.0, 2)) * 100, 2
        ) AS machineUtilRate,
        --  良品率
        100 - ROUND( case when SUM(IFNULL(pmt.reported_quantity, 0.0)) > 0 then ( SUM(pt.defective_product) / (SUM(IFNULL(pmt.reported_quantity, 0.0)) + SUM(pt.defective_product)) ) * 100 else 100 end, 2 ) as yieldRate
        FROM
        product_ticket pt
        LEFT JOIN production_plan pp ON pt.machine_name = pp.erp_machine_name
        AND pt.plan_ticket_no = pp.worker_order_no
        AND pt.produce_date = pp.production_plan_date
        AND pt.product_name = pp.production_name
        AND pt.shift = pp.serial_no
        LEFT JOIN(
        SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
        FROM product_machine_task
        GROUP BY reported_product_id
        ) pmt ON pmt.reported_product_id = pt.id
        LEFT JOIN (
        select tem.* from
        ( select *,row_number() over (partition by company_code,produce_date,machine_name,plan_ticket_no,shift order by create_time desc) as rn
        from product_daily_report_send ) tem
        where rn = 1
        ) pdrs ON
        pt.company_code = pdrs.company_code AND pt.produce_date = pdrs.produce_date AND
        pt.machine_name = pdrs.machine_name AND pt.plan_ticket_no = pdrs.plan_ticket_no AND pt.shift = pdrs.shift
        <where>
            <if test="getProductReportByFactoryInVO.companyCode != null and getProductReportByFactoryInVO.companyCode != '' ">
                and pt.company_code = #{getProductReportByFactoryInVO.companyCode}
            </if>
            <if test="getProductReportByFactoryInVO.machineName != null and getProductReportByFactoryInVO.machineName != ''">
                and pt.machine_name like CONCAT('%',#{getProductReportByFactoryInVO.machineName},'%')
            </if>
            <if test="getProductReportByFactoryInVO.produceDate != null and getProductReportByFactoryInVO.produceDate != ''">
                and pt.produce_date = #{getProductReportByFactoryInVO.produceDate}
            </if>
            <if test="getProductReportByFactoryInVO.planTicketNo != null and getProductReportByFactoryInVO.planTicketNo != ''">
                and pt.plan_ticket_no = #{getProductReportByFactoryInVO.planTicketNo}
            </if>
            and pt.deleted = 0
        </where>
        GROUP BY
        pt.company_code,
        pt.produce_date,
        pt.machine_name,
        pt.plan_ticket_no,
        pp.unit,
        pt.shift,
        pt.product_name,
        pp.standard_production_capacity,
        pt.planned_product
        order by
        <if test="getProductReportByFactoryInVO.sortName != null and getProductReportByFactoryInVO.sortName != ''
                and getProductReportByFactoryInVO.sortType != null and getProductReportByFactoryInVO.sortType != '' ">
            ${getProductReportByFactoryInVO.sortName} ${getProductReportByFactoryInVO.sortType},
        </if>
        pt.produce_date DESC
    </select>

    <select id="getProductTicketInfoList"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProductTicketInfoListOutVO">
        SELECT machine_name    AS erp_machine_name,
               plan_ticket_no  AS worker_order_no,
               produce_date    AS production_plan_date,
               product_name    AS production_name,
               shift           AS serial_no,
               plan_start_date AS production_plan_start_time,
               plan_end_date   AS production_plan_end_time,
               planned_product AS planned_production_capacity,
               process_type    AS production_process_type,
               process         AS production_process,
               process_code    AS production_process_code
        FROM `product_ticket`
        where deleted = 0
          AND produce_date = #{produceDate}
          AND shift = #{shift}
          AND machine_name = #{machineName}
        GROUP BY machine_name,
                 plan_ticket_no,
                 product_name,
                 produce_date,
                 process_code,
                 shift,
                 plan_start_date,
                 plan_end_date,
                 planned_product,
                 process_type,
                 process,
                 process_code
    </select>


    <resultMap id="GetProduceDetailsOutVO" type="cn.jihong.mes.production.api.model.vo.out.GetProduceDetailsOutVO">
        <result column="process_code" property="process"/>
        <result column="process" property="processName"/>
        <result column="plan_ticket_no" property="planTicketNo"/>
        <collection property="produceLists"
                    ofType="cn.jihong.mes.production.api.model.vo.out.GetProduceDetailsOutVO$ProduceInfo">
            <result column="plan_ticket_no" property="planTicketNo"/>
            <result column="machine_name" property="machineName"/>
            <result column="produce_date" property="produceDate"/>
            <result column="shift" property="shift"/>
            <result column="team_users" property="teamUsers"/>
            <result column="reported_quantity" property="productionNum"/>
            <result column="defective_product" property="defectiveProduct"/>
            <result column="first_check_result" property="firstCheckResult"/>
        </collection>
    </resultMap>


    <select id="getProduceDetailsCalibreReview"
            resultMap="GetProduceDetailsOutVO">
        SELECT pt.process,
               pt.process_code,
               pt.plan_ticket_no,
               pt.machine_name,
               pt.produce_date,
               pt.shift,
               pt.team_users,
               SUM(pt.defective_product)  as defective_product,
               pt.product_name,
               SUM(pmt.reported_quantity) as reported_quantity,
               pi.first_check_result
        FROM product_ticket pt
                 LEFT JOIN(
            SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
            FROM product_machine_task
            GROUP BY reported_product_id
        ) pmt ON pmt.reported_product_id = pt.id
                 LEFT JOIN product_info pi ON pt.machine_name = pi.machine_name
            AND pt.plan_ticket_no = pi.plan_ticket_no
            AND pt.produce_date = pi.produce_date
            AND pt.product_name = pi.product_name
            AND pt.shift = pi.shift
        WHERE pt.plan_ticket_no = #{planTicketNo}
        GROUP BY pt.process,
                 pt.process_code,
                 pt.plan_ticket_no,
                 pt.produce_date,
                 pt.shift,
                 pt.team_users,
                 pt.machine_name,
                 pt.product_name,
                 pi.first_check_result
        order by pt.produce_date
    </select>


    <resultMap id="GetMaterialDetailsOutVO" type="cn.jihong.mes.production.api.model.vo.out.GetMaterialDetailsOutVO">
        <result column="process_code" property="process"/>
        <result column="process" property="processName"/>
        <result column="plan_ticket_no" property="planTicketNo"/>
        <collection property="materialInfos"
                    ofType="cn.jihong.mes.production.api.model.vo.out.GetMaterialDetailsOutVO$MaterialInfo">
            <result column="plan_ticket_no" property="planTicketNo"/>
            <result column="material_code" property="materialCode"/>
            <result column="material_name" property="materialName"/>
            <result column="material_unit" property="materialUnit"/>
            <result column="purchase_batch" property="purchaseBatch"/>
        </collection>
    </resultMap>

    <select id="getMaterialDetailsCalibreReview"
            resultMap="GetMaterialDetailsOutVO">
        SELECT distinct pt.plan_ticket_no,
                        pt.process,
                        pt.process_code,
                        pt.produce_date,
                        pm.material_code,
                        pm.material_name,
                        pm.material_unit,
                        pm.purchase_batch
        FROM product_ticket pt
                 INNER JOIN product_material pm ON pm.product_ticket_id = pt.id
        WHERE pt.plan_ticket_no = #{planTicketNo}
        order by pt.produce_date
    </select>

    <select id="getMaterialUseDetails"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetMaterialUseDetailsOutVO">
        SELECT pt.plan_ticket_no,
               pt.machine_name,
               pt.produce_date,
               pt.shift,
               pm.material_barcode_no,
               pm.material_code,
               pm.material_type,
               pm.purchase_batch,
               pmr.operation_type,
               pmr.create_time,
               pmr.original_quantity,
               pmr.consumption_quantity,
               pmr.remaining_quantity,
               pmr.operator,
               pmr.transfer_no,
               pmr.deduction_no
        FROM product_ticket pt
                 INNER JOIN product_material_operation_records pmr on pmr.product_ticket_id = pt.id
                 INNER JOIN product_material pm ON pm.id = pmr.product_material_id
        WHERE pt.plan_ticket_no = #{getMaterialUseDetailsInVO.planTicketNo}
          and pm.material_code = #{getMaterialUseDetailsInVO.materialCode}
          and pm.purchase_batch = #{getMaterialUseDetailsInVO.purchaseBatch}
          and operation_type = '20'
        order by pt.produce_date
    </select>

    <resultMap id="GetMachineGroupReportOutVO"
               type="cn.jihong.mes.production.api.model.vo.out.GetMachineGroupReportOutVO">
        <result column="id" property="groupId"/>
        <result column="machine_group_name" property="groupName"/>
        <collection property="machineGroupDetialReportOutVOList"
                    ofType="cn.jihong.mes.production.api.model.vo.out.GetMachineGroupReportOutVO$GetMachineGroupDetialReportOutVO">
            <result column="machine_name" property="machineName"/>
            <result column="team_users" property="teamUsers"/>
            <result column="process" property="process"/>
            <result column="process_code" property="processCode"/>
            <result column="plan_ticket_no" property="planTicketNo"/>
            <result column="product_name" property="productionName"/>
            <result column="include_product_ticket_ids" property="includeProductTicketIds"/>
            <result column="planned_product" jdbcType="BIGINT" javaType="java.lang.String"
                    property="plannedProductionCapacity"/>
            <result column="unit" property="plannedProductionCapacityUnit"/>
            <result column="reported_quantity" property="realProductionCapacity"/>
            <result column="defective_product" property="defectiveProduct"/>
            <result column="production_rate" property="planReachRate"/>
            <result column="loss_rate" property="lossRate"/>
        </collection>
    </resultMap>

    <select id="getMachineGroupReport"
            resultMap="GetMachineGroupReportOutVO">
        SELECT
        t1.id,
        t1.machine_group_name,
        t2.machine_name,
        t3.team_users,
        t3.process,
        t3.process_code,
        t3.plan_ticket_no,
        t3.product_name,
        GROUP_CONCAT( t3.id SEPARATOR ',' ) as include_product_ticket_ids,
        CAST(ROUND(t3.planned_product) AS SIGNED ) as planned_product,
        t3.unit,
        ( CASE WHEN SUM(t4.reported_quantity) IS NULL THEN 0 ELSE SUM(t4.reported_quantity) END ) as reported_quantity,
        ( CASE WHEN SUM(t3.defective_product) IS NULL THEN 0 ELSE SUM(t3.defective_product) END ) as defective_product,
        SUM(t4.reported_quantity)/CASE t3.planned_product
        WHEN 0 THEN 1
        ELSE t3.planned_product END as production_rate,
        SUM(t3.defective_product)/ (SUM(t4.reported_quantity) + SUM(t3.defective_product)) as loss_rate
        FROM
        product_machine_group t1
        INNER JOIN product_machine_group_relationship t2 ON t1.id = t2.machine_group_id and t2.deleted = 0
        INNER JOIN product_ticket t3 ON t3.machine_name = t2.machine_name
        AND t3.company_code = t2.company_code and t3.deleted = 0
        LEFT JOIN ( SELECT reported_product_id, SUM( reported_quantity ) AS reported_quantity FROM product_machine_task
        WHERE deleted = 0 GROUP BY reported_product_id ) t4 ON t3.id = t4.reported_product_id
        WHERE t1.deleted = 0
        and t1.id IN
        <foreach collection="getMachineGroupReportInVO.groupIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t3.company_code = #{companyCode}
        AND t3.produce_date = #{getMachineGroupReportInVO.produceDate}
        AND t3.shift = #{getMachineGroupReportInVO.shift}
        GROUP BY
        t1.id,
        t1.machine_group_name,
        t2.machine_name,
        t3.team_users,
        t3.process,
        t3.process_code,
        t3.plan_ticket_no,
        t3.product_name,
        t3.planned_product,
        t3.unit
        order by t1.id, t2.machine_name, t3.process, t3.plan_ticket_no, t3.product_name
    </select>

    <select id="getProductPlanReport"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProductReportByFactoryOutVO">
        select
        *
        from
        (
        select
        pp.company_code as companyCode,
        pp.worker_order_no as planTicketNo,
        pp.production_process as process,
        pp.erp_machine_name as machineName,
        pp.production_plan_date as produceDate,
        pp.serial_no as shift,
        pp.production_name as productName,
        pt.team_users,
        pp.unit as unit,
        1 as planStatus,
        case when pt.id is null then 0 else 1 end as productStatus,
        pp.planned_production_capacity as plannedProductionCapacity,
        ROUND( SUM( IFNULL( pmt.reported_quantity, 0.0 )), 2 ) as realProductionCapacity,
        ROUND( SUM(pt.defective_product), 2 ) as defectiveProduct,
        -- 损耗率：不良品/良品
        ROUND( case when SUM(IFNULL(pmt.reported_quantity, 0.0)) > 0 then ( SUM(pt.defective_product) /
        (SUM(IFNULL(pmt.reported_quantity, 0.0)) + SUM(pt.defective_product)) ) * 100 else 100 end, 2 ) as lossRate,
        -- 生产计划达成率：实际产量/计划产量
        ROUND( case when pp.planned_production_capacity > 0 then (SUM(IFNULL(pmt.reported_quantity, 0.0)) /
        pp.planned_production_capacity) * 100 else 100 end, 2 ) as planReachRate
        from
        production_plan pp
        left join product_ticket pt on
        pt.machine_name = pp.erp_machine_name
        and pt.plan_ticket_no = pp.worker_order_no
        and pt.produce_date = pp.production_plan_date
        and pt.product_name = pp.production_name
        and pt.shift = pp.serial_no
        and pt.deleted = 0
        left join (
        select
        reported_product_id,
        SUM(reported_quantity) as reported_quantity
        from
        product_machine_task
        group by
        reported_product_id ) pmt on pmt.reported_product_id = pt.id
        <where>
            <if test="getProductReportByFactoryInVO.companyCode != null and getProductReportByFactoryInVO.companyCode != '' ">
                and pp.company_code = #{getProductReportByFactoryInVO.companyCode}
            </if>
            <if test="getProductReportByFactoryInVO.machineName != null and getProductReportByFactoryInVO.machineName != ''">
                and pp.erp_machine_name like CONCAT('%',#{getProductReportByFactoryInVO.machineName},'%')
            </if>
            <if test="getProductReportByFactoryInVO.produceDate != null and getProductReportByFactoryInVO.produceDate != ''">
                and pp.production_plan_date = #{getProductReportByFactoryInVO.produceDate}
            </if>
            <if test="getProductReportByFactoryInVO.planTicketNo != null and getProductReportByFactoryInVO.planTicketNo != ''">
                and pp.worker_order_no = #{getProductReportByFactoryInVO.planTicketNo}
            </if>
            <if test="getProductReportByFactoryInVO.shift != null and getProductReportByFactoryInVO.shift != ''">
                and pp.serial_no = #{getProductReportByFactoryInVO.shift}
            </if>
            and pp.is_deleted = 0
        </where>
        group by
        pp.company_code,
        pp.worker_order_no,
        pp.production_process,
        pp.erp_machine_name,
        pp.production_plan_date,
        pp.serial_no,
        pp.production_name,
        pp.unit,
        planStatus,
        productStatus,
        pp.planned_production_capacity,
        pt.team_users
        union

        select
        pt.company_code as companyCode,
        pt.plan_ticket_no as planTicketNo,
        pt.process as process,
        pt.machine_name as machineName,
        pt.produce_date as produceDate,
        pt.shift as shift,
        pt.product_name as productName,
        pt.team_users,
        pp.unit as unit,
        case when pp.id is null then 0 else 1 end as planStatus,
        1 as productStatus,
        pt.planned_product as plannedProductionCapacity,
        ROUND(SUM(IFNULL(pmt.reported_quantity, 0.0)), 2) as realProductionCapacity,
        ROUND(SUM(pt.defective_product), 2) as defectiveProduct,
        -- 损耗率：不良品/良品
        ROUND( case when SUM(IFNULL(pmt.reported_quantity, 0.0)) > 0 then ( SUM(pt.defective_product) /
        (SUM(IFNULL(pmt.reported_quantity, 0.0)) + SUM(pt.defective_product)) ) * 100 else 100 end, 2 ) as lossRate,
        -- 生产计划达成率：实际产量/计划产量
        ROUND( case when pt.planned_product > 0 then (SUM(IFNULL(pmt.reported_quantity, 0.0)) / pt.planned_product) *
        100 else 100 end, 2 ) as planReachRate
        from
        product_ticket pt
        left join production_plan pp on
        pt.machine_name = pp.erp_machine_name
        and pt.plan_ticket_no = pp.worker_order_no
        and pt.produce_date = pp.production_plan_date
        and pt.product_name = pp.production_name
        and pt.shift = pp.serial_no
        left join(
        select
        reported_product_id,
        SUM(reported_quantity) as reported_quantity
        from
        product_machine_task
        group by
        reported_product_id ) pmt on
        pmt.reported_product_id = pt.id
        <where>
            <if test="getProductReportByFactoryInVO.companyCode != null and getProductReportByFactoryInVO.companyCode != '' ">
                and pt.company_code = #{getProductReportByFactoryInVO.companyCode}
            </if>
            <if test="getProductReportByFactoryInVO.machineName != null and getProductReportByFactoryInVO.machineName != ''">
                and pt.machine_name like CONCAT('%',#{getProductReportByFactoryInVO.machineName},'%')
            </if>
            <if test="getProductReportByFactoryInVO.produceDate != null and getProductReportByFactoryInVO.produceDate != ''">
                and pt.produce_date = #{getProductReportByFactoryInVO.produceDate}
            </if>
            <if test="getProductReportByFactoryInVO.planTicketNo != null and getProductReportByFactoryInVO.planTicketNo != ''">
                and pt.plan_ticket_no = #{getProductReportByFactoryInVO.planTicketNo}
            </if>
            <if test="getProductReportByFactoryInVO.shift != null and getProductReportByFactoryInVO.shift != ''">
                and pp.serial_no = #{getProductReportByFactoryInVO.shift}
            </if>
            and pt.deleted = 0
        </where>
        group by
        pt.company_code,
        pt.produce_date,
        pt.process,
        pt.machine_name,
        pt.plan_ticket_no,
        pp.unit,
        planStatus,
        productStatus,
        pt.shift,
        pt.product_name,
        pt.planned_product,
        pt.team_users
        ) as t
        <where>
            <if test="getProductReportByFactoryInVO.planStatus != null">
                and planStatus = #{getProductReportByFactoryInVO.planStatus}
            </if>
            <if test="getProductReportByFactoryInVO.productStatus != null">
                and productStatus = #{getProductReportByFactoryInVO.productStatus}
            </if>
        </where>
        ORDER BY
        <if test="getProductReportByFactoryInVO.sortName != null and getProductReportByFactoryInVO.sortName != ''
                and getProductReportByFactoryInVO.sortType != null and getProductReportByFactoryInVO.sortType != '' ">
            ${getProductReportByFactoryInVO.sortName} ${getProductReportByFactoryInVO.sortType},
        </if>
        t.produceDate DESC
    </select>

    <select id="getOneDayTaskSummary" resultType="cn.jihong.mes.production.api.model.vo.out.GetOneDayTaskSummaryOutVO">
        SELECT pt.id as taskId,
               pt.plan_ticket_no,
               pt.machine_name,
               pt.produce_date,
               pt.shift,
               pt.process_code,
               pt.product_name,
               SUM(pt.defective_product)  as defective_product,
               SUM(pd.defective_product)  as unconfirDefectiveQuantity,
               SUM(pmt.reported_quantity) as reported_quantity
        FROM product_ticket pt
        LEFT JOIN (
            SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
            FROM product_machine_task
            where deleted = 0
            GROUP BY reported_product_id
        ) pmt ON pmt.reported_product_id = pt.id
        LEFT JOIN (
            SELECT product_ticket_id, SUM(defective_products_quantity) AS defective_product
            FROM product_defective_products
            WHERE deleted = 0
            AND (defective_type != 0  or defective_type is null)
            GROUP BY product_ticket_id
        ) pd ON pd.product_ticket_id = pt.id
        WHERE pt.company_code = #{companyCode}
          AND pt.machine_name = #{machineName}
          AND pt.produce_date = #{produceDate}
          AND pt.shift = #{shift}
          AND pt.deleted = 0
        GROUP BY pt.id,
                 pt.plan_ticket_no,
                 pt.produce_date,
                 pt.process_code,
                 pt.shift,
                 pt.product_name,
                 pt.machine_name
    </select>

    <select id="getOneDayTaskSummaryDetail"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetOneDayTaskSummaryOutVO">
        SELECT *,
               -- 计算占比
               CASE
                   WHEN total_quantity > 0 then (t.reported_quantity + t.defective_product) / total_quantity
                   ELSE 0
                   END AS product_ratio
        FROM (
                 SELECT pt.id                      as taskId,
                        pt.plan_ticket_no,
                        pt.machine_name,
                        pt.produce_date,
                        pt.shift,
                        SUM(pt.defective_product)  as defective_product,
                        SUM(pmt.reported_quantity) as reported_quantity,
                        -- 计算所有数据的良品和不良品总数量
                        (SELECT SUM(pt2.defective_product) + SUM(pmt2.reported_quantity)
                         FROM product_ticket pt2
                                  LEFT JOIN (
                             SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
                             FROM product_machine_task
                             WHERE deleted = 0
                             GROUP BY reported_product_id
                         ) pmt2 ON pmt2.reported_product_id = pt2.id
                         WHERE pt2.company_code = #{companyCode}
                           AND pt2.machine_name = #{machineName}
                           AND pt2.produce_date = #{produceDate}
                           AND pt2.shift = #{shift}
                           and pt2.plan_ticket_no = pt.plan_ticket_no
                           AND pt2.deleted = 0)    AS total_quantity

                 FROM product_ticket pt
                          LEFT JOIN (
                     SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
                     FROM product_machine_task
                     where deleted = 0
                     GROUP BY reported_product_id
                 ) pmt ON pmt.reported_product_id = pt.id

                 WHERE pt.company_code = #{companyCode}
                   AND pt.machine_name = #{machineName}
                   AND pt.produce_date = #{produceDate}
                   AND pt.shift = #{shift}
                   AND pt.deleted = 0
                 GROUP BY pt.id,
                          pt.plan_ticket_no,
                          pt.produce_date,
                          pt.shift,
                          pt.machine_name
             ) t
    </select>


    <select id="deviceOEEReportShow"
            resultType="cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO">
        select
        tb.machine_name,
        tb.process as process_name,
        tb.produce_date,
        tb.time_actuation_rate,
        tb.performance_rate,
        tb.good_product_rate,
        ROUND((tb.time_actuation_rate/100)* (tb.performance_rate/100)*(tb.good_product_rate/100),4)*100 oeeRate,
        tb.total_reported_quantity,
        tb.total_duration_hours,
        tb.standard_production_capacity
        from (
        SELECT
        t.machine_name,
        t.process,
        t.produce_date,
        SUM(t.reported_quantity) AS total_reported_quantity,
        SUM(t.defective_product) as total_defective_product_quantity,
        sum(t.theory_quantity) as theory_quantity,
        case when SUM(t.duration_hours) > 24 then 24 else SUM(t.duration_hours) end AS total_duration_hours,
        ROUND(case when SUM(t.duration_hours) > 24 then 24 else SUM(t.duration_hours) end / 24, 4) * 100 AS
        time_actuation_rate,
        ROUND( SUM(t.reported_quantity) /sum(t.theory_quantity), 4) * 100 AS performance_rate,
        case when SUM(t.reported_quantity) = 0 then 0 else ROUND(SUM(t.reported_quantity)
        /(SUM(t.reported_quantity) + SUM(t.defective_product)), 4) * 100 end AS good_product_rate,
        max(t.standard_production_capacity) as standard_production_capacity
        from (
        select
        pt.company_code,
        pt.machine_name,
        pt.process,
        pt.produce_date,
        pp.standard_production_capacity,
        pt.plan_ticket_no,
        pmth.start_time,
        pmth.end_time,
        pt.real_product,
        pt.defective_product,
        pmt.reported_quantity,
        case when ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) / 3600.0, 2) > 24 then 24 else
        ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) / 3600.0, 2) end AS duration_hours,
        pp.standard_production_capacity * case when ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) /
        3600.0, 2) > 24 then 24 else ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) / 3600.0, 2) end AS
        theory_quantity
        from
        product_ticket pt
        inner join product_machine_task pmt on
        pt.id = pmt.reported_product_id
        and pmt.type = 1
        inner join product_machine_task_history pmth on
        pt.id = pmth.reported_product_id
        and pmth.type = 1
        and pmth.finished = 1
        inner join (
        select
        id,
        worker_order_no,
        production_process,
        standard_production_capacity,
        row_number() over (
        partition by worker_order_no,
        production_process
        order by
        id desc
        ) as rn
        from production_plan) pp on
        pt.plan_ticket_no = pp.worker_order_no
        and pt.process = pp.production_process
        and pp.rn = 1
        where pt.deleted = 0 and pmt.deleted = 0 and pmth.deleted = 0
        and DATE_FORMAT( pt.produce_date, '%Y-%m' ) = #{produceYearMonth}
        <if test="companyCode != '' and companyCode != null">
            AND pt.company_code = #{companyCode}
        </if>
        <if test="machineName != '' and machineName != null">
            AND pt.machine_name LIKE CONCAT('%',#{machineName},'%')
        </if>
        <if test="processName != '' and processName != null">
            AND pt.process LIKE CONCAT('%',#{processName},'%')
        </if>
        order by pt.company_code
        ) t
        GROUP BY
        t.
        machine_name,
        t.process,
        t.produce_date
        ) tb
    </select>


    <select id="selectProductTicketPage" resultType="cn.jihong.mes.production.api.model.vo.out.ProductionTicketInfoOutVO">
        SELECT pt.*,
            pmt.reported_quantity
        FROM product_ticket pt
        LEFT JOIN (
            SELECT reported_product_id, SUM(reported_quantity) AS reported_quantity
            FROM product_machine_task
            where deleted = 0
            GROUP BY reported_product_id
        ) pmt ON pmt.reported_product_id = pt.id
        <where>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                AND company_code = #{vo.companyCode}
            </if>
            <if test="vo.machineName != null and vo.machineName != ''">
                AND machine_name LIKE CONCAT('%', #{vo.machineName}, '%')
            </if>
            <if test="vo.planTicketNo != null and vo.planTicketNo != ''">
                AND plan_ticket_no = #{vo.planTicketNo}
            </if>
            <if test="vo.produceDate != null">
                AND produce_date = #{vo.produceDate}
            </if>
            <if test="vo.shift != null">
                AND shift = #{vo.shift}
            </if>
            <if test="vo.status != null and vo.status != ''">
                AND status = #{vo.status}
            </if>

            <choose>
                <when test="vo.isSignUp != null and vo.isSignUp == 1">
                    AND (is_sign_up = 1 AND is_defection_sign_up = 1)
                </when>
                <when test="vo.isSignUp != null and vo.isSignUp == 0">
                    AND (is_sign_up != 1 OR is_defection_sign_up != 1)
                </when>
            </choose>
            <if test="vo.isAppSignGood != null and vo.isAppSignGood == 1">
                AND pmt.reported_quantity > 0
            </if>
            <if test="vo.isAppSignGood != null and vo.isAppSignGood == 0">
                AND pmt.reported_quantity = 0
            </if>
            <if test="vo.isAppSignBad != null and vo.isAppSignBad == 1">
                AND pt.defective_product > 0
            </if>
            <if test="vo.isAppSignBad != null and vo.isAppSignBad == 0">
                AND pt.defective_product = 0
            </if>
        </where>
        ORDER BY create_time DESC
    </select>



    <select id="getProcessQuantityByPlanTicketNo"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProcessQuantityByPlanTicketNo">

        select
            t.plan_ticket_no,
            t.process_code,
            t.unit,
            max(t.create_time) as report_time,
            SUM(t.reported_quantity) as total_report_quantity,
            SUM(t.defective_product) as total_defective_product_quantity,
            SUM(t.reported_quantity) as total_good_product_quantity
        from
            (
                select
                    pt.company_code,
                    pt.machine_name,
                    pt.process,
                    pt.process_code,
                    pt.produce_date,
                    pt.plan_ticket_no,
                    pt.unit,
                    pmth.start_time,
                    pmth.end_time,
                    pt.real_product,
                    pt.defective_product,
                    pmt.reported_quantity,
                    pmth.create_time
                from
                    product_ticket pt
                        inner join product_machine_task pmt on
                                pt.id = pmt.reported_product_id
                            and pmt.type = 1
                        inner join product_machine_task_history pmth on
                                pt.id = pmth.reported_product_id
                            and pmth.type = 1
                            and pmth.finished = 1
                where
                    pt.deleted = 0
                  and pmt.deleted = 0
                  and pmth.deleted = 0
                  and pt.plan_ticket_no = #{planTicketNo}
                  and pt.company_code = #{companyCode}
            ) t
        group by
            t.plan_ticket_no,
            t.process_code,
            t.unit


    </select>


    <select id="getOeeBaseData" resultType="cn.jihong.mes.production.api.model.vo.out.GetOeeBaseDataOutVO">
        select
        pt.company_code,
        pmt.machine_name,
        pt.process,
        pt.produce_date,
        pp.standard_production_capacity,
        pt.plan_ticket_no,
        CASE
        WHEN (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        THEN #{dto.startTime}
        ELSE pmt.start_time
        END AS start_time,
        CASE
        WHEN (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        THEN #{dto.endTime}
        ELSE pmt.end_time
        END AS end_time,
        IFNULL(pt.real_product, 0) as real_product,
        IFNULL(pdp.defective_product, 0) as defective_product,
        IFNULL(pmt.reported_quantity, 0) as reported_quantity,
        CASE
        WHEN TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime}
        THEN ROUND(TIMESTAMPDIFF(SECOND, pmt.start_time, pmt.end_time) / 60.0, 2)

        WHEN (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        THEN ROUND(TIMESTAMPDIFF(SECOND, #{dto.startTime}, pmt.end_time) / 60.0, 2)

        WHEN (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        THEN ROUND(TIMESTAMPDIFF(SECOND, pmt.start_time, #{dto.endTime}) / 60.0, 2)

        ELSE 0
        END AS duration_minutes
        from
        product_machine_task pmt
        left join product_ticket pt on
        pt.id = pmt.reported_product_id
        and pmt.type = 1
        left join (
        select
        product_ticket_id,
        sum(defective_products_quantity) as defective_product
        from
        product_defective_products
        group by
        product_ticket_id ) pdp on
        pt.id = pdp.product_ticket_id
        left join (
        select
        id,
        worker_order_no,
        production_process,
        standard_production_capacity,
        row_number() over ( partition by worker_order_no,
        production_process
        order by
        id desc ) as rn
        from
        production_plan) pp on
        pt.plan_ticket_no = pp.worker_order_no
        and pt.process = pp.production_process
        and pp.rn = 1
        where
        pmt.deleted = 0
        and pmt.type = 1
        and pmt.finished = 1
        AND
        (DATE(pt.produce_date) &gt;= DATE(#{dto.startDate}) AND DATE(pt.produce_date) &lt;= DATE(#{dto.endDate})
        or
        (
        (TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        OR (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        OR (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        )
        )
        <if test="dto.shift != '' and dto.shift != null">
            AND pt.shift = #{dto.shift}
        </if>
        <if test="dto.machineNameList != null and dto.machineNameList .size() > 0">
            AND pmt.machine_name in
            <foreach collection="dto.machineNameList" item="machineName" index="index" open="(" close=")" separator=",">
                #{machineName}
            </foreach>
        </if>
    </select>

    <select id="getOee" resultType="cn.jihong.mes.production.api.model.vo.out.GetOeeOutVO">

        select
        SUM(IFNULL(t.reported_quantity, 0)) as total_reported_quantity,
        SUM(IFNULL(t.defective_product, 0)) as total_defective_product_quantity,
        SUM(IFNULL(t.duration_minutes, 0)) as total_duration_minutes,
        ROUND(SUM(IFNULL(t.reported_quantity, 0))/ SUM(t.duration_minutes), 4) as actual_speed,
        case
        when SUM(t.reported_quantity) = 0 then 0
        else ROUND(SUM(IFNULL(t.reported_quantity, 0)) /(SUM(IFNULL(t.reported_quantity, 0)) + SUM(IFNULL(t.defective_product, 0))), 4)
        end as good_product_rate,
        max(t.standard_production_capacity) as standard_production_capacity
        from
        (
        select
        pt.company_code,
        pt.machine_name,
        pt.process,
        pt.produce_date,
        pp.standard_production_capacity,
        pt.plan_ticket_no,
        pmt.start_time,
        pmt.end_time,
        pt.real_product,
        pt.defective_product,
        pmt.reported_quantity,
        case
        when ROUND(TIMESTAMPDIFF(second, pmt.start_time, pmt.end_time) / 60.0, 0) > (24 * 60) then (24 * 60)
        else ROUND(TIMESTAMPDIFF(second, pmt.start_time, pmt.end_time) / 60.0, 0)
        end as duration_minutes
        from
        product_machine_task pmt

        left join product_ticket pt on
        pt.id = pmt.reported_product_id
        and pmt.type = 1

        left join (
        select
        id,
        worker_order_no,
        production_process,
        standard_production_capacity,
        row_number() over ( partition by worker_order_no,
        production_process
        order by
        id desc ) as rn
        from
        production_plan) pp on
        pt.plan_ticket_no = pp.worker_order_no
        and pt.process = pp.production_process
        and pp.rn = 1
        where
        pmt.deleted = 0
        and pmt.type = 1
        and pmt.finished = 1
        and date(pmt.start_time) &gt;=  #{dto.startDate}
        and date(pmt.end_time) &lt;=  #{dto.endDate}
        <if test="dto.shift != '' and dto.shift != null">
            AND pt.shift = #{dto.shift}
        </if>
        <if test="dto.machineNameList != null and dto.machineNameList .size() > 0">
            AND pmt.machine_name in
            <foreach collection="dto.machineNameList" item="machineName" index="index" open="(" close=")" separator=",">
                #{machineName}
            </foreach>
        </if>

        ) t
    </select>
</mapper>
