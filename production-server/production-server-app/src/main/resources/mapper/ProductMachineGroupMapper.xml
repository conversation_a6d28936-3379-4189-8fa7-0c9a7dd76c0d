<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMachineGroupMapper">

    <resultMap id="ProductMachineGroup" type="cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO">
        <id property="id" column="id"/>
        <result property="companyCode" column="company_code"/>
        <result property="machineGroupName" column="machine_group_name"/>
        <collection property="productMachineGroupRelationships" ofType="cn.jihong.mes.production.api.model.vo.out.ProductMachineGroupOutVO$ProductMachineGroupRelationship">
            <result property="machineName" column="machine_name"/>
        </collection>
    </resultMap>

    <select id="getList" resultMap="ProductMachineGroup">
        SELECT
            t1.id,
            t1.company_code,
            t1.machine_group_name,
            t2.machine_group_id,
            t2.machine_name
        FROM
            product_machine_group t1
                LEFT JOIN product_machine_group_relationship t2 ON t1.id = t2.machine_group_id
        WHERE t1.company_code = #{companyCode}
        and t2.deleted = 0 and t1.deleted = 0
    </select>
</mapper>
