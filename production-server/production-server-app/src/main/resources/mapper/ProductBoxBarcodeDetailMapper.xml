<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductBoxBarcodeDetailMapper">

    <select id="getList" resultType="cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO">
        SELECT
            t1.id,
            t1.customer_no,
            t1.company_code,
            t2.customer_name,
            t2.barcode_name,
            t1.plan_ticket_no,
            t1.product_name,
            t1.production_date,
            t1.shift,
            t1.machine,
            t2.barcode_no_start as barcodeNo,
            t1.barcode_no_start,
            t1.barcode_no_end,
            t1.barcode_total,
            t1.barcode_remaining,
            t1.print_status,
            t1.disable_status,
            t1.spurt_status
        FROM `product_box_barcode_detail` t1
        LEFT JOIN product_box_barcode t2 ON t1.product_barcode_id = t2.id
        <where>
            <if test="getProductBoxBarcodeDetailInVO.companyCode!= null and getProductBoxBarcodeDetailInVO.companyCode!= ''">
                AND t1.company_code = #{getProductBoxBarcodeDetailInVO.companyCode}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.customerNo!= null and getProductBoxBarcodeDetailInVO.customerNo!= ''">
                AND t1.customer_no = #{getProductBoxBarcodeDetailInVO.customerNo}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.barcodeName!= null and getProductBoxBarcodeDetailInVO.barcodeName!= ''">
                AND t2.barcode_name = #{getProductBoxBarcodeDetailInVO.barcodeName}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.planTicketNo!= null and getProductBoxBarcodeDetailInVO.planTicketNo!= ''">
                AND t1.plan_ticket_no = #{getProductBoxBarcodeDetailInVO.planTicketNo}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.productionDate!= null">
                AND t1.production_date = #{getProductBoxBarcodeDetailInVO.productionDate}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.shift!= null and getProductBoxBarcodeDetailInVO.shift!= ''">
                AND t1.shift = #{getProductBoxBarcodeDetailInVO.shift}
            </if>
            <if test="getProductBoxBarcodeDetailInVO.machine!= null and getProductBoxBarcodeDetailInVO.machine!= ''">
                AND t1.machine = #{getProductBoxBarcodeDetailInVO.machine}
            </if>
        </where>
        order by t1.id desc

    </select>
</mapper>
