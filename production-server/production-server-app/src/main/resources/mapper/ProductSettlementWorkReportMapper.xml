<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductSettlementWorkReportMapper">


    <select id="getSettlementWorkReportList" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportListOutVO">
        SELECT
            b.process AS process_name,
            SUM(b.real_product) AS real_product_quantity,
            ( CASE WHEN SUM( a.defective_products_quantity ) IS NULL THEN 0 ELSE SUM( a.defective_products_quantity ) END ) AS scrap_quantity,
            ROUND(( ( CASE WHEN SUM( a.defective_products_quantity ) IS NULL THEN 0 ELSE SUM( a.defective_products_quantity ) END ) / SUM(b.real_product) )* 100,2) AS attrition_rate
        FROM  product_ticket b
             LEFT JOIN   ( SELECT product_ticket_id, SUM( defective_products_quantity ) AS defective_products_quantity FROM product_defective_products WHERE deleted = 0 GROUP BY product_ticket_id ) a
             ON a.product_ticket_id = b.id
        WHERE
            b.plan_ticket_no = #{productTicketNo}
          AND b.deleted = 0
          AND b.STATUS = 70
        GROUP BY
            b.process
    </select>


    <select id="getSettlementWorkReportDetail" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementWorkReportDetailOutVO">
        SELECT
            b.id,
            ( CASE WHEN  a.defective_products_quantity  IS NULL THEN 0 ELSE  a.defective_products_quantity  END ) AS defective_products_quantity,
            b.process,
            b.real_product as real_product,
            b.produce_date as product_date,
            b.shift,
            ROUND((( CASE WHEN  a.defective_products_quantity  IS NULL THEN 0 ELSE  a.defective_products_quantity END )/b.real_product)*100,2) as attrition_rate
        FROM product_ticket b
                 LEFT JOIN ( SELECT product_ticket_id, SUM( defective_products_quantity ) AS defective_products_quantity FROM product_defective_products WHERE deleted = 0 GROUP BY product_ticket_id ) a
                           ON a.product_ticket_id = b.id
        WHERE
            b.deleted = 0 AND b.status = 70 AND  b.plan_ticket_no = #{inVo.productTicketNo} AND b.process = #{inVo.defectiveProductsSourceName}
        ORDER BY
            b.produce_date,
            shift ASC
    </select>

</mapper>
