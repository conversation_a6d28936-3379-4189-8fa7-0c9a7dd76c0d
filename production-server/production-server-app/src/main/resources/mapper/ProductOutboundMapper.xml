<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductOutboundMapper">

    <select id="getOutboundRecords" resultType="cn.jihong.mes.production.api.model.vo.out.OutboundInfoOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            pt.process as process,
            po.id as id,
            po.id as outboundId,
            po.create_by as createBy,
            po.pallet_code as palletCode,
            po.produced_quantity as producedQuantity,
            po.machine_stop_no as machineStopNo,
            po.outbound_time as outboundTime,
            po.case_code,
            po.unit,
            pp.pallet_short_code as palletShortCode
        FROM
            product_ticket pt
        LEFT JOIN
            product_outbound po on pt.id = po.product_ticket_id
        LEFT JOIN
            product_pallet pp on pp.pallet_code = po.pallet_code
        WHERE
            pt.id = #{productTicketId}
            AND pt.deleted = 0
            and po.deleted = 0
            and pp.deleted = 0
            AND pt.company_code = #{companyCode}
    </select>

    <select id="getOutboundRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.OutboundInfoOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            pt.process as process,
            po.id as id,
            po.create_by as createBy,
            po.pallet_code as palletCode,
            po.produced_quantity as producedQuantity,
            po.machine_stop_no as machineStopNo,
            po.outbound_time as outboundTime,
            po.case_code,
            po.unit
        FROM
            product_ticket pt
        LEFT JOIN
            product_outbound po on pt.id = po.product_ticket_id
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pt.deleted = 0
            and po.deleted = 0
    </select>
</mapper>
