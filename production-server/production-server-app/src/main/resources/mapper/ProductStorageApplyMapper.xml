<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductStorageApplyMapper">

    <select id="getStorageApplyInfo" resultType="cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO">
        SELECT
            plan_ticket_no,
            main_info as palletCode,
            create_by,
            create_time,
            result,
            response AS storageApplyNO
        from  product_interface_records t1
        <where>
            <if test="getStorageApplyInfoInVO.planTicketNo != null and getStorageApplyInfoInVO.planTicketNo != '' ">
                AND t1.plan_ticket_no = #{getStorageApplyInfoInVO.planTicketNo}
            </if>
            <if test="getStorageApplyInfoInVO.palletCode != null and getStorageApplyInfoInVO.palletCode != '' ">
                AND t1.main_info = #{getStorageApplyInfoInVO.palletCode}
            </if>
            <if test="getStorageApplyInfoInVO.createBy != null and getStorageApplyInfoInVO.createBy != '' ">
                AND t1.create_by = #{getStorageApplyInfoInVO.createBy}
            </if>
        </where>
        ORDER BY
            create_time DESC
    </select>

    <select id="getPlanTicketNoByPalletCode" resultType="java.lang.String">
        SELECT
            plan_ticket_no
        FROM
            (
                SELECT
                t3.plan_ticket_no,
                t1.create_time
                FROM
                product_outbound t1
                INNER JOIN product_ticket t3 ON t3.id = t1.product_ticket_id
                <where>
                    t1.pallet_code = #{getStorageApplyInfoInVO.palletCode}
                </where>
            UNION
                SELECT
                t2.plan_ticket_no,
                t2.create_time
                FROM
                product_storage_apply t2
                <where>
                    t2.pallet_code = #{getStorageApplyInfoInVO.palletCode}
                </where>
                ORDER BY
                create_time DESC
            ) AS storage_apply
        ORDER BY
        create_time DESC
        LIMIT 1
    </select>
</mapper>
