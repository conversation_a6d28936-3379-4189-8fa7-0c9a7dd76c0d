<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductBarcodeSplitBarcodeMapper">

    <select id="getPageList" resultType="cn.jihong.mes.production.api.model.dto.ProductBarcodeSplitBarcodeDTO">
        select
        t2.*,
        t1.barcode_no as wms_barcode_no
        from product_barcode_split_barcode t1
        inner join product_barcode_split_barcode_detail t2 on t1.id = t2.product_spit_barcode_id
        <where>
            t2.company_code = #{companyCode}
            and t2.enable_status = 1
            <if test="productBarcodeSplitBarcodeInVO.barcodeNo!= null and productBarcodeSplitBarcodeInVO.barcodeNo!= ''">
                and t2.barcode_no like concat('%', #{productBarcodeSplitBarcodeInVO.barcodeNo}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.wmsBarcodeNo!= null and productBarcodeSplitBarcodeInVO.wmsBarcodeNo!= ''">
                and t1.barcode_no like concat('%', #{productBarcodeSplitBarcodeInVO.barcodeNo}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.itemNumber!= null and productBarcodeSplitBarcodeInVO.itemNumber!= ''">
                and t2.item_number like concat('%', #{productBarcodeSplitBarcodeInVO.itemNumber}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.itemName!= null and productBarcodeSplitBarcodeInVO.itemName!= ''">
                and t2.item_name like concat('%', #{productBarcodeSplitBarcodeInVO.itemName}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.itemSpece!= null and productBarcodeSplitBarcodeInVO.itemSpece!= ''">
                and t2.item_spece like concat('%', #{productBarcodeSplitBarcodeInVO.itemSpece}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.itemDate!= null">
                and t2.item_date = #{productBarcodeSplitBarcodeInVO.itemDate}
            </if>
            <if test="productBarcodeSplitBarcodeInVO.warehouse!= null and productBarcodeSplitBarcodeInVO.warehouse!= ''">
                and t2.warehouse like concat('%', #{productBarcodeSplitBarcodeInVO.warehouse}, '%')
            </if>
            <if test="productBarcodeSplitBarcodeInVO.printStatus!= null">
                and t2.print_status = #{productBarcodeSplitBarcodeInVO.printStatus}
            </if>
            <if test="productBarcodeSplitBarcodeInVO.startDate!= null">
                and t2.create_time &gt;= #{productBarcodeSplitBarcodeInVO.startDate}
            </if>
            <if test="productBarcodeSplitBarcodeInVO.endDate!= null">
                and t2.create_time &lt;= #{productBarcodeSplitBarcodeInVO.endDate}
            </if>
        </where>
        order by t2.id desc
    </select>
</mapper>
