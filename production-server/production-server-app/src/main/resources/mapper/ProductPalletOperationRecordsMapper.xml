<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductPalletOperationRecordsMapper">

    <select id="getLastPalletRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO">
        SELECT
            pr.id,
            pr.operation_type,
            pr.ticket_type,
            pr.ticket_number,
            pr.operator as createBy,
            pr.original_quantity,
            pr.consumption_quantity,
            pr.remaining_quantity,
            pr.product_ticket_id,
            pr.product_pallet_id,
            pr.pallet_code,
            pr.machine_stop_no,
            pt.company_code,
            pt.produce_date,
            pt.machine_name,
            pt.process_type,
            pt.process,
            pt.shift,
            pt.plan_ticket_no,
            pt.ticket_request_id,
            pt.team_users,
            plp.pallet_source,
            plp.unit,
            plp.product_ticket_id,
            plp.requisition_ticket_id,
            plp.production_order,
            plp.requisition_order,
            plp.loading_time,
            pr.original_quantity as loading_quantity
        FROM
            product_pallet_operation_records pr
            LEFT JOIN product_pallet plp ON plp.id = pr.product_pallet_id
            LEFT JOIN product_ticket pt ON pt.id = pr.product_ticket_id
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pr.deleted = 0
            AND plp.deleted = 0
            AND pt.deleted = 0
            and pr.operation_type = '3'  -- 只查询上料记录，  下料记录不展示
        order by pr.id desc
    </select>

    <select id="getLastPalletRecords" resultType="cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO">
        SELECT
            pr.id,
            pr.operation_type,
            pr.ticket_type,
            pr.ticket_number,
            pr.operator as createBy,
            pr.original_quantity,
            pr.consumption_quantity,
            pr.remaining_quantity,
            pr.product_ticket_id,
            pr.product_pallet_id,
            pr.pallet_code,
            pr.machine_stop_no,
            pt.company_code,
            pt.produce_date,
            pt.machine_name,
            pt.process_type,
            pt.process,
            pt.shift,
            pt.plan_ticket_no,
            pt.ticket_request_id,
            pt.team_users,
            plp.pallet_source,
            plp.unit,
            plp.product_ticket_id,
            plp.requisition_ticket_id,
            plp.production_order,
            plp.requisition_order,
            plp.loading_time,
            pr.original_quantity as loadingQuantity,
            plp.status
        FROM
            product_pallet_operation_records pr
            LEFT JOIN product_pallet plp ON plp.id = pr.product_pallet_id
            LEFT JOIN product_ticket pt ON pt.id = pr.product_ticket_id
        WHERE
            pt.id = #{productTicketId}
            AND pr.deleted = 0
            AND plp.deleted = 0
            AND pt.deleted = 0
            AND pt.company_code = #{companyCode}
            and pr.operation_type in (40,80)
        order by pr.id desc
    </select>

    <select id="getByProductTicketIds"
            resultType="cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO">
        SELECT
            pr.*,
            pr.original_quantity as loadingQuantity,
            plp.pallet_source,
            plp.unit
        FROM
            product_pallet_operation_records pr
            LEFT JOIN product_pallet plp ON plp.id = pr.product_pallet_id
        WHERE
            pr.deleted = 0
            AND plp.deleted = 0
            and pr.operation_type in
            <foreach collection="operationTypes" item="operationType" index="index" open="(" close=")" separator=",">
                #{operationType}
            </foreach>
            AND pr.product_ticket_id in
            <foreach collection="productTicketIds" item="productTicketId" index="index" open="(" close=")" separator=",">
                #{productTicketId}
            </foreach>
        order by pr.id desc
    </select>

    <select id="getDownPalletRecords" resultType="cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO">
        SELECT
            pr.id,
            pr.operation_type,
            pr.ticket_type,
            pr.ticket_number,
            pr.operator as createBy,
            pr.original_quantity,
            pr.consumption_quantity,
            pr.remaining_quantity,
            pr.product_ticket_id,
            pr.product_pallet_id,
            pr.pallet_code,
            pr.machine_stop_no,
            pt.company_code,
            pt.produce_date,
            pt.machine_name,
            pt.process_type,
            pt.process,
            pt.shift,
            pt.plan_ticket_no,
            pt.ticket_request_id,
            pt.team_users,
            plp.pallet_source,
            plp.unit,
            plp.product_ticket_id,
            plp.requisition_ticket_id,
            plp.production_order,
            plp.requisition_order,
            plp.loading_time,
            pr.original_quantity as loadingQuantity,
            plp.status
        FROM
            product_pallet_operation_records pr
                LEFT JOIN product_pallet plp ON plp.id = pr.product_pallet_id
                LEFT JOIN product_ticket pt ON pt.id = pr.product_ticket_id
        WHERE
            pt.id = #{productTicketId}
          AND pr.deleted = 0
          AND plp.deleted = 0
          AND pt.deleted = 0
          AND pt.company_code = #{companyCode}
          and pr.operation_type = 30
        order by pr.id desc
    </select>
</mapper>
