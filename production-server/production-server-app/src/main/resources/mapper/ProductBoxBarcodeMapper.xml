<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductBoxBarcodeMapper">

    <select id="getListByBarcodes" resultType="cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO">
        SELECT
            t1.*,
            t2.customer_no,
            t2.customer_name
        FROM
        product_box_barcode_detail t1
        LEFT JOIN product_box_barcode t2 ON t1.product_barcode_id = t2.id
        WHERE
        t1.company_code = #{companyCode}
        and
        <foreach collection="barcodes" item="barcode" open="(" separator=" OR " close=")">
            t1.barcode_no_start <![CDATA[<=]]> #{barcode} AND t1.barcode_no_end <![CDATA[>=]]> #{barcode}
        </foreach>
    </select>
</mapper>
