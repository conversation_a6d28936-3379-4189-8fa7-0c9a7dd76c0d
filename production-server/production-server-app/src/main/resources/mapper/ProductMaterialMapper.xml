<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMaterialMapper">

    <select id="getMaterialRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.MaterialInfoOutVO">
        select
            pm.id as id,
            pm.material_name as materialName,
            pm.material_code as materialCode,
            pm.material_barcode_no as materialBarcodeNo,
            pm.create_by as createBy,
            pm.loading_quantity as loadingQuantity,
            pm.material_unit as materialUnit,
            pm.machine_stop_no as machineStopNo,
            pm.loading_time as loadingTime
        FROM
            product_ticket pt
        LEFT JOIN
            product_material pm on pt.id = pm.product_ticket_id
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pt.deleted = 0
            and pm.deleted = 0
    </select>

    <select id="getMaterialUseList" resultType="cn.jihong.mes.production.api.model.vo.out.MaterialUseOutVO">
        select pm.*,
        pt.plan_ticket_no
        FROM
            product_material pm
        INNER join
            product_ticket pt on pt.id = pm.product_ticket_id
        <where>
            pt.deleted = 0
            <if test="getMaterialListPageInVO.companyCode != null and getMaterialListPageInVO.companyCode != '' ">
                and pt.company_code = #{getMaterialListPageInVO.companyCode}
            </if>
            <if test="getMaterialListPageInVO.planTicketNo != null and getMaterialListPageInVO.planTicketNo != '' ">
                and pt.plan_ticket_no like CONCAT('%',#{getMaterialListPageInVO.planTicketNo},'%')
            </if>
            <if test="getMaterialListPageInVO.materialType != null and getMaterialListPageInVO.materialType != '' ">
                and pm.material_type like CONCAT('%',#{getMaterialListPageInVO.materialType},'%')
            </if>
            <if test="getMaterialListPageInVO.purchaseBatch != null and getMaterialListPageInVO.purchaseBatch != '' ">
                and pm.purchase_batch like CONCAT('%',#{getMaterialListPageInVO.purchaseBatch},'%')
            </if>
            <if test="getMaterialListPageInVO.materialCode != null and getMaterialListPageInVO.materialCode != '' ">
                and pm.material_code = #{getMaterialListPageInVO.materialCode}
            </if>
            <if test="getMaterialListPageInVO.materialBarcodeNo != null and getMaterialListPageInVO.materialBarcodeNo != '' ">
                and pm.material_barcode_no = #{getMaterialListPageInVO.materialBarcodeNo}
            </if>
            <if test="getMaterialListPageInVO.materialStatus != null and getMaterialListPageInVO.materialStatus != '' ">
                and pm.status = #{getMaterialListPageInVO.materialStatus}
            </if>
            <if test="getMaterialListPageInVO.writeOffStatus != null and getMaterialListPageInVO.writeOffStatus != '' ">
                and pm.write_off_status = #{getMaterialListPageInVO.writeOffStatus}
            </if>
        </where>
        order by pm.create_time desc
    </select>

    <select id="getMaterialUseDetial" resultType="cn.jihong.mes.production.api.model.vo.out.MaterialUseOutVO">
        select pm.*,
        pt.plan_ticket_no
        FROM
        product_material pm
        INNER join
        product_ticket pt on pt.id = pm.product_ticket_id
        <where>
            pm.id = #{id}
        </where>

    </select>

    <select id="getMaterialUseDetialList" resultType="cn.jihong.mes.production.api.model.vo.out.MaterialUseRecordOutVO">
        select
            pr.*,
            pr.id as materialRecordId,
            pm.id as materialId,
            pr.operator as createBy,
            pt.ticket_request_id,
            pt.plan_ticket_no
        FROM
            product_material_operation_records pr
        inner join
            product_material pm on pr.product_material_id = pm.id
        INNER join
            product_ticket pt on pt.id = pr.product_ticket_id
        <where>
            pm.id = #{id}
        </where>
    </select>

    <select id="getListByMaterialCode" resultType="cn.jihong.mes.production.api.model.vo.out.GetListByMaterialCodeOutVO">
        SELECT
            b.process,
            a.material_name,
            a.material_code,
            a.material_unit,
            a.loading_quantity,
            a.consumption_quantity
        FROM
            product_material a
                LEFT JOIN product_ticket b ON a.product_ticket_id = b.id
        WHERE
            a.deleted = 0
          AND b.deleted = 0
          AND a.product_ticket_id = #{inVO.productTicketId}
    </select>

    <select id="getMaterialTotalUse" resultType="cn.jihong.mes.production.api.model.vo.out.MaterialTotalUseOutVO">
        SELECT
            pm.material_code,
            pm.material_name,
            pm.material_unit,
            pm.material_place,
            pm.material_place_name,
            SUM(pr.consumption_quantity) as produceTotalUseQuantity,
            max(pr.workflow_request_id) as workflow_request_id,
            max(pr.workflow_request_status) as workflow_request_status
        FROM
            product_material_operation_records pr
            INNER JOIN product_ticket pt on pt.id = pr.product_ticket_id
            LEFT JOIN product_material pm ON pm.id = pr.product_material_id
        WHERE
            pr.deleted = 0
            AND pm.deleted = 0
            AND pt.plan_ticket_no = #{planTicketNo}
            AND pt.machine_name = #{machineName}
            and pr.operation_type in
            <foreach collection="operationTypes" item="operationType" index="index" open="(" close=")" separator=",">
                #{operationType}
            </foreach>
        GROUP BY pm.material_code,pm.material_name,pm.material_unit,pm.material_place,pm.material_place_name
    </select>
</mapper>
