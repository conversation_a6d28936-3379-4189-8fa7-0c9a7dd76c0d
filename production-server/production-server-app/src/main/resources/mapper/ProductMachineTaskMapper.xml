<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMachineTaskMapper">

    <select id="getProjectDailyReports"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProjectDailyReportsOutVO">
        select
        t2.plan_ticket_no,
        t2.machine_name,
        t3.material_type,
        t3.material_code,
        t2.process_code,
        t2.process,
        t2.produce_date,
        t3.material_unit, &#45;&#45; 单位（用料）
        t3.material_place_name as material_place,
        t3.total_consumption_quantity, &#45;&#45; 累计用料
        t1.reported_unit, &#45;&#45; 单位（报工）
        t1.total_reported_quantity, &#45;&#45; 累计报工
        t4.total_produced_quantity, &#45;&#45; 累计出站
        t5.total_defective_products_quantity, &#45;&#45; 累计不良品
        ROUND(t5.total_defective_products_quantity/t4.total_produced_quantity,4) as attrition_rate &#45;&#45; 损耗率（%）

        from ( select reported_product_id,reported_unit,sum(reported_quantity) as total_reported_quantity from
        product_machine_task
        where type = 1 and reported_product_id is not null
        GROUP BY reported_product_id,reported_unit) t1

        JOIN ( select * from product_ticket where DATE_FORMAT(produce_date,'%Y-%m-%d') = #{inVO.produceDate}
        <if test="inVO.companyCode != null and inVO.companyCode != '' ">
            AND company_code = #{inVO.companyCode}
        </if>
        <if test="inVO.planTicketNo != null and inVO.planTicketNo != '' ">
            AND plan_ticket_no like CONCAT('%',#{inVO.planTicketNo} ,'%')
        </if>
         ) t2
        ON t1.reported_product_id = t2.id

        LEFT JOIN ( select
        product_ticket_id,material_code,material_type,material_unit,material_place_name,sum(consumption_quantity) as
        total_consumption_quantity from product_material GROUP BY
        product_ticket_id,material_place,material_code,material_type,material_unit,material_place_name ) t3
        ON t1.reported_product_id = t3.product_ticket_id

        LEFT JOIN ( select product_ticket_id,sum(produced_quantity) as total_produced_quantity from product_outbound GROUP BY
        product_ticket_id ) t4
        ON t1.reported_product_id = t4.product_ticket_id

        LEFT JOIN (select product_ticket_id,sum(defective_products_quantity) as total_defective_products_quantity from
        product_defective_products_callback where check_result is not null and check_result!=2 GROUP BY
        product_ticket_id ) t5
        ON t1.reported_product_id = t5.product_ticket_id

    </select>


    <select id="getPageByDay"  resultType="cn.jihong.mes.production.api.model.vo.out.GetPageByDayOutVO">
        SELECT
            t1.plan_ticket_no,
            t1.product_name,
            t1.process_code,
            SUBSTRING_INDEX(GROUP_CONCAT(t1.process SEPARATOR ','), ',', 1) AS process,
            SUBSTRING_INDEX(GROUP_CONCAT(t1.produce_date SEPARATOR ','), ',', 1) AS produce_date,
            SUBSTRING_INDEX(GROUP_CONCAT(t2.reported_unit SEPARATOR ','), ',', 1) AS reported_unit,
            sum( t1.planned_product ) AS total_planned_quantity,
            (case when sum( t2.total_reported_quantity ) is null then 0 else sum( t2.total_reported_quantity ) end) AS total_reported_quantity,
            (case when sum( t3.total_defective_products_quantity ) is null then 0 else sum( t3.total_defective_products_quantity ) end) AS total_defective_products_quantity
        FROM
            product_ticket t1
                LEFT JOIN (
                SELECT
                    reported_product_id,
                    reported_unit,
                    sum( reported_quantity ) AS total_reported_quantity
                FROM
                    product_machine_task
                WHERE
                    deleted = 0
                  AND type = 1
                  AND reported_product_id IS NOT NULL
                  AND reported_quantity IS NOT NULL
                GROUP BY
                    reported_product_id,
                    reported_unit
            ) t2 ON t1.id = t2.reported_product_id
                LEFT JOIN ( SELECT product_ticket_id, sum( defective_products_quantity ) AS total_defective_products_quantity FROM product_defective_products WHERE deleted = 0 AND defective_products_quantity IS NOT NULL GROUP BY product_ticket_id ) t3 ON t1.id = t3.product_ticket_id
        WHERE
            t1.deleted = 0 AND DATE_FORMAT( t1.produce_date, '%Y-%m-%d' ) = #{inVO.produceDate}
        <if test="inVO.companyCode != null and inVO.companyCode != '' ">
            AND t1.company_code = #{inVO.companyCode}
        </if>
        <if test="inVO.planTicketNo != null and inVO.planTicketNo != '' ">
            AND t1.plan_ticket_no like CONCAT('%',#{inVO.planTicketNo} ,'%')
        </if>
        GROUP BY
            t1.plan_ticket_no,
            t1.product_name,
            t1.process_code

    </select>

    <select id="getPageByDayDetail"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetPageByDayDetailOutVO">
        SELECT
            t1.plan_ticket_no,
            t1.process_code,
            t1.machine_name,
            SUBSTRING_INDEX(GROUP_CONCAT(t1.process SEPARATOR ','), ',', 1) AS process,
            SUBSTRING_INDEX(GROUP_CONCAT(t1.produce_date SEPARATOR ','), ',', 1) AS produce_date,
            SUBSTRING_INDEX(GROUP_CONCAT(t2.reported_unit SEPARATOR ','), ',', 1) AS reported_unit,
            sum( t1.planned_product ) AS total_planned_quantity,
            (case when sum( t2.total_reported_quantity ) is null then 0 else sum( t2.total_reported_quantity ) end) AS total_reported_quantity,
            (case when sum( t3.total_defective_products_quantity ) is null then 0 else sum( t3.total_defective_products_quantity ) end) AS total_defective_products_quantity
        FROM
            product_ticket t1
                LEFT JOIN (
                SELECT
                    reported_product_id,
                    reported_unit,
                    sum( reported_quantity ) AS total_reported_quantity
                FROM
                    product_machine_task
                WHERE
                    deleted = 0
                  AND type = 1
                  AND reported_product_id IS NOT NULL
                  AND reported_quantity IS NOT NULL
                GROUP BY
                    reported_product_id,
                    reported_unit
            ) t2 ON t1.id = t2.reported_product_id
                LEFT JOIN ( SELECT product_ticket_id, sum( defective_products_quantity ) AS total_defective_products_quantity FROM product_defective_products WHERE deleted = 0 AND defective_products_quantity IS NOT NULL GROUP BY product_ticket_id ) t3 ON t1.id = t3.product_ticket_id
        WHERE
            t1.deleted = 0 AND DATE_FORMAT( t1.produce_date, '%Y-%m-%d' ) = #{inVO.produceDate} AND t1.company_code = #{inVO.companyCode}
           AND t1.plan_ticket_no = #{inVO.planTicketNo} AND t1.process_code = #{inVO.processCode}

        GROUP BY
            t1.plan_ticket_no,
            t1.machine_name

    </select>

    <select id="getPageByEnd"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetPageByEndOutVO">

        SELECT
            t1.plan_ticket_no,
            max(t1.company_code) AS company_code,
            max(t1.produce_date) AS produce_date,
            GROUP_CONCAT( t1.process_code SEPARATOR ',' ) AS process_code_concat,
            GROUP_CONCAT( t1.process SEPARATOR ',' ) AS process_concat,
            GROUP_CONCAT( t2.reported_unit SEPARATOR ',' ) AS reported_unit_concat,
            GROUP_CONCAT( t1.planned_product SEPARATOR ',' ) AS total_planned_quantity_concat,
            GROUP_CONCAT( t2.total_reported_quantity SEPARATOR ',' ) AS total_reported_quantity_concat,
            (case when sum( t3.total_defective_products_quantity ) is null then 0 else sum( t3.total_defective_products_quantity ) end) AS total_defective_products_quantity
        FROM
        product_ticket t1
        LEFT JOIN (
        SELECT
        reported_product_id,
        reported_unit,
        sum( reported_quantity ) AS total_reported_quantity
        FROM
        product_machine_task
        WHERE
        deleted = 0
        AND type = 1
        AND reported_product_id IS NOT NULL
        AND reported_quantity IS NOT NULL
        GROUP BY
        reported_product_id,
        reported_unit
        ) t2 ON t1.id = t2.reported_product_id
        LEFT JOIN ( SELECT product_ticket_id, sum( defective_products_quantity ) AS total_defective_products_quantity FROM product_defective_products WHERE deleted = 0 AND defective_products_quantity IS NOT NULL GROUP BY product_ticket_id ) t3 ON t1.id = t3.product_ticket_id
        WHERE
        t1.deleted = 0
        AND DATE_FORMAT( t1.produce_date, '%Y-%m-%d' ) &lt;= #{inVO.endDate}
        AND t1.company_code = #{inVO.companyCode}
        <if test="inVO.planTicketNo != null and inVO.planTicketNo != '' ">
            AND t1.plan_ticket_no like CONCAT('%',#{inVO.planTicketNo} ,'%')
        </if>
        <if test="workingPlanTicketNoList != null and workingPlanTicketNoList.size() > 0">
            AND t1.plan_ticket_no IN
            <foreach collection="workingPlanTicketNoList" item="workingPlanTicketNo" open="(" separator="," close=")">
                #{workingPlanTicketNo}
            </foreach>
        </if>

        GROUP BY
        t1.plan_ticket_no

    </select>


    <select id="getPageByEndDetail"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetPageByEndDetailOutVO">

        SELECT
        t1.plan_ticket_no,
        t1.process_code,
        max( t1.process ) AS process,
        max( t1.produce_date ) AS produce_date,
        max( t2.reported_unit ) AS reported_unit,
        (case when sum( t1.planned_product ) is null then 0 else sum( t1.planned_product ) end) AS total_planned_quantity,
        (case when sum( t2.total_reported_quantity ) is null then 0 else sum( t2.total_reported_quantity ) end) AS total_reported_quantity,
        (case when sum( t3.total_defective_products_quantity ) is null then 0 else sum( t3.total_defective_products_quantity ) end) AS total_defective_products_quantity
        FROM
        product_ticket t1
        LEFT JOIN (
        SELECT
        reported_product_id,
        reported_unit,
        sum( reported_quantity ) AS total_reported_quantity
        FROM
        product_machine_task
        WHERE
        deleted = 0
        AND type = 1
        AND reported_product_id IS NOT NULL
        AND reported_quantity IS NOT NULL
        GROUP BY
        reported_product_id,
        reported_unit
        ) t2 ON t1.id = t2.reported_product_id
        LEFT JOIN ( SELECT product_ticket_id, sum( defective_products_quantity ) AS total_defective_products_quantity FROM product_defective_products WHERE deleted = 0 AND defective_products_quantity IS NOT NULL GROUP BY product_ticket_id ) t3 ON t1.id = t3.product_ticket_id
        WHERE
        t1.deleted = 0
        AND DATE_FORMAT( t1.produce_date, '%Y-%m-%d' ) &lt;= #{inVO.endDate}
        AND t1.company_code = #{inVO.companyCode}
        AND t1.plan_ticket_no = #{inVO.planTicketNo}
        GROUP BY
        t1.process_code

    </select>

    <select id="getProductMachineTaskPage"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskPageOutVO">
        SELECT
        t1.type,
        t1.loss_type,
        t1.create_by as report_person_id,
        t2.team_users,
        t2.process,
        t2.produce_date,
        t2.shift,
        t1.machine_name,
        t1.start_time,
        t1.end_time
        FROM
        product_machine_task t1
        JOIN product_ticket t2 ON t1.reported_product_id = t2.id
        WHERE  t2.id = #{inVO.productTicketId}
            AND t2.company_code = #{companyCode}
        ORDER BY t1.create_time ASC
    </select>
</mapper>
