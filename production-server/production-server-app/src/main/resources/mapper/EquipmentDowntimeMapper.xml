<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.EquipmentDowntimeMapper">

    <select id="getEquipmentDowntimePage"
            resultType="cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO">
        select t1.*,t2.process_code,t2.process_name from equipment_downtime t1
        left join equipment_downtime_process t2 on t2.equipment_downtime_id = t1.id
        where t1.deleted = 0
        order by t1.downtime_code asc
    </select>


    <select id="getOeeBaseEquipmentDowntimeList"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetOeeBaseEquipmentDowntime">
        select
        pmt.id as product_machine_task_id,
        pmt.reported_product_id,
        pm.company_code,
        pmt.machine_name,
        pt.process,
        pt.produce_date,
        pt.plan_ticket_no,
        CASE
        WHEN (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        THEN #{dto.startTime}
        ELSE pmt.start_time
        END AS start_time,
        CASE
        WHEN (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        THEN #{dto.endTime}
        ELSE pmt.end_time
        END AS end_time,
        IFNULL(pt.real_product,0) as real_product,
        IFNULL(pt.defective_product,0) as defective_product,
        IFNULL(pmt.reported_quantity,0) as reported_quantity,
        CASE
        WHEN TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime}
        THEN ROUND(TIMESTAMPDIFF(SECOND, pmt.start_time, pmt.end_time) / 60.0, 2)

        WHEN (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        THEN ROUND(TIMESTAMPDIFF(SECOND, #{dto.startTime}, pmt.end_time) / 60.0, 2)

        WHEN (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        THEN ROUND(TIMESTAMPDIFF(SECOND, pmt.start_time, #{dto.endTime}) / 60.0, 2)

        ELSE 0
        END AS down_time_minters,
        pmt.loss_type
        from
        product_machine_task pmt
        inner join (
        select
        id,
        erp_machine_name,
        company_code,
        row_number() over ( partition by erp_machine_name
        order by
        id desc ) as rn
        from
        production_machine) pm on
        pm.erp_machine_name = pmt.machine_name
        and pm.rn = 1
        left join product_ticket pt on
        pt.id = pmt.reported_product_id
        where
        pmt.deleted = 0
        and pmt.type = -1
        and pmt.loss_type is not null
        AND (
        (TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        OR (TIMESTAMP(pmt.start_time) &lt; #{dto.startTime} AND TIMESTAMP(pmt.end_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.end_time) &lt;= #{dto.endTime})
        OR (TIMESTAMP(pmt.end_time) &gt; #{dto.endTime} AND TIMESTAMP(pmt.start_time) &gt;= #{dto.startTime} AND TIMESTAMP(pmt.start_time) &lt;= #{dto.endTime})
        )
        <if test="dto.machineNameList != null and dto.machineNameList .size() > 0">
            AND pmt.machine_name in
            <foreach collection="dto.machineNameList" item="machineName" index="index" open="(" close=")" separator=",">
                #{machineName}
            </foreach>
        </if>
    </select>

    <select id="getOeeEquipmentDowntimeList"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime">

        select
        t.loss_type,
        count(1) as down_time_num,
        ROUND(SUM(IFNULL(t.reported_quantity, 0)), 2) as total_reported_quantity,
        ROUND(SUM(IFNULL(t.defective_product, 0)), 2) as total_defective_product_quantity,
        ROUND(SUM(IFNULL(t.duration_minutes, 0)), 0) as down_time_minters
        from
        (
        select
        pmt.id,
        pmt.reported_product_id,
        pm.company_code,
        pmt.machine_name,
        pt.process,
        pt.produce_date,
        pt.plan_ticket_no,
        pmt.start_time,
        pmt.end_time,
        pt.real_product,
        pt.defective_product,
        pmt.reported_quantity,
        case
        when ROUND(TIMESTAMPDIFF(second, pmt.start_time, pmt.end_time) / 60.0, 0) > (24 * 60) then (24 * 60)
        else ROUND(TIMESTAMPDIFF(second, pmt.start_time, pmt.end_time) / 60.0, 0)
        end as duration_minutes,
        pmt.loss_type
        from
        product_machine_task pmt
        inner join (
        select
        id,
        erp_machine_name,
        company_code,
        row_number() over ( partition by erp_machine_name
        order by
        id desc ) as rn
        from
        production_machine) pm on
        pm.erp_machine_name = pmt.machine_name
        and pm.rn = 1
        left join product_ticket pt on
        pt.id = pmt.reported_product_id
        where
        pmt.deleted = 0
        and pmt.loss_type is not null
        and date(pmt.start_time) &gt;= #{dto.startDate}
        and date(pmt.end_time) &lt;= #{dto.endDate}
        <if test="dto.machineNameList != null and dto.machineNameList .size() > 0">
            AND pmt.machine_name in
            <foreach collection="dto.machineNameList" item="machineName" index="index" open="(" close=")" separator=",">
                #{machineName}
            </foreach>
        </if>
             ) t
        group by t.loss_type having count(1)

    </select>
    <select id="getEquipmentDowntimeList"
            resultType="cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO">

        select
            pt.process_code,
            pt.process as process_name,
            ed.*
        from
            equipment_downtime ed
                left join equipment_downtime_process edp on
                ed.id = edp.equipment_downtime_id
                left join product_ticket pt on
                pt.process_code = edp.process_code
        where
            ed.deleted = 0
        <if test="inVO.downtimeCode != '' and inVO.downtimeCode != null">
            and ed.downtime_code LIKE CONCAT('%', #{inVO.downtimeCode},'%')
        </if>
          and ( ed.code_type = 1
        <if test="processCode != '' and processCode != null">
            or ( edp.process_code = #{processCode} and pt.id = #{inVO.productTicketId} )
        </if>
        )
        order by ed.downtime_code asc
    </select>

    <select id="getEquipmentDowntimeOeeList"
            resultType="cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO">

        select
        pt.process_code,
        pt.process as process_name,
        ed.*
        from
        equipment_downtime ed
        left join equipment_downtime_process edp on
        ed.id = edp.equipment_downtime_id
        left join (
        select
        id,
        process_code,
        process,
        company_code,
        row_number() over ( partition by process_code
        order by
        id desc ) as rn
        from
        product_ticket ) pt on
        pt.process_code = edp.process_code and pt.rn = 1
        where
        ed.deleted = 0
        <if test="inVO.downtimeCode != '' and inVO.downtimeCode != null">
            and ed.downtime_code LIKE CONCAT('%', #{inVO.downtimeCode},'%')
        </if>
        and ( ed.code_type = 1 or  edp.process_code = #{processCode} )
        order by ed.downtime_code asc
    </select>
</mapper>
