<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductSettlementEndProductionMapper">



    <select id="getSettlementEndProductionList" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionListOutVO">
        SELECT
            b.process as process_name,
            SUM( b.real_product ) as product_quantity,
            SUM( a.remaining_quantity ) as remaining_quantity,
            ( CASE WHEN  SUM( a.consumption_quantity )  IS NULL THEN 0 ELSE  SUM( a.consumption_quantity )  END ) AS consumption_quantity
        FROM product_ticket b
            LEFT JOIN ( SELECT product_ticket_id, SUM( remaining_quantity ) AS remaining_quantity, SUM( consumption_quantity ) AS consumption_quantity FROM product_pallet WHERE deleted = 0 GROUP BY product_ticket_id ) a
                  ON b.id = a.product_ticket_id
        WHERE
            b.deleted = 0
          AND b.status = 70
          AND b.plan_ticket_no = #{productTicketNo}
        GROUP BY
            b.process
    </select>

    <select id="getSettlementEndProductionCollectDetail" resultType="cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionCollectDetailOutVO">
        SELECT DISTINCT
            d.process,
            a.pallet_code,
            a.outbound_time,
            d.shift AS outbound_shift,
            a.produced_quantity AS product_quantity,
            b.loading_time,
            b.shift_source AS pallet_shift,
            b.loading_quantity,
            a.id AS outbound_id,
            a.product_ticket_id,
            b.requisition_ticket_id
        FROM
            product_ticket d
                LEFT JOIN product_outbound a  ON d.id = a.product_ticket_id AND a.STATUS = 1 AND a.deleted = 0
                LEFT JOIN product_pallet b ON a.product_ticket_id = b.product_ticket_id AND b.deleted = 0
        WHERE
            d.deleted = 0
           and d.status = 70 and d.plan_ticket_no = #{inVo.productTicketNo} and d.process = #{inVo.processName}
    </select>
</mapper>
