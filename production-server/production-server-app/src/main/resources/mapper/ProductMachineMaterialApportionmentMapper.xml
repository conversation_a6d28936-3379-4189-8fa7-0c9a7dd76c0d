<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMachineMaterialApportionmentMapper">

    <resultMap id="getMaterialDailyApportionmentResultMap" type="cn.jihong.mes.production.api.model.vo.out.GetMaterialDailySettlementOutVO">
        <result property="planTicketNo" column="plan_ticket_no"/>
        <result property="taskId" column="task_id"/>
        <result property="businessNo" column="business_no"/>
        <result property="materialBarcodeNo" column="material_barcode_no"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="materialUnit" column="material_unit"/>
        <result property="loadingQuantity" column="loading_quantity"/>
        <result property="consumptionQuantity" column="consumption_quantity"/>
        <result property="remainingQuantity" column="remaining_quantity"/>
        <result property="materialPlace" column="material_place"/>
        <result property="materialPlaceName" column="material_place_name"/>
        <result property="process" column="process"/>
        <result property="processCode" column="process_code"/>
    </resultMap>

    <select id="getMaterialDailyApportionment"
            resultMap="getMaterialDailyApportionmentResultMap">
        select * from
            product_machine_material_apportionment
        where machine_name = #{getMaterialDailySettlementInVO.machineName}
            and produce_date = #{getMaterialDailySettlementInVO.produceDate}
            and shift = #{getMaterialDailySettlementInVO.shift}
            AND deleted = 0
    </select>

    <select id="useMaterials" resultType="cn.jihong.mes.production.api.model.vo.out.GetProductUseMaterialsOutVO">
        select
            t1.id,
            t1.company_code,
            t1.machine_name,
            t1.produce_date,
            t1.shift,
            t1.plan_ticket_no,
            t1.material_barcode_no,
            t1.material_code,
            t1.material_name,
            t1.material_unit,
            t1.material_type,
            t1.material_place,
            t1.material_place_name,
            t1.purchase_batch,
            t1.use_type,
            t1.consumption_quantity,
            t1.remaining_quantity,
            'APPORTIONMENT' as table_type,
            t1.product_ticket_id
        from
            product_machine_material_apportionment t1
        where
            deleted = 0
          and (company_code = #{companyCode}
            and product_ticket_id = #{getProductUseMaterialsInVO.id})
        union all
        select
            t1.id,
            t1.company_code,
            t1.machine_name,
            t1.produce_date,
            t1.shift,
            t1.plan_ticket_no,
            t1.material_barcode_no,
            t1.material_code,
            t1.material_name,
            t1.material_unit,
            t1.material_type,
            t1.material_place,
            t1.material_place_name,
            t1.purchase_batch,
            t1.use_type,
            t1.consumption_quantity,
            t1.remaining_quantity,
            'RECORD' as table_type,
            t1.product_ticket_id
        from
            product_machine_material_record t1
        where
            deleted = 0
            and use_type = 30
          and (company_code = #{companyCode}
            and product_ticket_id = #{getProductUseMaterialsInVO.id})
    </select>
</mapper>
