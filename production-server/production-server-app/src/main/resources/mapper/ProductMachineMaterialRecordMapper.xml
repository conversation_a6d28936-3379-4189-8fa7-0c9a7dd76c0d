<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMachineMaterialRecordMapper">
    <select id="getConsumptionQuantity"
            resultType="cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO">
        select t.company_code,
               t.machine_name,
               t.produce_date,
               t.shift,
               t.plan_ticket_no,
               t.material_barcode_no,
               t.material_code,
               t.material_name,
               t.material_unit,
               t.parts,
               t.parts_name,
               sum(t.consumption_quantity) as consumptionQuantity,
               (
                   SELECT t2.remaining_quantity
                   FROM product_machine_material_record t2
                   <where>
                       <if test="productMachineMaterialRecordDTO.machineName != null and productMachineMaterialRecordDTO.machineName != ''">
                           and t2.machine_name = #{productMachineMaterialRecordDTO.machineName}
                       </if>
                       <if test="productMachineMaterialRecordDTO.produceDate != null ">
                           and t2.produce_date = #{productMachineMaterialRecordDTO.produceDate}
                       </if>
                       <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
                           and t2.shift = #{productMachineMaterialRecordDTO.shift}
                       </if>
                       and t2.deleted = 0
                       and t2.material_barcode_no = t.material_barcode_no
                   </where>
                   ORDER BY t2.id DESC
                   LIMIT 1
               ) AS remainingQuantity,
               t.material_type,
               t.material_place,
               t.material_place_name
        from product_machine_material_record t
        <where>
            <if test="productMachineMaterialRecordDTO.machineName != null and productMachineMaterialRecordDTO.machineName != ''">
                and t.machine_name = #{productMachineMaterialRecordDTO.machineName}
            </if>
            <if test="productMachineMaterialRecordDTO.produceDate != null">
                and t.produce_date = #{productMachineMaterialRecordDTO.produceDate}
            </if>
            <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
                and t.shift = #{productMachineMaterialRecordDTO.shift}
            </if>
            <if test="productMachineMaterialRecordDTO.materialBarcodeNo != null and productMachineMaterialRecordDTO.materialBarcodeNo != ''">
                and t.material_barcode_no = #{productMachineMaterialRecordDTO.materialBarcodeNo}
            </if>
            <if test="productMachineMaterialRecordDTO.materialCode != null and productMachineMaterialRecordDTO.materialCode != ''">
                and t.material_code = #{productMachineMaterialRecordDTO.materialCode}
            </if>
            <if test="productMachineMaterialRecordDTO.materialName != null and productMachineMaterialRecordDTO.materialName != ''">
                and t.material_name like concat('%', #{productMachineMaterialRecordDTO.materialName}, '%')
            </if>
            and t.deleted = 0
        </where>
        group by t.company_code,
                 t.machine_name,
                 t.produce_date,
                 t.shift,
                 t.plan_ticket_no,
                 t.material_barcode_no,
                 t.material_code,
                 t.material_name,
                 t.material_unit,
                 t.material_type,
                 t.material_place,
                 t.material_place_name,
                 t.parts,
                 t.parts_name
    </select>

    <select id="getConsumptionQuantityNeedDistribution"
            resultType="cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO">
        select t.company_code,
        t.machine_name,
        t.produce_date,
        t.shift,
        t.plan_ticket_no,
        t.material_barcode_no,
        t.material_code,
        t.material_name,
        t.material_unit,
        t.parts,
        t.parts_name,
        sum(t.consumption_quantity) as consumptionQuantity,
        (
        SELECT t2.remaining_quantity
        FROM product_machine_material_record t2
        where t2.machine_name = #{productMachineMaterialRecordDTO.machineName}
        <if test="productMachineMaterialRecordDTO.produceDate != null ">
            and t2.produce_date = #{productMachineMaterialRecordDTO.produceDate}
        </if>
        <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
            and t2.shift = #{productMachineMaterialRecordDTO.shift}
        </if>
        and t2.deleted = 0
        and t2.material_barcode_no = t.material_barcode_no
        ORDER BY t2.id DESC
        LIMIT 1
        ) AS remainingQuantity,
        t.material_type,
        t.material_place,
        t.material_place_name
        from product_machine_material_record t
        where t.machine_name = #{productMachineMaterialRecordDTO.machineName}
        <if test="productMachineMaterialRecordDTO.produceDate != null">
            and t.produce_date = #{productMachineMaterialRecordDTO.produceDate}
        </if>
        <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
            and t.shift = #{productMachineMaterialRecordDTO.shift}
        </if>
        and t.use_type = 20
        and t.deleted = 0
        group by t.company_code,
        t.machine_name,
        t.produce_date,
        t.shift,
        t.plan_ticket_no,
        t.material_barcode_no,
        t.material_code,
        t.material_name,
        t.material_unit,
        t.material_type,
        t.material_place,
        t.material_place_name,
        t.parts,
        t.parts_name
    </select>

    <select id="getConsumptionQuantityApp"
            resultType="cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO">
        select t.company_code,
            t.machine_name,
            t.produce_date,
            t.shift,
            t.plan_ticket_no,
            t.material_barcode_no,
            t.material_code,
            t.material_name,
            t.material_unit,
            t.parts,
            t.parts_name,
            sum(t.consumption_quantity) as consumptionQuantity,
            (
                SELECT t2.remaining_quantity
                FROM product_machine_material_record t2
                where t2.machine_name = #{productMachineMaterialRecordDTO.machineName}
                <if test="productMachineMaterialRecordDTO.produceDate != null ">
                    and t2.produce_date = #{productMachineMaterialRecordDTO.produceDate}
                </if>
                <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
                    and t2.shift = #{productMachineMaterialRecordDTO.shift}
                </if>
                and t2.deleted = 0
                and t2.material_barcode_no = t.material_barcode_no
                ORDER BY t2.id DESC
                LIMIT 1
            ) AS remainingQuantity,
            t.material_type,
            t.material_place,
            t.material_place_name
        from product_machine_material_record t
        where t.machine_name = #{productMachineMaterialRecordDTO.machineName}
            <if test="productMachineMaterialRecordDTO.produceDate != null">
                and t.produce_date = #{productMachineMaterialRecordDTO.produceDate}
            </if>
            <if test="productMachineMaterialRecordDTO.shift != null and productMachineMaterialRecordDTO.shift != ''">
                and t.shift = #{productMachineMaterialRecordDTO.shift}
            </if>
            and t.use_type in (10, 20)
            and t.deleted = 0
        group by t.company_code,
            t.machine_name,
            t.produce_date,
            t.shift,
            t.plan_ticket_no,
            t.material_barcode_no,
            t.material_code,
            t.material_name,
            t.material_unit,
            t.material_type,
            t.material_place,
            t.material_place_name,
            t.parts,
            t.parts_name
    </select>
</mapper>
