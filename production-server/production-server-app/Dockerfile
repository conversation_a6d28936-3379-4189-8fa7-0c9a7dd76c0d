FROM openjdk:11
MAINTAINER lxl

#设置时区
RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone
#设置工作目录
WORKDIR /app/
#日志挂载
VOLUME ["/home/<USER>/app/logs/production-server:/data/logs/production-server", "/home/<USER>/app/logs:/app/gc_logs"]
#迁移java到工作目录
ADD ./target/production-server-app.jar /app/
#配置容器内部端口为9003
EXPOSE 9003
#docker run时执行
CMD ["java", "-Xms128m", "-Xmx1024m", "-Ddubbo.reference.check=false", "-Ddubbo.registry.check=false", "-Ddubbo.consumer.check=false", "-jar", "/app/production-server-app.jar"]
